<?php

// Simple database connection
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'smartflo';

$mysqli = new mysqli($host, $username, $password, $database);

if ($mysqli->connect_error) {
    die('Connection failed: ' . $mysqli->connect_error);
}

try {
    // Show all tables first
    echo "All tables in database:\n";
    $result = $mysqli->query("SHOW TABLES");
    while ($row = $result->fetch_array()) {
        echo "- {$row[0]}\n";
    }
    
    // Check projects table structure first
    echo "\nprojects table structure:\n";
    $result = $mysqli->query("DESCRIBE projects");
    while ($row = $result->fetch_assoc()) {
        echo "{$row['Field']}: {$row['Type']} {$row['Null']} {$row['Default']}\n";
    }

    // Check if project_tasks table exists
    $result = $mysqli->query("SHOW TABLES LIKE 'project_tasks'");
    if ($result->num_rows > 0) {
        echo "\nproject_tasks table structure:\n";
        $result = $mysqli->query("DESCRIBE project_tasks");
        while ($row = $result->fetch_assoc()) {
            echo "{$row['Field']}: {$row['Type']} {$row['Null']} {$row['Default']}\n";
        }
    } else {
        echo "\nproject_tasks table does NOT exist!\n";
        
        // Create the table without foreign keys first
        echo "Creating project_tasks table...\n";
        $sql = "CREATE TABLE project_tasks (
            id INT AUTO_INCREMENT PRIMARY KEY,
            project_id BIGINT UNSIGNED NOT NULL,
            task_type_id INT NULL,
            task_name VARCHAR(150) NOT NULL,
            description TEXT NULL,
            assigned_to BIGINT UNSIGNED NOT NULL,
            status ENUM('not_started','in_progress','on_hold','completed') DEFAULT 'not_started',
            priority ENUM('low','medium','high','urgent') DEFAULT 'medium',
            depends_on INT NULL,
            estimated_hours INT NULL,
            target_days INT NULL,
            task_order INT NULL,
            is_locked BOOLEAN DEFAULT FALSE,
            auto_activated_at DATETIME NULL,
            actual_hours INT NULL,
            start_date DATE NULL,
            due_date DATE NULL,
            completed_date DATETIME NULL,
            notes TEXT NULL,
            metadata JSON NULL,
            created_by BIGINT UNSIGNED NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            deleted_at DATETIME NULL
        )";
        
        if ($mysqli->query($sql)) {
            echo "project_tasks table created successfully!\n";
        } else {
            echo "Error creating table: " . $mysqli->error . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

$mysqli->close();
