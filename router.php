<?php

/**
 * CodeIgniter 4 Router for PHP Built-in Server
 *
 * This file is used when running CodeIgniter 4 with PHP's built-in development server.
 * It handles routing for static files and passes dynamic requests to CodeIgniter.
 */

// Get the requested URI
$uri = urldecode(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH));

// If the request is for a static file that exists, serve it directly
if ($uri !== '/' && file_exists(__DIR__ . '/public' . $uri)) {
    return false; // Let PHP serve the static file
}

// For all other requests, route through CodeIgniter's front controller
$_SERVER['SCRIPT_NAME'] = '/index.php';
require_once __DIR__ . '/public/index.php';
