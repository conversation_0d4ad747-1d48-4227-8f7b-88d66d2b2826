<?php

// Add password policy fields to users table
$host = 'localhost';
$dbname = 'smartflo_auth';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Adding password policy fields to users table...\n";
    
    // Check if columns already exist
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('password_changed_at', $columns)) {
        $pdo->exec("ALTER TABLE users ADD COLUMN password_changed_at DATETIME NULL COMMENT 'When password was last changed'");
        echo "✓ Added password_changed_at column\n";
    } else {
        echo "✓ password_changed_at column already exists\n";
    }
    
    if (!in_array('force_password_change', $columns)) {
        $pdo->exec("ALTER TABLE users ADD COLUMN force_password_change BOOLEAN DEFAULT FALSE COMMENT 'Force user to change password on next login'");
        echo "✓ Added force_password_change column\n";
    } else {
        echo "✓ force_password_change column already exists\n";
    }
    
    if (!in_array('first_login', $columns)) {
        $pdo->exec("ALTER TABLE users ADD COLUMN first_login BOOLEAN DEFAULT TRUE COMMENT 'Is this the users first login'");
        echo "✓ Added first_login column\n";
    } else {
        echo "✓ first_login column already exists\n";
    }
    
    // Update existing admin user
    $stmt = $pdo->prepare("UPDATE users SET password_changed_at = NOW(), first_login = FALSE WHERE username = 'admin'");
    $stmt->execute();
    echo "✓ Updated admin user password policy fields\n";
    
    echo "\nPassword policy fields added successfully!\n";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
