-- SmartFlo Authentication System Database Schema
-- MySQL 8.0+ Compatible
-- Created: 2024

SET FOREIGN_KEY_CHECKS = 0;

-- Create database
CREATE DATABASE IF NOT EXISTS `smartflo_auth` 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `smartflo_auth`;

-- Users table
CREATE TABLE `users` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL,
    `email` varchar(255) NOT NULL,
    `password_hash` varchar(255) NOT NULL,
    `is_active` tinyint(1) NOT NULL DEFAULT 1,
    `last_login` datetime DEFAULT NULL,
    `created_at` datetime DEFAULT NULL,
    `updated_at` datetime DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `username` (`username`),
    UNIQUE KEY `email` (`email`),
    <PERSON><PERSON><PERSON> `is_active` (`is_active`),
    <PERSON><PERSON>Y `last_login` (`last_login`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Roles table
CREATE TABLE `roles` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `role_name` varchar(50) NOT NULL,
    `description` text DEFAULT NULL,
    `permissions` json DEFAULT NULL,
    `created_at` datetime DEFAULT NULL,
    `updated_at` datetime DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `role_name` (`role_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User roles junction table
CREATE TABLE `user_roles` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(11) unsigned NOT NULL,
    `role_id` int(11) unsigned NOT NULL,
    `created_at` datetime DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `user_role_unique` (`user_id`, `role_id`),
    KEY `user_id` (`user_id`),
    KEY `role_id` (`role_id`),
    CONSTRAINT `user_roles_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `user_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Remember tokens table
CREATE TABLE `remember_tokens` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(11) unsigned NOT NULL,
    `token_hash` varchar(255) NOT NULL,
    `expires_at` datetime NOT NULL,
    `created_at` datetime DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `token_hash` (`token_hash`),
    KEY `user_id` (`user_id`),
    KEY `expires_at` (`expires_at`),
    CONSTRAINT `remember_tokens_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Password resets table
CREATE TABLE `password_resets` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `email` varchar(255) NOT NULL,
    `token_hash` varchar(255) NOT NULL,
    `expires_at` datetime NOT NULL,
    `created_at` datetime DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `email` (`email`),
    KEY `token_hash` (`token_hash`),
    KEY `expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Login attempts table
CREATE TABLE `login_attempts` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `ip_address` varchar(45) NOT NULL,
    `username` varchar(50) DEFAULT NULL,
    `attempts` int(11) NOT NULL DEFAULT 1,
    `last_attempt` datetime NOT NULL,
    `created_at` datetime DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `ip_address` (`ip_address`),
    KEY `username` (`username`),
    KEY `last_attempt` (`last_attempt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default roles
INSERT INTO `roles` (`role_name`, `description`, `permissions`, `created_at`, `updated_at`) VALUES
('admin', 'System Administrator', JSON_ARRAY('user.create', 'user.read', 'user.update', 'user.delete', 'role.create', 'role.read', 'role.update', 'role.delete', 'admin.access'), NOW(), NOW()),
('user', 'Regular User', JSON_ARRAY('user.read'), NOW(), NOW());

-- Insert default admin user
-- Password: Admin@123
INSERT INTO `users` (`username`, `email`, `password_hash`, `is_active`, `created_at`, `updated_at`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, NOW(), NOW());

-- Assign admin role to admin user
INSERT INTO `user_roles` (`user_id`, `role_id`, `created_at`) VALUES
(1, 1, NOW());

-- Create indexes for performance
CREATE INDEX `idx_users_active_created` ON `users` (`is_active`, `created_at`);
CREATE INDEX `idx_users_last_login` ON `users` (`last_login`);
CREATE INDEX `idx_login_attempts_ip_time` ON `login_attempts` (`ip_address`, `last_attempt`);
CREATE INDEX `idx_password_resets_email_time` ON `password_resets` (`email`, `created_at`);
CREATE INDEX `idx_remember_tokens_expires` ON `remember_tokens` (`expires_at`);

-- Create views for common queries
CREATE VIEW `user_roles_view` AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.is_active,
    u.last_login,
    u.created_at,
    GROUP_CONCAT(r.role_name) as roles,
    GROUP_CONCAT(r.permissions) as permissions
FROM users u
LEFT JOIN user_roles ur ON u.id = ur.user_id
LEFT JOIN roles r ON ur.role_id = r.id
GROUP BY u.id;

-- Note: Stored procedures and events should be created separately
-- due to DELIMITER limitations in some MySQL clients

SET FOREIGN_KEY_CHECKS = 1;

-- Sample data for testing (optional)
-- Uncomment the following lines to insert sample data

/*
-- Sample users
INSERT INTO `users` (`username`, `email`, `password_hash`, `is_active`, `created_at`, `updated_at`) VALUES
('john_doe', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, NOW(), NOW()),
('jane_smith', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, NOW(), NOW()),
('bob_wilson', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 0, NOW(), NOW());

-- Assign user role to sample users
INSERT INTO `user_roles` (`user_id`, `role_id`, `created_at`) VALUES
(2, 2, NOW()),
(3, 2, NOW()),
(4, 2, NOW());

-- Sample role
INSERT INTO `roles` (`role_name`, `description`, `permissions`, `created_at`, `updated_at`) VALUES
('moderator', 'Content Moderator', JSON_ARRAY('user.read', 'user.update'), NOW(), NOW());
*/

-- Display setup completion message
SELECT 'SmartFlo Authentication System database setup completed successfully!' as message;
