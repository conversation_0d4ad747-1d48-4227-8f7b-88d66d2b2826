-- Create project_timeline table if it doesn't exist
CREATE TABLE IF NOT EXISTS `project_timeline` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `project_id` int(11) unsigned NOT NULL,
    `user_id` int(11) unsigned NOT NULL,
    `action_type` enum('status_change','comment_added','file_uploaded','project_created','project_updated','assignment_changed') NOT NULL DEFAULT 'status_change',
    `old_status` varchar(50) DEFAULT NULL,
    `new_status` varchar(50) DEFAULT NULL,
    `title` varchar(255) NOT NULL,
    `description` text DEFAULT NULL,
    `notes` text DEFAULT NULL,
    `file_path` varchar(500) DEFAULT NULL,
    `metadata` json DEFAULT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY <PERSON>EY (`id`),
    <PERSON><PERSON><PERSON> `idx_project_timeline_project_id` (`project_id`),
    <PERSON><PERSON><PERSON> `idx_project_timeline_user_id` (`user_id`),
    KEY `idx_project_timeline_action_type` (`action_type`),
    KEY `idx_project_timeline_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
