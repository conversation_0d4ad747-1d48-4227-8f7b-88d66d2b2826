<?php

namespace App\Models;

use CodeIgniter\Model;

class SiteStatusModel extends Model
{
    protected $table = 'site_status';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'status',
        'message',
        'estimated_completion',
        'updated_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation rules
    protected $validationRules = [
        'status' => 'required|in_list[online,maintenance,construction,offline]',
        'message' => 'max_length[500]',
        'updated_by' => 'required|integer'
    ];

    protected $validationMessages = [
        'status' => [
            'required' => 'Status is required',
            'in_list' => 'Status must be one of: online, maintenance, construction, offline'
        ],
        'updated_by' => [
            'required' => 'Updated by user ID is required',
            'integer' => 'Updated by must be a valid user ID'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;

    /**
     * Get current site status
     */
    public function getCurrentStatus()
    {
        $status = $this->select('site_status.*, users.username as updated_by_username')
                      ->join('users', 'users.id = site_status.updated_by', 'left')
                      ->orderBy('site_status.updated_at', 'DESC')
                      ->first();

        if (!$status) {
            // Return default status if none exists
            return [
                'id' => null,
                'status' => 'online',
                'message' => null,
                'estimated_completion' => null,
                'updated_by' => null,
                'updated_by_username' => 'System',
                'updated_at' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s')
            ];
        }

        return $status;
    }

    /**
     * Update site status
     */
    public function updateStatus($data)
    {
        return $this->insert($data);
    }

    /**
     * Get status history
     */
    public function getStatusHistory($limit = 20, $offset = 0)
    {
        return $this->select('site_status.*, users.username as updated_by_username')
                   ->join('users', 'users.id = site_status.updated_by', 'left')
                   ->orderBy('site_status.updated_at', 'DESC')
                   ->limit($limit, $offset)
                   ->find();
    }

    /**
     * Count status history records
     */
    public function countStatusHistory()
    {
        return $this->countAllResults();
    }

    /**
     * Get status statistics
     */
    public function getStatusStats()
    {
        // Get status distribution
        $statusDistribution = $this->select('status, COUNT(*) as count')
                                  ->groupBy('status')
                                  ->find();

        // Get recent changes (last 30 days)
        $recentChanges = $this->where('updated_at >=', date('Y-m-d H:i:s', strtotime('-30 days')))
                             ->countAllResults();

        // Get current uptime (time since last non-online status)
        $lastDowntime = $this->where('status !=', 'online')
                            ->orderBy('updated_at', 'DESC')
                            ->first();

        $uptime = null;
        if ($lastDowntime) {
            $uptimeStart = strtotime($lastDowntime['updated_at']);
            $uptime = time() - $uptimeStart;
        }

        // Get average downtime per month
        $monthlyDowntime = $this->select('YEAR(updated_at) as year, MONTH(updated_at) as month, COUNT(*) as downtime_events')
                               ->where('status !=', 'online')
                               ->where('updated_at >=', date('Y-m-d H:i:s', strtotime('-12 months')))
                               ->groupBy('YEAR(updated_at), MONTH(updated_at)')
                               ->find();

        return [
            'status_distribution' => $statusDistribution,
            'recent_changes' => $recentChanges,
            'current_uptime' => $uptime,
            'monthly_downtime' => $monthlyDowntime,
            'total_status_changes' => $this->countAllResults()
        ];
    }

    /**
     * Schedule maintenance
     */
    public function scheduleMaintenance($data)
    {
        $db = \Config\Database::connect();
        $builder = $db->table('scheduled_maintenance');
        
        return $builder->insert($data);
    }

    /**
     * Get scheduled maintenance
     */
    public function getScheduledMaintenance()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('scheduled_maintenance');
        
        return $builder->select('scheduled_maintenance.*, users.username as scheduled_by_username')
                      ->join('users', 'users.id = scheduled_maintenance.scheduled_by', 'left')
                      ->where('start_time >', date('Y-m-d H:i:s'))
                      ->where('is_cancelled', 0)
                      ->orderBy('start_time', 'ASC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Cancel maintenance
     */
    public function cancelMaintenance($maintenanceId)
    {
        $db = \Config\Database::connect();
        $builder = $db->table('scheduled_maintenance');
        
        return $builder->where('id', $maintenanceId)
                      ->update(['is_cancelled' => 1, 'cancelled_at' => date('Y-m-d H:i:s')]);
    }

    /**
     * Get maintenance history
     */
    public function getMaintenanceHistory($limit = 20, $offset = 0)
    {
        $db = \Config\Database::connect();
        $builder = $db->table('scheduled_maintenance');
        
        return $builder->select('scheduled_maintenance.*, users.username as scheduled_by_username')
                      ->join('users', 'users.id = scheduled_maintenance.scheduled_by', 'left')
                      ->orderBy('created_at', 'DESC')
                      ->limit($limit, $offset)
                      ->get()
                      ->getResultArray();
    }

    /**
     * Check if site is currently in maintenance
     */
    public function isInMaintenance()
    {
        $currentStatus = $this->getCurrentStatus();
        return in_array($currentStatus['status'], ['maintenance', 'construction', 'offline']);
    }

    /**
     * Get upcoming maintenance
     */
    public function getUpcomingMaintenance()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('scheduled_maintenance');
        
        return $builder->select('scheduled_maintenance.*, users.username as scheduled_by_username')
                      ->join('users', 'users.id = scheduled_maintenance.scheduled_by', 'left')
                      ->where('start_time >', date('Y-m-d H:i:s'))
                      ->where('start_time <=', date('Y-m-d H:i:s', strtotime('+7 days')))
                      ->where('is_cancelled', 0)
                      ->orderBy('start_time', 'ASC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Get status for specific date range
     */
    public function getStatusForDateRange($startDate, $endDate)
    {
        return $this->select('site_status.*, users.username as updated_by_username')
                   ->join('users', 'users.id = site_status.updated_by', 'left')
                   ->where('site_status.updated_at >=', $startDate)
                   ->where('site_status.updated_at <=', $endDate)
                   ->orderBy('site_status.updated_at', 'ASC')
                   ->find();
    }

    /**
     * Calculate uptime percentage for date range
     */
    public function calculateUptimePercentage($startDate, $endDate)
    {
        $totalTime = strtotime($endDate) - strtotime($startDate);
        $statusChanges = $this->getStatusForDateRange($startDate, $endDate);
        
        $uptimeSeconds = 0;
        $currentTime = strtotime($startDate);
        $currentStatus = 'online'; // Assume online at start
        
        foreach ($statusChanges as $change) {
            $changeTime = strtotime($change['updated_at']);
            
            // Add time for current status
            if ($currentStatus === 'online') {
                $uptimeSeconds += ($changeTime - $currentTime);
            }
            
            $currentTime = $changeTime;
            $currentStatus = $change['status'];
        }
        
        // Add remaining time if currently online
        if ($currentStatus === 'online') {
            $uptimeSeconds += (strtotime($endDate) - $currentTime);
        }
        
        return ($uptimeSeconds / $totalTime) * 100;
    }

    /**
     * Get status summary for dashboard
     */
    public function getStatusSummary()
    {
        $current = $this->getCurrentStatus();
        $upcoming = $this->getUpcomingMaintenance();
        $recentChanges = $this->getStatusHistory(5);
        
        // Calculate uptime for last 30 days
        $startDate = date('Y-m-d H:i:s', strtotime('-30 days'));
        $endDate = date('Y-m-d H:i:s');
        $uptimePercentage = $this->calculateUptimePercentage($startDate, $endDate);
        
        return [
            'current_status' => $current,
            'upcoming_maintenance' => $upcoming,
            'recent_changes' => $recentChanges,
            'uptime_30_days' => round($uptimePercentage, 2)
        ];
    }
}
