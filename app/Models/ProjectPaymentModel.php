<?php

namespace App\Models;

use CodeIgniter\Model;

class ProjectPaymentModel extends Model
{
    protected $table = 'project_payments';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'project_id',
        'payment_amount',
        'payment_method',
        'payment_status',
        'payment_date',
        'payment_notes',
        'invoice_number',
        'created_by'
    ];

    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'project_id' => 'required|integer',
        'payment_amount' => 'required|decimal|greater_than[0]',
        'payment_method' => 'required|in_list[cash,check,bank_transfer,credit_card,other]',
        'payment_status' => 'in_list[pending,partial,completed,overdue]',
        'created_by' => 'required|integer'
    ];

    protected $validationMessages = [
        'payment_amount' => [
            'required' => 'Payment amount is required',
            'decimal' => 'Payment amount must be a valid decimal number',
            'greater_than' => 'Payment amount must be greater than 0'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get payments for a specific project
     */
    public function getProjectPayments($projectId)
    {
        return $this->select('
            project_payments.*,
            users.username as created_by_username
        ')
        ->join('users', 'users.id = project_payments.created_by', 'left')
        ->where('project_payments.project_id', $projectId)
        ->orderBy('project_payments.created_at', 'DESC')
        ->findAll();
    }

    /**
     * Get payment summary for a project
     */
    public function getProjectPaymentSummary($projectId)
    {
        $payments = $this->where('project_id', $projectId)->findAll();
        
        $summary = [
            'total_amount' => 0,
            'paid_amount' => 0,
            'pending_amount' => 0,
            'payment_count' => count($payments),
            'last_payment_date' => null,
            'payment_status' => 'pending'
        ];

        foreach ($payments as $payment) {
            $summary['total_amount'] += $payment['payment_amount'];
            
            if (in_array($payment['payment_status'], ['completed', 'partial'])) {
                $summary['paid_amount'] += $payment['payment_amount'];
            } else {
                $summary['pending_amount'] += $payment['payment_amount'];
            }
            
            if ($payment['payment_date'] && 
                (!$summary['last_payment_date'] || $payment['payment_date'] > $summary['last_payment_date'])) {
                $summary['last_payment_date'] = $payment['payment_date'];
            }
        }

        // Determine overall payment status
        if ($summary['paid_amount'] >= $summary['total_amount'] && $summary['total_amount'] > 0) {
            $summary['payment_status'] = 'completed';
        } elseif ($summary['paid_amount'] > 0) {
            $summary['payment_status'] = 'partial';
        } else {
            $summary['payment_status'] = 'pending';
        }

        return $summary;
    }

    /**
     * Check if project has completed payments
     */
    public function isProjectPaymentCompleted($projectId)
    {
        $summary = $this->getProjectPaymentSummary($projectId);
        return $summary['payment_status'] === 'completed';
    }

    /**
     * Get overdue payments
     */
    public function getOverduePayments()
    {
        return $this->select('
            project_payments.*,
            projects.project_name,
            projects.client_name,
            users.username as created_by_username
        ')
        ->join('projects', 'projects.id = project_payments.project_id', 'left')
        ->join('users', 'users.id = project_payments.created_by', 'left')
        ->where('project_payments.payment_status', 'overdue')
        ->orWhere('project_payments.payment_date <', date('Y-m-d'))
        ->where('project_payments.payment_status !=', 'completed')
        ->orderBy('project_payments.payment_date', 'ASC')
        ->findAll();
    }

    /**
     * Get payment statistics
     */
    public function getPaymentStats()
    {
        $stats = [
            'total_payments' => $this->countAll(),
            'pending_payments' => $this->where('payment_status', 'pending')->countAllResults(),
            'completed_payments' => $this->where('payment_status', 'completed')->countAllResults(),
            'overdue_payments' => $this->where('payment_status', 'overdue')->countAllResults(),
            'total_amount' => 0,
            'paid_amount' => 0,
            'pending_amount' => 0
        ];

        // Calculate amounts
        $allPayments = $this->findAll();
        foreach ($allPayments as $payment) {
            $stats['total_amount'] += $payment['payment_amount'];
            
            if ($payment['payment_status'] === 'completed') {
                $stats['paid_amount'] += $payment['payment_amount'];
            } else {
                $stats['pending_amount'] += $payment['payment_amount'];
            }
        }

        return $stats;
    }

    /**
     * Create payment record
     */
    public function createPayment($projectId, $paymentData, $createdBy)
    {
        $data = array_merge($paymentData, [
            'project_id' => $projectId,
            'created_by' => $createdBy,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        return $this->insert($data);
    }

    /**
     * Update payment status
     */
    public function updatePaymentStatus($paymentId, $status, $notes = null)
    {
        $updateData = [
            'payment_status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($notes) {
            $updateData['payment_notes'] = $notes;
        }

        if ($status === 'completed' && !$this->find($paymentId)['payment_date']) {
            $updateData['payment_date'] = date('Y-m-d');
        }

        return $this->update($paymentId, $updateData);
    }

    /**
     * Get payment methods for dropdown
     */
    public function getPaymentMethods()
    {
        return [
            'cash' => 'Cash',
            'check' => 'Check',
            'bank_transfer' => 'Bank Transfer',
            'credit_card' => 'Credit Card',
            'other' => 'Other'
        ];
    }

    /**
     * Get payment statuses for dropdown
     */
    public function getPaymentStatuses()
    {
        return [
            'pending' => 'Pending',
            'partial' => 'Partial',
            'completed' => 'Completed',
            'overdue' => 'Overdue'
        ];
    }

    /**
     * Generate invoice number
     */
    public function generateInvoiceNumber($projectId)
    {
        $project = model('ProjectModel')->find($projectId);
        $year = date('Y');
        $month = date('m');
        
        // Count existing payments for this project this month
        $count = $this->where('project_id', $projectId)
                     ->where('YEAR(created_at)', $year)
                     ->where('MONTH(created_at)', $month)
                     ->countAllResults();
        
        $sequence = str_pad($count + 1, 3, '0', STR_PAD_LEFT);
        
        return "INV-{$project['project_id']}-{$year}{$month}-{$sequence}";
    }
}
