<?php

namespace App\Models;

use CodeIgniter\Model;

class DashboardControlModel extends Model
{
    protected $table = 'dashboard_controls';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'title',
        'description',
        'icon',
        'action_type',
        'action_value',
        'color',
        'roles',
        'is_active',
        'sort_order',
        'created_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation rules
    protected $validationRules = [
        'title' => 'required|max_length[100]',
        'icon' => 'required|max_length[50]',
        'action_type' => 'required|in_list[url,function,modal]',
        'action_value' => 'required|max_length[255]',
        'color' => 'required|max_length[20]',
        'created_by' => 'required|integer'
    ];

    protected $validationMessages = [
        'title' => [
            'required' => 'Title is required',
            'max_length' => 'Title cannot exceed 100 characters'
        ],
        'icon' => [
            'required' => 'Icon is required',
            'max_length' => 'Icon cannot exceed 50 characters'
        ],
        'action_type' => [
            'required' => 'Action type is required',
            'in_list' => 'Action type must be one of: url, function, modal'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;

    /**
     * Get controls for a specific user based on their roles
     */
    public function getControlsForUser($userId)
    {
        // For now, we'll get user roles from session or a simple check
        // In a full implementation, you'd join with user roles table
        
        return $this->select('dashboard_controls.*, users.username as created_by_username')
                   ->join('users', 'users.id = dashboard_controls.created_by', 'left')
                   ->where('dashboard_controls.is_active', 1)
                   ->orderBy('dashboard_controls.sort_order', 'ASC')
                   ->orderBy('dashboard_controls.created_at', 'ASC')
                   ->find();
    }

    /**
     * Get all controls (for admin)
     */
    public function getAllControls()
    {
        return $this->select('dashboard_controls.*, users.username as created_by_username')
                   ->join('users', 'users.id = dashboard_controls.created_by', 'left')
                   ->orderBy('dashboard_controls.sort_order', 'ASC')
                   ->orderBy('dashboard_controls.created_at', 'ASC')
                   ->find();
    }

    /**
     * Get active controls
     */
    public function getActiveControls()
    {
        return $this->where('is_active', 1)
                   ->orderBy('sort_order', 'ASC')
                   ->orderBy('created_at', 'ASC')
                   ->find();
    }

    /**
     * Get controls by role
     */
    public function getControlsByRole($role)
    {
        return $this->where('is_active', 1)
                   ->like('roles', $role)
                   ->orderBy('sort_order', 'ASC')
                   ->orderBy('created_at', 'ASC')
                   ->find();
    }

    /**
     * Get default dashboard controls for construction management
     */
    public function getDefaultControls()
    {
        return [
            [
                'title' => 'Admin Panel',
                'description' => 'Access administrative functions and system settings',
                'icon' => 'fas fa-shield-alt',
                'action_type' => 'url',
                'action_value' => '/admin/panel',
                'color' => 'danger',
                'roles' => 'admin',
                'is_active' => 1,
                'sort_order' => 1
            ],
            [
                'title' => 'Project Status',
                'description' => 'Monitor construction project progress and status',
                'icon' => 'fas fa-building',
                'action_type' => 'url',
                'action_value' => '/projects/status',
                'color' => 'primary',
                'roles' => 'admin,manager,staff',
                'is_active' => 1,
                'sort_order' => 2
            ],
            [
                'title' => 'Messages',
                'description' => 'Team communication and project updates',
                'icon' => 'fas fa-envelope',
                'action_type' => 'url',
                'action_value' => '/messages',
                'color' => 'success',
                'roles' => 'user,admin,manager,staff',
                'is_active' => 1,
                'sort_order' => 3
            ],
            [
                'title' => 'Task Manager',
                'description' => 'Manage construction tasks and assignments',
                'icon' => 'fas fa-tasks',
                'action_type' => 'url',
                'action_value' => '/tasks',
                'color' => 'info',
                'roles' => 'admin,manager,staff',
                'is_active' => 1,
                'sort_order' => 4
            ],
            [
                'title' => 'Add Material Expense',
                'description' => 'Record material costs and purchases',
                'icon' => 'fas fa-boxes',
                'action_type' => 'url',
                'action_value' => '/expenses/material',
                'color' => 'warning',
                'roles' => 'admin,manager',
                'is_active' => 1,
                'sort_order' => 5
            ],
            [
                'title' => 'Add Labour Expense',
                'description' => 'Track labor costs and worker payments',
                'icon' => 'fas fa-hard-hat',
                'action_type' => 'url',
                'action_value' => '/expenses/labour',
                'color' => 'secondary',
                'roles' => 'admin,manager',
                'is_active' => 1,
                'sort_order' => 6
            ],
            [
                'title' => 'Add Office Expense',
                'description' => 'Record office and administrative expenses',
                'icon' => 'fas fa-briefcase',
                'action_type' => 'url',
                'action_value' => '/expenses/office',
                'color' => 'dark',
                'roles' => 'admin,manager',
                'is_active' => 1,
                'sort_order' => 7
            ],
            [
                'title' => 'My Entry',
                'description' => 'Personal time tracking and daily reports',
                'icon' => 'fas fa-clock',
                'action_type' => 'url',
                'action_value' => '/timesheet/entry',
                'color' => 'primary',
                'roles' => 'user,admin,manager,staff',
                'is_active' => 1,
                'sort_order' => 8
            ],
            [
                'title' => 'Credit Purchases',
                'description' => 'Manage credit purchases and vendor payments',
                'icon' => 'fas fa-credit-card',
                'action_type' => 'url',
                'action_value' => '/purchases/credit',
                'color' => 'success',
                'roles' => 'admin,manager',
                'is_active' => 1,
                'sort_order' => 9
            ],
            [
                'title' => 'Client Pay',
                'description' => 'Track client payments and invoicing',
                'icon' => 'fas fa-money-bill-wave',
                'action_type' => 'url',
                'action_value' => '/payments/client',
                'color' => 'info',
                'roles' => 'admin,manager',
                'is_active' => 1,
                'sort_order' => 10
            ],
            [
                'title' => 'My Pettycash',
                'description' => 'Manage petty cash and small expenses',
                'icon' => 'fas fa-wallet',
                'action_type' => 'url',
                'action_value' => '/pettycash',
                'color' => 'warning',
                'roles' => 'admin,manager,staff',
                'is_active' => 1,
                'sort_order' => 11
            ],
            [
                'title' => 'Purchases',
                'description' => 'Material and equipment purchase management',
                'icon' => 'fas fa-shopping-cart',
                'action_type' => 'url',
                'action_value' => '/purchases',
                'color' => 'secondary',
                'roles' => 'admin,manager',
                'is_active' => 1,
                'sort_order' => 12
            ]
        ];
    }

    /**
     * Initialize default controls
     */
    public function initializeDefaultControls($createdBy)
    {
        $defaultControls = $this->getDefaultControls();
        $insertedCount = 0;

        foreach ($defaultControls as $control) {
            $control['created_by'] = $createdBy;
            
            // Check if control already exists
            $existing = $this->where('title', $control['title'])->first();
            if (!$existing) {
                if ($this->insert($control)) {
                    $insertedCount++;
                }
            }
        }

        return $insertedCount;
    }

    /**
     * Get control statistics
     */
    public function getControlStats()
    {
        $stats = [
            'total_controls' => $this->countAllResults(),
            'active_controls' => $this->where('is_active', 1)->countAllResults(),
            'inactive_controls' => $this->where('is_active', 0)->countAllResults()
        ];

        // Get controls by action type
        $actionTypes = $this->select('action_type, COUNT(*) as count')
                           ->groupBy('action_type')
                           ->find();

        $stats['by_action_type'] = $actionTypes;

        // Get controls by color
        $colors = $this->select('color, COUNT(*) as count')
                      ->groupBy('color')
                      ->find();

        $stats['by_color'] = $colors;

        return $stats;
    }

    /**
     * Search controls
     */
    public function searchControls($query, $limit = 20, $offset = 0)
    {
        return $this->select('dashboard_controls.*, users.username as created_by_username')
                   ->join('users', 'users.id = dashboard_controls.created_by', 'left')
                   ->groupStart()
                   ->like('dashboard_controls.title', $query)
                   ->orLike('dashboard_controls.description', $query)
                   ->orLike('dashboard_controls.action_value', $query)
                   ->groupEnd()
                   ->orderBy('dashboard_controls.sort_order', 'ASC')
                   ->limit($limit, $offset)
                   ->find();
    }

    /**
     * Get controls created by user
     */
    public function getControlsByCreator($userId, $limit = 20, $offset = 0)
    {
        return $this->where('created_by', $userId)
                   ->orderBy('created_at', 'DESC')
                   ->limit($limit, $offset)
                   ->find();
    }

    /**
     * Duplicate control
     */
    public function duplicateControl($controlId, $createdBy)
    {
        $control = $this->find($controlId);
        if (!$control) {
            return false;
        }

        // Remove ID and update metadata
        unset($control['id']);
        $control['title'] = $control['title'] . ' (Copy)';
        $control['created_by'] = $createdBy;
        $control['created_at'] = date('Y-m-d H:i:s');
        $control['updated_at'] = date('Y-m-d H:i:s');

        return $this->insert($control);
    }

    /**
     * Get next sort order
     */
    public function getNextSortOrder()
    {
        $maxOrder = $this->selectMax('sort_order')->first();
        return ($maxOrder['sort_order'] ?? 0) + 1;
    }

    /**
     * Reorder controls
     */
    public function reorderControls($controlIds)
    {
        $success = true;
        
        foreach ($controlIds as $index => $controlId) {
            if (!$this->update($controlId, ['sort_order' => $index + 1])) {
                $success = false;
            }
        }

        return $success;
    }

    /**
     * Get controls for dashboard display
     */
    public function getControlsForDashboard($userRoles = [])
    {
        $builder = $this->where('is_active', 1);

        // Filter by user roles if provided
        if (!empty($userRoles)) {
            $builder->groupStart();
            foreach ($userRoles as $role) {
                $builder->orLike('roles', $role);
            }
            $builder->groupEnd();
        }

        return $builder->orderBy('sort_order', 'ASC')
                      ->orderBy('created_at', 'ASC')
                      ->find();
    }
}
