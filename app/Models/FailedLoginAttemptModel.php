<?php

namespace App\Models;

use CodeIgniter\Model;

class FailedLoginAttemptModel extends Model
{
    protected $table = 'failed_login_attempts';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'username',
        'email', 
        'ip_address',
        'user_agent',
        'attempted_at',
        'reason',
        'blocked_until'
    ];

    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'username' => 'required|max_length[255]',
        'ip_address' => 'required|valid_ip',
        'attempted_at' => 'required'
    ];

    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Log a failed login attempt
     */
    public function logFailedAttempt($username, $email = null, $reason = 'invalid_credentials')
    {
        $request = service('request');
        
        $data = [
            'username' => $username,
            'email' => $email,
            'ip_address' => $request->getIPAddress(),
            'user_agent' => $request->getUserAgent()->getAgentString(),
            'attempted_at' => date('Y-m-d H:i:s'),
            'reason' => $reason
        ];

        return $this->insert($data);
    }

    /**
     * Get failed attempts for a specific username/IP in the last X minutes
     */
    public function getRecentAttempts($username, $ipAddress, $minutes = 15)
    {
        $since = date('Y-m-d H:i:s', strtotime("-{$minutes} minutes"));
        
        return $this->where('username', $username)
                   ->where('ip_address', $ipAddress)
                   ->where('attempted_at >=', $since)
                   ->countAllResults();
    }

    /**
     * Get all failed attempts for admin view
     */
    public function getFailedAttemptsForAdmin($limit = 100, $offset = 0)
    {
        return $this->orderBy('attempted_at', 'DESC')
                   ->limit($limit, $offset)
                   ->findAll();
    }

    /**
     * Get failed attempts by IP address
     */
    public function getAttemptsByIP($ipAddress, $hours = 24)
    {
        $since = date('Y-m-d H:i:s', strtotime("-{$hours} hours"));
        
        return $this->where('ip_address', $ipAddress)
                   ->where('attempted_at >=', $since)
                   ->orderBy('attempted_at', 'DESC')
                   ->findAll();
    }

    /**
     * Get failed attempts by username
     */
    public function getAttemptsByUsername($username, $hours = 24)
    {
        $since = date('Y-m-d H:i:s', strtotime("-{$hours} hours"));
        
        return $this->where('username', $username)
                   ->where('attempted_at >=', $since)
                   ->orderBy('attempted_at', 'DESC')
                   ->findAll();
    }

    /**
     * Check if IP is currently blocked
     */
    public function isIPBlocked($ipAddress)
    {
        $now = date('Y-m-d H:i:s');
        
        return $this->where('ip_address', $ipAddress)
                   ->where('blocked_until >', $now)
                   ->countAllResults() > 0;
    }

    /**
     * Block IP address for specified duration
     */
    public function blockIP($ipAddress, $minutes = 30)
    {
        $blockedUntil = date('Y-m-d H:i:s', strtotime("+{$minutes} minutes"));
        
        return $this->where('ip_address', $ipAddress)
                   ->where('blocked_until IS NULL OR blocked_until <=', date('Y-m-d H:i:s'))
                   ->set('blocked_until', $blockedUntil)
                   ->update();
    }

    /**
     * Clean old failed attempts (older than 30 days)
     */
    public function cleanOldAttempts()
    {
        $cutoff = date('Y-m-d H:i:s', strtotime('-30 days'));
        
        return $this->where('attempted_at <', $cutoff)->delete();
    }

    /**
     * Get statistics for dashboard
     */
    public function getStatistics()
    {
        $today = date('Y-m-d');
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        $thisWeek = date('Y-m-d', strtotime('-7 days'));
        
        return [
            'today' => $this->where('DATE(attempted_at)', $today)->countAllResults(),
            'yesterday' => $this->where('DATE(attempted_at)', $yesterday)->countAllResults(),
            'this_week' => $this->where('attempted_at >=', $thisWeek)->countAllResults(),
            'total' => $this->countAllResults(),
            'unique_ips_today' => $this->select('DISTINCT ip_address')
                                      ->where('DATE(attempted_at)', $today)
                                      ->countAllResults(),
            'blocked_ips' => $this->where('blocked_until >', date('Y-m-d H:i:s'))
                                 ->select('DISTINCT ip_address')
                                 ->countAllResults()
        ];
    }
}
