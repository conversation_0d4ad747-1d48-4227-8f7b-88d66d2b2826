<?php

namespace App\Models;

use CodeIgniter\Model;

class UserRoleModel extends Model
{
    protected $table = 'user_roles';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'user_id', 'role_id'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = '';

    // Validation
    protected $validationRules = [
        'user_id' => 'required|integer',
        'role_id' => 'required|integer',
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'User ID is required',
            'integer' => 'User ID must be an integer'
        ],
        'role_id' => [
            'required' => 'Role ID is required',
            'integer' => 'Role ID must be an integer'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Assign role to user
     */
    public function assignRole($userId, $roleId)
    {
        // Check if assignment already exists
        $existing = $this->where('user_id', $userId)
            ->where('role_id', $roleId)
            ->first();

        if ($existing) {
            return false; // Already assigned
        }

        return $this->insert([
            'user_id' => $userId,
            'role_id' => $roleId
        ]);
    }

    /**
     * Remove role from user
     */
    public function removeRole($userId, $roleId)
    {
        return $this->where('user_id', $userId)
            ->where('role_id', $roleId)
            ->delete();
    }

    /**
     * Remove all roles from user
     */
    public function removeAllUserRoles($userId)
    {
        return $this->where('user_id', $userId)->delete();
    }

    /**
     * Get user roles
     */
    public function getUserRoles($userId)
    {
        return $this->select('roles.*')
            ->join('roles', 'roles.id = user_roles.role_id')
            ->where('user_roles.user_id', $userId)
            ->findAll();
    }

    /**
     * Get role users
     */
    public function getRoleUsers($roleId)
    {
        return $this->select('users.*')
            ->join('users', 'users.id = user_roles.user_id')
            ->where('user_roles.role_id', $roleId)
            ->findAll();
    }

    /**
     * Sync user roles
     */
    public function syncUserRoles($userId, $roleIds)
    {
        // Start transaction
        $this->db->transStart();

        // Remove all existing roles
        $this->removeAllUserRoles($userId);

        // Add new roles
        if (!empty($roleIds)) {
            $data = [];
            foreach ($roleIds as $roleId) {
                $data[] = [
                    'user_id' => $userId,
                    'role_id' => $roleId,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            $this->insertBatch($data);
        }

        // Complete transaction
        $this->db->transComplete();

        return $this->db->transStatus();
    }
}
