<?php

namespace App\Models;

use CodeIgniter\Model;

class LoginAttemptModel extends Model
{
    protected $table = 'login_attempts';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'ip_address', 'username', 'attempts', 'last_attempt'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = '';

    // Validation
    protected $validationRules = [
        'ip_address' => 'required|max_length[45]',
        'username' => 'permit_empty|max_length[50]',
        'attempts' => 'required|integer',
        'last_attempt' => 'required|valid_date',
    ];

    protected $validationMessages = [
        'ip_address' => [
            'required' => 'IP address is required',
            'max_length' => 'IP address cannot exceed 45 characters'
        ],
        'username' => [
            'max_length' => 'Username cannot exceed 50 characters'
        ],
        'attempts' => [
            'required' => 'Attempts count is required',
            'integer' => 'Attempts must be an integer'
        ],
        'last_attempt' => [
            'required' => 'Last attempt time is required',
            'valid_date' => 'Please provide a valid date'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Record login attempt
     */
    public function recordAttempt($ipAddress, $username = null)
    {
        $existing = $this->where('ip_address', $ipAddress);
        
        if ($username) {
            $existing->where('username', $username);
        } else {
            $existing->where('username IS NULL');
        }
        
        $record = $existing->first();

        if ($record) {
            // Update existing record
            return $this->update($record['id'], [
                'attempts' => $record['attempts'] + 1,
                'last_attempt' => date('Y-m-d H:i:s')
            ]);
        } else {
            // Create new record
            return $this->insert([
                'ip_address' => $ipAddress,
                'username' => $username,
                'attempts' => 1,
                'last_attempt' => date('Y-m-d H:i:s')
            ]);
        }
    }

    /**
     * Check if IP/username is rate limited
     */
    public function isRateLimited($ipAddress, $username = null, $maxAttempts = 5, $windowMinutes = 15)
    {
        $timeLimit = date('Y-m-d H:i:s', strtotime("-{$windowMinutes} minutes"));
        
        $builder = $this->where('ip_address', $ipAddress)
            ->where('last_attempt >', $timeLimit)
            ->where('attempts >=', $maxAttempts);
        
        if ($username) {
            $builder->where('username', $username);
        }
        
        return $builder->countAllResults() > 0;
    }

    /**
     * Get remaining lockout time
     */
    public function getRemainingLockoutTime($ipAddress, $username = null, $windowMinutes = 15)
    {
        $builder = $this->where('ip_address', $ipAddress);
        
        if ($username) {
            $builder->where('username', $username);
        }
        
        $record = $builder->orderBy('last_attempt', 'DESC')->first();
        
        if (!$record) {
            return 0;
        }
        
        $lockoutEnd = strtotime($record['last_attempt']) + ($windowMinutes * 60);
        $remaining = $lockoutEnd - time();
        
        return max(0, $remaining);
    }

    /**
     * Clear attempts for IP/username
     */
    public function clearAttempts($ipAddress, $username = null)
    {
        $builder = $this->where('ip_address', $ipAddress);
        
        if ($username) {
            $builder->where('username', $username);
        }
        
        return $builder->delete();
    }

    /**
     * Clean old attempts
     */
    public function cleanOldAttempts($daysOld = 7)
    {
        $timeLimit = date('Y-m-d H:i:s', strtotime("-{$daysOld} days"));
        return $this->where('last_attempt <', $timeLimit)->delete();
    }

    /**
     * Get attempt statistics
     */
    public function getAttemptStats($hours = 24)
    {
        $timeLimit = date('Y-m-d H:i:s', strtotime("-{$hours} hours"));
        
        return [
            'total_attempts' => $this->where('last_attempt >', $timeLimit)->countAllResults(),
            'unique_ips' => $this->select('DISTINCT ip_address')->where('last_attempt >', $timeLimit)->countAllResults(),
            'blocked_ips' => $this->where('last_attempt >', $timeLimit)->where('attempts >=', 5)->countAllResults()
        ];
    }
}
