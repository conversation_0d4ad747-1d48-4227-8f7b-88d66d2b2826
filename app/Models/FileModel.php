<?php

namespace App\Models;

use CodeIgniter\Model;

class FileModel extends Model
{
    protected $table = 'files';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'filename',
        'original_name',
        'file_path',
        'file_size',
        'mime_type',
        'uploaded_by',
        'file_type',
        'is_public',
        'metadata',
        'access_count',
        'last_accessed'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation rules
    protected $validationRules = [
        'filename' => 'required|max_length[255]',
        'original_name' => 'required|max_length[255]',
        'file_path' => 'required|max_length[500]',
        'file_size' => 'required|integer',
        'mime_type' => 'required|max_length[100]',
        'uploaded_by' => 'required|integer',
        'file_type' => 'required|in_list[image,document,video,audio,other]',
        'is_public' => 'required|in_list[0,1]'
    ];

    protected $validationMessages = [
        'filename' => [
            'required' => 'Filename is required',
            'max_length' => 'Filename cannot exceed 255 characters'
        ],
        'file_size' => [
            'required' => 'File size is required',
            'integer' => 'File size must be a valid number'
        ],
        'uploaded_by' => [
            'required' => 'Uploader ID is required',
            'integer' => 'Uploader ID must be a valid number'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['generateFileHash'];
    protected $beforeUpdate = ['updateAccessTime'];

    /**
     * Generate file hash before insert
     */
    protected function generateFileHash(array $data)
    {
        if (isset($data['data']['file_path']) && file_exists($data['data']['file_path'])) {
            $data['data']['file_hash'] = hash_file('sha256', $data['data']['file_path']);
        }
        return $data;
    }

    /**
     * Update access time when file is accessed
     */
    protected function updateAccessTime(array $data)
    {
        $data['data']['last_accessed'] = date('Y-m-d H:i:s');
        return $data;
    }

    /**
     * Get files by user
     */
    public function getFilesByUser($userId, $fileType = null, $limit = 20, $offset = 0)
    {
        $builder = $this->where('uploaded_by', $userId);
        
        if ($fileType) {
            $builder->where('file_type', $fileType);
        }
        
        return $builder->orderBy('created_at', 'DESC')
                      ->limit($limit, $offset)
                      ->find();
    }

    /**
     * Get public files
     */
    public function getPublicFiles($fileType = null, $limit = 20, $offset = 0)
    {
        $builder = $this->where('is_public', 1);
        
        if ($fileType) {
            $builder->where('file_type', $fileType);
        }
        
        return $builder->orderBy('created_at', 'DESC')
                      ->limit($limit, $offset)
                      ->find();
    }

    /**
     * Get file statistics
     */
    public function getFileStats($userId = null)
    {
        $builder = $this->selectSum('file_size', 'total_size')
                       ->selectCount('id', 'total_files');
        
        if ($userId) {
            $builder->where('uploaded_by', $userId);
        }
        
        $result = $builder->first();
        
        return [
            'total_files' => (int) $result['total_files'],
            'total_size' => (int) $result['total_size'],
            'total_size_formatted' => $this->formatBytes($result['total_size'])
        ];
    }

    /**
     * Get files by type
     */
    public function getFilesByType($fileType, $limit = 20, $offset = 0)
    {
        return $this->where('file_type', $fileType)
                   ->orderBy('created_at', 'DESC')
                   ->limit($limit, $offset)
                   ->find();
    }

    /**
     * Search files
     */
    public function searchFiles($query, $userId = null, $fileType = null, $limit = 20, $offset = 0)
    {
        $builder = $this->groupStart()
                       ->like('original_name', $query)
                       ->orLike('filename', $query)
                       ->groupEnd();
        
        if ($userId) {
            $builder->where('uploaded_by', $userId);
        }
        
        if ($fileType) {
            $builder->where('file_type', $fileType);
        }
        
        return $builder->orderBy('created_at', 'DESC')
                      ->limit($limit, $offset)
                      ->find();
    }

    /**
     * Increment access count
     */
    public function incrementAccessCount($fileId)
    {
        return $this->set('access_count', 'access_count + 1', false)
                   ->set('last_accessed', date('Y-m-d H:i:s'))
                   ->where('id', $fileId)
                   ->update();
    }

    /**
     * Get duplicate files by hash
     */
    public function getDuplicateFiles($fileHash)
    {
        return $this->where('file_hash', $fileHash)->findAll();
    }

    /**
     * Clean up orphaned files (files without database records)
     */
    public function cleanupOrphanedFiles($uploadPath)
    {
        $orphanedFiles = [];
        $files = glob($uploadPath . '*');
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $filename = basename($file);
                $dbFile = $this->where('filename', $filename)->first();
                
                if (!$dbFile) {
                    $orphanedFiles[] = $file;
                }
            }
        }
        
        return $orphanedFiles;
    }

    /**
     * Get files older than specified days
     */
    public function getOldFiles($days = 30, $fileType = null)
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        $builder = $this->where('created_at <', $cutoffDate);
        
        if ($fileType) {
            $builder->where('file_type', $fileType);
        }
        
        return $builder->findAll();
    }

    /**
     * Format bytes to human readable format
     */
    protected function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Get file type statistics
     */
    public function getFileTypeStats($userId = null)
    {
        $builder = $this->select('file_type, COUNT(*) as count, SUM(file_size) as total_size')
                       ->groupBy('file_type');
        
        if ($userId) {
            $builder->where('uploaded_by', $userId);
        }
        
        return $builder->findAll();
    }

    /**
     * Get recent files
     */
    public function getRecentFiles($limit = 10, $userId = null)
    {
        $builder = $this->orderBy('created_at', 'DESC')
                       ->limit($limit);
        
        if ($userId) {
            $builder->where('uploaded_by', $userId);
        }
        
        return $builder->findAll();
    }

    /**
     * Get most accessed files
     */
    public function getMostAccessedFiles($limit = 10, $userId = null)
    {
        $builder = $this->orderBy('access_count', 'DESC')
                       ->limit($limit);
        
        if ($userId) {
            $builder->where('uploaded_by', $userId);
        }
        
        return $builder->findAll();
    }
}
