<?php

namespace App\Models;

use CodeIgniter\Model;

class TaskTypeModel extends Model
{
    protected $table = 'task_types';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'name',
        'description',
        'color',
        'icon',
        'is_default',
        'sort_order',
        'is_active',
        'created_by'
    ];

    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'name' => 'required|max_length[100]',
        'color' => 'max_length[7]',
        'icon' => 'max_length[50]',
        'created_by' => 'required|integer'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'Task type name is required',
            'max_length' => 'Task type name cannot exceed 100 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get all active task types ordered by sort_order
     */
    public function getActiveTaskTypes()
    {
        return $this->where('is_active', 1)
                   ->orderBy('sort_order', 'ASC')
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }

    /**
     * Get default task types
     */
    public function getDefaultTaskTypes()
    {
        return $this->where('is_default', 1)
                   ->where('is_active', 1)
                   ->orderBy('sort_order', 'ASC')
                   ->findAll();
    }

    /**
     * Get task types for dropdown
     */
    public function getTaskTypesForDropdown()
    {
        $taskTypes = $this->getActiveTaskTypes();
        $dropdown = [];
        
        foreach ($taskTypes as $taskType) {
            $dropdown[$taskType['id']] = $taskType['name'];
        }
        
        return $dropdown;
    }

    /**
     * Create default task types for a new installation
     */
    public function createDefaultTaskTypes($createdBy = 1)
    {
        $defaultTypes = [
            [
                'name' => 'Planning',
                'description' => 'Initial project planning and design phase',
                'color' => '#6c757d',
                'icon' => 'fas fa-drafting-compass',
                'is_default' => 1,
                'sort_order' => 1,
                'is_active' => 1,
                'created_by' => $createdBy
            ],
            [
                'name' => '3D Design',
                'description' => '3D modeling and architectural design',
                'color' => '#0d6efd',
                'icon' => 'fas fa-cube',
                'is_default' => 1,
                'sort_order' => 2,
                'is_active' => 1,
                'created_by' => $createdBy
            ],
            [
                'name' => 'Permit Approval',
                'description' => 'Government permits and regulatory approvals',
                'color' => '#fd7e14',
                'icon' => 'fas fa-stamp',
                'is_default' => 1,
                'sort_order' => 3,
                'is_active' => 1,
                'created_by' => $createdBy
            ],
            [
                'name' => 'Construction',
                'description' => 'Main construction and building phase',
                'color' => '#dc3545',
                'icon' => 'fas fa-hard-hat',
                'is_default' => 1,
                'sort_order' => 4,
                'is_active' => 1,
                'created_by' => $createdBy
            ],
            [
                'name' => 'Completion',
                'description' => 'Final inspection and project handover',
                'color' => '#198754',
                'icon' => 'fas fa-check-circle',
                'is_default' => 1,
                'sort_order' => 5,
                'is_active' => 1,
                'created_by' => $createdBy
            ]
        ];

        return $this->insertBatch($defaultTypes);
    }

    /**
     * Update sort order for task types
     */
    public function updateSortOrder($taskTypeIds)
    {
        $this->db->transStart();
        
        foreach ($taskTypeIds as $index => $taskTypeId) {
            $this->update($taskTypeId, ['sort_order' => $index + 1]);
        }
        
        $this->db->transComplete();
        
        return $this->db->transStatus();
    }

    /**
     * Get task type statistics
     */
    public function getTaskTypeStats()
    {
        $stats = [
            'total_types' => $this->countAll(),
            'active_types' => $this->where('is_active', 1)->countAllResults(),
            'default_types' => $this->where('is_default', 1)->countAllResults(),
            'custom_types' => $this->where('is_default', 0)->countAllResults()
        ];

        return $stats;
    }

    /**
     * Search task types
     */
    public function searchTaskTypes($query, $limit = 20, $offset = 0)
    {
        return $this->like('name', $query)
                   ->orLike('description', $query)
                   ->orderBy('sort_order', 'ASC')
                   ->limit($limit, $offset)
                   ->findAll();
    }

    /**
     * Get task type with usage count
     */
    public function getTaskTypeWithUsage($taskTypeId)
    {
        $taskType = $this->find($taskTypeId);
        
        if ($taskType) {
            // Count how many project tasks use this task type
            $taskType['usage_count'] = $this->db->table('project_tasks')
                                                ->where('task_type_id', $taskTypeId)
                                                ->countAllResults();
        }
        
        return $taskType;
    }

    /**
     * Check if task type can be deleted
     */
    public function canDelete($taskTypeId)
    {
        // Cannot delete if it's a default type or if it's being used
        $taskType = $this->find($taskTypeId);
        
        if (!$taskType) {
            return false;
        }
        
        if ($taskType['is_default']) {
            return false;
        }
        
        $usageCount = $this->db->table('project_tasks')
                              ->where('task_type_id', $taskTypeId)
                              ->countAllResults();
        
        return $usageCount === 0;
    }

    /**
     * Soft delete by setting is_active to 0
     */
    public function softDelete($taskTypeId)
    {
        return $this->update($taskTypeId, ['is_active' => 0]);
    }

    /**
     * Restore soft deleted task type
     */
    public function restore($taskTypeId)
    {
        return $this->update($taskTypeId, ['is_active' => 1]);
    }
}
