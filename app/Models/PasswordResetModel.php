<?php

namespace App\Models;

use CodeIgniter\Model;

class PasswordResetModel extends Model
{
    protected $table = 'password_resets';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'email', 'token_hash', 'expires_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = '';

    // Validation
    protected $validationRules = [
        'email' => 'required|valid_email',
        'token_hash' => 'required|max_length[255]',
        'expires_at' => 'required|valid_date',
    ];

    protected $validationMessages = [
        'email' => [
            'required' => 'Email is required',
            'valid_email' => 'Please provide a valid email address'
        ],
        'token_hash' => [
            'required' => 'Token hash is required',
            'max_length' => 'Token hash cannot exceed 255 characters'
        ],
        'expires_at' => [
            'required' => 'Expiration date is required',
            'valid_date' => 'Please provide a valid expiration date'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Create password reset token
     */
    public function createResetToken($email, $token, $expiresInHours = 1)
    {
        // Remove existing tokens for this email
        $this->where('email', $email)->delete();

        // Create new token
        $tokenHash = hash('sha256', $token);
        $expiresAt = date('Y-m-d H:i:s', strtotime("+{$expiresInHours} hours"));

        return $this->insert([
            'email' => $email,
            'token_hash' => $tokenHash,
            'expires_at' => $expiresAt
        ]);
    }

    /**
     * Verify reset token
     */
    public function verifyResetToken($token)
    {
        $tokenHash = hash('sha256', $token);
        
        $tokenData = $this->where('token_hash', $tokenHash)
            ->where('expires_at >', date('Y-m-d H:i:s'))
            ->first();

        return $tokenData;
    }

    /**
     * Delete reset token
     */
    public function deleteResetToken($token)
    {
        $tokenHash = hash('sha256', $token);
        return $this->where('token_hash', $tokenHash)->delete();
    }

    /**
     * Delete email tokens
     */
    public function deleteEmailTokens($email)
    {
        return $this->where('email', $email)->delete();
    }

    /**
     * Clean expired tokens
     */
    public function cleanExpiredTokens()
    {
        return $this->where('expires_at <', date('Y-m-d H:i:s'))->delete();
    }

    /**
     * Generate secure token
     */
    public function generateToken()
    {
        return bin2hex(random_bytes(32));
    }

    /**
     * Check if email has recent reset request
     */
    public function hasRecentRequest($email, $minutesAgo = 5)
    {
        $timeLimit = date('Y-m-d H:i:s', strtotime("-{$minutesAgo} minutes"));
        
        return $this->where('email', $email)
            ->where('created_at >', $timeLimit)
            ->countAllResults() > 0;
    }
}
