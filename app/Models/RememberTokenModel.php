<?php

namespace App\Models;

use CodeIgniter\Model;

class RememberTokenModel extends Model
{
    protected $table = 'remember_tokens';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'user_id', 'token_hash', 'expires_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = '';

    // Validation
    protected $validationRules = [
        'user_id' => 'required|integer',
        'token_hash' => 'required|max_length[255]',
        'expires_at' => 'required|valid_date',
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'User ID is required',
            'integer' => 'User ID must be an integer'
        ],
        'token_hash' => [
            'required' => 'Token hash is required',
            'max_length' => 'Token hash cannot exceed 255 characters'
        ],
        'expires_at' => [
            'required' => 'Expiration date is required',
            'valid_date' => 'Please provide a valid expiration date'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Create remember token
     */
    public function createToken($userId, $token, $expiresInDays = 7)
    {
        // Remove existing tokens for this user
        $this->where('user_id', $userId)->delete();

        // Create new token
        $tokenHash = hash('sha256', $token);
        $expiresAt = date('Y-m-d H:i:s', strtotime("+{$expiresInDays} days"));

        return $this->insert([
            'user_id' => $userId,
            'token_hash' => $tokenHash,
            'expires_at' => $expiresAt
        ]);
    }

    /**
     * Verify remember token
     */
    public function verifyToken($token)
    {
        $tokenHash = hash('sha256', $token);
        
        $tokenData = $this->select('remember_tokens.*, users.*')
            ->join('users', 'users.id = remember_tokens.user_id')
            ->where('remember_tokens.token_hash', $tokenHash)
            ->where('remember_tokens.expires_at >', date('Y-m-d H:i:s'))
            ->where('users.is_active', 1)
            ->first();

        return $tokenData;
    }

    /**
     * Delete token
     */
    public function deleteToken($token)
    {
        $tokenHash = hash('sha256', $token);
        return $this->where('token_hash', $tokenHash)->delete();
    }

    /**
     * Delete user tokens
     */
    public function deleteUserTokens($userId)
    {
        return $this->where('user_id', $userId)->delete();
    }

    /**
     * Clean expired tokens
     */
    public function cleanExpiredTokens()
    {
        return $this->where('expires_at <', date('Y-m-d H:i:s'))->delete();
    }

    /**
     * Generate secure token
     */
    public function generateToken()
    {
        return bin2hex(random_bytes(32));
    }
}
