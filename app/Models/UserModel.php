<?php

namespace App\Models;

use CodeIgniter\Model;

class UserModel extends Model
{
    protected $table = 'users';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'username', 'email', 'roles', 'password_hash', 'is_active', 'last_login',
        'password_changed_at', 'force_password_change', 'first_login',
        'first_name', 'last_name', 'phone', 'bio'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation rules for creation
    protected $validationRules = [
        'username' => 'required|min_length[3]|max_length[50]|is_unique[users.username]',
        'email' => 'required|valid_email|is_unique[users.email]',
        'password_hash' => 'permit_empty|min_length[8]',
    ];

    // Validation rules for updates
    protected $updateValidationRules = [
        'username' => 'required|min_length[3]|max_length[50]',
        'email' => 'required|valid_email',
        'password_hash' => 'permit_empty|min_length[8]',
    ];

    protected $validationMessages = [
        'username' => [
            'required' => 'Username is required',
            'min_length' => 'Username must be at least 3 characters',
            'max_length' => 'Username cannot exceed 50 characters',
            'is_unique' => 'Username already exists'
        ],
        'email' => [
            'required' => 'Email is required',
            'valid_email' => 'Please enter a valid email address',
            'is_unique' => 'Email already exists'
        ],
        'password_hash' => [
            'required' => 'Password is required',
            'min_length' => 'Password must be at least 8 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Custom errors property for manual validation
    protected $customErrors = [];

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];

    protected function hashPassword(array $data)
    {
        if (isset($data['data']['password'])) {
            $data['data']['password_hash'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
            unset($data['data']['password']);
        }
        return $data;
    }

    /**
     * Custom update method with proper validation
     */
    public function updateUser($id, $data)
    {
        // Clear previous errors
        $this->customErrors = [];

        // Validate uniqueness manually for updates
        if (isset($data['username'])) {
            $existingUser = $this->where('username', $data['username'])
                                ->where('id !=', $id)
                                ->first();
            if ($existingUser) {
                $this->customErrors['username'] = 'Username already exists';
                return false;
            }
        }

        if (isset($data['email'])) {
            $existingUser = $this->where('email', $data['email'])
                                ->where('id !=', $id)
                                ->first();
            if ($existingUser) {
                $this->customErrors['email'] = 'Email already exists';
                return false;
            }
        }

        // Use update validation rules
        $originalRules = $this->validationRules;
        $this->validationRules = $this->updateValidationRules;

        $result = $this->update($id, $data);

        // Restore original rules
        $this->validationRules = $originalRules;

        return $result;
    }

    /**
     * Override errors method to include custom errors
     */
    public function errors($field = null)
    {
        $modelErrors = parent::errors($field);

        if (!empty($this->customErrors)) {
            if ($field) {
                return $this->customErrors[$field] ?? $modelErrors;
            }
            return array_merge($modelErrors, $this->customErrors);
        }

        return $modelErrors;
    }

    /**
     * Get user with roles
     */
    public function getUserWithRoles($userId)
    {
        return $this->select('users.*, GROUP_CONCAT(roles.role_name) as roles, GROUP_CONCAT(roles.permissions) as permissions')
            ->join('user_roles', 'user_roles.user_id = users.id', 'left')
            ->join('roles', 'roles.id = user_roles.role_id', 'left')
            ->where('users.id', $userId)
            ->groupBy('users.id')
            ->first();
    }

    /**
     * Get user by username
     */
    public function getUserByUsername($username)
    {
        return $this->where('username', $username)->first();
    }

    /**
     * Get user by email
     */
    public function getUserByEmail($email)
    {
        return $this->where('email', $email)->first();
    }

    /**
     * Update last login
     */
    public function updateLastLogin($userId)
    {
        return $this->update($userId, ['last_login' => date('Y-m-d H:i:s')]);
    }

    /**
     * Get users with pagination and search
     */
    public function getUsersWithRoles($search = '', $limit = 10, $offset = 0)
    {
        $builder = $this->select('users.*, GROUP_CONCAT(roles.role_name) as roles')
            ->join('user_roles', 'user_roles.user_id = users.id', 'left')
            ->join('roles', 'roles.id = user_roles.role_id', 'left')
            ->groupBy('users.id');

        if (!empty($search)) {
            $builder->groupStart()
                ->like('users.username', $search)
                ->orLike('users.email', $search)
                ->groupEnd();
        }

        return $builder->limit($limit, $offset)->findAll();
    }

    /**
     * Count users with search
     */
    public function countUsers($search = '')
    {
        $builder = $this->builder();
        
        if (!empty($search)) {
            $builder->groupStart()
                ->like('username', $search)
                ->orLike('email', $search)
                ->groupEnd();
        }

        return $builder->countAllResults();
    }

    /**
     * Check if user has permission
     */
    public function hasPermission($userId, $permission)
    {
        $user = $this->getUserWithRoles($userId);

        if (!$user || !$user['permissions']) {
            return false;
        }

        $permissions = [];

        // Handle case where permissions is a single JSON string
        if (strpos($user['permissions'], '[') === 0) {
            // Single JSON array
            $decoded = json_decode($user['permissions'], true);
            if (is_array($decoded)) {
                $permissions = $decoded;
            }
        } else {
            // Multiple JSON strings separated by commas
            $permissionSets = explode(',', $user['permissions']);

            foreach ($permissionSets as $permissionSet) {
                $decoded = json_decode($permissionSet, true);
                if (is_array($decoded)) {
                    $permissions = array_merge($permissions, $decoded);
                }
            }
        }

        return in_array($permission, $permissions) || in_array('admin.access', $permissions);
    }

    /**
     * Check if user needs to change password
     */
    public function needsPasswordChange($userId)
    {
        $user = $this->find($userId);

        if (!$user) {
            return false;
        }

        // Check if forced password change is required
        if ($user['force_password_change']) {
            return true;
        }

        // Check if it's first login
        if ($user['first_login']) {
            return true;
        }

        // Check if password is older than 90 days
        if ($user['password_changed_at']) {
            $passwordAge = strtotime($user['password_changed_at']);
            $ninetyDaysAgo = strtotime('-90 days');

            if ($passwordAge < $ninetyDaysAgo) {
                return true;
            }
        }

        return false;
    }

    /**
     * Update user password with policy tracking
     */
    public function updatePassword($userId, $newPassword)
    {
        $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);

        return $this->update($userId, [
            'password_hash' => $passwordHash,
            'password_changed_at' => date('Y-m-d H:i:s'),
            'force_password_change' => false,
            'first_login' => false
        ]);
    }

    /**
     * Mark user for forced password change
     */
    public function forcePasswordChange($userId)
    {
        return $this->update($userId, [
            'force_password_change' => true
        ]);
    }

    /**
     * Assign manager role to user
     */
    public function assignManagerRole($userId)
    {
        $roleModel = new \App\Models\RoleModel();
        $userRoleModel = new \App\Models\UserRoleModel();

        // Get manager role
        $managerRole = $roleModel->where('role_name', 'manager')->first();

        if (!$managerRole) {
            return false;
        }

        // Remove existing roles and assign manager role
        return $userRoleModel->syncUserRoles($userId, [$managerRole['id']]);
    }

    /**
     * Check if user has manager role
     */
    public function isManager($userId)
    {
        $user = $this->getUserWithRoles($userId);

        if (!$user || !$user['roles']) {
            return false;
        }

        $roles = explode(',', $user['roles']);
        return in_array('manager', $roles);
    }

    /**
     * Get all managers
     */
    public function getManagers()
    {
        return $this->select('users.*')
            ->join('user_roles', 'user_roles.user_id = users.id')
            ->join('roles', 'roles.id = user_roles.role_id')
            ->where('roles.role_name', 'manager')
            ->where('users.is_active', 1)
            ->findAll();
    }

    /**
     * Get user statistics
     */
    public function getUserStats()
    {
        return [
            'total_users' => $this->countAll(),
            'active_users' => $this->where('is_active', 1)->countAllResults(),
            'inactive_users' => $this->where('is_active', 0)->countAllResults(),
            'recent_logins' => $this->where('last_login >', date('Y-m-d H:i:s', strtotime('-7 days')))->countAllResults()
        ];
    }
}
