<?php

namespace App\Models;

use CodeIgniter\Model;

class ProjectTimelineModel extends Model
{
    protected $table = 'project_timeline';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'project_id',
        'user_id', 
        'action_type',
        'old_status',
        'new_status',
        'title',
        'description',
        'notes',
        'file_path',
        'metadata',
        'created_at',
        'updated_at'
    ];

    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'project_id' => 'required|integer',
        'user_id' => 'required|integer',
        'action_type' => 'required|in_list[status_change,comment_added,file_uploaded,project_created,project_updated,assignment_changed]',
        'title' => 'required|max_length[255]'
    ];

    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required',
            'integer' => 'Project ID must be an integer'
        ],
        'user_id' => [
            'required' => 'User ID is required',
            'integer' => 'User ID must be an integer'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get timeline for a specific project
     */
    public function getProjectTimeline($projectId)
    {
        try {
            $timelineData = $this->select('
                project_timeline.*,
                users.username,
                users.first_name,
                users.last_name,
                project_tasks.task_name as current_task_name
            ')
            ->join('users', 'users.id = project_timeline.user_id', 'left')
            ->join('project_tasks', 'project_tasks.id = JSON_UNQUOTE(JSON_EXTRACT(project_timeline.metadata, "$.task_id"))', 'left')
            ->where('project_timeline.project_id', $projectId)
            ->orderBy('project_timeline.created_at', 'DESC')
            ->findAll();

            // Format data for frontend consumption
            $formattedTimeline = [];
            foreach ($timelineData as $item) {
                $metadata = json_decode($item['metadata'], true) ?? [];

                // Use current task name if available, otherwise extract from description
                $taskName = $item['current_task_name'] ?? $metadata['task_name'] ?? 'Unknown Task';

                // Update description to use current task name
                $description = $item['description'] ?? '';
                if ($item['current_task_name'] && strpos($description, 'Task \'') !== false) {
                    // Replace old task name in description with current one
                    $description = preg_replace('/Task \'[^\']*\'/', "Task '{$item['current_task_name']}'", $description);
                }

                $formattedTimeline[] = [
                    'id' => $item['id'],
                    'action' => $item['title'] ?? 'Status Update',
                    'status' => $item['new_status'] ?? $item['action_type'] ?? 'unknown',
                    'user' => $item['username'] ?? $item['full_name'] ?? 'Unknown User',
                    'timestamp' => $item['created_at'],
                    'duration' => $this->calculateDuration($item['created_at']),
                    'notes' => $description,
                    'task_name' => $taskName,
                    'color' => $this->getStatusColor($item['new_status'] ?? $item['action_type'] ?? 'unknown')
                ];
            }

            return $formattedTimeline;
        } catch (\Exception $e) {
            log_message('error', 'ProjectTimelineModel::getProjectTimeline failed: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Calculate duration since timestamp
     */
    private function calculateDuration($timestamp)
    {
        if (!$timestamp) return 'Unknown';

        $date = new \DateTime($timestamp);
        $now = new \DateTime();
        $diff = $now->diff($date);

        if ($diff->days > 0) {
            return $diff->days . ' day' . ($diff->days > 1 ? 's' : '') . ' ago';
        } elseif ($diff->h > 0) {
            return $diff->h . ' hour' . ($diff->h > 1 ? 's' : '') . ' ago';
        } elseif ($diff->i > 0) {
            return $diff->i . ' minute' . ($diff->i > 1 ? 's' : '') . ' ago';
        } else {
            return 'Just now';
        }
    }

    /**
     * Get status color for timeline display
     */
    private function getStatusColor($status)
    {
        $colors = [
            'not_started' => '#6c757d',
            'planning' => '#17a2b8',
            'in_progress' => '#007bff',
            'on_hold' => '#ffc107',
            'completed' => '#28a745',
            'review' => '#17a2b8',
            'project_created' => '#28a745',
            'status_change' => '#007bff',
            'comment_added' => '#17a2b8',
            'file_uploaded' => '#6f42c1'
        ];

        return $colors[$status] ?? '#6c757d';
    }



    /**
     * Add a timeline entry for status change with time tracking
     */
    public function addStatusChange($projectId, $userId, $oldStatus, $newStatus, $notes = null, $filePath = null)
    {
        try {
            $statusTexts = [
                'not_started' => 'Not Started',
                'planning' => 'Planning',
                'in_progress' => 'In Progress',
                'on_hold' => 'On Hold',
                'completed' => 'Completed',
                'review' => 'Under Review'
            ];

            $oldStatusText = $statusTexts[$oldStatus] ?? $oldStatus;
            $newStatusText = $statusTexts[$newStatus] ?? $newStatus;

            // Get detailed information for timeline
            $userModel = new \App\Models\UserModel();
            $projectModel = new \App\Models\ProjectModel();

            $user = $userModel->find($userId);
            $userFullName = $user ? ($user['username'] ?: 'Unknown User') : 'Unknown User';

            // Get project details
            $project = $projectModel->find($projectId);
            $projectName = $project ? $project['project_name'] : 'Unknown Project';

            // Get assignee details
            $assigneeName = 'Unknown Assignee';
            $taskName = 'Project Management';

            if ($project && $project['assigned_to']) {
                $assignee = $userModel->find($project['assigned_to']);
                $assigneeName = $assignee ? ($assignee['username'] ?: 'Unknown Assignee') : 'Unknown Assignee';
            }

        // Calculate duration if transitioning from in_progress
        $duration = null;
        $startTime = null;

        if ($oldStatus === 'in_progress' && $newStatus !== 'in_progress') {
            // Get the last start time for this project
            $lastStart = $this->where('project_id', $projectId)
                             ->where('new_status', 'in_progress')
                             ->orderBy('created_at', 'DESC')
                             ->first();

            if ($lastStart) {
                $startTime = $lastStart['created_at'];
                $start = new \DateTime($startTime);
                $end = new \DateTime();
                $duration = $end->getTimestamp() - $start->getTimestamp(); // Duration in seconds
            }
        }

        $metadata = [
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
            'timestamp' => date('Y-m-d H:i:s'),
            'action_time' => date('Y-m-d H:i:s'),
            'assigner_id' => $userId,
            'assigner_name' => $userFullName,
            'assignee_id' => $project['assigned_to'] ?? null,
            'assignee_name' => $assigneeName,
            'task_name' => $taskName,
            'project_name' => $projectName
        ];

        // Add time tracking data
        if ($newStatus === 'in_progress') {
            $metadata['start_time'] = date('Y-m-d H:i:s');
            $metadata['action_type'] = 'task_started';
        } elseif ($oldStatus === 'in_progress') {
            $metadata['end_time'] = date('Y-m-d H:i:s');
            $metadata['start_time'] = $startTime;
            $metadata['duration_seconds'] = $duration;
            $metadata['duration_formatted'] = $this->formatDuration($duration);
            $metadata['action_type'] = $newStatus === 'completed' ? 'task_completed' : 'task_paused';
        }

        // Create detailed title
        $detailedTitle = "Status changed from {$oldStatusText} to {$newStatusText}";
        $detailedDescription = "Task '{$taskName}' assigned to {$assigneeName} - changed by {$userFullName}";

        $data = [
            'project_id' => $projectId,
            'user_id' => $userId,
            'action_type' => 'status_change',
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
            'title' => $detailedTitle,
            'description' => $detailedDescription,
            'notes' => $notes,
            'file_path' => $filePath,
            'metadata' => json_encode($metadata)
        ];

            return $this->insert($data);
        } catch (\Exception $e) {
            log_message('error', 'ProjectTimelineModel::addStatusChange failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Format duration in seconds to human readable format
     */
    private function formatDuration($seconds)
    {
        if (!$seconds) return '0 seconds';

        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $secs = $seconds % 60;

        $parts = [];
        if ($hours > 0) $parts[] = $hours . 'h';
        if ($minutes > 0) $parts[] = $minutes . 'm';
        if ($secs > 0 || empty($parts)) $parts[] = $secs . 's';

        return implode(' ', $parts);
    }

    /**
     * Add a timeline entry for project creation
     */
    public function addProjectCreated($projectId, $userId, $projectName)
    {
        try {
            $data = [
                'project_id' => $projectId,
                'user_id' => $userId,
                'action_type' => 'project_created',
                'title' => 'Project created',
                'description' => "Project '{$projectName}' was created",
                'metadata' => json_encode([
                    'project_name' => $projectName,
                    'timestamp' => date('Y-m-d H:i:s')
                ])
            ];

            return $this->insert($data);
        } catch (\Exception $e) {
            log_message('error', 'ProjectTimelineModel::addProjectCreated failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Add a timeline entry for comment
     */
    public function addComment($projectId, $userId, $comment, $filePath = null)
    {
        $data = [
            'project_id' => $projectId,
            'user_id' => $userId,
            'action_type' => 'comment_added',
            'title' => 'Comment added',
            'description' => $comment,
            'notes' => $comment,
            'file_path' => $filePath,
            'metadata' => json_encode([
                'has_file' => !empty($filePath),
                'timestamp' => date('Y-m-d H:i:s')
            ])
        ];

        return $this->insert($data);
    }

    /**
     * Add a timeline entry for assignment change
     */
    public function addAssignmentChange($projectId, $userId, $oldAssignee, $newAssignee, $reason = null)
    {
        $data = [
            'project_id' => $projectId,
            'user_id' => $userId,
            'action_type' => 'assignment_changed',
            'title' => 'Project reassigned',
            'description' => "Project reassigned from user {$oldAssignee} to user {$newAssignee}",
            'notes' => $reason,
            'metadata' => json_encode([
                'old_assignee' => $oldAssignee,
                'new_assignee' => $newAssignee,
                'reason' => $reason,
                'timestamp' => date('Y-m-d H:i:s')
            ])
        ];

        return $this->insert($data);
    }
}
