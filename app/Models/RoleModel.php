<?php

namespace App\Models;

use CodeIgniter\Model;

class RoleModel extends Model
{
    protected $table = 'roles';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'role_name', 'description', 'permissions'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'role_name' => 'required|min_length[2]|max_length[50]|is_unique[roles.role_name,id,{id}]',
        'description' => 'permit_empty|max_length[255]',
    ];

    protected $validationMessages = [
        'role_name' => [
            'required' => 'Role name is required',
            'min_length' => 'Role name must be at least 2 characters',
            'max_length' => 'Role name cannot exceed 50 characters',
            'is_unique' => 'Role name already exists'
        ],
        'description' => [
            'max_length' => 'Description cannot exceed 255 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['encodePermissions'];
    protected $beforeUpdate = ['encodePermissions'];
    protected $afterFind = ['decodePermissions'];

    protected function encodePermissions(array $data)
    {
        if (isset($data['data']['permissions']) && is_array($data['data']['permissions'])) {
            $data['data']['permissions'] = json_encode($data['data']['permissions']);
        }
        return $data;
    }

    protected function decodePermissions(array $data)
    {
        if (isset($data['data'])) {
            if (is_array($data['data'])) {
                foreach ($data['data'] as &$row) {
                    if (isset($row['permissions']) && is_string($row['permissions'])) {
                        $row['permissions'] = json_decode($row['permissions'], true) ?: [];
                    }
                }
            } else {
                if (isset($data['data']['permissions']) && is_string($data['data']['permissions'])) {
                    $data['data']['permissions'] = json_decode($data['data']['permissions'], true) ?: [];
                }
            }
        }
        return $data;
    }

    /**
     * Get role with user count
     */
    public function getRoleWithUserCount($roleId)
    {
        return $this->select('roles.*, COUNT(user_roles.user_id) as user_count')
            ->join('user_roles', 'user_roles.role_id = roles.id', 'left')
            ->where('roles.id', $roleId)
            ->groupBy('roles.id')
            ->first();
    }

    /**
     * Get all roles with user counts
     */
    public function getRolesWithUserCounts($search = '', $limit = 10, $offset = 0)
    {
        $builder = $this->select('roles.*, COUNT(user_roles.user_id) as user_count')
            ->join('user_roles', 'user_roles.role_id = roles.id', 'left')
            ->groupBy('roles.id');

        if (!empty($search)) {
            $builder->groupStart()
                ->like('roles.role_name', $search)
                ->orLike('roles.description', $search)
                ->groupEnd();
        }

        return $builder->limit($limit, $offset)->findAll();
    }

    /**
     * Count roles with search
     */
    public function countRoles($search = '')
    {
        $builder = $this->builder();
        
        if (!empty($search)) {
            $builder->groupStart()
                ->like('role_name', $search)
                ->orLike('description', $search)
                ->groupEnd();
        }

        return $builder->countAllResults();
    }

    /**
     * Check if role can be deleted
     */
    public function canDelete($roleId)
    {
        $userRoleModel = new \App\Models\UserRoleModel();
        $userCount = $userRoleModel->where('role_id', $roleId)->countAllResults();
        return $userCount === 0;
    }

    /**
     * Get available permissions
     */
    public function getAvailablePermissions()
    {
        return [
            // User Management
            'user.create' => 'Create Users',
            'user.read' => 'View Users',
            'user.update' => 'Edit Users',
            'user.delete' => 'Delete Users',

            // Role Management
            'role.create' => 'Create Roles',
            'role.read' => 'View Roles',
            'role.update' => 'Edit Roles',
            'role.delete' => 'Delete Roles',

            // Project Management
            'project.create' => 'Create Projects',
            'project.read' => 'View All Projects',
            'project.update' => 'Edit All Projects',
            'project.delete' => 'Delete Projects',
            'project.assign' => 'Assign Projects to Users',
            'project.status' => 'Update Project Status',
            'project.timeline' => 'View Project Timeline',

            // Task Management
            'task.create' => 'Create Tasks',
            'task.read' => 'View All Tasks',
            'task.update' => 'Edit All Tasks',
            'task.delete' => 'Delete Tasks',
            'task.assign' => 'Assign Tasks to Users',
            'task.status' => 'Update Task Status',

            // Reports & Analytics
            'report.view' => 'View Reports',
            'report.export' => 'Export Reports',
            'analytics.view' => 'View Analytics Dashboard',

            // Team Management
            'team.manage' => 'Manage Team Members',
            'team.assign' => 'Assign Team Members to Projects',

            // Dashboard Controls
            'dashboard.manage' => 'Manage Dashboard Controls',

            // System Administration
            'admin.access' => 'Access Admin Panel',
            'system.settings' => 'Manage System Settings',
        ];
    }
}
