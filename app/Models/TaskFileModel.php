<?php

namespace App\Models;

use CodeIgniter\Model;

class TaskFileModel extends Model
{
    protected $table = 'task_files';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'task_id',
        'filename',
        'stored_filename',
        'file_path',
        'file_size',
        'mime_type',
        'file_hash',
        'uploaded_by',
        'is_public'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation rules
    protected $validationRules = [
        'task_id' => 'required|integer',
        'filename' => 'required|max_length[255]',
        'stored_filename' => 'required|max_length[255]',
        'file_path' => 'required|max_length[500]',
        'file_size' => 'required|integer',
        'mime_type' => 'required|max_length[100]',
        'uploaded_by' => 'required|integer',
        'is_public' => 'required|in_list[0,1]'
    ];

    protected $validationMessages = [
        'task_id' => [
            'required' => 'Task ID is required',
            'integer' => 'Task ID must be a valid integer'
        ],
        'filename' => [
            'required' => 'Filename is required',
            'max_length' => 'Filename cannot exceed 255 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['generateFileHash'];

    /**
     * Generate file hash before insert
     */
    protected function generateFileHash(array $data)
    {
        if (isset($data['data']['file_path']) && file_exists($data['data']['file_path'])) {
            $data['data']['file_hash'] = hash_file('sha256', $data['data']['file_path']);
        }
        return $data;
    }

    /**
     * Get files for a specific task
     */
    public function getTaskFiles($taskId, $includePrivate = false)
    {
        $builder = $this->where('task_id', $taskId);
        
        if (!$includePrivate) {
            $builder->where('is_public', 1);
        }
        
        return $builder->orderBy('created_at', 'ASC')->findAll();
    }

    /**
     * Get file by task and filename
     */
    public function getTaskFile($taskId, $filename)
    {
        return $this->where('task_id', $taskId)
                   ->where('filename', $filename)
                   ->first();
    }

    /**
     * Get total file size for a task
     */
    public function getTaskFilesSize($taskId)
    {
        $result = $this->selectSum('file_size')
                      ->where('task_id', $taskId)
                      ->first();
        
        return $result['file_size'] ?? 0;
    }

    /**
     * Count files for a task
     */
    public function countTaskFiles($taskId)
    {
        return $this->where('task_id', $taskId)->countAllResults();
    }

    /**
     * Delete file and remove from filesystem
     */
    public function deleteFile($fileId)
    {
        $file = $this->find($fileId);
        if (!$file) {
            return false;
        }

        // Delete from filesystem
        if (file_exists($file['file_path'])) {
            unlink($file['file_path']);
        }

        // Delete from database
        return $this->delete($fileId);
    }

    /**
     * Get file statistics
     */
    public function getFileStats($taskId = null)
    {
        $builder = $this;
        
        if ($taskId) {
            $builder = $builder->where('task_id', $taskId);
        }
        
        $stats = $builder->select('
            COUNT(*) as total_files,
            SUM(file_size) as total_size,
            AVG(file_size) as avg_size,
            MAX(file_size) as max_size,
            MIN(file_size) as min_size
        ')->first();
        
        return $stats;
    }
}
