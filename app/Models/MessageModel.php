<?php

namespace App\Models;

use CodeIgniter\Model;

class MessageModel extends Model
{
    protected $table = 'messages';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    
    protected $allowedFields = [
        'sender_id',
        'recipient_id',
        'subject',
        'message',
        'priority',
        'is_read',
        'is_broadcast',
        'read_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation rules
    protected $validationRules = [
        'sender_id' => 'required|integer',
        'subject' => 'required|max_length[255]',
        'message' => 'required',
        'priority' => 'in_list[low,normal,high,urgent]'
    ];

    protected $validationMessages = [
        'sender_id' => [
            'required' => 'Sender ID is required',
            'integer' => 'Sender ID must be a valid number'
        ],
        'subject' => [
            'required' => 'Subject is required',
            'max_length' => 'Subject cannot exceed 255 characters'
        ],
        'message' => [
            'required' => 'Message content is required'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;

    /**
     * Get messages by user (inbox, sent, etc.)
     */
    public function getMessagesByUser($userId, $type = 'inbox', $limit = 20, $offset = 0)
    {
        $builder = $this->select('messages.*, 
                                 sender.username as sender_username, 
                                 sender.first_name as sender_first_name, 
                                 sender.last_name as sender_last_name,
                                 recipient.username as recipient_username,
                                 recipient.first_name as recipient_first_name,
                                 recipient.last_name as recipient_last_name')
                       ->join('users as sender', 'sender.id = messages.sender_id', 'left')
                       ->join('users as recipient', 'recipient.id = messages.recipient_id', 'left');

        switch ($type) {
            case 'sent':
                $builder->where('messages.sender_id', $userId);
                break;
            case 'broadcast':
                $builder->where('messages.is_broadcast', 1);
                break;
            case 'inbox':
            default:
                $builder->groupStart()
                       ->where('messages.recipient_id', $userId)
                       ->orWhere('messages.is_broadcast', 1)
                       ->groupEnd();
                break;
        }

        return $builder->orderBy('messages.created_at', 'DESC')
                      ->limit($limit, $offset)
                      ->find();
    }

    /**
     * Count messages by user
     */
    public function countMessagesByUser($userId, $type = 'inbox')
    {
        $builder = $this;

        switch ($type) {
            case 'sent':
                $builder->where('sender_id', $userId);
                break;
            case 'broadcast':
                $builder->where('is_broadcast', 1);
                break;
            case 'inbox':
            default:
                $builder->groupStart()
                       ->where('recipient_id', $userId)
                       ->orWhere('is_broadcast', 1)
                       ->groupEnd();
                break;
        }

        return $builder->countAllResults();
    }

    /**
     * Get unread message count
     */
    public function getUnreadCount($userId)
    {
        return $this->where('recipient_id', $userId)
                   ->where('is_read', 0)
                   ->orWhere('is_broadcast', 1)
                   ->countAllResults();
    }

    /**
     * Get message statistics
     */
    public function getMessageStats($userId)
    {
        $stats = [
            'total_received' => $this->countMessagesByUser($userId, 'inbox'),
            'total_sent' => $this->countMessagesByUser($userId, 'sent'),
            'unread_count' => $this->getUnreadCount($userId),
            'broadcast_count' => $this->countMessagesByUser($userId, 'broadcast')
        ];

        return $stats;
    }

    /**
     * Get recent messages
     */
    public function getRecentMessages($userId, $limit = 5)
    {
        return $this->select('messages.*, 
                             sender.username as sender_username, 
                             sender.first_name as sender_first_name, 
                             sender.last_name as sender_last_name')
                   ->join('users as sender', 'sender.id = messages.sender_id', 'left')
                   ->groupStart()
                   ->where('messages.recipient_id', $userId)
                   ->orWhere('messages.is_broadcast', 1)
                   ->groupEnd()
                   ->orderBy('messages.created_at', 'DESC')
                   ->limit($limit)
                   ->find();
    }

    /**
     * Search messages
     */
    public function searchMessages($userId, $query, $limit = 20, $offset = 0)
    {
        return $this->select('messages.*, 
                             sender.username as sender_username, 
                             sender.first_name as sender_first_name, 
                             sender.last_name as sender_last_name')
                   ->join('users as sender', 'sender.id = messages.sender_id', 'left')
                   ->groupStart()
                   ->where('messages.recipient_id', $userId)
                   ->orWhere('messages.is_broadcast', 1)
                   ->groupEnd()
                   ->groupStart()
                   ->like('messages.subject', $query)
                   ->orLike('messages.message', $query)
                   ->orLike('sender.username', $query)
                   ->groupEnd()
                   ->orderBy('messages.created_at', 'DESC')
                   ->limit($limit, $offset)
                   ->find();
    }

    /**
     * Mark multiple messages as read
     */
    public function markMultipleAsRead($messageIds, $userId)
    {
        return $this->whereIn('id', $messageIds)
                   ->where('recipient_id', $userId)
                   ->set(['is_read' => 1, 'read_at' => date('Y-m-d H:i:s')])
                   ->update();
    }

    /**
     * Delete multiple messages
     */
    public function deleteMultiple($messageIds, $userId)
    {
        return $this->whereIn('id', $messageIds)
                   ->groupStart()
                   ->where('recipient_id', $userId)
                   ->orWhere('sender_id', $userId)
                   ->groupEnd()
                   ->delete();
    }

    /**
     * Get conversation between two users
     */
    public function getConversation($user1Id, $user2Id, $limit = 50, $offset = 0)
    {
        return $this->select('messages.*, 
                             sender.username as sender_username, 
                             sender.first_name as sender_first_name, 
                             sender.last_name as sender_last_name')
                   ->join('users as sender', 'sender.id = messages.sender_id', 'left')
                   ->groupStart()
                   ->groupStart()
                   ->where('messages.sender_id', $user1Id)
                   ->where('messages.recipient_id', $user2Id)
                   ->groupEnd()
                   ->orGroupStart()
                   ->where('messages.sender_id', $user2Id)
                   ->where('messages.recipient_id', $user1Id)
                   ->groupEnd()
                   ->groupEnd()
                   ->orderBy('messages.created_at', 'ASC')
                   ->limit($limit, $offset)
                   ->find();
    }

    /**
     * Get message priority counts
     */
    public function getPriorityStats($userId)
    {
        return $this->select('priority, COUNT(*) as count')
                   ->groupStart()
                   ->where('recipient_id', $userId)
                   ->orWhere('is_broadcast', 1)
                   ->groupEnd()
                   ->groupBy('priority')
                   ->find();
    }

    /**
     * Get messages by priority
     */
    public function getMessagesByPriority($userId, $priority, $limit = 20, $offset = 0)
    {
        return $this->select('messages.*, 
                             sender.username as sender_username, 
                             sender.first_name as sender_first_name, 
                             sender.last_name as sender_last_name')
                   ->join('users as sender', 'sender.id = messages.sender_id', 'left')
                   ->groupStart()
                   ->where('messages.recipient_id', $userId)
                   ->orWhere('messages.is_broadcast', 1)
                   ->groupEnd()
                   ->where('messages.priority', $priority)
                   ->orderBy('messages.created_at', 'DESC')
                   ->limit($limit, $offset)
                   ->find();
    }
}
