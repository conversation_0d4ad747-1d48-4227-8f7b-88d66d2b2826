<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use App\Libraries\AuthLibrary;

class AdminFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        $authLib = new AuthLibrary();
        
        // Check if user is logged in
        if (!$authLib->isLoggedIn()) {
            if ($request->isAJAX()) {
                return service('response')->setJSON([
                    'success' => false,
                    'message' => 'Authentication required.',
                    'redirect' => '/auth/login'
                ])->setStatusCode(401);
            }

            return redirect()->to('/auth/login')->with('error', 'Please log in to continue.');
        }

        // Check if user has admin access
        if (!$authLib->hasPermission('admin.access')) {
            if ($request->isAJAX()) {
                return service('response')->setJSON([
                    'success' => false,
                    'message' => 'Access denied. Admin privileges required.'
                ])->setStatusCode(403);
            }

            return redirect()->to('/auth/login')->with('error', 'Access denied. Admin privileges required.');
        }
        
        // Check specific permission if provided in arguments
        if (!empty($arguments)) {
            $permission = $arguments[0];
            if (!$authLib->hasPermission($permission)) {
                if ($request->isAJAX()) {
                    return service('response')->setJSON([
                        'success' => false,
                        'message' => 'Access denied. Insufficient privileges.'
                    ])->setStatusCode(403);
                }
                
                return redirect()->to('/admin/dashboard')->with('error', 'Access denied. Insufficient privileges.');
            }
        }
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Nothing to do here
    }
}
