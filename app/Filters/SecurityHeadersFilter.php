<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class SecurityHeadersFilter implements FilterInterface
{
    /**
     * Add security headers to response
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // This filter runs after the response is generated
        return null;
    }

    /**
     * Add security headers to response
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Get environment settings
        $enableHSTS = env('security.enableHSTS', true);
        $enableCSP = env('security.enableCSP', true);
        $enableXFrameOptions = env('security.enableXFrameOptions', true);
        $enableXContentTypeOptions = env('security.enableXContentTypeOptions', true);

        // HTTP Strict Transport Security (HSTS)
        if ($enableHSTS && $request->isSecure()) {
            $response->setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
        }

        // Content Security Policy
        if ($enableCSP) {
            $csp = "default-src 'self'; " .
                   "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
                   "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; " .
                   "font-src 'self' https://cdnjs.cloudflare.com https://fonts.gstatic.com; " .
                   "img-src 'self' data: https:; " .
                   "connect-src 'self'; " .
                   "frame-ancestors 'none'; " .
                   "base-uri 'self'; " .
                   "form-action 'self'";
            
            $response->setHeader('Content-Security-Policy', $csp);
        }

        // X-Frame-Options
        if ($enableXFrameOptions) {
            $response->setHeader('X-Frame-Options', 'DENY');
        }

        // X-Content-Type-Options
        if ($enableXContentTypeOptions) {
            $response->setHeader('X-Content-Type-Options', 'nosniff');
        }

        // X-XSS-Protection
        $response->setHeader('X-XSS-Protection', '1; mode=block');

        // Referrer Policy
        $response->setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

        // Permissions Policy
        $response->setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');

        // Remove server information
        $response->removeHeader('Server');
        $response->removeHeader('X-Powered-By');

        return $response;
    }
}
