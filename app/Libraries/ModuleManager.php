<?php

namespace App\Libraries;

class ModuleManager
{
    protected $modules = [];
    protected $modulesPath;

    public function __construct()
    {
        $this->modulesPath = APPPATH . 'Modules';
        $this->loadModules();
    }

    /**
     * Load all available modules
     */
    protected function loadModules()
    {
        if (!is_dir($this->modulesPath)) {
            return;
        }

        $directories = scandir($this->modulesPath);
        
        foreach ($directories as $dir) {
            if ($dir === '.' || $dir === '..' || !is_dir($this->modulesPath . '/' . $dir)) {
                continue;
            }

            $configFile = $this->modulesPath . '/' . $dir . '/module.json';
            
            if (file_exists($configFile)) {
                $config = json_decode(file_get_contents($configFile), true);
                
                if ($config && $this->validateModuleConfig($config)) {
                    $config['path'] = $this->modulesPath . '/' . $dir;
                    $config['namespace'] = $dir;
                    $this->modules[$dir] = $config;
                }
            }
        }
    }

    /**
     * Validate module configuration
     */
    protected function validateModuleConfig($config)
    {
        $required = ['name', 'version', 'description'];
        
        foreach ($required as $field) {
            if (!isset($config[$field])) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Get all loaded modules
     */
    public function getModules()
    {
        return $this->modules;
    }

    /**
     * Get a specific module
     */
    public function getModule($name)
    {
        return $this->modules[$name] ?? null;
    }

    /**
     * Check if a module is loaded
     */
    public function hasModule($name)
    {
        return isset($this->modules[$name]);
    }

    /**
     * Get module navigation items
     */
    public function getModuleNavigation($userPermissions = [])
    {
        $navigation = [];
        
        foreach ($this->modules as $module) {
            if (isset($module['navigation'])) {
                foreach ($module['navigation'] as $navItem) {
                    // Check permissions if specified
                    if (isset($navItem['permission'])) {
                        if (!in_array($navItem['permission'], $userPermissions) && 
                            !in_array('admin.access', $userPermissions)) {
                            continue;
                        }
                    }
                    
                    $navigation[] = $navItem;
                }
            }
        }
        
        return $navigation;
    }

    /**
     * Get module routes
     */
    public function getModuleRoutes()
    {
        $routes = [];
        
        foreach ($this->modules as $module) {
            if (isset($module['routes'])) {
                foreach ($module['routes'] as $route) {
                    $routes[] = [
                        'pattern' => $route['pattern'],
                        'controller' => $route['controller'],
                        'module' => $module['namespace']
                    ];
                }
            }
        }
        
        return $routes;
    }

    /**
     * Register module autoloader
     */
    public function registerAutoloader()
    {
        foreach ($this->modules as $moduleName => $module) {
            $namespace = $moduleName . '\\';
            $path = $module['path'] . '/';
            
            spl_autoload_register(function ($class) use ($namespace, $path) {
                if (strpos($class, $namespace) === 0) {
                    $file = $path . str_replace('\\', '/', substr($class, strlen($namespace))) . '.php';
                    if (file_exists($file)) {
                        require_once $file;
                    }
                }
            });
        }
    }

    /**
     * Get module statistics
     */
    public function getModuleStats()
    {
        return [
            'total_modules' => count($this->modules),
            'active_modules' => count(array_filter($this->modules, function($module) {
                return $module['status'] ?? true;
            })),
            'modules' => array_map(function($module) {
                return [
                    'name' => $module['name'],
                    'version' => $module['version'],
                    'status' => $module['status'] ?? true
                ];
            }, $this->modules)
        ];
    }
}
