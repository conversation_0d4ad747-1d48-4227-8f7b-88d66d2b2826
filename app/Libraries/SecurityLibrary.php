<?php

namespace App\Libraries;

class SecurityLibrary
{
    /**
     * Validate password strength
     */
    public function validatePassword($password)
    {
        $errors = [];
        
        // Minimum length
        if (strlen($password) < 8) {
            $errors[] = 'Password must be at least 8 characters long';
        }
        
        // Maximum length
        if (strlen($password) > 128) {
            $errors[] = 'Password cannot exceed 128 characters';
        }
        
        // Must contain uppercase letter
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter';
        }
        
        // Must contain lowercase letter
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'Password must contain at least one lowercase letter';
        }
        
        // Must contain number
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'Password must contain at least one number';
        }
        
        // Must contain special character
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'Password must contain at least one special character';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Generate secure random token
     */
    public function generateSecureToken($length = 32)
    {
        return bin2hex(random_bytes($length));
    }

    /**
     * Generate secure random string
     */
    public function generateSecureString($length = 16, $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789')
    {
        $string = '';
        $charactersLength = strlen($characters);
        
        for ($i = 0; $i < $length; $i++) {
            $string .= $characters[random_int(0, $charactersLength - 1)];
        }
        
        return $string;
    }

    /**
     * Hash token for storage
     */
    public function hashToken($token)
    {
        return hash('sha256', $token);
    }

    /**
     * Sanitize input
     */
    public function sanitizeInput($input, $type = 'string')
    {
        switch ($type) {
            case 'email':
                return filter_var(trim($input), FILTER_SANITIZE_EMAIL);
            
            case 'url':
                return filter_var(trim($input), FILTER_SANITIZE_URL);
            
            case 'int':
                return filter_var($input, FILTER_SANITIZE_NUMBER_INT);
            
            case 'float':
                return filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
            
            case 'string':
            default:
                return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
        }
    }

    /**
     * Validate email format
     */
    public function validateEmail($email)
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Check if string contains only alphanumeric characters
     */
    public function isAlphanumeric($string)
    {
        return ctype_alnum($string);
    }

    /**
     * Check if string is a valid username
     */
    public function validateUsername($username)
    {
        $errors = [];
        
        // Length check
        if (strlen($username) < 3) {
            $errors[] = 'Username must be at least 3 characters long';
        }
        
        if (strlen($username) > 50) {
            $errors[] = 'Username cannot exceed 50 characters';
        }
        
        // Character check (alphanumeric, underscore, hyphen)
        if (!preg_match('/^[a-zA-Z0-9_-]+$/', $username)) {
            $errors[] = 'Username can only contain letters, numbers, underscores, and hyphens';
        }
        
        // Must start with letter or number
        if (!preg_match('/^[a-zA-Z0-9]/', $username)) {
            $errors[] = 'Username must start with a letter or number';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Generate CSRF token
     */
    public function generateCSRFToken()
    {
        $token = $this->generateSecureToken();
        session()->set('csrf_token', $token);
        return $token;
    }

    /**
     * Verify CSRF token
     */
    public function verifyCSRFToken($token)
    {
        $sessionToken = session()->get('csrf_token');
        return $sessionToken && hash_equals($sessionToken, $token);
    }

    /**
     * Rate limit check
     */
    public function checkRateLimit($key, $maxAttempts = 5, $windowSeconds = 900)
    {
        $cache = \Config\Services::cache();
        $attempts = $cache->get($key) ?: 0;
        
        if ($attempts >= $maxAttempts) {
            return [
                'allowed' => false,
                'remaining' => 0,
                'reset_time' => time() + $windowSeconds
            ];
        }
        
        return [
            'allowed' => true,
            'remaining' => $maxAttempts - $attempts - 1,
            'reset_time' => time() + $windowSeconds
        ];
    }

    /**
     * Record rate limit attempt
     */
    public function recordRateLimitAttempt($key, $windowSeconds = 900)
    {
        $cache = \Config\Services::cache();
        $attempts = $cache->get($key) ?: 0;
        $cache->save($key, $attempts + 1, $windowSeconds);
    }

    /**
     * Clear rate limit
     */
    public function clearRateLimit($key)
    {
        $cache = \Config\Services::cache();
        $cache->delete($key);
    }

    /**
     * Encrypt sensitive data
     */
    public function encrypt($data, $key = null)
    {
        $encryption = \Config\Services::encrypter();
        return $encryption->encrypt($data, $key);
    }

    /**
     * Decrypt sensitive data
     */
    public function decrypt($data, $key = null)
    {
        $encryption = \Config\Services::encrypter();
        return $encryption->decrypt($data, $key);
    }

    /**
     * Log security event
     */
    public function logSecurityEvent($event, $details = [])
    {
        $logger = \Config\Services::logger();
        
        $logData = [
            'event' => $event,
            'ip_address' => $this->getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'timestamp' => date('Y-m-d H:i:s'),
            'details' => $details
        ];
        
        $logger->warning('Security Event: ' . $event, $logData);
    }

    /**
     * Get client IP address
     */
    protected function getClientIP()
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
}
