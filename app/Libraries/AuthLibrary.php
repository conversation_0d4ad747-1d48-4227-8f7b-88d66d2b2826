<?php

namespace App\Libraries;

use App\Models\UserModel;
use App\Models\RememberTokenModel;
use App\Models\LoginAttemptModel;
use CodeIgniter\Session\Session;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

class AuthLibrary
{
    protected $userModel;
    protected $rememberTokenModel;
    protected $loginAttemptModel;
    protected $session;
    protected $config;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->rememberTokenModel = new RememberTokenModel();
        $this->loginAttemptModel = new LoginAttemptModel();
        $this->session = session();
        $this->config = config('App');
    }

    /**
     * Attempt to log in a user
     */
    public function attempt($username, $password, $remember = false)
    {
        $ipAddress = $this->getClientIP();
        
        // Check rate limiting (can be disabled for testing)
        $rateLimitingEnabled = env('auth.rate_limiting_enabled', true);
        if ($rateLimitingEnabled && $this->loginAttemptModel->isRateLimited($ipAddress, $username)) {
            $remainingTime = $this->loginAttemptModel->getRemainingLockoutTime($ipAddress, $username);
            return [
                'success' => false,
                'message' => "Too many failed attempts. Try again in " . ceil($remainingTime / 60) . " minutes.",
                'lockout_time' => $remainingTime
            ];
        }

        // Find user
        $user = $this->userModel->getUserByUsername($username);
        
        if (!$user || !password_verify($password, $user['password_hash'])) {
            // Record failed attempt (if rate limiting is enabled)
            if ($rateLimitingEnabled) {
                $this->loginAttemptModel->recordAttempt($ipAddress, $username);
            }

            return [
                'success' => false,
                'message' => 'Invalid username or password.'
            ];
        }

        // Check if user is active
        if (!$user['is_active']) {
            return [
                'success' => false,
                'message' => 'Your account has been deactivated.'
            ];
        }

        // Clear failed attempts (if rate limiting is enabled)
        if ($rateLimitingEnabled) {
            $this->loginAttemptModel->clearAttempts($ipAddress, $username);
        }

        // Check if password change is required
        $needsPasswordChange = $this->userModel->needsPasswordChange($user['id']);

        // Update last login
        $this->userModel->updateLastLogin($user['id']);

        // Set session
        $this->setUserSession($user);

        // Handle remember me
        if ($remember) {
            $this->setRememberToken($user['id']);
        }

        $response = [
            'success' => true,
            'message' => 'Login successful.',
            'user' => $user
        ];

        // Add password change requirement to response
        if ($needsPasswordChange) {
            $response['requires_password_change'] = true;
            $response['redirect'] = '/auth/change-password';
        }

        return $response;
    }

    /**
     * Log out the current user
     */
    public function logout()
    {
        $userId = $this->session->get('user_id');
        
        if ($userId) {
            // Remove remember tokens
            $this->rememberTokenModel->deleteUserTokens($userId);
            
            // Remove remember cookie
            if (isset($_COOKIE['remember_token'])) {
                setcookie('remember_token', '', time() - 3600, '/');
            }
        }

        // Destroy session
        $this->session->destroy();
        
        return true;
    }

    /**
     * Check if user is logged in
     */
    public function isLoggedIn()
    {
        return $this->session->get('user_id') !== null;
    }

    /**
     * Get current user
     */
    public function user()
    {
        $userId = $this->session->get('user_id');
        
        if (!$userId) {
            return null;
        }

        return $this->userModel->getUserWithRoles($userId);
    }

    /**
     * Check if user has permission
     */
    public function hasPermission($permission)
    {
        $user = $this->user();
        
        if (!$user) {
            return false;
        }

        return $this->userModel->hasPermission($user['id'], $permission);
    }

    /**
     * Check if user has role
     */
    public function hasRole($roleName)
    {
        $user = $this->user();

        if (!$user || !$user['roles']) {
            return false;
        }

        $roles = explode(',', $user['roles']);
        return in_array($roleName, $roles);
    }

    /**
     * Check if user has any of the specified roles
     */
    public function hasAnyRole($roleNames)
    {
        if (is_string($roleNames)) {
            $roleNames = [$roleNames];
        }

        $user = $this->user();

        if (!$user || !$user['roles']) {
            return false;
        }

        $userRoles = explode(',', $user['roles']);

        foreach ($roleNames as $roleName) {
            if (in_array($roleName, $userRoles)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if user can manage projects (admin or manager)
     */
    public function canManageProjects()
    {
        return $this->hasAnyRole(['admin', 'manager']);
    }

    /**
     * Check if user can manage tasks (admin or manager)
     */
    public function canManageTasks()
    {
        return $this->hasAnyRole(['admin', 'manager']);
    }

    /**
     * Check if user can view all projects (admin or manager)
     */
    public function canViewAllProjects()
    {
        return $this->hasAnyRole(['admin', 'manager']);
    }

    /**
     * Check if user can assign projects/tasks (admin or manager)
     */
    public function canAssignWork()
    {
        return $this->hasAnyRole(['admin', 'manager']);
    }

    /**
     * Set user session
     */
    protected function setUserSession($user)
    {
        $sessionData = [
            'user_id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'is_logged_in' => true,
            'login_time' => time()
        ];

        $this->session->set($sessionData);
        
        // Regenerate session ID for security
        $this->session->regenerate();
    }

    /**
     * Set remember token
     */
    protected function setRememberToken($userId)
    {
        $token = $this->rememberTokenModel->generateToken();
        
        if ($this->rememberTokenModel->createToken($userId, $token)) {
            // Set cookie for 7 days
            setcookie('remember_token', $token, time() + (7 * 24 * 60 * 60), '/', '', true, true);
            return true;
        }
        
        return false;
    }

    /**
     * Check remember token
     */
    public function checkRememberToken()
    {
        if (!isset($_COOKIE['remember_token']) || $this->isLoggedIn()) {
            return false;
        }

        $token = $_COOKIE['remember_token'];
        $tokenData = $this->rememberTokenModel->verifyToken($token);

        if ($tokenData) {
            // Log user in automatically
            $this->setUserSession($tokenData);
            
            // Refresh the token
            $this->setRememberToken($tokenData['user_id']);
            
            return true;
        } else {
            // Invalid token, remove cookie
            setcookie('remember_token', '', time() - 3600, '/');
            return false;
        }
    }

    /**
     * Generate JWT token
     */
    public function generateJWT($user)
    {
        $payload = [
            'iss' => base_url(),
            'aud' => base_url(),
            'iat' => time(),
            'exp' => time() + (int)env('jwt.expiration', 3600),
            'user_id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email']
        ];

        return JWT::encode($payload, env('jwt.secret'), env('jwt.algorithm', 'HS256'));
    }

    /**
     * Verify JWT token
     */
    public function verifyJWT($token)
    {
        try {
            $decoded = JWT::decode($token, new Key(env('jwt.secret'), env('jwt.algorithm', 'HS256')));
            return (array) $decoded;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get client IP address
     */
    protected function getClientIP()
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
}
