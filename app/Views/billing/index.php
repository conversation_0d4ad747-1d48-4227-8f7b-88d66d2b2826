<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-money-bill-wave me-2"></i>
                Billing Management
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item active">Billing</li>
                </ol>
            </nav>
        </div>
        <div>
            <button class="btn btn-primary" onclick="refreshBillingData()">
                <i class="fas fa-sync-alt me-2"></i>Refresh
            </button>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4" id="summaryCards">
        <!-- Cards will be loaded here -->
    </div>

    <!-- Main Content -->
    <div class="row">
        <!-- Pending Payments -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Pending Payments
                    </h5>
                </div>
                <div class="card-body">
                    <div id="pendingPayments">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Payments -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        Recent Payments
                    </h5>
                </div>
                <div class="card-body">
                    <div id="recentPayments">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Status Modal -->
<div class="modal fade" id="paymentStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Payment Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="paymentStatusForm">
                    <input type="hidden" id="taskId" name="task_id">
                    <div class="mb-3">
                        <label class="form-label">Payment Amount</label>
                        <input type="number" class="form-control" id="paymentAmount" name="payment_amount" step="0.01" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Payment Status</label>
                        <select class="form-control" id="paymentStatus" name="payment_status" required>
                            <option value="unpaid">Unpaid</option>
                            <option value="paid">Paid</option>
                            <option value="partial">Partial</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Payment Account</label>
                        <select class="form-control" id="paymentAccount" name="payment_account">
                            <option value="">Select Account</option>
                            <option value="bank_account_1">Main Bank Account</option>
                            <option value="bank_account_2">Secondary Account</option>
                            <option value="cash">Cash</option>
                            <option value="online">Online Payment</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea class="form-control" id="paymentNotes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updatePaymentStatus()">Update Status</button>
            </div>
        </div>
    </div>
</div>

<style>
.billing-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.billing-card:hover {
    transform: translateY(-2px);
}

.billing-card .card-body {
    padding: 1.5rem;
}

.billing-amount {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.billing-label {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

.payment-item {
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 0;
}

.payment-item:last-child {
    border-bottom: none;
}

.payment-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.payment-status.paid {
    background-color: #d1e7dd;
    color: #0f5132;
}

.payment-status.unpaid {
    background-color: #f8d7da;
    color: #842029;
}

.payment-status.partial {
    background-color: #fff3cd;
    color: #664d03;
}

.pending-payment-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
}

.pending-payment-card:hover {
    border-color: #0d6efd;
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.15);
}
</style>

<script>
let billingData = {};

// Load billing data on page load
document.addEventListener('DOMContentLoaded', function() {
    loadBillingData();
});

// Auto-refresh every 30 seconds
setInterval(function() {
    loadBillingData();
}, 30000);

function loadBillingData() {
    fetch(`/billing/getBillingData?_t=${Date.now()}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        },
        cache: 'no-store'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            billingData = data.data;
            displaySummaryCards(data.data.summary);
            displayPendingPayments(data.data.pending_payments);
            displayRecentPayments(data.data.recent_payments);
        } else {
            showAlert('error', data.message || 'Failed to load billing data');
        }
    })
    .catch(error => {
        console.error('Error loading billing data:', error);
        showAlert('error', 'Error loading billing data');
    });
}

function displaySummaryCards(summary) {
    const container = document.getElementById('summaryCards');
    container.innerHTML = `
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card billing-card bg-success text-white">
                <div class="card-body text-center">
                    <div class="billing-amount">₹${formatNumber(summary.total_paid)}</div>
                    <div class="billing-label">Total Paid</div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card billing-card bg-danger text-white">
                <div class="card-body text-center">
                    <div class="billing-amount">₹${formatNumber(summary.total_unpaid)}</div>
                    <div class="billing-label">Total Unpaid</div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card billing-card bg-warning text-white">
                <div class="card-body text-center">
                    <div class="billing-amount">₹${formatNumber(summary.total_partial)}</div>
                    <div class="billing-label">Partial Payments</div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card billing-card bg-primary text-white">
                <div class="card-body text-center">
                    <div class="billing-amount">₹${formatNumber(summary.total_revenue)}</div>
                    <div class="billing-label">Total Revenue</div>
                </div>
            </div>
        </div>
    `;
}

function displayPendingPayments(payments) {
    const container = document.getElementById('pendingPayments');

    if (payments.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                <h6>No Pending Payments</h6>
                <p class="text-muted">All payments are up to date!</p>
            </div>
        `;
        return;
    }

    container.innerHTML = payments.map(payment => `
        <div class="pending-payment-card">
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <h6 class="mb-1">${payment.project_name}</h6>
                    <p class="text-muted mb-1">${payment.task_name}</p>
                    <small class="text-muted">Project #${payment.project_number}</small>
                </div>
                <div class="text-end">
                    <div class="fw-bold text-primary mb-1">₹${formatNumber(payment.payment_amount)}</div>
                    <span class="payment-status ${payment.payment_status}">${payment.payment_status.toUpperCase()}</span>
                </div>
            </div>
            <div class="mt-2">
                <button class="btn btn-sm btn-outline-primary" onclick="openPaymentModal(${payment.id}, '${payment.payment_status}')">
                    <i class="fas fa-edit me-1"></i>Update Status
                </button>
            </div>
        </div>
    `).join('');
}

function displayRecentPayments(payments) {
    const container = document.getElementById('recentPayments');

    if (payments.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-history fa-2x text-muted mb-3"></i>
                <p class="text-muted">No recent payments</p>
            </div>
        `;
        return;
    }

    container.innerHTML = payments.map(payment => `
        <div class="payment-item">
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <div class="fw-bold">${payment.project_name}</div>
                    <small class="text-muted">${payment.task_name}</small>
                    <div class="mt-1">
                        <span class="payment-status ${payment.payment_status}">${payment.payment_status.toUpperCase()}</span>
                    </div>
                </div>
                <div class="text-end">
                    <div class="fw-bold">₹${formatNumber(payment.payment_amount)}</div>
                    <small class="text-muted">${formatDate(payment.updated_at)}</small>
                    <div class="mt-1">
                        <button class="btn btn-sm btn-outline-secondary" onclick="openEditPaymentModal(${payment.id}, '${payment.payment_status}', ${payment.payment_amount})">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

function openPaymentModal(taskId, currentStatus) {
    document.getElementById('taskId').value = taskId;
    document.getElementById('paymentStatus').value = currentStatus;
    document.getElementById('paymentAmount').value = '';

    const modal = new bootstrap.Modal(document.getElementById('paymentStatusModal'));
    modal.show();
}

function openEditPaymentModal(taskId, currentStatus, currentAmount) {
    document.getElementById('taskId').value = taskId;
    document.getElementById('paymentStatus').value = currentStatus;
    document.getElementById('paymentAmount').value = currentAmount;

    const modal = new bootstrap.Modal(document.getElementById('paymentStatusModal'));
    modal.show();
}

function updatePaymentStatus() {
    const taskId = document.getElementById('taskId').value;
    const paymentAmount = document.getElementById('paymentAmount').value;
    const paymentStatus = document.getElementById('paymentStatus').value;
    const paymentAccount = document.getElementById('paymentAccount').value;
    const notes = document.getElementById('paymentNotes').value;

    const formData = new FormData();
    formData.append('payment_amount', paymentAmount);
    formData.append('payment_status', paymentStatus);
    formData.append('payment_account', paymentAccount);
    formData.append('notes', notes);

    fetch(`/billing/updatePaymentStatus/${taskId}`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('paymentStatusModal')).hide();
            loadBillingData(); // Refresh data
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error updating payment status:', error);
        showAlert('error', 'Error updating payment status');
    });
}

function refreshBillingData() {
    loadBillingData();
    showAlert('info', 'Billing data refreshed');
}

function formatNumber(num) {
    return new Intl.NumberFormat('en-IN').format(num || 0);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function showAlert(type, message) {
    // Simple alert implementation
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}
</script>
<?= $this->endSection() ?>
