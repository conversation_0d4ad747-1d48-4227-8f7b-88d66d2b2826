<!-- Bottom Navigation -->
<nav class="bottom-nav">
    <div class="bottom-nav-container">
        <a href="/dashboard" class="bottom-nav-item <?= (uri_string() === 'dashboard' || uri_string() === '') ? 'active' : '' ?>">
            <i class="fas fa-home"></i>
            <span>Dashboard</span>
        </a>
        <?php if ($user && (strpos($user['roles'] ?? '', 'admin') !== false || strpos($user['permissions'] ?? '', 'user.read') !== false)): ?>
        <a href="/admin/users" class="bottom-nav-item <?= strpos(uri_string(), 'admin/users') === 0 ? 'active' : '' ?>">
            <i class="fas fa-users"></i>
            <span>Users</span>
        </a>
        <?php else: ?>
        <a href="/messages" class="bottom-nav-item <?= strpos(uri_string(), 'messages') === 0 ? 'active' : '' ?>">
            <i class="fas fa-envelope"></i>
            <span>Messages</span>
        </a>
        <?php endif; ?>
        <a href="/admin/users/create" class="bottom-nav-item">
            <i class="fas fa-plus"></i>
            <span>Add</span>
        </a>
        <a href="/notifications" class="bottom-nav-item <?= strpos(uri_string(), 'notifications') === 0 ? 'active' : '' ?>">
            <i class="fas fa-bell"></i>
            <span>Alerts</span>
        </a>
        <a href="/auth/logout" class="bottom-nav-item">
            <i class="fas fa-sign-out-alt"></i>
            <span>Logout</span>
        </a>
    </div>
</nav>
