<!-- Main Header -->
<header class="header">
    <div class="header-left">
        <h1><?= $pageTitle ?? 'SmartFlo' ?></h1>
    </div>

    <div class="header-right">
        <!-- Messages Dropdown -->
        <div class="header-dropdown">
            <button class="header-btn" onclick="toggleMessagesDropdown()" title="Messages">
                <i class="fas fa-envelope"></i>
                <span class="notification-badge" id="messagesBadge">2</span>
            </button>
            <div class="dropdown-tray" id="messagesDropdown">
                <div class="dropdown-header">
                    <h4>Recent Messages</h4>
                    <button class="dropdown-close" onclick="toggleMessagesDropdown()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="dropdown-content" id="messagesContent">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Loading messages...</p>
                    </div>
                </div>
                <div class="dropdown-footer">
                    <a href="/messages" class="btn btn-primary btn-sm">
                        <i class="fas fa-envelope me-1"></i>
                        See All Messages
                    </a>
                </div>
            </div>
        </div>

        <!-- Notifications Dropdown -->
        <div class="header-dropdown">
            <button class="header-btn" onclick="toggleNotificationsDropdown()" title="Notifications">
                <i class="fas fa-bell"></i>
                <span class="notification-badge" id="notificationsBadge" style="display: none;">0</span>
            </button>
            <div class="dropdown-tray" id="notificationsDropdown">
                <div class="dropdown-header">
                    <h4>Recent Notifications</h4>
                    <button class="dropdown-close" onclick="toggleNotificationsDropdown()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="dropdown-content" id="notificationsContent">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Loading notifications...</p>
                    </div>
                </div>
                <div class="dropdown-footer">
                    <a href="/notifications" class="btn btn-primary btn-sm">
                        <i class="fas fa-bell me-1"></i>
                        See All Notifications
                    </a>
                </div>
            </div>
        </div>

        <!-- File Manager -->
        <button class="header-btn" onclick="window.location.href='/blob/manager'" title="File Manager">
            <i class="fas fa-folder-open"></i>
        </button>

        <div class="user-menu" onclick="toggleUserMenu()">
            <div class="user-avatar">
                <?= strtoupper(substr($user['username'] ?? 'A', 0, 1)) ?>
            </div>
            <div class="user-info">
                <div class="user-name"><?= $user['username'] ?? 'User' ?></div>
                <div class="user-role"><?= ucfirst($user['roles'] ?? 'User') ?></div>
            </div>
            <i class="fas fa-chevron-down"></i>
        </div>
    </div>
</header>

<!-- User Dropdown Menu -->
<div class="user-dropdown" id="userDropdown" style="display: none;">
    <div class="user-dropdown-header">
        <div class="user-avatar large">
            <?= strtoupper(substr($user['username'] ?? 'A', 0, 1)) ?>
        </div>
        <div class="user-info">
            <div class="user-name"><?= $user['username'] ?? 'User' ?></div>
            <div class="user-role"><?= ucfirst($user['roles'] ?? 'User') ?></div>
        </div>
    </div>
    <div class="user-dropdown-menu">
        <a href="/auth/profile" class="dropdown-item">
            <i class="fas fa-user"></i>
            Profile Settings
        </a>
        <a href="/settings" class="dropdown-item">
            <i class="fas fa-cog"></i>
            Settings
        </a>
        <div class="dropdown-divider"></div>
        <a href="/auth/logout" class="dropdown-item logout">
            <i class="fas fa-sign-out-alt"></i>
            Logout
        </a>
    </div>
</div>

<style>
/* Header Dropdowns */
.header-dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-tray {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    width: 350px;
    max-height: 400px;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--gray-200);
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    overflow: hidden;
}

.dropdown-tray.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dropdown-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
}

.dropdown-close {
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.dropdown-close:hover {
    background: var(--gray-200);
    color: var(--gray-700);
}

.dropdown-content {
    max-height: 280px;
    overflow-y: auto;
}

.dropdown-footer {
    padding: var(--space-md);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
    text-align: center;
}

.loading-spinner {
    padding: var(--space-xl);
    text-align: center;
    color: var(--gray-500);
}

.loading-spinner i {
    font-size: 1.5rem;
    margin-bottom: var(--space-sm);
}

/* Message/Notification Items */
.dropdown-item-msg, .dropdown-item-notif {
    padding: var(--space-md);
    border-bottom: 1px solid var(--gray-100);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.dropdown-item-msg:hover, .dropdown-item-notif:hover {
    background: var(--gray-50);
}

.dropdown-item-msg:last-child, .dropdown-item-notif:last-child {
    border-bottom: none;
}

.dropdown-item-msg.unread, .dropdown-item-notif.unread {
    background: rgba(99, 102, 241, 0.05);
    border-left: 3px solid var(--primary);
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-xs);
}

.item-title {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 0.875rem;
    line-height: 1.3;
}

.item-time {
    font-size: 0.75rem;
    color: var(--gray-500);
    white-space: nowrap;
}

.item-content {
    color: var(--gray-600);
    font-size: 0.875rem;
    line-height: 1.4;
    margin-bottom: var(--space-xs);
}

.item-sender {
    font-size: 0.75rem;
    color: var(--gray-500);
}

.empty-state {
    padding: var(--space-xl);
    text-align: center;
    color: var(--gray-500);
}

.empty-state i {
    font-size: 2rem;
    margin-bottom: var(--space-md);
    opacity: 0.5;
}

/* User Dropdown */
.user-dropdown {
    position: fixed;
    top: 70px;
    right: 20px;
    width: 280px;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--gray-200);
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    display: block !important;
}

.user-dropdown-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.user-avatar.large {
    width: 48px;
    height: 48px;
    font-size: 1.125rem;
}

.user-dropdown-menu {
    padding: var(--space-sm);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-md);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    font-size: 0.875rem;
    font-weight: 500;
}

.dropdown-item:hover {
    background: var(--gray-50);
    color: var(--primary);
}

.dropdown-item.logout:hover {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error);
}

.dropdown-divider {
    height: 1px;
    background: var(--gray-200);
    margin: var(--space-sm) 0;
}
</style>

<script>
// Messages Dropdown
function toggleMessagesDropdown() {
    const dropdown = document.getElementById('messagesDropdown');
    const notifDropdown = document.getElementById('notificationsDropdown');
    const userDropdown = document.getElementById('userDropdown');

    // Close other dropdowns
    if (notifDropdown) notifDropdown.classList.remove('show');
    if (userDropdown) userDropdown.classList.remove('show');

    if (dropdown) {
        dropdown.classList.toggle('show');
        if (dropdown.classList.contains('show')) {
            loadRecentMessages();
        }
    }
}

// Notifications Dropdown
function toggleNotificationsDropdown() {
    const dropdown = document.getElementById('notificationsDropdown');
    const msgDropdown = document.getElementById('messagesDropdown');
    const userDropdown = document.getElementById('userDropdown');

    // Close other dropdowns
    if (msgDropdown) msgDropdown.classList.remove('show');
    if (userDropdown) userDropdown.classList.remove('show');

    if (dropdown) {
        dropdown.classList.toggle('show');
        if (dropdown.classList.contains('show')) {
            loadRecentNotifications();
        }
    }
}

// User Menu Dropdown
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    const msgDropdown = document.getElementById('messagesDropdown');
    const notifDropdown = document.getElementById('notificationsDropdown');

    // Close other dropdowns
    if (msgDropdown) msgDropdown.classList.remove('show');
    if (notifDropdown) notifDropdown.classList.remove('show');

    if (dropdown) {
        dropdown.classList.toggle('show');
    }
}

// Load Recent Messages
function loadRecentMessages() {
    const content = document.getElementById('messagesContent');
    if (!content) return;

    // Show loading
    content.innerHTML = `
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading messages...</p>
        </div>
    `;

    // Load real messages from API
    fetch('/api/recent-messages', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.data.length > 0) {
            content.innerHTML = data.data.map(message => `
                <div class="dropdown-item-msg ${!message.is_read ? 'unread' : ''}" onclick="openMessage(${message.id})">
                    <div class="item-header">
                        <div class="item-title">${message.subject}</div>
                        <div class="item-time">${formatTimeAgo(message.created_at)}</div>
                    </div>
                    <div class="item-content">${truncateText(message.message, 60)}</div>
                    <div class="item-sender">From: ${message.sender_username || 'Unknown'}</div>
                </div>
            `).join('');
        } else {
            content.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-envelope"></i>
                    <p>No recent messages</p>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error loading messages:', error);
        content.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle"></i>
                <p>Error loading messages</p>
            </div>
        `;
    });
}

// Load Recent Notifications
function loadRecentNotifications() {
    const content = document.getElementById('notificationsContent');
    if (!content) return;

    // Show loading
    content.innerHTML = `
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading notifications...</p>
        </div>
    `;

    // Load real notifications from API
    fetch('/api/recent-notifications', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.data.length > 0) {
            content.innerHTML = data.data.map(notification => `
                <div class="dropdown-item-notif ${!notification.is_read ? 'unread' : ''}" onclick="openNotification(${notification.id})">
                    <div class="item-header">
                        <div class="item-title">${notification.title}</div>
                        <div class="item-time">${formatTimeAgo(notification.created_at)}</div>
                    </div>
                    <div class="item-content">${truncateText(notification.message, 80)}</div>
                </div>
            `).join('');
        } else {
            content.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-bell"></i>
                    <p>No recent notifications</p>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error loading notifications:', error);
        content.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle"></i>
                <p>Error loading notifications</p>
            </div>
        `;
    });
}

// Utility functions
function formatTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;

    return date.toLocaleDateString();
}

function truncateText(text, maxLength) {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

// Open specific message
function openMessage(messageId) {
    window.location.href = `/messages/view/${messageId}`;
}

// Open specific notification
function openNotification(notificationId) {
    // Mark as read when clicked
    fetch(`/api/mark-notification-read/${notificationId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    }).then(() => {
        updateBadgeCounts(); // Update badge after marking as read
    });

    window.location.href = `/notifications/view/${notificationId}`;
}

// Update badge counts
function updateBadgeCounts() {
    fetch('/api/unread-counts', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const messagesBadge = document.getElementById('messagesBadge');
            const notificationsBadge = document.getElementById('notificationsBadge');

            if (messagesBadge) {
                if (data.data.messages > 0) {
                    messagesBadge.textContent = data.data.messages;
                    messagesBadge.style.display = 'flex';
                } else {
                    messagesBadge.style.display = 'none';
                }
            }

            if (notificationsBadge) {
                if (data.data.notifications > 0) {
                    notificationsBadge.textContent = data.data.notifications;
                    notificationsBadge.style.display = 'flex';
                } else {
                    notificationsBadge.style.display = 'none';
                }
            }
        }
    })
    .catch(error => console.log('Badge update failed:', error));
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const userMenu = document.querySelector('.user-menu');
    const userDropdown = document.getElementById('userDropdown');
    const msgDropdown = document.getElementById('messagesDropdown');
    const notifDropdown = document.getElementById('notificationsDropdown');

    // Check if click is outside any dropdown
    const isOutsideUser = userMenu && !userMenu.contains(event.target);
    const isOutsideMsg = !event.target.closest('.header-dropdown');

    if (isOutsideUser && userDropdown) {
        userDropdown.classList.remove('show');
    }

    if (isOutsideMsg) {
        if (msgDropdown) msgDropdown.classList.remove('show');
        if (notifDropdown) notifDropdown.classList.remove('show');
    }
});

// Update badges every 30 seconds
setInterval(updateBadgeCounts, 30000);

// Initial load
document.addEventListener('DOMContentLoaded', function() {
    updateBadgeCounts();
});
</script>
