<?= $this->extend('layouts/app') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="page-header-create">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <h1 class="page-title mb-0">
                            <i class="fas fa-tasks me-3"></i>
                            Task Management
                        </h1>
                    </div>
                    <div>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                            <i class="fas fa-plus me-2"></i>
                            Add Category
                        </button>
                    </div>
                </div>
            </div>

            <!-- Task Categories -->
            <div class="row" id="taskCategories">
                <!-- Categories will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addCategoryForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">Category Name</label>
                        <input type="text" class="form-control" id="categoryName" name="category_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="categoryId" class="form-label">Category ID</label>
                        <input type="text" class="form-control" id="categoryId" name="category_id" required>
                        <div class="form-text">Lowercase, no spaces (e.g., "custom_work")</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Category</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Task Modal -->
<div class="modal fade" id="addTaskModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Task</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addTaskForm">
                <div class="modal-body">
                    <input type="hidden" id="taskCategoryId" name="category_id">
                    <div class="mb-3">
                        <label for="taskName" class="form-label">Task Name</label>
                        <input type="text" class="form-control" id="taskName" name="task_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="taskId" class="form-label">Task ID</label>
                        <input type="text" class="form-control" id="taskId" name="task_id" required>
                        <div class="form-text">Lowercase, no spaces (e.g., "custom_task")</div>
                    </div>
                    <div class="mb-3">
                        <label for="defaultDays" class="form-label">Default Target Days</label>
                        <input type="number" class="form-control" id="defaultDays" name="default_days" min="1" max="365" value="7" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Task</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Task Modal -->
<div class="modal fade" id="editTaskModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Task</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editTaskForm">
                <div class="modal-body">
                    <input type="hidden" id="editTaskId" name="task_id">
                    <div class="mb-3">
                        <label for="editTaskName" class="form-label">Task Name</label>
                        <input type="text" class="form-control" id="editTaskName" name="task_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="editDefaultDays" class="form-label">Default Target Days</label>
                        <input type="number" class="form-control" id="editDefaultDays" name="default_days" min="1" max="365" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Task</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* Task Management Styles */
.page-header-create {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1rem;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    line-height: 1.2;
}

.category-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    overflow: hidden;
}

.category-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: between;
    align-items: center;
}

.category-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    color: #1e293b;
}

.task-list {
    padding: 0;
    margin: 0;
    list-style: none;
}

.task-item {
    padding: 1rem;
    border-bottom: 1px solid #f1f5f9;
    display: flex;
    justify-content: between;
    align-items: center;
}

.task-item:last-child {
    border-bottom: none;
}

.task-info {
    flex: 1;
}

.task-name {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.task-details {
    font-size: 0.875rem;
    color: #64748b;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}
</style>

<script>
let taskData = {};

document.addEventListener('DOMContentLoaded', function() {
    loadTaskData();
    setupEventListeners();
});

function setupEventListeners() {
    // Add category form
    document.getElementById('addCategoryForm').addEventListener('submit', function(e) {
        e.preventDefault();
        addCategory();
    });

    // Add task form
    document.getElementById('addTaskForm').addEventListener('submit', function(e) {
        e.preventDefault();
        addTask();
    });

    // Edit task form
    document.getElementById('editTaskForm').addEventListener('submit', function(e) {
        e.preventDefault();
        updateTask();
    });
}

function loadTaskData() {
    fetch('/task-manager/getTaskData', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            taskData = data.tasks;
            displayCategories();
        } else {
            showAlert('error', data.message || 'Failed to load task data');
        }
    })
    .catch(error => {
        console.error('Error loading task data:', error);
        showAlert('error', 'Error loading task data');
    });
}

function displayCategories() {
    const container = document.getElementById('taskCategories');
    container.innerHTML = '';

    Object.keys(taskData).forEach(categoryId => {
        const categoryCard = createCategoryCard(categoryId, taskData[categoryId]);
        container.appendChild(categoryCard);
    });
}

function createCategoryCard(categoryId, tasks) {
    const card = document.createElement('div');
    card.className = 'col-lg-6 col-xl-4';
    
    card.innerHTML = `
        <div class="category-card">
            <div class="category-header">
                <h3 class="category-title">
                    <i class="fas fa-folder me-2"></i>
                    ${categoryId.charAt(0).toUpperCase() + categoryId.slice(1)}
                </h3>
                <button class="btn btn-sm btn-outline-primary" onclick="showAddTaskModal('${categoryId}')">
                    <i class="fas fa-plus"></i>
                </button>
            </div>
            <ul class="task-list">
                ${tasks.map(task => createTaskItem(task)).join('')}
            </ul>
        </div>
    `;
    
    return card;
}

function createTaskItem(task) {
    return `
        <li class="task-item">
            <div class="task-info">
                <div class="task-name">${task.name}</div>
                <div class="task-details">
                    ID: ${task.id} | Default: ${task.default_days || 7} days
                </div>
            </div>
            <div class="task-actions">
                <button class="btn btn-sm btn-outline-secondary" onclick="editTask('${task.id}', '${task.name}', ${task.default_days || 7})">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteTask('${task.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </li>
    `;
}

function showAddTaskModal(categoryId) {
    document.getElementById('taskCategoryId').value = categoryId;
    const modal = new bootstrap.Modal(document.getElementById('addTaskModal'));
    modal.show();
}

function editTask(taskId, taskName, defaultDays) {
    document.getElementById('editTaskId').value = taskId;
    document.getElementById('editTaskName').value = taskName;
    document.getElementById('editDefaultDays').value = defaultDays;
    
    const modal = new bootstrap.Modal(document.getElementById('editTaskModal'));
    modal.show();
}

function addCategory() {
    const formData = new FormData(document.getElementById('addCategoryForm'));
    
    fetch('/task-manager/addCategory', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('addCategoryModal')).hide();
            document.getElementById('addCategoryForm').reset();
            loadTaskData(); // Reload data
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error adding category:', error);
        showAlert('error', 'Error adding category');
    });
}

function addTask() {
    const formData = new FormData(document.getElementById('addTaskForm'));
    
    fetch('/task-manager/addTask', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('addTaskModal')).hide();
            document.getElementById('addTaskForm').reset();
            loadTaskData(); // Reload data
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error adding task:', error);
        showAlert('error', 'Error adding task');
    });
}

function updateTask() {
    const formData = new FormData(document.getElementById('editTaskForm'));
    
    fetch('/task-manager/updateTask', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('editTaskModal')).hide();
            loadTaskData(); // Reload data
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error updating task:', error);
        showAlert('error', 'Error updating task');
    });
}

function deleteTask(taskId) {
    if (!confirm('Are you sure you want to delete this task?')) {
        return;
    }
    
    const formData = new FormData();
    formData.append('task_id', taskId);
    
    fetch('/task-manager/deleteTask', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            loadTaskData(); // Reload data
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error deleting task:', error);
        showAlert('error', 'Error deleting task');
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>

<?= $this->endSection() ?>
