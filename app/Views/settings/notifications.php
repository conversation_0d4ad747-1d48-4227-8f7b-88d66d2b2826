<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-bell me-2"></i>
                        Notification Settings
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/settings">Settings</a></li>
                            <li class="breadcrumb-item active">Notifications</li>
                        </ol>
                    </nav>
                </div>
            </div>

            <div class="row">
                <!-- Settings Navigation -->
                <div class="col-md-3 mb-4">
                    <div class="card">
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                <a href="/settings" class="list-group-item list-group-item-action">
                                    <i class="fas fa-home me-2"></i>
                                    Overview
                                </a>
                                <a href="/settings/account" class="list-group-item list-group-item-action">
                                    <i class="fas fa-user me-2"></i>
                                    Account
                                </a>
                                <a href="/settings/notifications" class="list-group-item list-group-item-action active">
                                    <i class="fas fa-bell me-2"></i>
                                    Notifications
                                </a>
                                <a href="/settings/security" class="list-group-item list-group-item-action">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    Security
                                </a>
                                <a href="/settings/preferences" class="list-group-item list-group-item-action">
                                    <i class="fas fa-sliders-h me-2"></i>
                                    Preferences
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notification Settings Content -->
                <div class="col-md-9">
                    <!-- Notification Preferences -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-cog me-2"></i>
                                Notification Preferences
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="notificationForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="mb-3">Notification Types</h6>
                                        
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="emailNotifications" name="email_notifications" checked>
                                            <label class="form-check-label" for="emailNotifications">
                                                <i class="fas fa-envelope me-2"></i>
                                                Email Notifications
                                            </label>
                                            <small class="form-text text-muted d-block">Receive notifications via email</small>
                                        </div>

                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="pushNotifications" name="push_notifications" checked>
                                            <label class="form-check-label" for="pushNotifications">
                                                <i class="fas fa-mobile-alt me-2"></i>
                                                Push Notifications
                                            </label>
                                            <small class="form-text text-muted d-block">Receive browser push notifications</small>
                                        </div>

                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="smsNotifications" name="sms_notifications">
                                            <label class="form-check-label" for="smsNotifications">
                                                <i class="fas fa-sms me-2"></i>
                                                SMS Notifications
                                            </label>
                                            <small class="form-text text-muted d-block">Receive notifications via SMS</small>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h6 class="mb-3">Frequency</h6>
                                        
                                        <div class="mb-3">
                                            <label for="notificationFrequency" class="form-label">Notification Frequency</label>
                                            <select class="form-select" id="notificationFrequency" name="notification_frequency">
                                                <option value="immediate">Immediate</option>
                                                <option value="hourly">Hourly Digest</option>
                                                <option value="daily">Daily Digest</option>
                                                <option value="weekly">Weekly Digest</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <hr>

                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-outline-secondary" onclick="loadSettings()">
                                        <i class="fas fa-undo me-2"></i>
                                        Reset
                                    </button>
                                    <button type="submit" class="btn btn-primary" id="saveBtn">
                                        <i class="fas fa-save me-2"></i>
                                        Save Settings
                                    </button>
                                </div>

                                <input type="hidden" name="csrf_token" value="<?= csrf_hash() ?>">
                            </form>
                        </div>
                    </div>

                    <!-- Send Test Notification -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-paper-plane me-2"></i>
                                Send Test Notification
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="testNotificationForm">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="testMessage" class="form-label">Test Message</label>
                                            <input type="text" class="form-control" id="testMessage" name="message" 
                                                   placeholder="Enter test notification message" 
                                                   value="This is a test notification from SmartFlo">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="testType" class="form-label">Type</label>
                                            <select class="form-select" id="testType" name="type">
                                                <option value="info">Info</option>
                                                <option value="success">Success</option>
                                                <option value="warning">Warning</option>
                                                <option value="error">Error</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    Send Test Notification
                                </button>
                                <input type="hidden" name="csrf_token" value="<?= csrf_hash() ?>">
                            </form>
                        </div>
                    </div>

                    <!-- Notification History -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-history me-2"></i>
                                Recent Notifications
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="notificationHistory">
                                <div class="text-center py-4">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.list-group-item-action:hover {
    background-color: #f8f9fa;
}

.list-group-item-action.active {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.notification-item {
    border-left: 4px solid #dee2e6;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
}

.notification-item.info {
    border-left-color: #0dcaf0;
}

.notification-item.success {
    border-left-color: #198754;
}

.notification-item.warning {
    border-left-color: #ffc107;
}

.notification-item.error {
    border-left-color: #dc3545;
}
</style>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadSettings();
    loadNotificationHistory();

    // Save notification settings
    document.getElementById('notificationForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveSettings();
    });

    // Send test notification
    document.getElementById('testNotificationForm').addEventListener('submit', function(e) {
        e.preventDefault();
        sendTestNotification();
    });
});

function loadSettings() {
    fetch('/settings/get-notification-settings', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const settings = data.data;
            document.getElementById('emailNotifications').checked = settings.email_notifications;
            document.getElementById('pushNotifications').checked = settings.push_notifications;
            document.getElementById('smsNotifications').checked = settings.sms_notifications;
            document.getElementById('notificationFrequency').value = settings.notification_frequency;
        }
    })
    .catch(error => {
        console.error('Error loading settings:', error);
    });
}

function saveSettings() {
    const form = document.getElementById('notificationForm');
    const formData = new FormData(form);
    const saveBtn = document.getElementById('saveBtn');
    
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
    
    fetch('/settings/update-notifications', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error saving settings:', error);
        showAlert('danger', 'Error saving settings');
    })
    .finally(() => {
        saveBtn.disabled = false;
        saveBtn.innerHTML = '<i class="fas fa-save me-2"></i>Save Settings';
    });
}

function sendTestNotification() {
    const form = document.getElementById('testNotificationForm');
    const formData = new FormData(form);
    
    fetch('/notifications/create', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Test notification sent successfully');
            loadNotificationHistory();
        } else {
            showAlert('danger', data.message || 'Failed to send test notification');
        }
    })
    .catch(error => {
        console.error('Error sending test notification:', error);
        showAlert('danger', 'Error sending test notification');
    });
}

function loadNotificationHistory() {
    fetch('/notifications', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayNotificationHistory(data.notifications);
        } else {
            document.getElementById('notificationHistory').innerHTML = 
                '<p class="text-muted">No notifications found</p>';
        }
    })
    .catch(error => {
        console.error('Error loading notification history:', error);
        document.getElementById('notificationHistory').innerHTML = 
            '<p class="text-danger">Error loading notifications</p>';
    });
}

function displayNotificationHistory(notifications) {
    const container = document.getElementById('notificationHistory');
    
    if (notifications.length === 0) {
        container.innerHTML = '<p class="text-muted">No notifications found</p>';
        return;
    }
    
    container.innerHTML = notifications.map(notification => `
        <div class="notification-item ${notification.type}">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h6 class="mb-1">${notification.title}</h6>
                    <p class="mb-1">${notification.message}</p>
                    <small class="text-muted">${notification.time_ago}</small>
                </div>
                <div>
                    <i class="${notification.icon} text-${notification.type === 'error' ? 'danger' : notification.type}"></i>
                </div>
            </div>
        </div>
    `).join('');
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
<?= $this->endSection() ?>
