<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-cog me-2"></i>
                    Settings
                </h1>
            </div>

            <div class="row">
                <!-- Settings Navigation -->
                <div class="col-md-3 mb-4">
                    <div class="card">
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                <a href="/settings" class="list-group-item list-group-item-action active">
                                    <i class="fas fa-home me-2"></i>
                                    Overview
                                </a>
                                <a href="/settings/account" class="list-group-item list-group-item-action">
                                    <i class="fas fa-user me-2"></i>
                                    Account
                                </a>
                                <a href="/settings/notifications" class="list-group-item list-group-item-action">
                                    <i class="fas fa-bell me-2"></i>
                                    Notifications
                                </a>
                                <a href="/settings/security" class="list-group-item list-group-item-action">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    Security
                                </a>
                                <a href="/settings/preferences" class="list-group-item list-group-item-action">
                                    <i class="fas fa-sliders-h me-2"></i>
                                    Preferences
                                </a>
                                <a href="/settings/project" class="list-group-item list-group-item-action">
                                    <i class="fas fa-building me-2"></i>
                                    Project Settings
                                </a>
                                <a href="/settings/billing" class="list-group-item list-group-item-action">
                                    <i class="fas fa-credit-card me-2"></i>
                                    Billing & Payments
                                </a>
                                <a href="/settings/integrations" class="list-group-item list-group-item-action">
                                    <i class="fas fa-plug me-2"></i>
                                    Integrations
                                </a>
                                <a href="/settings/backup" class="list-group-item list-group-item-action">
                                    <i class="fas fa-download me-2"></i>
                                    Backup & Export
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Content -->
                <div class="col-md-9">
                    <!-- Overview Cards -->
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="icon-circle bg-primary text-white me-3">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Account Settings</h5>
                                            <small class="text-muted">Manage your profile and account information</small>
                                        </div>
                                    </div>
                                    <p class="card-text">Update your personal information, contact details, and account preferences.</p>
                                    <a href="/settings/account" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        Manage Account
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="icon-circle bg-success text-white me-3">
                                            <i class="fas fa-bell"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Notifications</h5>
                                            <small class="text-muted">Control how you receive notifications</small>
                                        </div>
                                    </div>
                                    <p class="card-text">Configure email, push, and SMS notification preferences.</p>
                                    <a href="/settings/notifications" class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        Manage Notifications
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="icon-circle bg-warning text-white me-3">
                                            <i class="fas fa-shield-alt"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Security</h5>
                                            <small class="text-muted">Protect your account and data</small>
                                        </div>
                                    </div>
                                    <p class="card-text">Change password, enable two-factor authentication, and review security settings.</p>
                                    <a href="/settings/security" class="btn btn-outline-warning btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        Security Settings
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="icon-circle bg-info text-white me-3">
                                            <i class="fas fa-sliders-h"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Preferences</h5>
                                            <small class="text-muted">Customize your experience</small>
                                        </div>
                                    </div>
                                    <p class="card-text">Set your theme, language, timezone, and display preferences.</p>
                                    <a href="/settings/preferences" class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        Set Preferences
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="icon-circle bg-secondary text-white me-3">
                                            <i class="fas fa-building"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Project Settings</h5>
                                            <small class="text-muted">Configure project defaults and templates</small>
                                        </div>
                                    </div>
                                    <p class="card-text">Set default project settings, templates, and construction-specific configurations.</p>
                                    <a href="/settings/project" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        Project Settings
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="icon-circle bg-success text-white me-3">
                                            <i class="fas fa-credit-card"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Billing & Payments</h5>
                                            <small class="text-muted">Manage subscription and payment methods</small>
                                        </div>
                                    </div>
                                    <p class="card-text">View billing history, update payment methods, and manage your subscription.</p>
                                    <a href="/settings/billing" class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        Billing Settings
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="icon-circle bg-primary text-white me-3">
                                            <i class="fas fa-plug"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Integrations</h5>
                                            <small class="text-muted">Connect with external tools and services</small>
                                        </div>
                                    </div>
                                    <p class="card-text">Integrate with accounting software, project management tools, and other services.</p>
                                    <a href="/settings/integrations" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        Manage Integrations
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="icon-circle bg-info text-white me-3">
                                            <i class="fas fa-download"></i>
                                        </div>
                                        <div>
                                            <h5 class="card-title mb-0">Backup & Export</h5>
                                            <small class="text-muted">Download and backup your data</small>
                                        </div>
                                    </div>
                                    <p class="card-text">Export project data, create backups, and download reports.</p>
                                    <a href="/settings/backup" class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        Backup Options
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="d-grid">
                                        <a href="/auth/change-password" class="btn btn-outline-primary">
                                            <i class="fas fa-key me-2"></i>
                                            Change Password
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="d-grid">
                                        <a href="/auth/profile" class="btn btn-outline-success">
                                            <i class="fas fa-user-edit me-2"></i>
                                            Edit Profile
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="d-grid">
                                        <a href="/blob/manager" class="btn btn-outline-info">
                                            <i class="fas fa-folder-open me-2"></i>
                                            File Manager
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.icon-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.list-group-item-action:hover {
    background-color: #f8f9fa;
}

.list-group-item-action.active {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

.card {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
</style>

<?= $this->endSection() ?>
