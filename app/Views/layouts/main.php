<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="csrf-token" content="<?= csrf_hash() ?>">
    <title><?= $title ?? 'SmartFlo' ?></title>
    
    <!-- Modern PWA Meta Tags -->
    <meta name="application-name" content="SmartFlo">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="SmartFlo">
    <meta name="description" content="SmartFlo - Modern Business Management">
    <meta name="theme-color" content="#6366f1">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" href="/icons/icon-192x192.png">
    <link rel="manifest" href="/manifest.json">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- DataTables CSS (if needed) -->
    <?php if (isset($useDataTables) && $useDataTables): ?>
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Chart.js CSS (if needed) -->
    <?php if (isset($useCharts) && $useCharts): ?>
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.css" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Common SmartFlo Styles -->
    <link rel="stylesheet" href="/assets/css/smartflo-common.css">
    
    <!-- Page-specific styles -->
    <?= $this->renderSection('styles') ?>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar Component -->
        <?= view('components/sidebar', ['user' => $user ?? null]) ?>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Header Component -->
            <?= view('components/header', [
                'user' => $user ?? null, 
                'pageTitle' => $pageTitle ?? $title ?? 'SmartFlo'
            ]) ?>

            <!-- Content -->
            <div class="content">
                <!-- Flash Messages -->
                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?= session()->getFlashdata('error') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (session()->getFlashdata('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?= session()->getFlashdata('success') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (session()->getFlashdata('warning')): ?>
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?= session()->getFlashdata('warning') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Page Content -->
                <?= $this->renderSection('content') ?>
            </div>
        </main>
    </div>
    
    <!-- Bottom Navigation Component -->
    <?= view('components/bottom-nav', ['user' => $user ?? null]) ?>
</div>

<!-- Simple Notification Tray -->
<div id="notificationTray" class="notification-tray">
    <div class="notification-tray-header">
        <span class="notification-tray-title">Notifications</span>
        <button class="notification-tray-close" onclick="toggleNotifications()">×</button>
    </div>
    <div class="notification-tray-list" id="notificationList">
        <div class="notification-empty">
            <i class="fas fa-bell"></i>
            <p>Loading...</p>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

<!-- DataTables JS (if needed) -->
<?php if (isset($useDataTables) && $useDataTables): ?>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<?php endif; ?>

<!-- Chart.js (if needed) -->
<?php if (isset($useCharts) && $useCharts): ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.js"></script>
<?php endif; ?>

<!-- Common JavaScript -->
<script src="/assets/js/smartflo-common.js"></script>
<script src="/assets/js/sidebar-mini.js"></script>

<!-- Page-specific scripts -->
<?= $this->renderSection('scripts') ?>
</body>
</html>
