<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-key me-2"></i>
                    Change Password
                </h1>
                <a href="/dashboard" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Back to Dashboard
                </a>
            </div>

            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-shield-alt me-2"></i>
                                Update Your Password
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (isset($required) && $required): ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    You must change your password to continue using the system.
                                </div>
                            <?php endif; ?>

                            <div id="alert-container"></div>

                            <form id="changePasswordForm">
                                <input type="hidden" name="csrf_token" value="<?= csrf_hash() ?>">

                                <div class="mb-3">
                                    <label for="current_password" class="form-label">
                                        <i class="fas fa-lock me-1"></i>
                                        Current Password
                                    </label>
                                    <input type="password" class="form-control" id="current_password" name="current_password" required>
                                </div>

                                <div class="mb-3">
                                    <label for="new_password" class="form-label">
                                        <i class="fas fa-key me-1"></i>
                                        New Password
                                    </label>
                                    <input type="password" class="form-control" id="new_password" name="new_password" required>
                                </div>

                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">
                                        <i class="fas fa-check-circle me-1"></i>
                                        Confirm New Password
                                    </label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>

                                <div class="password-requirements">
                                    <h6 class="mb-2">Password Requirements:</h6>
                                    <div class="requirement" id="req-length">
                                        <i class="fas fa-circle me-1"></i>
                                        At least 8 characters
                                    </div>
                                    <div class="requirement" id="req-uppercase">
                                        <i class="fas fa-circle me-1"></i>
                                        At least one uppercase letter
                                    </div>
                                    <div class="requirement" id="req-lowercase">
                                        <i class="fas fa-circle me-1"></i>
                                        At least one lowercase letter
                                    </div>
                                    <div class="requirement" id="req-number">
                                        <i class="fas fa-circle me-1"></i>
                                        At least one number
                                    </div>
                                    <div class="requirement" id="req-special">
                                        <i class="fas fa-circle me-1"></i>
                                        At least one special character
                                    </div>
                                </div>

                                <div class="d-grid gap-2 mt-4">
                                    <button type="submit" class="btn btn-primary" id="changePasswordBtn">
                                        <i class="fas fa-save me-2"></i>
                                        Change Password
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.password-requirements {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1rem;
}

.requirement {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.requirement.valid {
    color: #28a745;
}

.requirement.invalid {
    color: #dc3545;
}
</style>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('changePasswordForm');
    const newPasswordInput = document.getElementById('new_password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    const submitBtn = document.getElementById('changePasswordBtn');

    // Password validation
    newPasswordInput.addEventListener('input', function() {
        validatePassword(this.value);
    });

    function validatePassword(password) {
        const requirements = {
            'req-length': password.length >= 8,
            'req-uppercase': /[A-Z]/.test(password),
            'req-lowercase': /[a-z]/.test(password),
            'req-number': /\d/.test(password),
            'req-special': /[!@#$%^&*(),.?":{}|<>]/.test(password)
        };

        for (const [id, valid] of Object.entries(requirements)) {
            const element = document.getElementById(id);
            element.className = valid ? 'requirement valid' : 'requirement invalid';
            element.querySelector('i').className = valid ? 'fas fa-check-circle me-1' : 'fas fa-circle me-1';
        }
    }

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(form);
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Changing Password...';

        fetch('/auth/change-password', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1500);
            } else {
                showAlert('danger', data.message);
                if (data.errors) {
                    for (const [field, error] of Object.entries(data.errors)) {
                        showAlert('danger', error);
                    }
                }
            }
        })
        .catch(error => {
            showAlert('danger', 'An error occurred. Please try again.');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>Change Password';
        });
    });

    function showAlert(type, message) {
        const alertContainer = document.getElementById('alert-container');
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        alertContainer.appendChild(alert);

        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
});
</script>
<?= $this->endSection() ?>
