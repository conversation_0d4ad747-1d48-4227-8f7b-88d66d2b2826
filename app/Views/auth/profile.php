<?= $this->extend('layouts/app') ?>

<?= $this->section('styles') ?>
<style>
        /* Profile-specific styles */

        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            font-weight: 700;
            margin: 0 auto 1.5rem;
            box-shadow: var(--shadow-lg);
        }

        .role-badge {
            background: var(--primary);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            display: inline-block;
        }

        .info-item {
            background: var(--gray-50);
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            padding: var(--space-lg);
            margin-bottom: var(--space-md);
        }

        .info-label {
            font-weight: 600;
            color: var(--gray-700);
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }

        .info-value {
            color: var(--gray-900);
            font-size: 1rem;
        }


    </style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-title-wrapper">
            <h1 class="page-title">
                <i class="fas fa-user"></i>
                User Profile
            </h1>
            <p class="page-subtitle">Manage your account information and settings</p>
        </div>
    </div>
</div>

            <div class="row">
                <!-- Profile Information -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="profile-avatar">
                                <?= strtoupper(substr($user['username'], 0, 1)) ?>
                            </div>
                            <h4 class="mb-2"><?= htmlspecialchars($user['username']) ?></h4>
                            <p class="text-muted mb-3"><?= htmlspecialchars($user['email']) ?></p>

                            <div class="mb-3">
                                <?php if (!empty($userRoles)): ?>
                                    <?php foreach ($userRoles as $role): ?>
                                        <span class="role-badge"><?= htmlspecialchars($role['role_name']) ?></span>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <span class="text-muted">No roles assigned</span>
                                <?php endif; ?>
                            </div>

                            <div class="info-item">
                                <div class="info-label">Member Since</div>
                                <div class="info-value"><?= date('M j, Y', strtotime($user['created_at'])) ?></div>
                            </div>

                            <div class="info-item">
                                <div class="info-label">Last Login</div>
                                <div class="info-value">
                                    <?= $user['last_login'] ? date('M j, Y g:i A', strtotime($user['last_login'])) : 'Never' ?>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-label">Account Status</div>
                                <div class="info-value">
                                    <span class="badge bg-<?= $user['is_active'] ? 'success' : 'danger' ?>">
                                        <?= $user['is_active'] ? 'Active' : 'Inactive' ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Edit Profile Form -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-edit me-2"></i>
                                Edit Profile
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="profileForm">
                                <?= csrf_field() ?>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="username" class="form-label">
                                            <i class="fas fa-user me-1"></i>
                                            Username
                                        </label>
                                        <input type="text" class="form-control" id="username" name="username"
                                               value="<?= htmlspecialchars($user['username']) ?>" disabled>
                                        <small class="form-text text-muted">Username cannot be changed</small>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">
                                            <i class="fas fa-envelope me-1"></i>
                                            Email Address
                                        </label>
                                        <input type="email" class="form-control" id="email" name="email"
                                               value="<?= htmlspecialchars($user['email']) ?>" disabled>
                                        <small class="form-text text-muted">Email cannot be changed</small>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="first_name" class="form-label">
                                            <i class="fas fa-user me-1"></i>
                                            First Name
                                        </label>
                                        <input type="text" class="form-control" id="first_name" name="first_name"
                                               value="<?= htmlspecialchars($user['first_name'] ?? '') ?>">
                                        <div class="invalid-feedback"></div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="last_name" class="form-label">
                                            <i class="fas fa-user me-1"></i>
                                            Last Name
                                        </label>
                                        <input type="text" class="form-control" id="last_name" name="last_name"
                                               value="<?= htmlspecialchars($user['last_name'] ?? '') ?>">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="phone" class="form-label">
                                        <i class="fas fa-phone me-1"></i>
                                        Phone Number
                                    </label>
                                    <input type="text" class="form-control" id="phone" name="phone"
                                           value="<?= htmlspecialchars($user['phone'] ?? '') ?>">
                                    <div class="invalid-feedback"></div>
                                </div>

                                <div class="mb-4">
                                    <label for="bio" class="form-label">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Bio
                                    </label>
                                    <textarea class="form-control" id="bio" name="bio" rows="3"
                                              placeholder="Tell us about yourself..."><?= htmlspecialchars($user['bio'] ?? '') ?></textarea>
                                    <div class="invalid-feedback"></div>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary" id="submitBtn">
                                        <i class="fas fa-save me-2"></i>
                                        Update Profile
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Change Password Section -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-key me-2"></i>
                                Change Password
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="changePasswordForm">
                                <div class="mb-3">
                                    <label for="currentPassword" class="form-label">Current Password</label>
                                    <input type="password" class="form-control" id="currentPassword" name="current_password" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="mb-3">
                                    <label for="newPassword" class="form-label">New Password</label>
                                    <input type="password" class="form-control" id="newPassword" name="new_password" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="mb-3">
                                    <label for="confirmPassword" class="form-label">Confirm New Password</label>
                                    <input type="password" class="form-control" id="confirmPassword" name="confirm_password" required>
                                    <div class="invalid-feedback"></div>
                                </div>

                                <!-- Password Requirements -->
                                <div class="password-requirements">
                                    <h6>Password Requirements:</h6>
                                    <div class="requirement" id="lengthReq">
                                        <i class="fas fa-times text-danger me-2"></i>
                                        At least 8 characters long
                                    </div>
                                    <div class="requirement" id="uppercaseReq">
                                        <i class="fas fa-times text-danger me-2"></i>
                                        Contains uppercase letter
                                    </div>
                                    <div class="requirement" id="lowercaseReq">
                                        <i class="fas fa-times text-danger me-2"></i>
                                        Contains lowercase letter
                                    </div>
                                    <div class="requirement" id="numberReq">
                                        <i class="fas fa-times text-danger me-2"></i>
                                        Contains number
                                    </div>
                                    <div class="requirement" id="specialReq">
                                        <i class="fas fa-times text-danger me-2"></i>
                                        Contains special character
                                    </div>
                                    <div class="requirement" id="matchReq">
                                        <i class="fas fa-times text-danger me-2"></i>
                                        Passwords match
                                    </div>
                                </div>

                                <input type="hidden" name="csrf_token" value="<?= csrf_hash() ?>">

                                <div class="d-grid gap-2 mt-4">
                                    <button type="submit" class="btn btn-warning" id="changePasswordBtn">
                                        <i class="fas fa-key me-2"></i>
                                        Change Password
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.password-requirements {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1rem;
}

.requirement {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.requirement.valid {
    color: #28a745;
}

.requirement.invalid {
    color: #dc3545;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>

    <script>
        // Mobile viewport height fix
        function setVH() {
            let vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }

        window.addEventListener('resize', setVH);
        window.addEventListener('orientationchange', setVH);
        setVH();

        // Password validation
        const newPasswordInput = document.getElementById('newPassword');
        const confirmPasswordInput = document.getElementById('confirmPassword');

        function validatePassword() {
            const password = newPasswordInput.value;
            const confirmPassword = confirmPasswordInput.value;

            // Length check
            const lengthReq = document.getElementById('lengthReq');
            if (password.length >= 8) {
                lengthReq.innerHTML = '<i class="fas fa-check text-success me-2"></i>At least 8 characters long';
                lengthReq.classList.add('valid');
            } else {
                lengthReq.innerHTML = '<i class="fas fa-times text-danger me-2"></i>At least 8 characters long';
                lengthReq.classList.remove('valid');
            }

            // Uppercase check
            const uppercaseReq = document.getElementById('uppercaseReq');
            if (/[A-Z]/.test(password)) {
                uppercaseReq.innerHTML = '<i class="fas fa-check text-success me-2"></i>Contains uppercase letter';
                uppercaseReq.classList.add('valid');
            } else {
                uppercaseReq.innerHTML = '<i class="fas fa-times text-danger me-2"></i>Contains uppercase letter';
                uppercaseReq.classList.remove('valid');
            }

            // Lowercase check
            const lowercaseReq = document.getElementById('lowercaseReq');
            if (/[a-z]/.test(password)) {
                lowercaseReq.innerHTML = '<i class="fas fa-check text-success me-2"></i>Contains lowercase letter';
                lowercaseReq.classList.add('valid');
            } else {
                lowercaseReq.innerHTML = '<i class="fas fa-times text-danger me-2"></i>Contains lowercase letter';
                lowercaseReq.classList.remove('valid');
            }

            // Number check
            const numberReq = document.getElementById('numberReq');
            if (/[0-9]/.test(password)) {
                numberReq.innerHTML = '<i class="fas fa-check text-success me-2"></i>Contains number';
                numberReq.classList.add('valid');
            } else {
                numberReq.innerHTML = '<i class="fas fa-times text-danger me-2"></i>Contains number';
                numberReq.classList.remove('valid');
            }

            // Special character check
            const specialReq = document.getElementById('specialReq');
            if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
                specialReq.innerHTML = '<i class="fas fa-check text-success me-2"></i>Contains special character';
                specialReq.classList.add('valid');
            } else {
                specialReq.innerHTML = '<i class="fas fa-times text-danger me-2"></i>Contains special character';
                specialReq.classList.remove('valid');
            }

            // Match check
            const matchReq = document.getElementById('matchReq');
            if (password && confirmPassword && password === confirmPassword) {
                matchReq.innerHTML = '<i class="fas fa-check text-success me-2"></i>Passwords match';
                matchReq.classList.add('valid');
            } else {
                matchReq.innerHTML = '<i class="fas fa-times text-danger me-2"></i>Passwords match';
                matchReq.classList.remove('valid');
            }
        }

        newPasswordInput.addEventListener('input', validatePassword);
        confirmPasswordInput.addEventListener('input', validatePassword);

        // Change password form submission
        document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = document.getElementById('changePasswordBtn');
            const originalText = submitBtn.innerHTML;

            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Changing Password...';

            fetch('/auth/change-password', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                    this.reset();
                    validatePassword(); // Reset validation display
                } else {
                    showError(data.message);
                    if (data.errors) {
                        Object.keys(data.errors).forEach(field => {
                            const input = document.querySelector(`#changePasswordForm [name="${field}"]`);
                            if (input) {
                                input.classList.add('is-invalid');
                                const feedback = input.parentNode.querySelector('.invalid-feedback');
                                if (feedback) {
                                    feedback.textContent = data.errors[field];
                                }
                            }
                        });
                    }
                }
            })
            .catch(error => {
                showError('An error occurred while changing password.');
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });

        // Form submission
        document.getElementById('profileForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Clear previous errors
            clearErrors();

            // Get form data
            const formData = new FormData(this);

            // Disable submit button
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';

            // Submit form
            fetch('/auth/profile', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': formData.get('csrf_token')
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                } else {
                    if (data.offline) {
                        showOfflineError(data.message);
                    } else {
                        showError(data.message);
                        if (data.errors) {
                            displayErrors(data.errors);
                        }
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (error.message.includes('Failed to fetch') || !navigator.onLine) {
                    showOfflineError('No internet connection. Please check your network and try again.');
                } else {
                    showError('An error occurred while updating your profile.');
                }
            })
            .finally(() => {
                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });

        // Utility functions
        function clearErrors() {
            document.querySelectorAll('.is-invalid').forEach(el => {
                el.classList.remove('is-invalid');
            });
            document.querySelectorAll('.invalid-feedback').forEach(el => {
                el.textContent = '';
            });
        }

        function displayErrors(errors) {
            Object.keys(errors).forEach(field => {
                const input = document.querySelector(`[name="${field}"]`);
                if (input) {
                    input.classList.add('is-invalid');
                    const feedback = input.parentNode.querySelector('.invalid-feedback');
                    if (feedback) {
                        feedback.textContent = errors[field];
                    }
                }
            });
        }

        function showSuccess(message) {
            const toast = createToast('success', message);
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        }

        function showError(message) {
            const toast = createToast('error', message);
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        }

        function showOfflineError(message) {
            const modal = createOfflineModal(message);
            document.body.appendChild(modal);
        }

        function createToast(type, message) {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            return toast;
        }

        function createOfflineModal(message) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0, 0, 0, 0.8); z-index: 10000;
                display: flex; align-items: center; justify-content: center;
                padding: 1rem;
            `;
            modal.innerHTML = `
                <div style="
                    background: white; border-radius: 16px; padding: 2rem;
                    max-width: 400px; width: 100%; text-align: center;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                ">
                    <div style="
                        width: 80px; height: 80px; background: var(--error);
                        border-radius: 50%; display: flex; align-items: center;
                        justify-content: center; margin: 0 auto 1.5rem;
                        color: white; font-size: 2rem;
                    ">
                        📡
                    </div>
                    <h3 style="color: var(--gray-900); margin-bottom: 0.5rem;">No Internet Connection</h3>
                    <p style="color: var(--gray-600); margin-bottom: 2rem; line-height: 1.6;">
                        ${message}
                    </p>
                    <div style="display: flex; gap: 0.5rem; justify-content: center;">
                        <button onclick="this.closest('[style*=\"position: fixed\"]').remove(); document.getElementById('profileForm').dispatchEvent(new Event('submit'));"
                                style="background: var(--primary); color: white; border: none;
                                       padding: 0.75rem 1.5rem; border-radius: 8px; font-weight: 600; cursor: pointer;">
                            Try Again
                        </button>
                        <button onclick="this.closest('[style*=\"position: fixed\"]').remove();"
                                style="background: var(--gray-200); color: var(--gray-700); border: none;
                                       padding: 0.75rem 1.5rem; border-radius: 8px; font-weight: 600; cursor: pointer;">
                            Close
                        </button>
                    </div>
                </div>
            `;

            // Auto-close when back online
            const onlineHandler = () => {
                modal.remove();
                window.removeEventListener('online', onlineHandler);
            };
            window.addEventListener('online', onlineHandler);

            return modal;
        }

        // Placeholder functions for header buttons
        function showNotifications() {
            console.log('Show notifications panel');
        }

        function showUserMenu() {
            console.log('Show user menu');
        }

        // Mobile sidebar toggle
        const sidebar = document.getElementById('sidebar');
        const sidebarToggle = document.getElementById('sidebarToggle');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('show');
            });
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 991 && sidebar.classList.contains('show')) {
                if (!sidebar.contains(e.target) && !sidebarToggle?.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });
    </script>
<?= $this->endSection() ?>
