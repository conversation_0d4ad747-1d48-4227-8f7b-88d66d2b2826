<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title><?= $title ?? 'Login - SmartFlo' ?></title>

    <!-- Modern PWA Meta Tags -->
    <meta name="application-name" content="SmartFlo">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="SmartFlo">
    <meta name="description" content="SmartFlo - Modern Authentication">
    <meta name="theme-color" content="#6366f1">
    <meta name="csrf-token" content="<?= csrf_hash() ?>">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" href="/icons/icon-192x192.png">
    <link rel="manifest" href="/manifest.json">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* ===== MODERN CSS VARIABLES ===== */
        :root {
            /* Colors - Modern Palette */
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #a5b4fc;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --info: #3b82f6;

            /* Neutrals - Refined Grays */
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;

            /* Spacing */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;

            /* Border Radius */
            --radius-sm: 6px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 24px;

            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

            /* Transitions */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-base: 250ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* ===== GLOBAL RESET ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            font-size: 16px;
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-weight: 400;
            line-height: 1.6;
            color: var(--gray-800);
            background: var(--gray-50);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-lg);
            position: relative;
            overflow-x: hidden;
        }

        /* ===== BACKGROUND PATTERN ===== */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            opacity: 0.05;
            z-index: -2;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
            z-index: -1;
        }

        /* ===== LOGIN CONTAINER ===== */
        .login-container {
            width: 100%;
            max-width: 440px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        .login-card {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            border: 1px solid var(--gray-200);
            overflow: hidden;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        /* ===== LOGIN HEADER ===== */
        .login-header {
            padding: var(--space-2xl);
            text-align: center;
            background: white;
            border-bottom: 1px solid var(--gray-100);
            position: relative;
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-md);
            margin-bottom: var(--space-lg);
        }

        .logo-icon {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.75rem;
            box-shadow: var(--shadow-md);
        }

        .logo-text {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-900);
            letter-spacing: -0.025em;
        }

        .login-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: var(--space-sm);
        }

        .login-subtitle {
            font-size: 0.875rem;
            color: var(--gray-600);
            margin: 0;
        }

        /* ===== LOGIN BODY ===== */
        .login-body {
            padding: var(--space-2xl);
            background: white;
        }

        /* ===== FORM STYLES ===== */
        .form-group {
            margin-bottom: var(--space-lg);
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: var(--space-sm);
        }

        .form-control {
            width: 100%;
            padding: var(--space-md);
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: var(--gray-900);
            background: var(--gray-50);
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            background: white;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-control::placeholder {
            color: var(--gray-500);
            opacity: 1;
        }

        /* ===== CHECKBOX STYLES ===== */
        .form-check {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            margin-bottom: var(--space-lg);
        }

        .form-check-input {
            width: 18px;
            height: 18px;
            border: 2px solid var(--gray-300);
            border-radius: var(--radius-sm);
            background: white;
            cursor: pointer;
            transition: all var(--transition-fast);
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            position: relative;
            flex-shrink: 0;
        }

        .form-check-input:checked {
            background: var(--primary);
            border-color: var(--primary);
        }

        .form-check-input:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: 600;
        }

        .form-check-input:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-check-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--gray-700);
            cursor: pointer;
            user-select: none;
        }

        /* ===== BUTTON STYLES ===== */
        .btn-login {
            width: 100%;
            padding: var(--space-lg);
            font-size: 1rem;
            font-weight: 600;
            color: white;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all var(--transition-base);
            position: relative;
            overflow: hidden;
            min-height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-sm);
            box-shadow: var(--shadow-md);
            -webkit-tap-highlight-color: transparent;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-login:active {
            transform: translateY(0) scale(0.98);
            box-shadow: var(--shadow-md);
        }

        .btn-login:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: var(--shadow-sm);
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            opacity: 0;
            transition: opacity var(--transition-fast);
            pointer-events: none;
        }

        .btn-login:active::before {
            opacity: 1;
        }

        /* ===== ALERT STYLES ===== */
        .alert {
            padding: var(--space-lg);
            border-radius: var(--radius-md);
            margin-bottom: var(--space-lg);
            font-size: 0.875rem;
            font-weight: 500;
            border: none;
            position: relative;
            display: flex;
            align-items: center;
            gap: var(--space-sm);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error);
            border-left: 4px solid var(--error);
        }

        .alert-danger::before {
            content: '⚠';
            font-size: 1.125rem;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border-left: 4px solid var(--success);
        }

        .alert-success::before {
            content: '✓';
            font-size: 1.125rem;
        }



        /* ===== RESPONSIVE DESIGN ===== */
        @media (max-width: 768px) {
            body {
                padding: var(--space-md);
            }

            .login-container {
                max-width: 100%;
            }

            .login-header {
                padding: var(--space-xl);
            }

            .logo-icon {
                width: 48px;
                height: 48px;
                font-size: 1.5rem;
            }

            .logo-text {
                font-size: 1.75rem;
            }

            .login-title {
                font-size: 1.25rem;
            }

            .login-body {
                padding: var(--space-xl);
            }

            .form-control {
                font-size: 16px; /* Prevent iOS zoom */
                padding: var(--space-md);
            }
        }

        @media (max-width: 480px) {
            body {
                padding: var(--space-sm);
            }

            .login-header {
                padding: var(--space-lg);
            }

            .logo-container {
                margin-bottom: var(--space-md);
            }

            .logo-icon {
                width: 40px;
                height: 40px;
                font-size: 1.25rem;
            }

            .logo-text {
                font-size: 1.5rem;
            }

            .login-title {
                font-size: 1.125rem;
            }

            .login-subtitle {
                font-size: 0.8rem;
            }

            .login-body {
                padding: var(--space-lg);
            }

            .form-group {
                margin-bottom: var(--space-md);
            }
        }

        /* ===== ANIMATIONS ===== */
        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* ===== UTILITIES ===== */
        .text-center { text-align: center; }
        .mb-0 { margin-bottom: 0; }
        .w-100 { width: 100%; }

        /* ===== LOADING STATE ===== */
        .btn-login.loading {
            pointer-events: none;
            opacity: 0.8;
        }

        .btn-login.loading::after {
            content: '';
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: var(--space-sm);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="login-container fade-in">
        <div class="login-card fade-in-up">
            <!-- Login Header -->
            <div class="login-header">
                <div class="logo-container">
                    <div class="logo-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="logo-text">SmartFlo</div>
                </div>
                <h2 class="login-title">Welcome back</h2>
                <p class="login-subtitle">Sign in to your account to continue</p>
            </div>

            <!-- Login Body -->
            <div class="login-body">
                <!-- Flash Messages -->
                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger">
                        <?= session()->getFlashdata('error') ?>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('message')): ?>
                    <div class="alert alert-success">
                        <?= session()->getFlashdata('message') ?>
                    </div>
                <?php endif; ?>

                <!-- Login Form -->
                <form id="loginForm" method="post" action="/auth/login">
                    <?= csrf_field() ?>

                    <div class="form-group">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username"
                               placeholder="Enter your username" required autocomplete="username">
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password"
                               placeholder="Enter your password" required autocomplete="current-password">
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="remember" name="remember" value="1">
                        <label class="form-check-label" for="remember">
                            Remember me
                        </label>
                    </div>

                    <button type="submit" class="btn-login">
                        <i class="fas fa-sign-in-alt"></i>
                        Sign In
                    </button>
                </form>

                <!-- Footer Links -->
                <div class="text-center" style="margin-top: var(--space-lg); padding-top: var(--space-lg); border-top: 1px solid var(--gray-200);">
                    <a href="/auth/forgot-password" style="color: var(--gray-600); text-decoration: none; font-size: 0.875rem; font-weight: 500;">
                        <i class="fas fa-key" style="margin-right: var(--space-xs);"></i>
                        Forgot your password?
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // ===== MODERN LOGIN JAVASCRIPT =====

        // Initialize login page
        document.addEventListener('DOMContentLoaded', function() {
            initializeAnimations();
            setupFormHandling();
            setupMobileOptimizations();
        });

        // Initialize animations
        function initializeAnimations() {
            // Add entrance animations
            const container = document.querySelector('.login-container');
            const card = document.querySelector('.login-card');

            setTimeout(() => {
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);

            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 200);
        }

        // Setup form handling
        function setupFormHandling() {
            const form = document.getElementById('loginForm');
            const submitBtn = form.querySelector('button[type="submit"]');

            form.addEventListener('submit', function(e) {
                e.preventDefault();

                // Show loading state
                submitBtn.classList.add('loading');
                submitBtn.disabled = true;

                // Clear previous alerts
                document.querySelectorAll('.alert').forEach(alert => alert.remove());

                // Create FormData and submit via fetch
                const formData = new FormData(form);

                fetch('/auth/login', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // Show success state briefly
                        submitBtn.classList.remove('loading');
                        submitBtn.innerHTML = '<i class="fas fa-check"></i> Success!';

                        // Redirect after brief delay
                        setTimeout(() => {
                            window.location.href = data.redirect || '/dashboard';
                        }, 500);
                    } else {
                        // Show error message
                        showAlert('danger', data.message || 'Login failed');

                        // Reset button state completely
                        resetLoginForm();
                    }
                })
                .catch(error => {
                    console.error('Login error:', error);
                    if (error.message.includes('Failed to fetch') || !navigator.onLine) {
                        showAlert('danger', 'No internet connection. Please check your network and try again.');
                    } else {
                        showAlert('danger', 'An error occurred. Please try again: ' + error.message);
                    }

                    // Reset button state completely
                    resetLoginForm();
                });
            });

            // Add input focus effects
            const inputs = form.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    if (!this.value) {
                        this.parentElement.classList.remove('focused');
                    }
                });

                // Check if input has value on load
                if (input.value) {
                    input.parentElement.classList.add('focused');
                }
            });
        }

        // Reset login form to initial state
        function resetLoginForm() {
            const form = document.getElementById('loginForm');
            const submitBtn = form.querySelector('button[type="submit"]');

            // Reset button state
            submitBtn.classList.remove('loading');
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Sign In';

            // Clear any form errors
            form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
            form.querySelectorAll('.invalid-feedback').forEach(el => el.remove());

            // Focus on username field for retry
            const usernameField = form.querySelector('input[name="username"]');
            if (usernameField) {
                setTimeout(() => usernameField.focus(), 100);
            }
        }

        // Setup mobile optimizations
        function setupMobileOptimizations() {
            // Handle viewport height for mobile
            function setVH() {
                const vh = window.innerHeight * 0.01;
                document.documentElement.style.setProperty('--vh', `${vh}px`);
            }

            setVH();
            window.addEventListener('resize', setVH);
            window.addEventListener('orientationchange', setVH);

            // Prevent iOS zoom on input focus
            if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
                const inputs = document.querySelectorAll('input[type="text"], input[type="password"]');
                inputs.forEach(input => {
                    input.addEventListener('focus', function() {
                        this.style.fontSize = '16px';
                    });
                    input.addEventListener('blur', function() {
                        this.style.fontSize = '';
                    });
                });
            }
        }

        // Show alert function
        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.innerHTML = message;

            const loginBody = document.querySelector('.login-body');
            const form = document.getElementById('loginForm');
            loginBody.insertBefore(alertDiv, form);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // Add smooth transitions on load
        document.querySelector('.login-container').style.opacity = '0';
        document.querySelector('.login-container').style.transform = 'translateY(20px)';
        document.querySelector('.login-container').style.transition = 'all 0.6s ease-out';

        document.querySelector('.login-card').style.opacity = '0';
        document.querySelector('.login-card').style.transform = 'translateY(20px)';
        document.querySelector('.login-card').style.transition = 'all 0.6s ease-out 0.1s';
    </script>
</body>
</html>
