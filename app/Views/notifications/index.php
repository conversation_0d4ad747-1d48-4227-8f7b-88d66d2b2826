<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-bell me-2"></i>
                    Notifications
                </h1>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary" onclick="markAllAsRead()">
                        <i class="fas fa-check-double me-2"></i>
                        Mark All Read
                    </button>
                    <?php if ($user && (strpos($user['roles'] ?? '', 'admin') !== false || strpos($user['permissions'] ?? '', 'notification.send') !== false)): ?>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#sendNotificationModal">
                        <i class="fas fa-paper-plane me-2"></i>
                        Send Notification
                    </button>
                    <?php endif; ?>
                </div>
            </div>

            <div class="row">
                <!-- Notification Filters -->
                <div class="col-md-3 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-filter me-2"></i>
                                Filters
                            </h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                <a href="#" class="list-group-item list-group-item-action active" data-filter="all">
                                    <i class="fas fa-list me-2"></i>
                                    All Notifications
                                    <span class="badge bg-secondary float-end" id="allCount">0</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" data-filter="unread">
                                    <i class="fas fa-envelope me-2"></i>
                                    Unread
                                    <span class="badge bg-primary float-end" id="unreadCount">0</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" data-filter="info">
                                    <i class="fas fa-info-circle me-2 text-info"></i>
                                    Info
                                    <span class="badge bg-info float-end" id="infoCount">0</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" data-filter="success">
                                    <i class="fas fa-check-circle me-2 text-success"></i>
                                    Success
                                    <span class="badge bg-success float-end" id="successCount">0</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" data-filter="warning">
                                    <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                                    Warning
                                    <span class="badge bg-warning float-end" id="warningCount">0</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" data-filter="error">
                                    <i class="fas fa-times-circle me-2 text-danger"></i>
                                    Error
                                    <span class="badge bg-danger float-end" id="errorCount">0</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Notification Settings -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-cog me-2"></i>
                                Settings
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                <label class="form-check-label" for="emailNotifications">
                                    Email Notifications
                                </label>
                            </div>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="pushNotifications" checked>
                                <label class="form-check-label" for="pushNotifications">
                                    Push Notifications
                                </label>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="soundNotifications">
                                <label class="form-check-label" for="soundNotifications">
                                    Sound Notifications
                                </label>
                            </div>
                            <hr>
                            <a href="/settings/notifications" class="btn btn-outline-primary btn-sm w-100">
                                <i class="fas fa-cog me-2"></i>
                                Advanced Settings
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Notifications List -->
                <div class="col-md-9">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-list me-2"></i>
                                Notifications
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="notificationsList">
                                <div class="text-center py-4">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Send Notification Modal -->
<?php if ($user && (strpos($user['roles'] ?? '', 'admin') !== false || strpos($user['permissions'] ?? '', 'notification.send') !== false)): ?>
<div class="modal fade" id="sendNotificationModal" tabindex="-1" aria-labelledby="sendNotificationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sendNotificationModalLabel">Send Notification</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="sendNotificationForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="notificationTitle" class="form-label">Title</label>
                        <input type="text" class="form-control" id="notificationTitle" name="title" required>
                    </div>
                    <div class="mb-3">
                        <label for="notificationMessage" class="form-label">Message</label>
                        <textarea class="form-control" id="notificationMessage" name="message" rows="3" required></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="notificationType" class="form-label">Type</label>
                                <select class="form-select" id="notificationType" name="type" required>
                                    <option value="info">Info</option>
                                    <option value="success">Success</option>
                                    <option value="warning">Warning</option>
                                    <option value="error">Error</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="notificationRecipient" class="form-label">Send To</label>
                                <select class="form-select" id="notificationRecipient" name="recipient" required>
                                    <option value="all">All Users</option>
                                    <option value="admins">Admins Only</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" name="csrf_token" value="<?= csrf_hash() ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="sendNotificationBtn">
                        <i class="fas fa-paper-plane me-2"></i>
                        Send Notification
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<style>
.notification-item {
    border-left: 4px solid #dee2e6;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    transition: all 0.3s ease;
}

.notification-item:hover {
    background-color: #e9ecef;
    transform: translateX(5px);
}

.notification-item.unread {
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.notification-item.info {
    border-left-color: #0dcaf0;
}

.notification-item.success {
    border-left-color: #198754;
}

.notification-item.warning {
    border-left-color: #ffc107;
}

.notification-item.error {
    border-left-color: #dc3545;
}

.list-group-item-action.active {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

.notification-actions {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.notification-item:hover .notification-actions {
    opacity: 1;
}
</style>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
let currentFilter = 'all';

document.addEventListener('DOMContentLoaded', function() {
    loadNotifications();
    
    // Filter click handlers
    document.querySelectorAll('[data-filter]').forEach(filter => {
        filter.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update active filter
            document.querySelectorAll('[data-filter]').forEach(f => f.classList.remove('active'));
            this.classList.add('active');
            
            currentFilter = this.dataset.filter;
            loadNotifications();
        });
    });

    // Send notification form
    document.getElementById('sendNotificationForm')?.addEventListener('submit', function(e) {
        e.preventDefault();
        sendNotification();
    });
});

function loadNotifications() {
    fetch(`/notifications/get-notifications?filter=${currentFilter}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayNotifications(data.notifications);
            updateCounts(data);
        } else {
            document.getElementById('notificationsList').innerHTML = 
                '<p class="text-muted text-center">No notifications found</p>';
        }
    })
    .catch(error => {
        console.error('Error loading notifications:', error);
        document.getElementById('notificationsList').innerHTML = 
            '<p class="text-danger text-center">Error loading notifications</p>';
    });
}

function displayNotifications(notifications) {
    const container = document.getElementById('notificationsList');
    
    if (notifications.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">No notifications found</p>';
        return;
    }
    
    container.innerHTML = notifications.map(notification => `
        <div class="notification-item ${notification.type} ${!notification.is_read ? 'unread' : ''}" data-id="${notification.id}">
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <div class="d-flex align-items-center mb-2">
                        <i class="${notification.icon} me-2 text-${notification.type === 'error' ? 'danger' : notification.type}"></i>
                        <h6 class="mb-0">${notification.title}</h6>
                        ${!notification.is_read ? '<span class="badge bg-primary ms-2">New</span>' : ''}
                    </div>
                    <p class="mb-1">${notification.message}</p>
                    <small class="text-muted">${notification.time_ago}</small>
                </div>
                <div class="notification-actions">
                    <div class="btn-group btn-group-sm">
                        ${!notification.is_read ? `
                            <button class="btn btn-outline-primary" onclick="markAsRead(${notification.id})" title="Mark as read">
                                <i class="fas fa-check"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-outline-danger" onclick="deleteNotification(${notification.id})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

function updateCounts(data) {
    // This would be implemented with real data
    document.getElementById('allCount').textContent = data.notifications.length;
    document.getElementById('unreadCount').textContent = data.unread_count || 0;
}

function markAsRead(notificationId) {
    fetch(`/notifications/mark-read/${notificationId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            is_read: true,
            csrf_token: '<?= csrf_hash() ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadNotifications();
            showAlert('success', 'Notification marked as read');
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error marking notification as read:', error);
        showAlert('danger', 'Error updating notification');
    });
}

function markAllAsRead() {
    fetch('/notifications/mark-all-read', {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            csrf_token: '<?= csrf_hash() ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadNotifications();
            showAlert('success', 'All notifications marked as read');
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error marking all notifications as read:', error);
        showAlert('danger', 'Error updating notifications');
    });
}

function deleteNotification(notificationId) {
    if (!confirm('Are you sure you want to delete this notification?')) {
        return;
    }
    
    fetch(`/notifications/${notificationId}`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadNotifications();
            showAlert('success', 'Notification deleted');
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error deleting notification:', error);
        showAlert('danger', 'Error deleting notification');
    });
}

function sendNotification() {
    const form = document.getElementById('sendNotificationForm');
    const formData = new FormData(form);
    const sendBtn = document.getElementById('sendNotificationBtn');
    
    sendBtn.disabled = true;
    sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
    
    fetch('/notifications/create', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('sendNotificationModal')).hide();
            form.reset();
            loadNotifications();
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error sending notification:', error);
        showAlert('danger', 'Error sending notification');
    })
    .finally(() => {
        sendBtn.disabled = false;
        sendBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Send Notification';
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
<?= $this->endSection() ?>
