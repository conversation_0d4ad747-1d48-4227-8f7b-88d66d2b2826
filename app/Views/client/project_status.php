<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: #fafafa;
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: #262626;
        }

        .client-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 1rem;
        }

        @media (min-width: 768px) {
            .client-container {
                padding: 1.5rem;
            }
        }

        .project-header {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #dbdbdb;
        }

        .project-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #262626;
            margin: 0;
        }

        .project-id {
            color: #8e8e8e;
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }

        .last-updated {
            color: #8e8e8e;
            font-size: 0.8rem;
        }

        .activity-feed {
            background: white;
            border-radius: 12px;
            border: 1px solid #dbdbdb;
            overflow: hidden;
        }

        .activity-item {
            padding: 1rem;
            border-bottom: 1px solid #efefef;
            position: relative;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-header {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.75rem;
            font-size: 1.1rem;
            color: white;
        }

        .activity-icon.completed { background: #28a745; }
        .activity-icon.in_progress { background: #007bff; }
        .activity-icon.on_hold { background: #fd7e14; }
        .activity-icon.not_started { background: #6c757d; }
        .activity-icon.payment { background: #17a2b8; }
        .activity-icon.hold { background: #dc3545; }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: #262626;
            margin-bottom: 0.25rem;
        }

        .activity-meta {
            color: #8e8e8e;
            font-size: 0.85rem;
            margin-bottom: 0.5rem;
        }

        .activity-description {
            color: #262626;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .payment-highlight {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
            border: 1px solid #28a745;
            border-radius: 8px;
            padding: 0.75rem;
            margin-top: 0.75rem;
        }

        .payment-amount {
            font-size: 1.1rem;
            font-weight: 600;
            color: #28a745;
        }

        .hold-reason {
            background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
            border: 1px solid #dc3545;
            border-radius: 8px;
            padding: 0.75rem;
            margin-top: 0.75rem;
        }

        .files-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 0.75rem;
            margin-top: 0.75rem;
            border: 1px solid #e9ecef;
        }

        .btn-download {
            background: #28a745;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
            font-weight: 500;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-download:hover {
            background: #218838;
            transform: translateY(-1px);
        }

        .btn-receipt {
            background: #17a2b8;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
            font-weight: 500;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-receipt:hover {
            background: #138496;
            transform: translateY(-1px);
        }

        .btn-drive {
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-drive:hover {
            background: #3367d6;
            transform: translateY(-1px);
        }

        .payment-required {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 0.75rem;
            margin-top: 0.75rem;
        }

        .refresh-btn {
            position: fixed;
            bottom: 1.5rem;
            right: 1.5rem;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            border: none;
            box-shadow: 0 3px 10px rgba(0,123,255,0.3);
            font-size: 1rem;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .refresh-btn:hover {
            transform: scale(1.1);
            background: #0056b3;
        }

        .timestamp {
            color: #8e8e8e;
            font-size: 0.75rem;
            margin-top: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="client-container">
        <!-- Project Header -->
        <div class="project-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="project-title"><?= esc($project['project_name']) ?></h1>
                    <div class="project-id">ID: <?= $project['project_id'] ?></div>
                </div>
                <div class="last-updated">
                    <i class="fas fa-clock me-1"></i>
                    <span id="lastUpdated"><?= date('M j, Y g:i A') ?></span>
                </div>
            </div>
        </div>





        <?php if ($stats['on_hold'] > 0 && !empty($stats['hold_reasons'])): ?>
        <!-- Hold Reasons Modal -->
        <div class="modal fade" id="holdReasonsModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-pause-circle text-warning me-2"></i>
                            Tasks On Hold
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <?php foreach ($stats['hold_reasons'] as $holdReason): ?>
                        <div class="hold-reason-item mb-3 p-3 border rounded">
                            <h6 class="mb-2">
                                <i class="fas fa-tasks me-2"></i>
                                <?= esc($holdReason['task_name']) ?>
                            </h6>
                            <p class="text-muted mb-2"><?= esc($holdReason['reason']) ?></p>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                Since: <?= date('M j, Y g:i A', strtotime($holdReason['date'])) ?>
                            </small>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>



        <!-- Activity Feed -->
        <div class="activity-feed">
            <?php if (empty($tasks)): ?>
            <div class="activity-item text-center">
                <i class="fas fa-info-circle fa-2x text-muted mb-3"></i>
                <h6 class="text-muted">No activity yet</h6>
                <p class="text-muted mb-0">Project activity will appear here as work progresses.</p>
            </div>
            <?php else: ?>
            <?php
            // Create activity feed items
            $activities = [];

            foreach ($tasks as $task) {
                // Task activity
                $activities[] = [
                    'type' => 'task',
                    'task' => $task,
                    'timestamp' => $task['created_at'] ?? date('Y-m-d H:i:s'),
                    'sort_order' => strtotime($task['created_at'] ?? date('Y-m-d H:i:s'))
                ];

                // Payment activity (if task has payment)
                if (!empty($task['payment_amount']) && $task['payment_amount'] > 0) {
                    $activities[] = [
                        'type' => 'payment',
                        'task' => $task,
                        'timestamp' => $task['updated_at'] ?? date('Y-m-d H:i:s'),
                        'sort_order' => strtotime($task['updated_at'] ?? date('Y-m-d H:i:s')) + 1
                    ];
                }

                // Hold reason activity (if task is on hold)
                if ($task['status'] === 'on_hold') {
                    $activities[] = [
                        'type' => 'hold',
                        'task' => $task,
                        'timestamp' => $task['updated_at'] ?? date('Y-m-d H:i:s'),
                        'sort_order' => strtotime($task['updated_at'] ?? date('Y-m-d H:i:s')) + 2
                    ];
                }
            }

            // Sort activities by timestamp (newest first)
            usort($activities, function($a, $b) {
                return $b['sort_order'] - $a['sort_order'];
            });

            foreach ($activities as $activity):
                $task = $activity['task'];
            ?>
            <!-- Activity Item -->
            <div class="activity-item" data-task-id="<?= $task['id'] ?>">
                <?php if ($activity['type'] === 'task'): ?>
                <!-- Task Activity -->
                <div class="activity-header">
                    <div class="activity-icon <?= $task['status'] ?>">
                        <?php if ($task['status'] === 'completed'): ?>
                            <i class="fas fa-check"></i>
                        <?php elseif ($task['status'] === 'in_progress'): ?>
                            <i class="fas fa-play"></i>
                        <?php elseif ($task['status'] === 'on_hold'): ?>
                            <i class="fas fa-pause"></i>
                        <?php else: ?>
                            <i class="fas fa-circle"></i>
                        <?php endif; ?>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title"><?= esc($task['task_name']) ?></div>
                        <div class="activity-meta">
                            <?= esc($task['task_type_name']) ?> •
                            <?= ucwords(str_replace('_', ' ', $task['status'])) ?>
                        </div>
                        <?php if ($task['description']): ?>
                        <div class="activity-description"><?= esc($task['description']) ?></div>
                        <?php endif; ?>

                        <?php if ($task['completed_date']): ?>
                        <div class="timestamp">
                            <i class="fas fa-check me-1"></i>
                            Completed <?= date('M j, Y', strtotime($task['completed_date'])) ?>
                        </div>
                        <?php elseif ($task['due_date']): ?>
                        <div class="timestamp">
                            <i class="fas fa-calendar me-1"></i>
                            Due <?= date('M j, Y', strtotime($task['due_date'])) ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>





                <!-- Files Section for Completed Tasks -->
                <?php if ($task['status'] === 'completed' && isset($task['task_manager_status']) && $task['task_manager_status'] === 'sent_for_review'): ?>
                <?php
                    $hasFiles = !empty($task['google_drive_link']) || !empty($task['file_path']);
                ?>
                <?php if ($hasFiles): ?>
                <div class="files-section">
                    <div class="mb-2"><strong>Project Files</strong></div>
                    <?php if (isset($task['payment_status']) && $task['payment_status'] === 'paid'): ?>
                        <?php if (!empty($task['file_path'])): ?>
                        <button class="btn-download" onclick="downloadTaskFiles(<?= $task['id'] ?>)">
                            <i class="fas fa-download me-1"></i> Download Files
                        </button>
                        <?php endif; ?>
                        <?php if (!empty($task['google_drive_link'])): ?>
                        <a href="<?= esc($task['google_drive_link']) ?>" target="_blank" class="btn-drive">
                            <i class="fab fa-google-drive me-1"></i> Google Drive
                        </a>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="payment-required">
                            <i class="fas fa-lock me-2"></i>
                            <strong>Payment Required</strong><br>
                            Complete payment to access files.
                            <?php if (isset($task['payment_amount']) && $task['payment_amount'] > 0): ?>
                                <br><small>Amount: ₹<?= number_format($task['payment_amount'], 2) ?></small>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                <?php endif; ?>

                <?php elseif ($activity['type'] === 'payment'): ?>
                <!-- Payment Activity -->
                <div class="activity-header">
                    <div class="activity-icon payment">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">Payment for <?= esc($task['task_name']) ?></div>
                        <div class="activity-meta">
                            Status: <?= ucwords($task['payment_status']) ?>
                        </div>
                        <div class="payment-highlight">
                            <div class="payment-amount">₹<?= number_format($task['payment_amount'], 2) ?></div>
                            <div class="mt-2">
                                <?php if ($task['payment_status'] === 'paid'): ?>
                                <button class="btn-receipt" onclick="downloadPaymentReceipt(<?= $task['id'] ?>)">
                                    <i class="fas fa-receipt me-1"></i> Download Receipt
                                </button>
                                <?php else: ?>
                                <small class="text-muted">Payment pending</small>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="timestamp">
                            <i class="fas fa-clock me-1"></i>
                            <?= date('M j, Y g:i A', strtotime($activity['timestamp'])) ?>
                        </div>
                    </div>
                </div>

                <?php elseif ($activity['type'] === 'hold'): ?>
                <!-- Hold Activity -->
                <div class="activity-header">
                    <div class="activity-icon hold">
                        <i class="fas fa-pause"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">Task On Hold: <?= esc($task['task_name']) ?></div>
                        <div class="activity-meta">Task temporarily paused</div>
                        <div class="hold-reason">
                            <strong>Reason:</strong>
                            <?= esc($task['notes'] ?? 'Waiting for client input or external dependencies') ?>
                        </div>
                        <div class="timestamp">
                            <i class="fas fa-clock me-1"></i>
                            <?= date('M j, Y g:i A', strtotime($activity['timestamp'])) ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Refresh Button -->
    <button class="refresh-btn" onclick="refreshData()">
        <i class="fas fa-sync-alt"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Get access token from URL
        const accessToken = window.location.pathname.split('/').pop();

        // Auto-refresh every 30 seconds with AJAX
        setInterval(function() {
            loadProjectData();
        }, 30000);

        // Load project data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadProjectData();
        });

        // Load project data via AJAX
        function loadProjectData() {
            fetch(`/client/api/project/${accessToken}?_t=${Date.now()}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache'
                },
                cache: 'no-store'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateProjectData(data);
                    updateLastUpdatedTime();
                } else {
                    console.error('Failed to load project data:', data.message);
                }
            })
            .catch(error => {
                console.error('Error loading project data:', error);
            });
        }

        // Update project data on the page
        function updateProjectData(data) {
            const project = data.project;
            const tasks = data.tasks;
            const stats = data.stats;

            // Update project progress
            const progressBar = document.querySelector('.progress-bar');
            const progressText = document.querySelector('.progress-text');
            if (progressBar && progressText) {
                progressBar.style.width = project.progress_percentage + '%';
                progressText.textContent = Math.round(project.progress_percentage) + '%';
            }



            // Update task list
            updateTaskList(tasks);
        }

        // Update task list
        function updateTaskList(tasks) {
            if (!tasks || tasks.length === 0) return;

            // Check if any task has new files or status changes
            let needsReload = false;

            tasks.forEach(task => {
                const taskCard = document.querySelector(`[data-task-id="${task.id}"]`);
                if (taskCard) {
                    // Check if file status changed
                    const hasFiles = task.file_path || task.google_drive_link;
                    const currentDownloadSection = taskCard.querySelector('.mt-3.pt-3.border-top');

                    if (hasFiles && !currentDownloadSection) {
                        // Files were added, need reload
                        needsReload = true;
                    } else if (!hasFiles && currentDownloadSection) {
                        // Files were removed, need reload
                        needsReload = true;
                    }

                    // Check if payment status changed
                    const paymentStatusEl = taskCard.querySelector('.alert-warning, .btn-success');
                    if (paymentStatusEl && task.payment_status === 'paid' && paymentStatusEl.classList.contains('alert-warning')) {
                        needsReload = true;
                    }
                }
            });

            if (needsReload) {
                setTimeout(() => location.reload(), 1000);
            }
        }

        // Update last updated time
        function updateLastUpdatedTime() {
            const lastUpdatedEl = document.getElementById('lastUpdated');
            if (lastUpdatedEl) {
                lastUpdatedEl.textContent = new Date().toLocaleString();
            }
        }

        // Manual refresh function
        function refreshData() {
            const refreshBtn = document.querySelector('.refresh-btn');
            const originalIcon = refreshBtn.innerHTML;

            // Show loading state
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            refreshBtn.disabled = true;

            // Load fresh data
            loadProjectData();

            // Restore button after 2 seconds
            setTimeout(() => {
                refreshBtn.innerHTML = originalIcon;
                refreshBtn.disabled = false;
            }, 2000);
        }

        // Show hold reasons modal
        function showHoldReasons() {
            const modal = new bootstrap.Modal(document.getElementById('holdReasonsModal'));
            modal.show();
        }

        // Download task files
        function downloadTaskFiles(taskId) {
            // Show loading state
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Downloading...';
            btn.disabled = true;

            fetch(`/client/downloadTaskFiles/${taskId}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                } else {
                    throw new Error('Download failed');
                }
            })
            .then(blob => {
                // Create download link
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `task-${taskId}-files.zip`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                // Show success message
                showAlert('success', 'Files downloaded successfully!');
            })
            .catch(error => {
                console.error('Download error:', error);
                showAlert('error', 'Failed to download files. Please try again.');
            })
            .finally(() => {
                // Restore button
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }

        // Download payment receipt
        function downloadPaymentReceipt(taskId) {
            // Show loading state
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Generating...';
            btn.disabled = true;

            fetch(`/client/downloadPaymentReceipt/${taskId}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                } else {
                    throw new Error('Receipt generation failed');
                }
            })
            .then(blob => {
                // Create download link
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `payment-receipt-${taskId}.pdf`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                // Show success message
                showAlert('success', 'Payment receipt downloaded successfully!');
            })
            .catch(error => {
                console.error('Receipt download error:', error);
                showAlert('error', 'Failed to generate receipt. Please try again.');
            })
            .finally(() => {
                // Restore button
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }

        // Simple alert function
        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }
    </script>
</body>
</html>
