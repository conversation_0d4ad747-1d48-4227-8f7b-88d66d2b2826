<?php
// Helper function to format file size
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title><?= $title ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Razorpay -->
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    
    <style>
        body {
            background: #fafafa;
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: #262626;
        }

        .client-container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0.75rem;
        }

        @media (min-width: 576px) {
            .client-container {
                max-width: 540px;
                padding: 1rem;
            }
        }

        @media (min-width: 768px) {
            .client-container {
                max-width: 720px;
                padding: 1.5rem;
            }
        }

        @media (min-width: 992px) {
            .client-container {
                max-width: 600px;
            }
        }

        .project-header {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #dbdbdb;
        }

        .project-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #262626;
            margin: 0;
        }

        .project-id {
            color: #8e8e8e;
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }

        .last-updated {
            color: #8e8e8e;
            font-size: 0.8rem;
        }

        .activity-feed {
            background: white;
            border-radius: 12px;
            border: 1px solid #dbdbdb;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .activity-section {
            background: white;
            border-radius: 12px;
            border: 1px solid #dbdbdb;
            overflow: hidden;
            margin-bottom: 0.75rem;
        }

        .section-header {
            background: #f8f9fa;
            padding: 0.6rem 1rem;
            font-weight: 600;
            color: #495057;
            border-bottom: 1px solid #efefef;
            font-size: 0.85rem;
        }

        .activity-item {
            padding: 0.75rem;
            border-bottom: 1px solid #efefef;
            position: relative;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .task-highlight {
            background: linear-gradient(135deg, #ffffff 0%, #f8fff9 100%);
        }

        .payment-highlight {
            background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
        }

        .activity-header {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.75rem;
            font-size: 1.1rem;
            color: white;
        }

        .activity-icon.completed { background: #28a745; }
        .activity-icon.in_progress { background: #007bff; }
        .activity-icon.on_hold { background: #fd7e14; }
        .activity-icon.not_started { background: #6c757d; }
        .activity-icon.payment { background: #17a2b8; }
        .activity-icon.invoice { background: #6f42c1; }
        .activity-icon.hold { background: #dc3545; }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: #262626;
            margin-bottom: 0.25rem;
        }

        .activity-meta {
            color: #8e8e8e;
            font-size: 0.85rem;
            margin-bottom: 0.5rem;
        }

        .activity-description {
            color: #262626;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .status-text {
            font-weight: 600;
        }

        .status-text.status-completed { color: #28a745; }
        .status-text.status-in_progress { color: #007bff; }
        .status-text.status-on_hold { color: #fd7e14; }
        .status-text.status-not_started { color: #6c757d; }

        .payment-amount-display {
            font-size: 1.2rem;
            font-weight: 600;
            color: #28a745;
            margin: 0.5rem 0;
        }

        .payment-status-paid { color: #28a745; font-weight: 600; }
        .payment-status-unpaid { color: #dc3545; font-weight: 600; }
        .payment-status-partial { color: #fd7e14; font-weight: 600; }

        .hold-reason {
            background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
            border: 1px solid #dc3545;
            border-radius: 6px;
            padding: 0.75rem;
            margin-top: 0.5rem;
            font-size: 0.85rem;
        }

        .files-section {
            background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 0.75rem;
            border: 1px solid #28a745;
        }

        .files-header {
            font-size: 0.9rem;
            color: #155724;
            margin-bottom: 0.75rem;
        }

        .files-actions {
            margin-bottom: 1rem;
        }

        .btn-download-small {
            background: #28a745;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .btn-download-small:hover {
            background: #218838;
            transform: translateY(-1px);
            color: white;
        }

        .files-note {
            background: #fff;
            border: 1px solid #d1ecf1;
            border-radius: 6px;
            padding: 0.75rem;
            font-size: 0.8rem;
            color: #0c5460;
        }

        .contact-buttons {
            margin-top: 0.5rem;
        }

        .btn-contact {
            background: #17a2b8;
            color: white;
            border: none;
            border-radius: 15px;
            padding: 0.3rem 0.6rem;
            font-size: 0.75rem;
            margin-right: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-contact:hover {
            background: #138496;
            transform: translateY(-1px);
        }

        .btn-receipt {
            background: #17a2b8;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
            font-weight: 500;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-receipt:hover {
            background: #138496;
            transform: translateY(-1px);
        }

        .btn-drive {
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-drive:hover {
            background: #3367d6;
            transform: translateY(-1px);
        }

        .payment-required {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 0.75rem;
            text-align: center;
        }

        .payment-info {
            margin-bottom: 1rem;
        }

        .payment-amount-required {
            font-size: 1.2rem;
            font-weight: 600;
            color: #856404;
            margin-top: 0.5rem;
        }

        .btn-pay {
            background: #28a745;
            color: white;
            border: none;
            border-radius: 25px;
            padding: 0.6rem 1.5rem;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        }

        .btn-pay:hover {
            background: #218838;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
        }

        .refresh-btn {
            position: fixed;
            bottom: 1.5rem;
            right: 1.5rem;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            border: none;
            box-shadow: 0 3px 10px rgba(0,123,255,0.3);
            font-size: 1rem;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .refresh-btn:hover {
            transform: scale(1.1);
            background: #0056b3;
        }

        .language-selector {
            position: fixed;
            bottom: 1rem;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            border-radius: 25px;
            padding: 0.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #dbdbdb;
            z-index: 999;
        }

        .language-options {
            display: flex;
            gap: 0.25rem;
        }

        .lang-btn {
            background: transparent;
            border: none;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #666;
        }

        .lang-btn:hover {
            background: #f0f0f0;
            color: #333;
        }

        .lang-btn.active {
            background: #007bff;
            color: white;
        }

        .footer {
            margin-top: 3rem;
            margin-bottom: 5rem; /* Space for language selector */
            padding: 2rem 1rem 1rem;
            text-align: center;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }

        .footer-links {
            margin-bottom: 1rem;
        }

        .footer-links a {
            color: #007bff;
            text-decoration: none;
            font-size: 0.85rem;
            margin: 0 0.5rem;
            transition: color 0.2s ease;
        }

        .footer-links a:hover {
            color: #0056b3;
            text-decoration: underline;
        }

        .separator {
            color: #6c757d;
            margin: 0 0.5rem;
        }

        .copyright {
            color: #6c757d;
            font-size: 0.8rem;
        }

        .payment-status-info {
            margin-top: 1rem;
            text-align: center;
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 2px solid #ffc107;
            border-radius: 12px;
            padding: 1rem;
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
        }

        .payment-status-info .payment-warning {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 1rem;
            font-weight: 600;
            color: #856404;
        }

        .payment-status-info .payment-amount {
            font-size: 1.3rem;
            font-weight: bold;
            color: #dc3545;
            margin-top: 0.5rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .payment-collected {
            margin-top: 0.5rem;
            text-align: center;
        }

        .files-list {
            margin: 1rem 0;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            transition: all 0.2s ease;
        }

        .file-item:hover {
            background: #e9ecef;
        }

        .file-item.locked {
            background: #fff3cd;
            border-color: #ffeaa7;
        }

        .file-info {
            flex: 1;
            display: flex;
            align-items: center;
        }

        .file-name {
            font-weight: 500;
            color: #333;
            margin-right: 0.5rem;
        }

        .file-size {
            color: #6c757d;
            font-size: 0.8rem;
        }

        .btn-download-file {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
            animation: pulse-download 2s infinite;
        }

        .btn-download-file::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn-download-file:hover::before {
            left: 100%;
        }

        .btn-download-file:hover {
            background: linear-gradient(45deg, #218838, #1e7e34);
            transform: scale(1.15) rotate(5deg);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.5);
            animation: none;
        }

        .btn-download-file.locked {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            color: #856404;
            animation: shake 1s infinite;
        }

        .btn-download-file.locked:hover {
            background: linear-gradient(45deg, #e0a800, #dc3545);
            transform: scale(1.1);
        }

        @keyframes pulse-download {
            0% { box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3); }
            50% { box-shadow: 0 4px 15px rgba(40, 167, 69, 0.6); }
            100% { box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-2px); }
            75% { transform: translateX(2px); }
        }

        .timestamp {
            color: #8e8e8e;
            font-size: 0.75rem;
            margin-top: 0.5rem;
        }

        /* Mobile Responsive */
        @media (max-width: 576px) {
            .project-title {
                font-size: 1.3rem;
            }

            .activity-item {
                padding: 0.75rem;
            }

            .activity-icon {
                width: 35px;
                height: 35px;
                font-size: 1rem;
            }

            .activity-title {
                font-size: 1rem;
            }

            .files-section {
                padding: 0.75rem;
            }

            .btn-download-small {
                padding: 0.5rem 1rem;
                font-size: 0.85rem;
            }

            .btn-contact {
                padding: 0.4rem 0.8rem;
                font-size: 0.8rem;
                margin-bottom: 0.5rem;
                display: block;
                width: 100%;
            }

            .contact-buttons {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
            }

            .payment-amount-display {
                font-size: 1.1rem;
            }

            .refresh-btn {
                bottom: 1rem;
                right: 1rem;
                width: 45px;
                height: 45px;
            }
        }
    </style>
</head>
<body>
    <div class="client-container">
        <!-- Project Header -->
        <div class="project-header">
            <div>
                <h1 class="project-title"><?= esc($project['project_name']) ?></h1>
                <div class="project-id">ID: <?= $project['project_id'] ?></div>
            </div>
        </div>





        <?php if ($stats['on_hold'] > 0 && !empty($stats['hold_reasons'])): ?>
        <!-- Hold Reasons Modal -->
        <div class="modal fade" id="holdReasonsModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-pause-circle text-warning me-2"></i>
                            Tasks On Hold
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <?php foreach ($stats['hold_reasons'] as $holdReason): ?>
                        <div class="hold-reason-item mb-3 p-3 border rounded">
                            <h6 class="mb-2">
                                <i class="fas fa-tasks me-2"></i>
                                <?= esc($holdReason['task_name']) ?>
                            </h6>
                            <p class="text-muted mb-2"><?= esc($holdReason['reason']) ?></p>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                Since: <?= date('M j, Y g:i A', strtotime($holdReason['date'])) ?>
                            </small>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>



        <!-- Activity Feed -->
        <div class="activity-feed">
            <?php if (empty($tasks)): ?>
            <div class="activity-item text-center">
                <i class="fas fa-info-circle fa-2x text-muted mb-3"></i>
                <h6 class="text-muted">No activity yet</h6>
                <p class="text-muted mb-0">Project activity will appear here as work progresses.</p>
            </div>
            <?php else: ?>
            <?php
            // Separate tasks and payments
            $taskActivities = [];
            $paymentActivities = [];

            foreach ($tasks as $task) {
                // Show all tasks including not started
                $taskActivities[] = $task;

                // Payment activities (separate section)
                if (!empty($task['payment_amount']) && $task['payment_amount'] > 0) {
                    $paymentActivities[] = $task;
                }
            }

            // Sort by most recent first
            usort($taskActivities, function($a, $b) {
                $timeA = strtotime($a['updated_at'] ?? $a['created_at'] ?? '0');
                $timeB = strtotime($b['updated_at'] ?? $b['created_at'] ?? '0');
                return $timeB - $timeA;
            });

            usort($paymentActivities, function($a, $b) {
                $timeA = strtotime($a['updated_at'] ?? '0');
                $timeB = strtotime($b['updated_at'] ?? '0');
                return $timeB - $timeA;
            });
            ?>

            <!-- All Tasks -->
            <?php if (!empty($taskActivities)): ?>
            <div class="activity-section">
                <div class="section-header" data-translate="project_tasks">Project Tasks</div>
                <?php foreach ($taskActivities as $task): ?>
                <div class="activity-item task-highlight" data-task-id="<?= $task['id'] ?>">
                    <div class="activity-header">
                        <div class="activity-icon <?= $task['status'] ?>">
                            <?php if ($task['status'] === 'completed'): ?>
                                <i class="fas fa-check"></i>
                            <?php elseif ($task['status'] === 'in_progress'): ?>
                                <i class="fas fa-play"></i>
                            <?php elseif ($task['status'] === 'on_hold'): ?>
                                <i class="fas fa-pause"></i>
                            <?php else: ?>
                                <i class="fas fa-circle"></i>
                            <?php endif; ?>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title"><?= esc($task['task_name']) ?></div>
                            <div class="activity-meta">
                                <span class="status-text status-<?= $task['status'] ?>">
                                    <?= ucwords(str_replace('_', ' ', $task['status'])) ?>
                                </span>
                                <?php if ($task['completed_date']): ?>
                                    • <?= date('M j, Y', strtotime($task['completed_date'])) ?>
                                <?php elseif ($task['due_date']): ?>
                                    • Due <?= date('M j, Y', strtotime($task['due_date'])) ?>
                                <?php endif; ?>
                            </div>

                            <?php if ($task['status'] === 'on_hold' && !empty($task['notes'])): ?>
                            <div class="hold-reason">
                                <strong>Hold Reason:</strong> <?= esc($task['notes']) ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>





                    <!-- Files Section for Completed Tasks -->
                    <?php if ($task['status'] === 'completed' && isset($task['task_manager_status']) && $task['task_manager_status'] === 'sent_for_review'): ?>
                    <?php
                        $hasFiles = !empty($task['files']) || !empty($task['google_drive_link']);
                    ?>
                    <?php if ($hasFiles): ?>
                    <div class="files-section">
                        <div class="files-header">
                            <strong>📁 <span data-translate="files_available">Project Files Available</span></strong>
                            <?php if (!empty($task['payment_total_collected'])): ?>
                            <div class="payment-collected">
                                <small class="text-success">
                                    <i class="fas fa-check-circle me-1"></i>
                                    <span data-translate="payment_collected">Payment Collected</span>: ₹<?= number_format($task['payment_total_collected'], 2) ?>
                                </small>
                            </div>
                            <?php endif; ?>
                        </div>

                        <?php if (isset($task['payment_status']) && $task['payment_status'] === 'paid'): ?>
                        <!-- Files accessible - payment completed -->
                        <div class="files-list">
                            <?php if (!empty($task['files'])): ?>
                                <?php foreach ($task['files'] as $file): ?>
                                <div class="file-item">
                                    <div class="file-info">
                                        <i class="fas fa-file me-2"></i>
                                        <span class="file-name"><?= esc($file['filename']) ?></span>
                                        <small class="file-size">(<?= formatFileSize($file['file_size']) ?>)</small>
                                    </div>
                                    <button class="btn-download-file" onclick="downloadFile(<?= $task['id'] ?>, <?= $file['id'] ?>, '<?= esc($file['filename']) ?>')">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                                <?php endforeach; ?>
                            <?php endif; ?>

                            <?php if (!empty($task['google_drive_link'])): ?>
                            <div class="file-item">
                                <div class="file-info">
                                    <i class="fab fa-google-drive me-2"></i>
                                    <span class="file-name" data-translate="google_drive_files">Google Drive Files</span>
                                </div>
                                <button class="btn-download-file" onclick="window.open('<?= esc($task['google_drive_link']) ?>', '_blank')">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="files-note">
                            <strong data-translate="need_revisions">Need revisions or have questions?</strong>
                            <div class="contact-buttons">
                                <button class="btn-contact" onclick="window.open('tel:+919876543210')">
                                    <i class="fas fa-phone"></i> <span data-translate="call">Call</span>
                                </button>
                                <button class="btn-contact" onclick="window.open('https://wa.me/919876543210')">
                                    <i class="fab fa-whatsapp"></i> <span data-translate="whatsapp">WhatsApp</span>
                                </button>
                            </div>
                        </div>

                        <?php else: ?>
                        <!-- Files available but payment required -->
                        <div class="files-list">
                            <?php if (!empty($task['files'])): ?>
                                <?php foreach ($task['files'] as $file): ?>
                                <div class="file-item locked">
                                    <div class="file-info">
                                        <i class="fas fa-file me-2"></i>
                                        <span class="file-name"><?= esc($file['filename']) ?></span>
                                        <small class="file-size">(<?= formatFileSize($file['file_size']) ?>)</small>
                                    </div>
                                    <button class="btn-download-file locked" onclick="showPaymentAlert(<?= $task['id'] ?>, <?= $task['payment_amount'] ?>, '<?= esc($task['task_name']) ?>')">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>

                        <div class="payment-status-info">
                            <div class="payment-warning">
                                <i class="fas fa-lock"></i>
                                <span data-translate="payment_required">Payment required</span>
                            </div>
                            <div class="payment-amount">₹<?= number_format($task['payment_amount'], 2) ?></div>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

            <!-- Invoice Section -->
            <div class="activity-section">
                <div class="section-header" data-translate="invoice">Invoice</div>
                <div class="activity-item">
                    <div class="activity-header">
                        <div class="activity-icon invoice">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title" data-translate="invoice_status">Invoice Status</div>
                            <div class="activity-meta">
                                <span class="text-muted" data-translate="invoice_preparation">Will be prepared after each task completion</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Activities -->
            <?php if (!empty($paymentActivities)): ?>
            <div class="activity-section">
                <div class="section-header" data-translate="payments">Payments</div>
                <?php foreach ($paymentActivities as $task): ?>
                <div class="activity-item payment-highlight">
                    <div class="activity-header">
                        <div class="activity-icon payment">
                            <i class="fas fa-rupee-sign"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">Payment</div>
                            <div class="activity-meta">
                                <span class="payment-status-<?= $task['payment_status'] ?>">
                                    <?= ucwords($task['payment_status']) ?>
                                </span>
                                • ₹<?= number_format($task['payment_amount'], 2) ?>
                            </div>
                            <?php if ($task['payment_status'] === 'paid'): ?>
                            <div class="payment-actions">
                                <button class="btn-receipt" onclick="downloadPaymentReceipt(<?= $task['id'] ?>)">
                                    <i class="fas fa-receipt me-1"></i> <span data-translate="download_receipt">Download Receipt</span>
                                </button>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Refresh Button -->
    <button class="refresh-btn" onclick="refreshData()">
        <i class="fas fa-sync-alt"></i>
    </button>

    <!-- Language Selection -->
    <div class="language-selector">
        <div class="language-options">
            <button class="lang-btn active" onclick="changeLanguage('en')" data-lang="en">
                English
            </button>
            <button class="lang-btn" onclick="changeLanguage('ml')" data-lang="ml">
                മലയാളം
            </button>
            <button class="lang-btn" onclick="changeLanguage('kn')" data-lang="kn">
                ಕನ್ನಡ
            </button>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <div class="footer-links">
            <a href="#" onclick="showTermsAndConditions()" data-translate="terms_conditions">Terms & Conditions</a>
            <span class="separator">•</span>
            <a href="#" onclick="showPrivacyPolicy()" data-translate="privacy_policy">Privacy Policy</a>
            <span class="separator">•</span>
            <a href="#" onclick="showRefundPolicy()" data-translate="refund_policy">Refund Policy</a>
        </div>
        <div class="copyright">
            <span data-translate="copyright">© 2025 SmartFlo. All rights reserved.</span>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Get access token from URL
        const accessToken = window.location.pathname.split('/').pop();

        // Removed auto-refresh to prevent page disturbance
        // Users can manually refresh using the refresh button

        // Load project data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadProjectData();
        });

        // Load project data via AJAX
        function loadProjectData() {
            fetch(`/client/api/project/${accessToken}?_t=${Date.now()}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache'
                },
                cache: 'no-store'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateProjectData(data);
                    updateLastUpdatedTime();
                } else {
                    console.error('Failed to load project data:', data.message);
                }
            })
            .catch(error => {
                console.error('Error loading project data:', error);
            });
        }

        // Update project data on the page
        function updateProjectData(data) {
            const project = data.project;
            const tasks = data.tasks;
            const stats = data.stats;

            // Update project progress
            const progressBar = document.querySelector('.progress-bar');
            const progressText = document.querySelector('.progress-text');
            if (progressBar && progressText) {
                progressBar.style.width = project.progress_percentage + '%';
                progressText.textContent = Math.round(project.progress_percentage) + '%';
            }



            // Update task list
            updateTaskList(tasks);
        }

        // Update task list
        function updateTaskList(tasks) {
            if (!tasks || tasks.length === 0) return;

            // Check if any task has new files or status changes
            let needsReload = false;

            tasks.forEach(task => {
                const taskCard = document.querySelector(`[data-task-id="${task.id}"]`);
                if (taskCard) {
                    // Check if file status changed
                    const hasFiles = task.file_path || task.google_drive_link;
                    const currentDownloadSection = taskCard.querySelector('.mt-3.pt-3.border-top');

                    if (hasFiles && !currentDownloadSection) {
                        // Files were added, need reload
                        needsReload = true;
                    } else if (!hasFiles && currentDownloadSection) {
                        // Files were removed, need reload
                        needsReload = true;
                    }

                    // Check if payment status changed
                    const paymentStatusEl = taskCard.querySelector('.alert-warning, .btn-success');
                    if (paymentStatusEl && task.payment_status === 'paid' && paymentStatusEl.classList.contains('alert-warning')) {
                        needsReload = true;
                    }
                }
            });

            // Removed auto-reload to prevent page disturbance
            // if (needsReload) {
            //     setTimeout(() => location.reload(), 1000);
            // }
        }

        // Update last updated time
        function updateLastUpdatedTime() {
            const lastUpdatedEl = document.getElementById('lastUpdated');
            if (lastUpdatedEl) {
                lastUpdatedEl.textContent = new Date().toLocaleString();
            }
        }

        // Manual refresh function
        function refreshData() {
            const refreshBtn = document.querySelector('.refresh-btn');
            const originalIcon = refreshBtn.innerHTML;

            // Show loading state
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            refreshBtn.disabled = true;

            // Load fresh data
            loadProjectData();

            // Restore button after 2 seconds
            setTimeout(() => {
                refreshBtn.innerHTML = originalIcon;
                refreshBtn.disabled = false;
            }, 2000);
        }

        // Show hold reasons modal
        function showHoldReasons() {
            const modal = new bootstrap.Modal(document.getElementById('holdReasonsModal'));
            modal.show();
        }

        // Initiate Razorpay payment
        function initiatePayment(taskId, amount, taskName) {
            const options = {
                "key": "rzp_test_1234567890", // Replace with your Razorpay key
                "amount": amount * 100, // Amount in paise
                "currency": "INR",
                "name": "SmartFlo",
                "description": `Payment for ${taskName}`,
                "image": "/assets/logo.png", // Your logo
                "order_id": "", // Will be generated from backend
                "handler": function (response) {
                    // Payment successful
                    verifyPayment(response, taskId);
                },
                "prefill": {
                    "name": "<?= esc($project['client_name'] ?? '') ?>",
                    "email": "",
                    "contact": ""
                },
                "notes": {
                    "task_id": taskId,
                    "project_id": "<?= $project['project_id'] ?>"
                },
                "theme": {
                    "color": "#28a745"
                },
                "modal": {
                    "ondismiss": function() {
                        showAlert('info', 'Payment cancelled');
                    }
                }
            };

            // Create Razorpay order first
            fetch('/client/createPaymentOrder', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    task_id: taskId,
                    amount: amount
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    options.order_id = data.order_id;
                    const rzp = new Razorpay(options);
                    rzp.open();
                } else {
                    showAlert('error', 'Failed to create payment order');
                }
            })
            .catch(error => {
                console.error('Payment error:', error);
                showAlert('error', 'Payment initialization failed');
            });
        }

        // Verify payment after successful transaction
        function verifyPayment(response, taskId) {
            fetch('/client/verifyPayment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    razorpay_payment_id: response.razorpay_payment_id,
                    razorpay_order_id: response.razorpay_order_id,
                    razorpay_signature: response.razorpay_signature,
                    task_id: taskId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', 'Payment successful! Files are now available for download.');
                    // Refresh the page to show updated content
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    showAlert('error', 'Payment verification failed');
                }
            })
            .catch(error => {
                console.error('Verification error:', error);
                showAlert('error', 'Payment verification failed');
            });
        }

        // Handle download with payment check
        function handleDownload(taskId, paymentStatus, amount, taskName, googleDriveLink = null) {
            if (paymentStatus === 'paid') {
                // Payment completed - proceed with download
                if (googleDriveLink) {
                    window.open(googleDriveLink, '_blank');
                } else {
                    downloadTaskFiles(taskId);
                }
            } else {
                // Payment required - show SweetAlert
                Swal.fire({
                    title: 'Payment Required',
                    html: `
                        <div style="text-align: center;">
                            <i class="fas fa-lock" style="font-size: 3rem; color: #ffc107; margin-bottom: 1rem;"></i>
                            <h4>Complete payment to download files</h4>
                            <div style="font-size: 1.2rem; font-weight: bold; color: #28a745; margin: 1rem 0;">
                                ₹${amount.toFixed(2)}
                            </div>
                            <p style="color: #666;">Task: ${taskName}</p>
                        </div>
                    `,
                    showCancelButton: true,
                    confirmButtonText: '<i class="fas fa-credit-card"></i> Pay Now',
                    cancelButtonText: 'Cancel',
                    confirmButtonColor: '#28a745',
                    cancelButtonColor: '#6c757d',
                    customClass: {
                        popup: 'payment-popup'
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        initiatePayment(taskId, amount, taskName);
                    }
                });
            }
        }

        // Change language
        function changeLanguage(lang) {
            // Update active button
            document.querySelectorAll('.lang-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-lang="${lang}"]`).classList.add('active');

            // Store language preference
            localStorage.setItem('selectedLanguage', lang);

            // Apply translations
            applyTranslations(lang);
        }

        // Apply translations based on selected language
        function applyTranslations(lang) {
            const translations = {
                'en': {
                    'project_tasks': 'Project Tasks',
                    'payments': 'Payments',
                    'completed': 'Completed',
                    'in_progress': 'In Progress',
                    'on_hold': 'On Hold',
                    'not_started': 'Not Started',
                    'download': 'Download',
                    'view_files': 'View Files',
                    'need_revisions': 'Need revisions or have questions?',
                    'call': 'Call',
                    'whatsapp': 'WhatsApp',
                    'payment_required': 'Payment required',
                    'download_receipt': 'Download Receipt',
                    'files_available': 'Project Files Available',
                    'payment_collected': 'Payment Collected',
                    'google_drive_files': 'Google Drive Files',
                    'terms_conditions': 'Terms & Conditions',
                    'privacy_policy': 'Privacy Policy',
                    'refund_policy': 'Refund Policy',
                    'copyright': '© 2025 SmartFlo. All rights reserved.',
                    'invoice': 'Invoice',
                    'invoice_status': 'Invoice Status',
                    'invoice_preparation': 'Will be prepared after each task completion'
                },
                'ml': {
                    'project_tasks': 'പ്രോജക്റ്റ് ടാസ്കുകൾ',
                    'payments': 'പേയ്മെന്റുകൾ',
                    'completed': 'പൂർത്തിയായി',
                    'in_progress': 'പുരോഗതിയിൽ',
                    'on_hold': 'നിർത്തിവച്ചു',
                    'not_started': 'ആരംഭിച്ചിട്ടില്ല',
                    'download': 'ഡൗൺലോഡ്',
                    'view_files': 'ഫയലുകൾ കാണുക',
                    'need_revisions': 'മാറ്റങ്ങൾ വേണോ അല്ലെങ്കിൽ ചോദ്യങ്ങൾ ഉണ്ടോ?',
                    'call': 'വിളിക്കുക',
                    'whatsapp': 'വാട്സ്ആപ്പ്',
                    'payment_required': 'പേയ്മെന്റ് ആവശ്യം',
                    'download_receipt': 'രസീത് ഡൗൺലോഡ്',
                    'files_available': 'പ്രോജക്റ്റ് ഫയലുകൾ ലഭ്യം',
                    'payment_collected': 'പേയ്മെന്റ് ശേഖരിച്ചു',
                    'google_drive_files': 'ഗൂഗിൾ ഡ്രൈവ് ഫയലുകൾ',
                    'terms_conditions': 'നിബന്ധനകളും വ്യവസ്ഥകളും',
                    'privacy_policy': 'സ്വകാര്യതാ നയം',
                    'refund_policy': 'റീഫണ്ട് നയം',
                    'copyright': '© 2025 സ്മാർട്ട്ഫ്ലോ. എല്ലാ അവകാശങ്ങളും സംരക്ഷിതം.',
                    'invoice': 'ഇൻവോയ്സ്',
                    'invoice_status': 'ഇൻവോയ്സ് സ്റ്റാറ്റസ്',
                    'invoice_preparation': 'ഓരോ ടാസ്ക് പൂർത്തിയാക്കിയ ശേഷം തയ്യാറാക്കും'
                },
                'kn': {
                    'project_tasks': 'ಪ್ರಾಜೆಕ್ಟ್ ಕಾರ್ಯಗಳು',
                    'payments': 'ಪಾವತಿಗಳು',
                    'completed': 'ಪೂರ್ಣಗೊಂಡಿದೆ',
                    'in_progress': 'ಪ್ರಗತಿಯಲ್ಲಿದೆ',
                    'on_hold': 'ತಡೆಹಿಡಿಯಲಾಗಿದೆ',
                    'not_started': 'ಪ್ರಾರಂಭಿಸಿಲ್ಲ',
                    'download': 'ಡೌನ್‌ಲೋಡ್',
                    'view_files': 'ಫೈಲ್‌ಗಳನ್ನು ವೀಕ್ಷಿಸಿ',
                    'need_revisions': 'ಪರಿಷ್ಕರಣೆಗಳು ಬೇಕೇ ಅಥವಾ ಪ್ರಶ್ನೆಗಳಿವೆಯೇ?',
                    'call': 'ಕರೆ ಮಾಡಿ',
                    'whatsapp': 'ವಾಟ್ಸ್‌ಆಪ್',
                    'payment_required': 'ಪಾವತಿ ಅಗತ್ಯ',
                    'download_receipt': 'ರಸೀದಿ ಡೌನ್‌ಲೋಡ್',
                    'files_available': 'ಪ್ರಾಜೆಕ್ಟ್ ಫೈಲ್‌ಗಳು ಲಭ್ಯ',
                    'payment_collected': 'ಪಾವತಿ ಸಂಗ್ರಹಿಸಲಾಗಿದೆ',
                    'google_drive_files': 'ಗೂಗಲ್ ಡ್ರೈವ್ ಫೈಲ್‌ಗಳು',
                    'terms_conditions': 'ನಿಯಮಗಳು ಮತ್ತು ಷರತ್ತುಗಳು',
                    'privacy_policy': 'ಗೌಪ್ಯತಾ ನೀತಿ',
                    'refund_policy': 'ಮರುಪಾವತಿ ನೀತಿ',
                    'copyright': '© 2025 ಸ್ಮಾರ್ಟ್‌ಫ್ಲೋ. ಎಲ್ಲಾ ಹಕ್ಕುಗಳನ್ನು ಕಾಯ್ದಿರಿಸಲಾಗಿದೆ.',
                    'invoice': 'ಇನ್‌ವಾಯ್ಸ್',
                    'invoice_status': 'ಇನ್‌ವಾಯ್ಸ್ ಸ್ಥಿತಿ',
                    'invoice_preparation': 'ಪ್ರತಿ ಕಾರ್ಯ ಪೂರ್ಣಗೊಂಡ ನಂತರ ತಯಾರಿಸಲಾಗುವುದು'
                }
            };

            // Apply translations to elements with data-translate attributes
            document.querySelectorAll('[data-translate]').forEach(element => {
                const key = element.getAttribute('data-translate');
                if (translations[lang] && translations[lang][key]) {
                    element.textContent = translations[lang][key];
                }
            });
        }

        // Load saved language on page load
        document.addEventListener('DOMContentLoaded', function() {
            const savedLang = localStorage.getItem('selectedLanguage') || 'en';
            changeLanguage(savedLang);
        });

        // Show Terms and Conditions
        function showTermsAndConditions() {
            Swal.fire({
                title: 'Terms & Conditions',
                html: `
                    <div style="text-align: left; max-height: 400px; overflow-y: auto;">
                        <h5>Payment Terms</h5>
                        <p>• All payments must be completed before file downloads are enabled.</p>
                        <p>• Payments are processed securely through Razorpay.</p>
                        <p>• Refunds are subject to our refund policy.</p>

                        <h5>File Access</h5>
                        <p>• Project files are available for download only after payment completion.</p>
                        <p>• Files remain accessible for 90 days after project completion.</p>
                        <p>• Backup copies may be requested for an additional fee.</p>

                        <h5>Service Terms</h5>
                        <p>• All project work is subject to the agreed scope and timeline.</p>
                        <p>• Revisions may incur additional charges beyond the agreed scope.</p>
                        <p>• Client approval is required for major design changes.</p>

                        <h5>Contact</h5>
                        <p>For any questions, please contact our office via phone or WhatsApp.</p>
                    </div>
                `,
                width: '600px',
                confirmButtonText: 'I Understand',
                confirmButtonColor: '#007bff'
            });
        }

        // Show Privacy Policy
        function showPrivacyPolicy() {
            Swal.fire({
                title: 'Privacy Policy',
                html: `
                    <div style="text-align: left; max-height: 400px; overflow-y: auto;">
                        <h5>Information Collection</h5>
                        <p>• We collect only necessary project and contact information.</p>
                        <p>• Payment information is processed securely through Razorpay.</p>
                        <p>• Project files are stored securely and accessed only by authorized personnel.</p>

                        <h5>Data Usage</h5>
                        <p>• Your information is used solely for project delivery and communication.</p>
                        <p>• We do not share your data with third parties without consent.</p>
                        <p>• Project files may be retained for backup and support purposes.</p>

                        <h5>Data Security</h5>
                        <p>• All data is stored on secure servers with encryption.</p>
                        <p>• Access is restricted to authorized team members only.</p>
                        <p>• Regular security audits ensure data protection.</p>
                    </div>
                `,
                width: '600px',
                confirmButtonText: 'I Understand',
                confirmButtonColor: '#007bff'
            });
        }

        // Show Refund Policy
        function showRefundPolicy() {
            Swal.fire({
                title: 'Refund Policy',
                html: `
                    <div style="text-align: left; max-height: 400px; overflow-y: auto;">
                        <h5>Refund Eligibility</h5>
                        <p><strong>• NO refunds due to digital files being delivered.</strong></p>
                        <p>• Once project files are downloaded, no refunds will be processed.</p>
                        <p>• Digital deliverables cannot be returned once accessed.</p>

                        <h5>Limited Refund Scenarios</h5>
                        <p>• Technical issues preventing file access (within 24 hours).</p>
                        <p>• Duplicate payments made in error.</p>
                        <p>• Service not delivered due to our technical failure.</p>

                        <h5>Refund Process</h5>
                        <p>• Refund requests must be submitted within 24 hours of payment.</p>
                        <p>• Valid proof of technical issue required.</p>
                        <p>• Processing time is 7-14 business days after approval.</p>
                        <p>• Refunds processed through original payment method only.</p>

                        <h5>Non-Refundable Items</h5>
                        <p>• Digital files that have been successfully downloaded.</p>
                        <p>• Work completed and files delivered to client.</p>
                        <p>• Payment processing fees.</p>
                        <p>• Third-party service charges.</p>

                        <h5>Contact for Refunds</h5>
                        <p>Contact our office immediately for technical issues only.</p>
                        <p><strong>Note:</strong> This policy complies with digital goods regulations.</p>
                    </div>
                `,
                width: '600px',
                confirmButtonText: 'I Understand',
                confirmButtonColor: '#007bff'
            });
        }

        // Download individual file
        function downloadFile(taskId, fileId, filename) {
            const btn = event.target.closest('.btn-download-file');
            const originalContent = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            btn.disabled = true;

            fetch(`/client/downloadTaskFiles/${taskId}/${fileId}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                } else {
                    throw new Error('Download failed');
                }
            })
            .then(blob => {
                // Create download link
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                showAlert('success', `${filename} downloaded successfully!`);
            })
            .catch(error => {
                console.error('Download error:', error);
                showAlert('error', 'Failed to download file. Please try again.');
            })
            .finally(() => {
                btn.innerHTML = originalContent;
                btn.disabled = false;
            });
        }

        // Show payment alert for locked files
        function showPaymentAlert(taskId, amount, taskName) {
            Swal.fire({
                title: 'Payment Required',
                html: `
                    <div style="text-align: center;">
                        <i class="fas fa-lock" style="font-size: 3rem; color: #ffc107; margin-bottom: 1rem;"></i>
                        <h4>Complete payment to download files</h4>
                        <div style="font-size: 1.2rem; font-weight: bold; color: #28a745; margin: 1rem 0;">
                            ₹${amount.toFixed(2)}
                        </div>
                        <p style="color: #666;">Task: ${taskName}</p>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-credit-card"></i> Pay Now',
                cancelButtonText: 'Cancel',
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d'
            }).then((result) => {
                if (result.isConfirmed) {
                    initiatePayment(taskId, amount, taskName);
                }
            });
        }

        // Download task files (legacy function for compatibility)
        function downloadTaskFiles(taskId) {
            // Show loading state
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Downloading...';
            btn.disabled = true;

            fetch(`/client/downloadTaskFiles/${taskId}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                } else {
                    throw new Error('Download failed');
                }
            })
            .then(blob => {
                // Create download link
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `task-${taskId}-files.zip`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                // Show success message
                showAlert('success', 'Files downloaded successfully!');
            })
            .catch(error => {
                console.error('Download error:', error);
                showAlert('error', 'Failed to download files. Please try again.');
            })
            .finally(() => {
                // Restore button
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }

        // Download payment receipt
        function downloadPaymentReceipt(taskId) {
            // Show loading state
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Generating...';
            btn.disabled = true;

            fetch(`/client/downloadPaymentReceipt/${taskId}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                } else {
                    throw new Error('Receipt generation failed');
                }
            })
            .then(blob => {
                // Create download link
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `payment-receipt-${taskId}.pdf`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                // Show success message
                showAlert('success', 'Payment receipt downloaded successfully!');
            })
            .catch(error => {
                console.error('Receipt download error:', error);
                showAlert('error', 'Failed to generate receipt. Please try again.');
            })
            .finally(() => {
                // Restore button
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }

        // Simple alert function
        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }
    </script>
</body>
</html>
