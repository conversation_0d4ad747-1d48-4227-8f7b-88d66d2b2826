<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: #f8f9fa;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #333;
        }

        .client-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 1rem;
        }

        @media (min-width: 768px) {
            .client-container {
                padding: 1.5rem;
            }
        }

        .simple-header {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-left: 4px solid #0d6efd;
        }

        .project-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }

        .project-id {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
            margin-top: 0.25rem;
        }

        .last-updated {
            color: #6c757d;
            font-size: 0.85rem;
        }

        .task-card {
            background: white;
            border-radius: 10px;
            padding: 1.25rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border-left: 4px solid #e9ecef;
            transition: all 0.2s ease;
        }

        .task-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }

        .task-card.task-completed {
            border-left-color: #28a745;
            background: linear-gradient(135deg, #ffffff 0%, #f8fff9 100%);
        }
        .task-card.task-in_progress {
            border-left-color: #007bff;
            background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
        }
        .task-card.task-on_hold {
            border-left-color: #fd7e14;
            background: linear-gradient(135deg, #ffffff 0%, #fffaf8 100%);
        }
        .task-card.task-not_started {
            border-left-color: #6c757d;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .status-badge {
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-not_started { background: #6c757d; color: white; }
        .status-in_progress { background: #007bff; color: white; }
        .status-on_hold { background: #fd7e14; color: white; }
        .status-completed { background: #28a745; color: white; }

        .refresh-btn {
            position: fixed;
            bottom: 1.5rem;
            right: 1.5rem;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            border: none;
            box-shadow: 0 3px 10px rgba(0,123,255,0.3);
            font-size: 1rem;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .refresh-btn:hover {
            transform: scale(1.1);
            background: #0056b3;
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }

        .task-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .task-meta {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 0.75rem;
        }

        .task-details {
            font-size: 0.85rem;
        }

        .project-files-section {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .alert-warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffeaa7;
            border-radius: 8px;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 6px;
            font-weight: 500;
        }

        .btn-outline-primary {
            border-radius: 6px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="client-container">
        <!-- Simple Header -->
        <div class="simple-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="project-title"><?= esc($project['project_name']) ?></h1>
                    <div class="project-id">ID: <?= $project['project_id'] ?></div>
                </div>
                <div class="last-updated">
                    <i class="fas fa-clock me-1"></i>
                    <span id="lastUpdated"><?= date('M j, Y g:i A') ?></span>
                </div>
            </div>
        </div>





        <?php if ($stats['on_hold'] > 0 && !empty($stats['hold_reasons'])): ?>
        <!-- Hold Reasons Modal -->
        <div class="modal fade" id="holdReasonsModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-pause-circle text-warning me-2"></i>
                            Tasks On Hold
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <?php foreach ($stats['hold_reasons'] as $holdReason): ?>
                        <div class="hold-reason-item mb-3 p-3 border rounded">
                            <h6 class="mb-2">
                                <i class="fas fa-tasks me-2"></i>
                                <?= esc($holdReason['task_name']) ?>
                            </h6>
                            <p class="text-muted mb-2"><?= esc($holdReason['reason']) ?></p>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                Since: <?= date('M j, Y g:i A', strtotime($holdReason['date'])) ?>
                            </small>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>



        <!-- Tasks -->
        <div class="mt-4">
            <h5 class="mb-3" style="color: #2c3e50; font-weight: 600;">
                <i class="fas fa-tasks me-2"></i>
                Tasks
            </h5>
            
            <?php if (empty($tasks)): ?>
            <div class="task-card text-center">
                <i class="fas fa-info-circle fa-2x text-muted mb-3"></i>
                <h6 class="text-muted">No tasks available</h6>
                <p class="text-muted mb-0">Task information will be updated as the project progresses.</p>
            </div>
            <?php else: ?>
            <?php foreach ($tasks as $task): ?>
            <div class="task-card task-<?= $task['status'] ?>" data-task-id="<?= $task['id'] ?>">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h6 class="task-title"><?= esc($task['task_name']) ?></h6>
                        <div class="task-meta">
                            <i class="fas fa-tag me-1"></i>
                            <?= esc($task['task_type_name']) ?>
                        </div>
                    </div>
                    <span class="status-badge status-<?= $task['status'] ?>"><?= ucwords(str_replace('_', ' ', $task['status'])) ?></span>
                </div>

                <?php if ($task['description']): ?>
                <p class="task-meta mb-2"><?= esc($task['description']) ?></p>
                <?php endif; ?>

                <div class="task-details">
                    <?php if ($task['completed_date']): ?>
                    <small class="text-success">
                        <i class="fas fa-check me-1"></i>
                        Completed: <?= date('M j, Y', strtotime($task['completed_date'])) ?>
                    </small>
                    <?php elseif ($task['due_date']): ?>
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        Due: <?= date('M j, Y', strtotime($task['due_date'])) ?>
                    </small>
                    <?php endif; ?>
                </div>





                <?php if ($task['status'] === 'completed' && isset($task['task_manager_status']) && $task['task_manager_status'] === 'sent_for_review'): ?>
                <?php
                    // Check if task has files or Google Drive link
                    $hasFiles = !empty($task['google_drive_link']) || !empty($task['file_path']);
                ?>
                <?php if ($hasFiles): ?>
                <div class="project-files-section">
                    <h6 class="mb-3" style="color: #2c3e50; font-weight: 600;">
                        <i class="fas fa-download me-2"></i>
                        Project Files
                    </h6>
                    <?php if (isset($task['payment_status']) && $task['payment_status'] === 'paid'): ?>
                        <div class="d-grid gap-2">
                            <?php if (!empty($task['file_path'])): ?>
                            <button class="btn btn-success btn-sm" onclick="downloadTaskFiles(<?= $task['id'] ?>)">
                                <i class="fas fa-download me-2"></i>
                                Download Files
                            </button>
                            <?php endif; ?>
                            <?php if (!empty($task['google_drive_link'])): ?>
                            <a href="<?= esc($task['google_drive_link']) ?>" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fab fa-google-drive me-2"></i>
                                View on Google Drive
                            </a>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-lock me-2"></i>
                            <strong>Payment Required</strong><br>
                            Please complete payment to download project files.
                            <?php if (isset($task['payment_amount']) && $task['payment_amount'] > 0): ?>
                                <br><small>Amount: ₹<?= number_format($task['payment_amount'], 2) ?></small>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                <?php endif; ?>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Refresh Button -->
    <button class="refresh-btn" onclick="refreshData()">
        <i class="fas fa-sync-alt"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Get access token from URL
        const accessToken = window.location.pathname.split('/').pop();

        // Auto-refresh every 30 seconds with AJAX
        setInterval(function() {
            loadProjectData();
        }, 30000);

        // Load project data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadProjectData();
        });

        // Load project data via AJAX
        function loadProjectData() {
            fetch(`/client/api/project/${accessToken}?_t=${Date.now()}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache'
                },
                cache: 'no-store'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateProjectData(data);
                    updateLastUpdatedTime();
                } else {
                    console.error('Failed to load project data:', data.message);
                }
            })
            .catch(error => {
                console.error('Error loading project data:', error);
            });
        }

        // Update project data on the page
        function updateProjectData(data) {
            const project = data.project;
            const tasks = data.tasks;
            const stats = data.stats;

            // Update project progress
            const progressBar = document.querySelector('.progress-bar');
            const progressText = document.querySelector('.progress-text');
            if (progressBar && progressText) {
                progressBar.style.width = project.progress_percentage + '%';
                progressText.textContent = Math.round(project.progress_percentage) + '%';
            }



            // Update task list
            updateTaskList(tasks);
        }

        // Update task list
        function updateTaskList(tasks) {
            if (!tasks || tasks.length === 0) return;

            // Check if any task has new files or status changes
            let needsReload = false;

            tasks.forEach(task => {
                const taskCard = document.querySelector(`[data-task-id="${task.id}"]`);
                if (taskCard) {
                    // Check if file status changed
                    const hasFiles = task.file_path || task.google_drive_link;
                    const currentDownloadSection = taskCard.querySelector('.mt-3.pt-3.border-top');

                    if (hasFiles && !currentDownloadSection) {
                        // Files were added, need reload
                        needsReload = true;
                    } else if (!hasFiles && currentDownloadSection) {
                        // Files were removed, need reload
                        needsReload = true;
                    }

                    // Check if payment status changed
                    const paymentStatusEl = taskCard.querySelector('.alert-warning, .btn-success');
                    if (paymentStatusEl && task.payment_status === 'paid' && paymentStatusEl.classList.contains('alert-warning')) {
                        needsReload = true;
                    }
                }
            });

            if (needsReload) {
                setTimeout(() => location.reload(), 1000);
            }
        }

        // Update last updated time
        function updateLastUpdatedTime() {
            const lastUpdatedEl = document.getElementById('lastUpdated');
            if (lastUpdatedEl) {
                lastUpdatedEl.textContent = new Date().toLocaleString();
            }
        }

        // Manual refresh function
        function refreshData() {
            const refreshBtn = document.querySelector('.refresh-btn');
            const originalIcon = refreshBtn.innerHTML;

            // Show loading state
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            refreshBtn.disabled = true;

            // Load fresh data
            loadProjectData();

            // Restore button after 2 seconds
            setTimeout(() => {
                refreshBtn.innerHTML = originalIcon;
                refreshBtn.disabled = false;
            }, 2000);
        }

        // Show hold reasons modal
        function showHoldReasons() {
            const modal = new bootstrap.Modal(document.getElementById('holdReasonsModal'));
            modal.show();
        }

        // Download task files
        function downloadTaskFiles(taskId) {
            // Show loading state
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Downloading...';
            btn.disabled = true;

            fetch(`/client/downloadTaskFiles/${taskId}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                } else {
                    throw new Error('Download failed');
                }
            })
            .then(blob => {
                // Create download link
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `task-${taskId}-files.zip`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                // Show success message
                showAlert('success', 'Files downloaded successfully!');
            })
            .catch(error => {
                console.error('Download error:', error);
                showAlert('error', 'Failed to download files. Please try again.');
            })
            .finally(() => {
                // Restore button
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }

        // Simple alert function
        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }
    </script>
</body>
</html>
