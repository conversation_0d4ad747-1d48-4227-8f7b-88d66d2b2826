<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-building me-2"></i>
                    Project Status Dashboard
                </h1>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createProjectModal">
                    <i class="fas fa-plus me-2"></i>
                    New Project
                </button>
            </div>

            <!-- Current Status Card -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                Current Site Status
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="currentStatus">
                                <div class="text-center py-4">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                Quick Stats
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="stat-item mb-3">
                                <div class="stat-label">Uptime (30 days)</div>
                                <div class="stat-value text-success">99.9%</div>
                            </div>
                            <div class="stat-item mb-3">
                                <div class="stat-label">Last Update</div>
                                <div class="stat-value" id="lastUpdate">-</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">Status Changes</div>
                                <div class="stat-value" id="statusChanges">-</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status History -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>
                        Status History
                    </h5>
                </div>
                <div class="card-body">
                    <div id="statusHistory">
                        <div class="text-center py-4">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1" aria-labelledby="updateStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateStatusModalLabel">Update Site Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="updateStatusForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="statusSelect" class="form-label">Status</label>
                        <select class="form-select" id="statusSelect" name="status" required>
                            <option value="online">Online</option>
                            <option value="maintenance">Maintenance</option>
                            <option value="construction">Under Construction</option>
                            <option value="offline">Offline</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="statusMessage" class="form-label">Message (Optional)</label>
                        <textarea class="form-control" id="statusMessage" name="message" rows="3" placeholder="Enter a message about the current status..."></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="estimatedCompletion" class="form-label">Estimated Completion (Optional)</label>
                        <input type="datetime-local" class="form-control" id="estimatedCompletion" name="estimated_completion">
                    </div>
                    <input type="hidden" name="csrf_token" value="<?= csrf_hash() ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="updateStatusBtn">
                        <i class="fas fa-save me-2"></i>
                        Update Status
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
}

.status-online {
    background-color: #d1fae5;
    color: #065f46;
}

.status-maintenance {
    background-color: #fef3c7;
    color: #92400e;
}

.status-construction {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-offline {
    background-color: #fee2e2;
    color: #991b1b;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    font-size: 0.875rem;
    color: #6b7280;
}

.stat-value {
    font-weight: 600;
    color: #111827;
}

.history-item {
    border-left: 4px solid #e5e7eb;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
}

.history-item.online {
    border-left-color: #10b981;
}

.history-item.maintenance {
    border-left-color: #f59e0b;
}

.history-item.construction {
    border-left-color: #3b82f6;
}

.history-item.offline {
    border-left-color: #ef4444;
}
</style>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadCurrentStatus();
    loadStatusHistory();

    // Update status form
    document.getElementById('updateStatusForm').addEventListener('submit', function(e) {
        e.preventDefault();
        updateStatus();
    });
});

function loadCurrentStatus() {
    fetch('/site-status/get-status', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayCurrentStatus(data.status);
        } else {
            // Show default status
            displayCurrentStatus({
                status: 'online',
                message: 'All systems operational',
                updated_at: new Date().toISOString(),
                updated_by_username: 'System'
            });
        }
    })
    .catch(error => {
        console.error('Error loading status:', error);
        // Show default status
        displayCurrentStatus({
            status: 'online',
            message: 'All systems operational',
            updated_at: new Date().toISOString(),
            updated_by_username: 'System'
        });
    });
}

function displayCurrentStatus(status) {
    const container = document.getElementById('currentStatus');
    
    container.innerHTML = `
        <div class="d-flex align-items-center mb-3">
            <div class="status-badge status-${status.status} me-3">
                ${status.status}
            </div>
            <div>
                <h5 class="mb-0">Site is ${status.status}</h5>
                <small class="text-muted">Last updated: ${new Date(status.updated_at).toLocaleString()}</small>
            </div>
        </div>
        ${status.message ? `<p class="mb-3">${status.message}</p>` : ''}
        ${status.estimated_completion ? `
            <div class="alert alert-info">
                <i class="fas fa-clock me-2"></i>
                Estimated completion: ${new Date(status.estimated_completion).toLocaleString()}
            </div>
        ` : ''}
        <div class="text-muted">
            <small>Updated by: ${status.updated_by_username || 'System'}</small>
        </div>
    `;
    
    // Update last update stat
    document.getElementById('lastUpdate').textContent = new Date(status.updated_at).toLocaleDateString();
}

function loadStatusHistory() {
    fetch('/site-status/history', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayStatusHistory(data.history);
        } else {
            document.getElementById('statusHistory').innerHTML = 
                '<p class="text-muted text-center">No status history found</p>';
        }
    })
    .catch(error => {
        console.error('Error loading status history:', error);
        // Show sample history
        displaySampleHistory();
    });
}

function displaySampleHistory() {
    const sampleHistory = [
        {
            status: 'online',
            message: 'All systems operational',
            updated_at: new Date().toISOString(),
            updated_by_username: 'Admin'
        },
        {
            status: 'maintenance',
            message: 'Scheduled maintenance window',
            updated_at: new Date(Date.now() - 86400000).toISOString(),
            updated_by_username: 'System'
        }
    ];
    
    displayStatusHistory(sampleHistory);
}

function displayStatusHistory(history) {
    const container = document.getElementById('statusHistory');
    
    if (history.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">No status history found</p>';
        return;
    }
    
    container.innerHTML = history.map(item => `
        <div class="history-item ${item.status}">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="d-flex align-items-center mb-2">
                        <span class="status-badge status-${item.status} me-2">${item.status}</span>
                        <small class="text-muted">${new Date(item.updated_at).toLocaleString()}</small>
                    </div>
                    ${item.message ? `<p class="mb-1">${item.message}</p>` : ''}
                    <small class="text-muted">Updated by: ${item.updated_by_username || 'System'}</small>
                </div>
            </div>
        </div>
    `).join('');
}

function updateStatus() {
    const form = document.getElementById('updateStatusForm');
    const formData = new FormData(form);
    const updateBtn = document.getElementById('updateStatusBtn');
    
    updateBtn.disabled = true;
    updateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
    
    fetch('/site-status/update-status', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('updateStatusModal')).hide();
            form.reset();
            loadCurrentStatus();
            loadStatusHistory();
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error updating status:', error);
        showAlert('danger', 'Error updating status');
    })
    .finally(() => {
        updateBtn.disabled = false;
        updateBtn.innerHTML = '<i class="fas fa-save me-2"></i>Update Status';
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
<?= $this->endSection() ?>
