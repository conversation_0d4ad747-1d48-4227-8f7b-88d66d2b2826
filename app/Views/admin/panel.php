<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #6366f1;
            --secondary: #8b5cf6;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --transition-fast: 0.15s ease-in-out;
            --transition-base: 0.2s ease-in-out;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--gray-800);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--space-xl);
        }

        .header {
            text-align: center;
            margin-bottom: var(--space-2xl);
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: var(--space-md);
        }

        .header p {
            font-size: 1.125rem;
            opacity: 0.9;
        }

        .back-button {
            position: absolute;
            top: var(--space-xl);
            left: var(--space-xl);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: var(--space-md) var(--space-lg);
            border-radius: var(--radius-lg);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            font-weight: 500;
            transition: all var(--transition-base);
            backdrop-filter: blur(10px);
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .admin-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-xl);
            margin-top: var(--space-2xl);
        }

        .admin-card {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--space-2xl);
            text-decoration: none;
            color: inherit;
            transition: all var(--transition-base);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--gray-200);
            position: relative;
            overflow: hidden;
        }

        .admin-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
        }

        .admin-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
        }

        .admin-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin-bottom: var(--space-lg);
            transition: all var(--transition-base);
        }

        .admin-card:hover .admin-icon {
            transform: scale(1.1);
        }

        .admin-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: var(--space-md);
        }

        .admin-description {
            color: var(--gray-600);
            line-height: 1.6;
            font-size: 1rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: var(--space-md);
            }

            .header h1 {
                font-size: 2rem;
            }

            .back-button {
                position: relative;
                top: auto;
                left: auto;
                margin-bottom: var(--space-lg);
                display: inline-flex;
            }

            .admin-grid {
                grid-template-columns: 1fr;
                gap: var(--space-lg);
            }

            .admin-card {
                padding: var(--space-xl);
            }

            .admin-icon {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }

            .admin-title {
                font-size: 1.25rem;
            }
        }
    </style>
</head>
<body>
    <a href="/dashboard" class="back-button">
        <i class="fas fa-arrow-left"></i>
        Back to Dashboard
    </a>

    <div class="container">
        <div class="header">
            <h1><i class="fas fa-user-shield"></i> Admin Panel</h1>
            <p>System administration and management tools</p>
        </div>

        <div class="admin-grid">
            <?php foreach ($adminActions as $action): ?>
                <a href="<?= $action['url'] ?>" class="admin-card">
                    <div class="admin-icon">
                        <i class="<?= $action['icon'] ?>"></i>
                    </div>
                    <div class="admin-title"><?= $action['title'] ?></div>
                    <div class="admin-description"><?= $action['description'] ?></div>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
</body>
</html>
