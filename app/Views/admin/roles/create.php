<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title><?= $title ?? 'Create Role - SmartFlo' ?></title>

    <!-- Modern PWA Meta Tags -->
    <meta name="application-name" content="SmartFlo">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="SmartFlo">
    <meta name="description" content="Create Role - SmartFlo">
    <meta name="theme-color" content="#6366f1">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" href="/icons/icon-192x192.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        /* ===== MODERN CSS VARIABLES ===== */
        :root {
            /* Colors - Modern Palette */
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #a5b4fc;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --info: #3b82f6;
            
            /* Neutrals - Refined Grays */
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            
            /* Layout */
            --sidebar-width: 280px;
            --header-height: 72px;
            --bottom-nav-height: 80px;
            
            /* Spacing */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;
            
            /* Border Radius */
            --radius-sm: 6px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 24px;
            
            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            
            /* Transitions */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-base: 250ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
        }

        html {
            height: 100%;
            height: calc(var(--vh, 1vh) * 100);
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            margin: 0;
            padding: 0;
            color: var(--gray-800);
            line-height: 1.6;
            min-height: 100vh;
            min-height: calc(var(--vh, 1vh) * 100);
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            position: relative;
            scroll-behavior: smooth;
        }

        /* ===== LAYOUT STRUCTURE ===== */
        .dashboard-container {
            display: flex;
            min-height: 100vh;
            min-height: calc(var(--vh, 1vh) * 100);
            background: var(--gray-50);
            position: relative;
        }

        /* ===== MAIN CONTENT AREA ===== */
        .main-content {
            flex: 1;
            padding: 2rem;
            min-height: 100vh;
            min-height: calc(var(--vh, 1vh) * 100);
            overflow-x: hidden;
            width: 100%;
            margin-left: var(--sidebar-width);
        }

        /* ===== DESKTOP SIDEBAR ===== */
        .sidebar {
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            transition: transform var(--transition-base);
            box-shadow: var(--shadow-lg);
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .sidebar-header h4 {
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .sidebar-header h4 i {
            color: rgba(255, 255, 255, 0.9);
            margin-right: 0.5rem;
        }

        .sidebar-header small {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.875rem;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            margin: 0.25rem 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.875rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-weight: 500;
            transition: all var(--transition-fast);
            border-left: 3px solid transparent;
            position: relative;
        }

        .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            text-decoration: none;
            border-left-color: rgba(255, 255, 255, 0.3);
        }

        .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.15);
            border-left-color: white;
            font-weight: 600;
        }

        .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
            font-size: 1rem;
            text-align: center;
        }

        .sidebar-footer {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 1rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        /* Modern Mobile Header */
        .mobile-header {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: var(--mobile-header-height);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--gray-200);
            z-index: 1000;
            padding: 0 1rem;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-sm);
        }

        .mobile-header .logo {
            display: flex;
            align-items: center;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-900);
        }

        .mobile-header .logo i {
            color: var(--primary);
            margin-right: 0.5rem;
        }

        .mobile-header-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .header-btn {
            width: 44px;
            height: 44px;
            border-radius: var(--border-radius-sm);
            border: none;
            background: var(--gray-100);
            color: var(--gray-600);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-fast);
            cursor: pointer;
            position: relative;
        }

        .header-btn:hover {
            background: var(--gray-200);
            color: var(--gray-800);
            transform: translateY(-1px);
        }

        .header-btn:active {
            transform: scale(0.95);
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: var(--border-radius-lg);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(50%, -50%);
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 1;
        }

        .page-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            margin-bottom: 2rem;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
        }

        .card-body {
            padding: 1.5rem;
        }

        .btn {
            border-radius: var(--border-radius-sm);
            font-weight: 600;
            transition: all var(--transition-base);
            min-height: 44px;
            padding: 0.75rem 1.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            border: none;
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        .form-control {
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            padding: 0.75rem;
            font-size: 0.875rem;
            transition: all var(--transition-fast);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-label {
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
        }

        .form-check-input {
            border-radius: var(--radius-sm);
        }

        .form-check-input:checked {
            background-color: var(--primary);
            border-color: var(--primary);
        }

        .permission-group {
            background: var(--gray-50);
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            padding: var(--space-lg);
            margin-bottom: var(--space-md);
        }

        .permission-group h6 {
            color: var(--gray-800);
            font-weight: 600;
            margin-bottom: var(--space-md);
            display: flex;
            align-items: center;
            gap: var(--space-sm);
        }

        .permission-checkbox {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-sm);
            padding: var(--space-md);
            margin-bottom: var(--space-sm);
            transition: all var(--transition-fast);
        }

        .permission-checkbox:hover {
            background: var(--gray-50);
        }

        .permission-checkbox.selected {
            background: rgba(99, 102, 241, 0.1);
            border-color: var(--primary);
        }

        /* ===== BOTTOM NAVIGATION ===== */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: var(--bottom-nav-height);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid var(--gray-200);
            z-index: 1000;
            padding: 0 env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
        }

        .bottom-nav-container {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            height: 100%;
            align-items: center;
            max-width: 100%;
        }

        .bottom-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: var(--gray-500);
            font-size: 0.75rem;
            font-weight: 500;
            transition: all var(--transition-fast);
            min-height: 44px;
            min-width: 44px;
            padding: 0.5rem;
            position: relative;
        }

        .bottom-nav-item i {
            font-size: 1.125rem;
            margin-bottom: 0.25rem;
            transition: all var(--transition-fast);
        }

        .bottom-nav-item.active {
            color: var(--primary);
            font-weight: 600;
        }

        .bottom-nav-item.active::before {
            content: '';
            position: absolute;
            top: -1px;
            left: 50%;
            transform: translateX(-50%);
            width: 24px;
            height: 3px;
            background: var(--primary);
            border-radius: 0 0 4px 4px;
        }

        .bottom-nav-item:hover {
            color: var(--primary);
            transform: translateY(-2px);
        }

        .bottom-nav-item.logout-btn {
            color: var(--error);
        }

        .bottom-nav-item.logout-btn:hover {
            background: rgba(245, 101, 101, 0.1);
            color: var(--error);
        }

        /* ===== RESPONSIVE DESIGN ===== */
        @media (max-width: 991px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding-top: calc(var(--mobile-header-height) + 1rem);
                padding-bottom: calc(var(--bottom-nav-height) + 1rem);
            }

            .mobile-header {
                display: flex;
            }

            .page-header {
                padding: 1.5rem 1rem;
                margin-bottom: 1.5rem;
            }

            .page-title {
                font-size: 1.5rem;
            }

            .page-subtitle {
                font-size: 1rem;
            }
        }

        @media (min-width: 992px) {
            .bottom-nav {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Desktop Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4>
                <i class="fas fa-shield-alt"></i>
                SmartFlo
            </h4>
            <small>Create Role</small>
        </div>
        <ul class="sidebar-nav">
            <li class="nav-item">
                <a href="/dashboard" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a href="/admin/users" class="nav-link">
                    <i class="fas fa-users"></i>
                    User Management
                </a>
            </li>
            <li class="nav-item">
                <a href="/admin/roles" class="nav-link">
                    <i class="fas fa-user-tag"></i>
                    Role Management
                </a>
            </li>
            <li class="nav-item">
                <a href="/admin/system" class="nav-link">
                    <i class="fas fa-cog"></i>
                    System Settings
                </a>
            </li>
        </ul>
        <div class="sidebar-footer">
            <small class="text-light">SmartFlo v3.0</small>
        </div>
    </nav>

    <!-- Mobile Header -->
    <header class="mobile-header">
        <div class="logo">
            <i class="fas fa-shield-alt"></i>
            SmartFlo
        </div>
        <div class="mobile-header-actions">
            <button class="header-btn notification-btn" onclick="showNotifications()">
                <i class="fas fa-bell"></i>
            </button>
            <button class="header-btn" onclick="showUserMenu()">
                <i class="fas fa-user-circle"></i>
            </button>
        </div>
    </header>

    <!-- Dashboard Container -->
    <div class="dashboard-container">
        <main class="main-content">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-user-shield me-3"></i>
                    Create New Role
                </h1>
                <p class="page-subtitle">Define a new role with specific permissions</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-shield me-2"></i>
                        Role Information
                    </h5>
                </div>
                <div class="card-body">
                    <form id="createRoleForm">
                        <?= csrf_field() ?>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="role_name" class="form-label">
                                    <i class="fas fa-tag me-1"></i>
                                    Role Name *
                                </label>
                                <input type="text" class="form-control" id="role_name" name="role_name" required>
                                <div class="invalid-feedback"></div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="description" class="form-label">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Description
                                </label>
                                <input type="text" class="form-control" id="description" name="description">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-key me-1"></i>
                                Permissions
                            </label>
                            <p class="text-muted small mb-3">Select the permissions this role should have:</p>

                            <div id="permissionsContainer">
                                <?php if (!empty($availablePermissions)): ?>
                                    <?php
                                    $groupedPermissions = [];
                                    foreach ($availablePermissions as $permission => $description) {
                                        $parts = explode('.', $permission);
                                        $group = $parts[0];
                                        $groupedPermissions[$group][] = ['permission' => $permission, 'description' => $description];
                                    }
                                    ?>

                                    <?php foreach ($groupedPermissions as $group => $permissions): ?>
                                        <div class="permission-group">
                                            <h6>
                                                <i class="fas fa-<?= $group === 'user' ? 'users' : ($group === 'role' ? 'user-tag' : 'cog') ?>"></i>
                                                <?= ucfirst($group) ?> Permissions
                                            </h6>

                                            <?php foreach ($permissions as $perm): ?>
                                                <div class="permission-checkbox">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox"
                                                               id="perm_<?= str_replace('.', '_', $perm['permission']) ?>"
                                                               name="permissions[]"
                                                               value="<?= $perm['permission'] ?>">
                                                        <label class="form-check-label" for="perm_<?= str_replace('.', '_', $perm['permission']) ?>">
                                                            <strong><?= htmlspecialchars($perm['description']) ?></strong>
                                                            <br><small class="text-muted"><?= htmlspecialchars($perm['permission']) ?></small>
                                                        </label>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <p class="text-muted">No permissions available.</p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-save me-2"></i>
                                Create Role
                            </button>
                            <a href="/admin/roles" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>

    <!-- Bottom Navigation -->
    <nav class="bottom-nav">
        <div class="bottom-nav-container">
            <a href="/dashboard" class="bottom-nav-item">
                <i class="fas fa-home"></i>
                <span>Dashboard</span>
            </a>

            <a href="/admin/users" class="bottom-nav-item">
                <i class="fas fa-users"></i>
                <span>Users</span>
            </a>

            <a href="/admin/roles/create" class="bottom-nav-item active">
                <i class="fas fa-plus-circle"></i>
                <span>Add</span>
            </a>

            <a href="/admin/system" class="bottom-nav-item">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>

            <a href="/auth/logout" class="bottom-nav-item logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <span>Logout</span>
            </a>
        </div>
    </nav>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Mobile viewport height fix
        function setVH() {
            let vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }

        window.addEventListener('resize', setVH);
        window.addEventListener('orientationchange', setVH);
        setVH();

        // Form submission
        document.getElementById('createRoleForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Clear previous errors
            clearErrors();

            // Get form data
            const formData = new FormData(this);

            // Disable submit button
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';

            // Submit form
            fetch('/admin/roles/store', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                    // Redirect after success
                    setTimeout(() => {
                        window.location.href = data.redirect || '/admin/roles';
                    }, 1500);
                } else {
                    showError(data.message);
                    if (data.errors) {
                        displayErrors(data.errors);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('An error occurred while creating the role.');
            })
            .finally(() => {
                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });

        // Permission checkbox styling
        document.querySelectorAll('.permission-checkbox input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const permissionBox = this.closest('.permission-checkbox');
                if (this.checked) {
                    permissionBox.classList.add('selected');
                } else {
                    permissionBox.classList.remove('selected');
                }
            });
        });

        // Utility functions
        function clearErrors() {
            document.querySelectorAll('.is-invalid').forEach(el => {
                el.classList.remove('is-invalid');
            });
            document.querySelectorAll('.invalid-feedback').forEach(el => {
                el.textContent = '';
            });
        }

        function displayErrors(errors) {
            Object.keys(errors).forEach(field => {
                const input = document.querySelector(`[name="${field}"]`);
                if (input) {
                    input.classList.add('is-invalid');
                    const feedback = input.parentNode.querySelector('.invalid-feedback');
                    if (feedback) {
                        feedback.textContent = errors[field];
                    }
                }
            });
        }

        function showSuccess(message) {
            const toast = createToast('success', message);
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        }

        function showError(message) {
            const toast = createToast('error', message);
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        }

        function createToast(type, message) {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            return toast;
        }

        // Placeholder functions for header buttons
        function showNotifications() {
            console.log('Show notifications panel');
        }

        function showUserMenu() {
            console.log('Show user menu');
        }

        // Mobile sidebar toggle
        const sidebar = document.getElementById('sidebar');
        const sidebarToggle = document.getElementById('sidebarToggle');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('show');
            });
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 991 && sidebar.classList.contains('show')) {
                if (!sidebar.contains(e.target) && !sidebarToggle?.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });
    </script>
</body>
</html>
