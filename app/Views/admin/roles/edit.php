<?= $this->extend('layouts/app') ?>

<?= $this->section('content') ?>
<!-- <PERSON> Header -->
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-user-edit me-3"></i>
        Edit Role: <?= htmlspecialchars($editRole['role_name']) ?>
    </h1>
    <p class="page-subtitle">Update role permissions and information</p>
</div>

<!-- Main Content -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-user-shield me-2"></i>
                    Role Information
                </h5>
                <a href="/admin/roles" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-arrow-left me-1"></i>
                    Back to Roles
                </a>
            </div>
            <div class="card-body">
                <form id="editRoleForm" action="/admin/roles/update/<?= $editRole['id'] ?>" method="POST">
                    <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>">

                    <!-- Role Basic Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="role_name" class="form-label">Role Name *</label>
                            <input type="text"
                                   class="form-control"
                                   id="role_name"
                                   name="role_name"
                                   value="<?= htmlspecialchars($editRole['role_name']) ?>"
                                   required>
                        </div>
                        <div class="col-md-6">
                            <label for="description" class="form-label">Description</label>
                            <input type="text"
                                   class="form-control"
                                   id="description"
                                   name="description"
                                   value="<?= htmlspecialchars($editRole['description']) ?>">
                        </div>
                    </div>

                    <!-- Permissions Section -->
                    <div class="mb-4">
                        <h6 class="mb-3">
                            <i class="fas fa-shield-alt me-2"></i>
                            Role Permissions
                        </h6>
                        <div class="row">
                            <?php if (!empty($availablePermissions)): ?>
                                <?php
                                $permissionGroups = [
                                    'User Management' => ['user.create', 'user.read', 'user.update', 'user.delete'],
                                    'Role Management' => ['role.create', 'role.read', 'role.update', 'role.delete'],
                                    'System Access' => ['admin.access']
                                ];
                                ?>

                                <?php foreach ($permissionGroups as $groupName => $groupPermissions): ?>
                                    <div class="col-md-4 mb-3">
                                        <div class="permission-group">
                                            <h6 class="text-primary mb-3">
                                                <?php if ($groupName === 'User Management'): ?>
                                                    <i class="fas fa-users me-2"></i>
                                                <?php elseif ($groupName === 'Role Management'): ?>
                                                    <i class="fas fa-user-shield me-2"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-cog me-2"></i>
                                                <?php endif; ?>
                                                <?= $groupName ?>
                                            </h6>

                                            <?php foreach ($groupPermissions as $permission): ?>
                                                <?php if (isset($availablePermissions[$permission])): ?>
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input"
                                                               type="checkbox"
                                                               name="permissions[]"
                                                               value="<?= $permission ?>"
                                                               id="perm_<?= str_replace('.', '_', $permission) ?>"
                                                               <?= in_array($permission, $rolePermissions ?? []) ? 'checked' : '' ?>>
                                                        <label class="form-check-label" for="perm_<?= str_replace('.', '_', $permission) ?>">
                                                            <?= htmlspecialchars($availablePermissions[$permission]) ?>
                                                        </label>
                                                    </div>
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        No permissions available to assign.
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="/admin/roles" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Update Role
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('editRoleForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Updating...';

    fetch('/admin/roles/update/<?= $editRole['id'] ?>', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('success', data.message);
            setTimeout(() => {
                window.location.href = '/admin/roles';
            }, 1500);
        } else {
            showToast('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', 'An error occurred while updating the role.');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});
</script>

<?= $this->endSection() ?>


