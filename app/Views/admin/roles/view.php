<?= $this->extend('layouts/app') ?>

<?= $this->section('styles') ?>
<style>
    .permission-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--space-md);
        margin-top: var(--space-lg);
    }

    .permission-category {
        background: var(--gray-50);
        border: 1px solid var(--gray-200);
        border-radius: var(--radius-lg);
        padding: var(--space-lg);
    }

    .permission-category h6 {
        color: var(--primary);
        font-weight: 600;
        margin-bottom: var(--space-md);
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.5px;
    }

    .permission-item {
        display: flex;
        align-items: center;
        padding: var(--space-sm) 0;
        border-bottom: 1px solid var(--gray-200);
    }

    .permission-item:last-child {
        border-bottom: none;
    }

    .permission-icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: var(--space-sm);
        font-size: 0.75rem;
    }

    .permission-granted {
        background: var(--success);
        color: white;
    }

    .permission-denied {
        background: var(--gray-300);
        color: var(--gray-600);
    }

    .role-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--space-lg);
        margin-bottom: var(--space-xl);
    }

    .stat-card {
        background: white;
        border: 1px solid var(--gray-200);
        border-radius: var(--radius-lg);
        padding: var(--space-lg);
        text-align: center;
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary);
        margin-bottom: var(--space-sm);
    }

    .stat-label {
        color: var(--gray-600);
        font-size: 0.875rem;
        font-weight: 500;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-title-wrapper">
            <h1 class="page-title">
                <i class="fas fa-eye"></i>
                View Role: <?= htmlspecialchars($role['role_name']) ?>
            </h1>
            <p class="page-subtitle"><?= htmlspecialchars($role['description'] ?: 'No description provided') ?></p>
        </div>
        <div class="page-actions">
            <a href="/admin/roles" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Back to Roles
            </a>
            <?php if ($authLib->hasPermission('role.update')): ?>
                <a href="/admin/roles/edit/<?= $role['id'] ?>" class="btn btn-primary">
                    <i class="fas fa-edit"></i>
                    Edit Role
                </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Role Statistics -->
<div class="role-stats">
    <div class="stat-card">
        <div class="stat-value"><?= $role['user_count'] ?></div>
        <div class="stat-label">Users Assigned</div>
    </div>
    <div class="stat-card">
        <div class="stat-value"><?= count($role['permissions']) ?></div>
        <div class="stat-label">Permissions Granted</div>
    </div>
    <div class="stat-card">
        <div class="stat-value"><?= date('M j, Y', strtotime($role['created_at'])) ?></div>
        <div class="stat-label">Created Date</div>
    </div>
</div>

<!-- Role Information -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-info-circle me-2"></i>
            Role Information
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">Role Name</label>
                    <div class="form-control-plaintext"><?= htmlspecialchars($role['role_name']) ?></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">Created Date</label>
                    <div class="form-control-plaintext"><?= date('M j, Y g:i A', strtotime($role['created_at'])) ?></div>
                </div>
            </div>
        </div>
        <div class="mb-3">
            <label class="form-label">Description</label>
            <div class="form-control-plaintext"><?= htmlspecialchars($role['description'] ?: 'No description provided') ?></div>
        </div>
    </div>
</div>

<!-- Permissions -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-shield-alt me-2"></i>
            Permissions
        </h5>
    </div>
    <div class="card-body">
        <div class="permission-grid">
            <?php
            $permissionsByCategory = [];
            foreach ($availablePermissions as $permission) {
                $category = explode('.', $permission)[0];
                $permissionsByCategory[$category][] = $permission;
            }
            ?>

            <?php foreach ($permissionsByCategory as $category => $permissions): ?>
                <div class="permission-category">
                    <h6><?= ucfirst($category) ?> Permissions</h6>
                    <?php foreach ($permissions as $permission): ?>
                        <?php $hasPermission = in_array($permission, $role['permissions']); ?>
                        <div class="permission-item">
                            <div class="permission-icon <?= $hasPermission ? 'permission-granted' : 'permission-denied' ?>">
                                <i class="fas <?= $hasPermission ? 'fa-check' : 'fa-times' ?>"></i>
                            </div>
                            <span><?= ucwords(str_replace(['.', '_'], [' ', ' '], $permission)) ?></span>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Add any role view specific JavaScript here
    console.log('Role view loaded');
</script>
<?= $this->endSection() ?>
