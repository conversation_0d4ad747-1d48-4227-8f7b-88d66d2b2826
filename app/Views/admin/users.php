<?= $this->extend('layouts/app') ?>

<?= $this->section('styles') ?>
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
<style>
        /* User-specific styles only */
        .user-card {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
            transition: all var(--transition-fast);
            position: relative;
            overflow: hidden;
            margin-bottom: var(--space-md);
        }

        .user-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary);
        }

        .user-card-header {
            display: flex;
            align-items: center;
            gap: var(--space-md);
            margin-bottom: var(--space-md);
        }

        .user-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.125rem;
        }

        .user-info h3 {
            margin: 0;
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray-900);
        }

        .user-info p {
            margin: 0;
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .user-card-body {
            margin-bottom: var(--space-lg);
        }

        .user-roles {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-xs);
            margin-bottom: var(--space-md);
        }

        .role-badge {
            background: var(--primary);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .user-status {
            display: flex;
            align-items: center;
            gap: var(--space-xs);
            font-size: 0.875rem;
        }

        .status-active {
            color: var(--success);
        }

        .status-inactive {
            color: var(--error);
        }

        .user-card-actions {
            display: flex;
            gap: var(--space-sm);
            justify-content: flex-end;
        }

        .user-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: var(--space-lg);
            margin-bottom: var(--space-xl);
        }



        @media (max-width: 768px) {
            .user-cards {
                grid-template-columns: 1fr;
                gap: var(--space-md);
            }

            .search-filters .row {
                gap: var(--space-md);
            }

            .search-filters .col-md-6:last-child {
                text-align: left !important;
            }
        }


    </style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-users me-3"></i>
        User Management
    </h1>
    <p class="page-subtitle">Manage user accounts, roles, and permissions</p>
</div>

            <!-- Search and Actions -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <input type="text" id="userSearch" class="form-control" placeholder="Search users by name or email...">
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="/admin/users/create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                Add New User
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Grid -->
            <div id="usersContainer">
                <div class="user-cards" id="userCards">
                    <!-- Users will be loaded here via AJAX -->
                </div>

                <!-- Loading State -->
                <div id="loadingState" class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3 text-muted">Loading users...</p>
                </div>

                <!-- Empty State -->
                <div id="emptyState" class="text-center py-5" style="display: none;">
                    <i class="fas fa-users text-muted" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                    <h3 class="text-muted">No Users Found</h3>
                    <p class="text-muted">No users match your search criteria.</p>
                </div>
            </div>


    <!-- User Action Modal -->
    <div class="modal fade" id="userActionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userActionModalTitle">Confirm Action</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="userActionModalBody">
                    <!-- Content will be set by JavaScript -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmActionBtn">Confirm</button>
                </div>
            </div>
        </div>
    </div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script>
        let users = [];
        let filteredUsers = [];

        // Mobile viewport height fix
        function setVH() {
            let vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }

        window.addEventListener('resize', setVH);
        window.addEventListener('orientationchange', setVH);
        setVH();

        // Load users on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadUsers();

            // Search functionality
            document.getElementById('userSearch').addEventListener('input', function() {
                filterUsers(this.value);
            });
        });

        // Load users from server
        function loadUsers() {
            document.getElementById('loadingState').style.display = 'block';
            document.getElementById('userCards').style.display = 'none';
            document.getElementById('emptyState').style.display = 'none';

            fetch('/admin/users/data')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success !== false) {
                        users = data.data || [];
                        filteredUsers = [...users];
                        renderUsers();
                    } else {
                        if (data.offline) {
                            showOfflineError(data.message);
                        } else {
                            showError('Failed to load users: ' + (data.message || 'Unknown error'));
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading users:', error);
                    if (error.message.includes('Failed to fetch') || !navigator.onLine) {
                        showOfflineError('No internet connection. Please check your network and try again.');
                    } else {
                        showError('Failed to load users. Please try again.');
                    }
                })
                .finally(() => {
                    document.getElementById('loadingState').style.display = 'none';
                });
        }

        // Filter users based on search
        function filterUsers(searchTerm) {
            if (!searchTerm.trim()) {
                filteredUsers = [...users];
            } else {
                const term = searchTerm.toLowerCase();
                filteredUsers = users.filter(user =>
                    user.username.toLowerCase().includes(term) ||
                    user.email.toLowerCase().includes(term) ||
                    (user.roles && user.roles.toLowerCase().includes(term))
                );
            }
            renderUsers();
        }

        // Render users in card layout
        function renderUsers() {
            const container = document.getElementById('userCards');
            const emptyState = document.getElementById('emptyState');

            if (filteredUsers.length === 0) {
                container.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            container.style.display = 'grid';
            emptyState.style.display = 'none';

            container.innerHTML = filteredUsers.map(user => createUserCard(user)).join('');

            // Add event listeners to action buttons
            addActionListeners();
        }

        // Create user card HTML
        function createUserCard(user) {
            const roles = user.roles ? user.roles.split(',').map(role =>
                `<span class="role-badge">${role.trim()}</span>`
            ).join('') : '<span class="text-muted">No roles</span>';

            const statusClass = user.is_active === 'Active' ? 'status-active' : 'status-inactive';
            const statusIcon = user.is_active === 'Active' ? 'fa-check-circle' : 'fa-times-circle';

            const avatar = user.username.charAt(0).toUpperCase();

            return `
                <div class="user-card">
                    <div class="user-card-header">
                        <div class="user-avatar">${avatar}</div>
                        <div class="user-info">
                            <h3>${escapeHtml(user.username)}</h3>
                            <p>${escapeHtml(user.email)}</p>
                        </div>
                    </div>
                    <div class="user-card-body">
                        <div class="user-roles">
                            ${roles}
                        </div>
                        <div class="user-status ${statusClass}">
                            <i class="fas ${statusIcon}"></i>
                            ${user.is_active}
                        </div>
                        <div class="user-details">
                            <div class="text-muted small mb-1">
                                <i class="fas fa-calendar-plus me-1"></i>
                                Created: ${user.created_at}
                            </div>
                            ${user.last_login ? `
                                <div class="text-muted small mb-1">
                                    <i class="fas fa-clock me-1"></i>
                                    Last Login: ${user.last_login}
                                </div>
                            ` : ''}
                            <div class="text-muted small">
                                <i class="fas fa-id-badge me-1"></i>
                                ID: ${user.id}
                            </div>
                        </div>
                    </div>
                    <div class="user-card-actions">
                        ${user.actions}
                    </div>
                </div>
            `;
        }

        // Add event listeners to action buttons
        function addActionListeners() {
            // Edit buttons
            document.querySelectorAll('a[href*="/admin/users/edit/"]').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // Allow default navigation
                });
            });

            // Toggle status buttons
            document.querySelectorAll('.toggle-status').forEach(btn => {
                btn.addEventListener('click', function() {
                    const userId = this.dataset.id;
                    toggleUserStatus(userId);
                });
            });

            // No delete functionality - users can only be deactivated
        }

        // Toggle user status
        function toggleUserStatus(userId) {
            // Get fresh CSRF token from meta tag or hidden input
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                             document.querySelector('input[name="csrf_token"]')?.value ||
                             '<?= csrf_hash() ?>';

            const formData = new FormData();
            formData.append('csrf_token', csrfToken);

            fetch(`/admin/users/toggle-status/${userId}`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                    loadUsers(); // Reload users
                } else {
                    showError(data.message || 'Failed to update user status');
                    // Refresh CSRF token on failure
                    refreshCSRFToken();
                }
            })
            .catch(error => {
                console.error('Error toggling user status:', error);
                if (error.message.includes('Failed to fetch') || !navigator.onLine) {
                    showOfflineError('No internet connection. Please check your network and try again.');
                } else if (error.message.includes('403')) {
                    showError('Security token expired. Please refresh the page and try again.');
                    // Auto-refresh CSRF token
                    refreshCSRFToken();
                } else {
                    showError('Failed to update user status: ' + error.message);
                }
            });
        }

        // Refresh CSRF token
        function refreshCSRFToken() {
            fetch('/auth/csrf-token', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.token) {
                    // Update meta tag
                    const metaTag = document.querySelector('meta[name="csrf-token"]');
                    if (metaTag) {
                        metaTag.setAttribute('content', data.token);
                    }
                    // Update any hidden inputs
                    const hiddenInputs = document.querySelectorAll('input[name="csrf_token"]');
                    hiddenInputs.forEach(input => {
                        input.value = data.token;
                    });
                }
            })
            .catch(error => {
                console.error('Failed to refresh CSRF token:', error);
            });
        }

        // Utility functions
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function showSuccess(message) {
            // Create and show success toast/notification
            const toast = createToast('success', message);
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        }

        function showError(message) {
            // Create and show error toast/notification
            const toast = createToast('error', message);
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        }

        function showOfflineError(message) {
            // Create and show offline error modal
            const modal = createOfflineModal(message);
            document.body.appendChild(modal);
        }

        function createToast(type, message) {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            return toast;
        }

        function createOfflineModal(message) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0, 0, 0, 0.8); z-index: 10000;
                display: flex; align-items: center; justify-content: center;
                padding: 1rem;
            `;
            modal.innerHTML = `
                <div style="
                    background: white; border-radius: 16px; padding: 2rem;
                    max-width: 400px; width: 100%; text-align: center;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                ">
                    <div style="
                        width: 80px; height: 80px; background: var(--error);
                        border-radius: 50%; display: flex; align-items: center;
                        justify-content: center; margin: 0 auto 1.5rem;
                        color: white; font-size: 2rem;
                    ">
                        📡
                    </div>
                    <h3 style="color: var(--gray-900); margin-bottom: 0.5rem;">No Internet Connection</h3>
                    <p style="color: var(--gray-600); margin-bottom: 2rem; line-height: 1.6;">
                        ${message}
                    </p>
                    <div style="display: flex; gap: 0.5rem; justify-content: center;">
                        <button onclick="this.closest('[style*=\"position: fixed\"]').remove(); loadUsers();"
                                style="background: var(--primary); color: white; border: none;
                                       padding: 0.75rem 1.5rem; border-radius: 8px; font-weight: 600; cursor: pointer;">
                            Try Again
                        </button>
                        <button onclick="this.closest('[style*=\"position: fixed\"]').remove();"
                                style="background: var(--gray-200); color: var(--gray-700); border: none;
                                       padding: 0.75rem 1.5rem; border-radius: 8px; font-weight: 600; cursor: pointer;">
                            Close
                        </button>
                    </div>
                    <div style="margin-top: 1.5rem; padding: 0.75rem; border-radius: 8px; font-size: 0.875rem;
                                background: ${navigator.onLine ? 'rgba(16, 185, 129, 0.1)' : 'rgba(239, 68, 68, 0.1)'};
                                color: ${navigator.onLine ? '#10b981' : 'var(--error)'};">
                        Status: ${navigator.onLine ? 'Online' : 'Offline'}
                    </div>
                </div>
            `;

            // Auto-close when back online
            const onlineHandler = () => {
                modal.remove();
                loadUsers();
                window.removeEventListener('online', onlineHandler);
            };
            window.addEventListener('online', onlineHandler);

            return modal;
        }



</script>
<?= $this->endSection() ?>
