<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">System Information</h1>
                    <p class="text-muted">System version, server details, and configuration</p>
                </div>
                <button class="btn btn-primary" onclick="checkForUpdates()">
                    <i class="fas fa-sync-alt me-2"></i>
                    Check for Updates
                </button>
            </div>
        </div>
    </div>

    <!-- Version Information -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Version Information
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Version:</strong></td>
                            <td><?= $system_info['version'] ?></td>
                        </tr>
                        <tr>
                            <td><strong>Codename:</strong></td>
                            <td><?= $system_info['codename'] ?></td>
                        </tr>
                        <tr>
                            <td><strong>Build:</strong></td>
                            <td><?= $system_info['build'] ?></td>
                        </tr>
                        <tr>
                            <td><strong>Release Date:</strong></td>
                            <td><?= $system_info['release_date'] ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-server me-2"></i>
                        Server Information
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>PHP Version:</strong></td>
                            <td><?= $server_info['php_version'] ?></td>
                        </tr>
                        <tr>
                            <td><strong>Server Software:</strong></td>
                            <td><?= $server_info['server_software'] ?></td>
                        </tr>
                        <tr>
                            <td><strong>Memory Limit:</strong></td>
                            <td><?= $server_info['memory_limit'] ?></td>
                        </tr>
                        <tr>
                            <td><strong>Timezone:</strong></td>
                            <td><?= $server_info['timezone'] ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Database Information -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-database me-2"></i>
                        Database Information
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Database:</strong></td>
                            <td><?= $database_info['database_name'] ?></td>
                        </tr>
                        <tr>
                            <td><strong>Version:</strong></td>
                            <td><?= $database_info['version'] ?></td>
                        </tr>
                        <tr>
                            <td><strong>Size:</strong></td>
                            <td><?= $database_info['size'] ?></td>
                        </tr>
                        <tr>
                            <td><strong>Tables:</strong></td>
                            <td><?= $database_info['table_count'] ?></td>
                        </tr>
                        <tr>
                            <td><strong>Status:</strong></td>
                            <td>
                                <span class="badge bg-success">
                                    <?= $database_info['connection_status'] ?>
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-hdd me-2"></i>
                        Storage Information
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Free Space:</strong></td>
                            <td><?= $server_info['disk_free_space'] ?></td>
                        </tr>
                        <tr>
                            <td><strong>Total Space:</strong></td>
                            <td><?= $server_info['disk_total_space'] ?></td>
                        </tr>
                        <tr>
                            <td><strong>Upload Max:</strong></td>
                            <td><?= $server_info['upload_max_filesize'] ?></td>
                        </tr>
                        <tr>
                            <td><strong>Post Max:</strong></td>
                            <td><?= $server_info['post_max_size'] ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Version History -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>
                        Version History
                    </h5>
                </div>
                <div class="card-body">
                    <?php foreach ($version_history as $version => $info): ?>
                    <div class="border-start border-primary border-3 ps-3 mb-3">
                        <h6 class="mb-1">
                            Version <?= $version ?> - <?= $info['codename'] ?>
                            <small class="text-muted">(<?= $info['date'] ?>)</small>
                        </h6>
                        <ul class="mb-0">
                            <?php foreach ($info['changes'] as $change): ?>
                            <li><?= $change ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function checkForUpdates() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Checking...';
    
    fetch('/admin/system/update-version', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        showAlert('danger', 'Error checking for updates');
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
<?= $this->endSection() ?>
