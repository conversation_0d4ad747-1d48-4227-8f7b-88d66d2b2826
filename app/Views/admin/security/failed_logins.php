<?= $this->extend('layouts/app') ?>

<?= $this->section('styles') ?>
<style>
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--space-lg);
        margin-bottom: var(--space-xl);
    }

    .stat-card {
        background: white;
        border: 1px solid var(--gray-200);
        border-radius: var(--radius-lg);
        padding: var(--space-lg);
        text-align: center;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary);
        margin-bottom: var(--space-sm);
    }

    .stat-label {
        color: var(--gray-600);
        font-size: 0.875rem;
    }

    .attempts-table {
        background: white;
        border: 1px solid var(--gray-200);
        border-radius: var(--radius-lg);
        overflow: hidden;
    }

    .table-header {
        background: var(--gray-50);
        padding: var(--space-lg);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .attempts-list {
        max-height: 600px;
        overflow-y: auto;
    }

    .attempt-item {
        padding: var(--space-md) var(--space-lg);
        border-bottom: 1px solid var(--gray-100);
        display: grid;
        grid-template-columns: 1fr 150px 120px 80px;
        gap: var(--space-md);
        align-items: center;
    }

    .attempt-item:last-child {
        border-bottom: none;
    }

    .attempt-info h4 {
        margin: 0 0 var(--space-xs) 0;
        font-size: 0.875rem;
        font-weight: 600;
    }

    .attempt-info p {
        margin: 0;
        font-size: 0.75rem;
        color: var(--gray-600);
    }

    .attempt-ip {
        font-family: monospace;
        font-size: 0.875rem;
        color: var(--gray-700);
    }

    .attempt-time {
        font-size: 0.75rem;
        color: var(--gray-600);
    }

    .attempt-status {
        display: flex;
        align-items: center;
        gap: var(--space-xs);
        font-size: 0.75rem;
    }

    .status-blocked {
        color: var(--error);
    }

    .status-normal {
        color: var(--gray-600);
    }

    .block-btn {
        background: var(--error);
        color: white;
        border: none;
        padding: 0.25rem 0.5rem;
        border-radius: var(--radius-sm);
        font-size: 0.75rem;
        cursor: pointer;
    }

    .unblock-btn {
        background: var(--success);
        color: white;
        border: none;
        padding: 0.25rem 0.5rem;
        border-radius: var(--radius-sm);
        font-size: 0.75rem;
        cursor: pointer;
    }

    @media (max-width: 768px) {
        .attempt-item {
            grid-template-columns: 1fr;
            gap: var(--space-sm);
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-shield-alt me-3"></i>
        Failed Login Attempts
    </h1>
    <p class="page-subtitle">Monitor and manage security threats</p>
</div>

<!-- Statistics -->
<div class="stats-grid" id="statisticsGrid">
    <!-- Statistics will be loaded here -->
</div>

<!-- Failed Attempts Table -->
<div class="attempts-table">
    <div class="table-header">
        <h3>Recent Failed Login Attempts</h3>
        <div>
            <button class="btn btn-sm btn-outline-primary" onclick="refreshData()">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
            <button class="btn btn-sm btn-outline-danger" onclick="cleanOldAttempts()">
                <i class="fas fa-trash"></i> Clean Old
            </button>
        </div>
    </div>

    <div class="attempts-list" id="attemptsList">
        <!-- Loading state -->
        <div id="loadingState" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading failed login attempts...</p>
        </div>

        <!-- Empty state -->
        <div id="emptyState" class="text-center py-5" style="display: none;">
            <i class="fas fa-shield-alt text-success" style="font-size: 3rem; margin-bottom: 1rem;"></i>
            <h4 class="text-success">No Failed Login Attempts</h4>
            <p class="text-muted">Great! No failed login attempts have been recorded recently.</p>
        </div>
    </div>
</div>

<!-- Block IP Modal -->
<div class="modal fade" id="blockIPModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Block IP Address</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Block IP address: <strong id="blockIPAddress"></strong></p>
                <div class="mb-3">
                    <label for="blockDuration" class="form-label">Block Duration (minutes)</label>
                    <select class="form-select" id="blockDuration">
                        <option value="30">30 minutes</option>
                        <option value="60" selected>1 hour</option>
                        <option value="180">3 hours</option>
                        <option value="360">6 hours</option>
                        <option value="720">12 hours</option>
                        <option value="1440">24 hours</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmBlockIP()">Block IP</button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    let currentBlockIP = null;

    // Load data on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadStatistics();
        loadFailedAttempts();
    });

    // Load statistics
    function loadStatistics() {
        fetch('/admin/security/statistics')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderStatistics(data.data);
                }
            })
            .catch(error => {
                console.error('Error loading statistics:', error);
            });
    }

    // Load failed attempts
    function loadFailedAttempts() {
        document.getElementById('loadingState').style.display = 'block';
        document.getElementById('attemptsList').style.display = 'block';

        fetch('/admin/security/failed-logins-data')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderFailedAttempts(data.data);
                    if (data.statistics) {
                        renderStatistics(data.statistics);
                    }
                } else {
                    showError(data.message);
                }
            })
            .catch(error => {
                console.error('Error loading failed attempts:', error);
                showError('Failed to load data');
            })
            .finally(() => {
                document.getElementById('loadingState').style.display = 'none';
            });
    }

    // Render statistics
    function renderStatistics(stats) {
        const grid = document.getElementById('statisticsGrid');
        grid.innerHTML = `
            <div class="stat-card">
                <div class="stat-number">${stats.today}</div>
                <div class="stat-label">Today</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.this_week}</div>
                <div class="stat-label">This Week</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.unique_ips_today}</div>
                <div class="stat-label">Unique IPs Today</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.blocked_ips}</div>
                <div class="stat-label">Blocked IPs</div>
            </div>
        `;
    }

    // Render failed attempts
    function renderFailedAttempts(attempts) {
        const container = document.getElementById('attemptsList');
        const emptyState = document.getElementById('emptyState');

        if (attempts.length === 0) {
            container.innerHTML = '';
            emptyState.style.display = 'block';
            return;
        }

        emptyState.style.display = 'none';
        
        const attemptsHTML = attempts.map(attempt => `
            <div class="attempt-item">
                <div class="attempt-info">
                    <h4>${escapeHtml(attempt.username)}</h4>
                    <p>${escapeHtml(attempt.email)} • ${escapeHtml(attempt.reason)}</p>
                    <p title="${escapeHtml(attempt.user_agent)}">${escapeHtml(attempt.user_agent)}</p>
                </div>
                <div class="attempt-ip">${escapeHtml(attempt.ip_address)}</div>
                <div class="attempt-time">${attempt.attempted_at}</div>
                <div class="attempt-status">
                    ${attempt.is_blocked ? 
                        `<span class="status-blocked">
                            <i class="fas fa-ban"></i> Blocked
                        </span>
                        <button class="unblock-btn" onclick="unblockIP('${attempt.ip_address}')">
                            Unblock
                        </button>` :
                        `<span class="status-normal">Normal</span>
                        <button class="block-btn" onclick="showBlockModal('${attempt.ip_address}')">
                            Block
                        </button>`
                    }
                </div>
            </div>
        `).join('');

        container.innerHTML = attemptsHTML;
    }

    // Show block IP modal
    function showBlockModal(ipAddress) {
        currentBlockIP = ipAddress;
        document.getElementById('blockIPAddress').textContent = ipAddress;
        new bootstrap.Modal(document.getElementById('blockIPModal')).show();
    }

    // Confirm block IP
    function confirmBlockIP() {
        if (!currentBlockIP) return;

        const duration = document.getElementById('blockDuration').value;

        fetch('/admin/security/block-ip', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                ip_address: currentBlockIP,
                minutes: duration
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                refreshData();
                bootstrap.Modal.getInstance(document.getElementById('blockIPModal')).hide();
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error blocking IP:', error);
            showError('Failed to block IP');
        });
    }

    // Unblock IP
    function unblockIP(ipAddress) {
        if (!confirm(`Are you sure you want to unblock ${ipAddress}?`)) {
            return;
        }

        fetch('/admin/security/unblock-ip', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                ip_address: ipAddress
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                refreshData();
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error unblocking IP:', error);
            showError('Failed to unblock IP');
        });
    }

    // Clean old attempts
    function cleanOldAttempts() {
        if (!confirm('Are you sure you want to clean old failed login attempts (older than 30 days)?')) {
            return;
        }

        fetch('/admin/security/clean-old-attempts', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                refreshData();
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error cleaning old attempts:', error);
            showError('Failed to clean old attempts');
        });
    }

    // Refresh data
    function refreshData() {
        loadStatistics();
        loadFailedAttempts();
    }

    // Utility functions
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function showSuccess(message) {
        // Use the common toast function from smartflo-common.js
        if (typeof showToast === 'function') {
            showToast(message, 'success');
        } else {
            alert(message);
        }
    }

    function showError(message) {
        // Use the common toast function from smartflo-common.js
        if (typeof showToast === 'function') {
            showToast(message, 'error');
        } else {
            alert(message);
        }
    }
</script>
<?= $this->endSection() ?>
