<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Activity Log - SmartFlo' ?></title>
    
    <!-- Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary: #6366f1;
            --secondary: #8b5cf6;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --info: #3b82f6;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-600: #475569;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
        }
        
        body {
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .activity-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1rem;
            border-bottom: 1px solid var(--gray-200);
            transition: all 0.2s;
        }
        
        .activity-item:hover {
            background: var(--gray-50);
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            flex-shrink: 0;
        }
        
        .activity-icon.success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }
        
        .activity-icon.warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning);
        }
        
        .activity-icon.info {
            background: rgba(59, 130, 246, 0.1);
            color: var(--info);
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-message {
            font-size: 0.875rem;
            color: var(--gray-900);
            margin-bottom: 0.25rem;
        }
        
        .activity-time {
            font-size: 0.75rem;
            color: var(--gray-600);
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Activity Log</h1>
                    <a href="/dashboard" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Dashboard
                    </a>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Recent System Activity</h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($activities)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-info-circle text-muted" style="font-size: 3rem;"></i>
                                <p class="text-muted mt-3">No recent activity to display.</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($activities as $activity): ?>
                                <?php
                                // Convert activity timestamp to IST
                                $activityTimeIST = null;
                                if ($activity['timestamp']) {
                                    $activityUTC = new DateTime($activity['timestamp'], new DateTimeZone('UTC'));
                                    $activityUTC->setTimezone(new DateTimeZone('Asia/Kolkata'));
                                    $activityTimeIST = $activityUTC->format('M j, Y \a\t g:i A') . ' IST';
                                }
                                ?>
                                <div class="activity-item">
                                    <div class="activity-icon <?= $activity['color'] ?>">
                                        <i class="<?= $activity['icon'] ?>"></i>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-message"><?= htmlspecialchars($activity['message']) ?></div>
                                        <div class="activity-time"><?= $activityTimeIST ?: 'Unknown time' ?></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
