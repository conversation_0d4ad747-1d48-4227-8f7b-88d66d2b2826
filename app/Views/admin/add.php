<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Add New Item - SmartFlo</title>

    <!-- Cache busting for mobile optimizations -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">

    <!-- PWA Meta Tags -->
    <meta name="application-name" content="SmartFlo">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="SmartFlo">
    <meta name="description" content="Add new users, items, and content">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="msapplication-config" content="/browserconfig.xml">
    <meta name="msapplication-TileColor" content="#667eea">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="theme-color" content="#667eea">

    <!-- PWA Icons -->
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="192x192" href="/icons/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-16x16.png">
    <link rel="manifest" href="/manifest.json">
    <link rel="mask-icon" href="/icons/safari-pinned-tab.svg" color="#667eea">
    <link rel="shortcut icon" href="/favicon.ico">

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --vh: 1vh;
            /* Modern Color Palette - Matching Dashboard */
            --primary: #667eea;
            --primary-light: rgba(102, 126, 234, 0.1);
            --secondary: #764ba2;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --success: #10b981;
            --error: #f56565;
            --warning: #f59e0b;
            --info: #3b82f6;

            /* Design Tokens */
            --border-radius: 16px;
            --border-radius-sm: 8px;
            --border-radius-lg: 24px;
            --shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
            --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-base: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 0.3s cubic-bezier(0.4, 0, 0.2, 1);

            /* Layout Variables */
            --sidebar-width: 280px;
            --header-height: 70px;
            --bottom-nav-height: 70px;
            --mobile-header-height: 60px;
        }

        html {
            height: 100%;
            height: calc(var(--vh, 1vh) * 100);
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            margin: 0;
            padding: 0;
            color: var(--gray-800);
            line-height: 1.6;
            min-height: 100vh;
            min-height: calc(var(--vh, 1vh) * 100);
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            position: relative;
            scroll-behavior: smooth;
        }

        /* ===== LAYOUT STRUCTURE ===== */
        .dashboard-container {
            display: flex;
            min-height: 100vh;
            min-height: calc(var(--vh, 1vh) * 100);
            background: var(--gray-50);
            position: relative;
        }

        /* ===== MAIN CONTENT AREA ===== */
        .main-content {
            flex: 1;
            padding: 2rem;
            min-height: 100vh;
            min-height: calc(var(--vh, 1vh) * 100);
            overflow-x: hidden;
            width: 100%;
            margin-left: var(--sidebar-width);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: var(--border-radius-lg);
            text-align: center;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            margin-bottom: 2rem;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
        }

        .card-body {
            padding: 1.5rem;
        }

        .btn {
            border-radius: var(--border-radius-sm);
            font-weight: 600;
            transition: all var(--transition-base);
            min-height: 44px;
            padding: 0.75rem 1.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            border: none;
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .coming-soon {
            text-align: center;
            padding: 3rem 2rem;
            color: var(--gray-600);
        }

        .coming-soon i {
            font-size: 4rem;
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .coming-soon h3 {
            color: var(--gray-800);
            margin-bottom: 1rem;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
                padding-bottom: calc(var(--bottom-nav-height) + 1rem);
            }

            .page-header {
                padding: 1.5rem 1rem;
                margin-bottom: 1.5rem;
            }

            .page-title {
                font-size: 1.5rem;
            }

            .page-subtitle {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-plus-circle me-3"></i>
                Add New Item
            </h1>
            <p class="page-subtitle">Create new users, content, and system items</p>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="coming-soon">
                    <i class="fas fa-plus-circle"></i>
                    <h3>Add New Items Coming Soon</h3>
                    <p>This feature is currently under development. You'll be able to:</p>
                    <ul style="text-align: left; max-width: 400px; margin: 0 auto;">
                        <li>Add new user accounts</li>
                        <li>Create content items</li>
                        <li>Upload files and documents</li>
                        <li>Configure system settings</li>
                    </ul>
                    <div class="mt-4">
                        <a href="/dashboard" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <nav class="bottom-nav" style="position: fixed; bottom: 0; left: 0; right: 0; height: var(--bottom-nav-height); background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(20px); border-top: 1px solid var(--gray-200); z-index: 1000; padding: 0;">
        <div style="display: grid; grid-template-columns: repeat(5, 1fr); height: 100%; align-items: center;">
            <a href="/dashboard" style="display: flex; flex-direction: column; align-items: center; text-decoration: none; color: var(--gray-500); font-size: 0.75rem; font-weight: 500; min-height: 44px; justify-content: center; padding: 0.5rem;">
                <i class="fas fa-tachometer-alt" style="font-size: 1.125rem; margin-bottom: 0.25rem;"></i>
                <span>Dashboard</span>
            </a>
            <a href="/admin/users" style="display: flex; flex-direction: column; align-items: center; text-decoration: none; color: var(--gray-500); font-size: 0.75rem; font-weight: 500; min-height: 44px; justify-content: center; padding: 0.5rem;">
                <i class="fas fa-users" style="font-size: 1.125rem; margin-bottom: 0.25rem;"></i>
                <span>Users</span>
            </a>
            <a href="/admin/add" style="display: flex; flex-direction: column; align-items: center; text-decoration: none; color: var(--primary); font-size: 0.75rem; font-weight: 600; min-height: 44px; justify-content: center; padding: 0.5rem; position: relative;">
                <div style="position: absolute; top: -1px; left: 50%; transform: translateX(-50%); width: 24px; height: 3px; background: var(--primary); border-radius: 0 0 4px 4px;"></div>
                <i class="fas fa-plus-circle" style="font-size: 1.125rem; margin-bottom: 0.25rem;"></i>
                <span>Add</span>
            </a>
            <a href="/admin/system" style="display: flex; flex-direction: column; align-items: center; text-decoration: none; color: var(--gray-500); font-size: 0.75rem; font-weight: 500; min-height: 44px; justify-content: center; padding: 0.5rem;">
                <i class="fas fa-cog" style="font-size: 1.125rem; margin-bottom: 0.25rem;"></i>
                <span>Settings</span>
            </a>
            <a href="/auth/logout" onclick="return confirm('Are you sure you want to logout?')" style="display: flex; flex-direction: column; align-items: center; text-decoration: none; color: var(--error); font-size: 0.75rem; font-weight: 500; min-height: 44px; justify-content: center; padding: 0.5rem;">
                <i class="fas fa-sign-out-alt" style="font-size: 1.125rem; margin-bottom: 0.25rem;"></i>
                <span>Logout</span>
            </a>
        </div>
    </nav>

    <script>
        // Mobile viewport height fix
        function setVH() {
            let vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }
        
        window.addEventListener('resize', setVH);
        window.addEventListener('orientationchange', setVH);
        setVH();
    </script>
</body>
</html>
