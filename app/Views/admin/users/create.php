<?= $this->extend('layouts/app') ?>

<?= $this->section('styles') ?>
<style>
        /* Page-specific styles only */
        .role-checkbox {
            background: var(--gray-50);
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            transition: all var(--transition-fast);
        }

        .role-checkbox:hover {
            background: var(--gray-100);
        }

        .role-checkbox.selected {
            background: rgba(99, 102, 241, 0.1);
            border-color: var(--primary);
        }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>


<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-user-plus me-3"></i>
        Create New User
    </h1>
    <p class="page-subtitle">Add a new user account to the system</p>
</div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        User Information
                    </h5>
                </div>
                <div class="card-body">
                    <form id="createUserForm">
                        <?= csrf_field() ?>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    Username *
                                </label>
                                <input type="text" class="form-control" id="username" name="username" required>
                                <div class="invalid-feedback"></div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    Email Address *
                                </label>
                                <input type="email" class="form-control" id="email" name="email" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    Password *
                                </label>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <div class="invalid-feedback"></div>
                                <small class="form-text text-muted">
                                    Password must be at least 8 characters long
                                </small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="password_confirm" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    Confirm Password *
                                </label>
                                <input type="password" class="form-control" id="password_confirm" name="password_confirm" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                                <label class="form-check-label" for="is_active">
                                    <i class="fas fa-check-circle me-1"></i>
                                    Active User
                                </label>
                                <small class="form-text text-muted d-block">
                                    Active users can log in to the system
                                </small>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-user-tag me-1"></i>
                                Assign Role *
                            </label>
                            <div id="rolesContainer">
                                <?php if (!empty($roles)): ?>
                                    <?php foreach ($roles as $role): ?>
                                        <div class="role-checkbox">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio"
                                                       id="role_<?= $role['id'] ?>"
                                                       name="roles"
                                                       value="<?= $role['id'] ?>"
                                                       required>
                                                <label class="form-check-label" for="role_<?= $role['id'] ?>">
                                                    <strong><?= htmlspecialchars($role['role_name']) ?></strong>
                                                    <?php if ($role['description']): ?>
                                                        <br><small class="text-muted"><?= htmlspecialchars($role['description']) ?></small>
                                                    <?php endif; ?>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <p class="text-muted">No roles available. <a href="/admin/roles/create">Create a role first</a>.</p>
                                <?php endif; ?>
                            </div>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-save me-2"></i>
                                Create User
                            </button>
                            <a href="/admin/users" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
        // Mobile viewport height fix
        function setVH() {
            let vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }

        window.addEventListener('resize', setVH);
        window.addEventListener('orientationchange', setVH);
        setVH();

        // Form submission
        document.getElementById('createUserForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Clear previous errors
            clearErrors();

            // Note: Active status validation removed - users can be created as inactive

            // Get form data
            const formData = new FormData(this);

            // Disable submit button
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';

            // Submit form with CSRF retry logic
            function submitForm(retryCount = 0) {
                return fetch('/admin/users/store', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': formData.get('csrf_token')
                    }
                })
                .then(response => {
                    if (response.status === 403 && retryCount < 2) {
                        // CSRF token expired, refresh and retry
                        return fetch('/auth/csrf-token', {
                            method: 'GET',
                            headers: { 'X-Requested-With': 'XMLHttpRequest' }
                        })
                        .then(res => res.json())
                        .then(data => {
                            if (data.csrf_token) {
                                formData.set('csrf_token', data.csrf_token);
                                return submitForm(retryCount + 1);
                            }
                            throw new Error('Failed to refresh CSRF token');
                        });
                    }
                    return response;
                });
            }

            submitForm()
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                    // Redirect after success
                    setTimeout(() => {
                        window.location.href = data.redirect || '/admin/users';
                    }, 1500);
                } else {
                    showError(data.message || 'Failed to create user');
                    if (data.errors) {
                        displayErrors(data.errors);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (error.message.includes('Failed to fetch') || !navigator.onLine) {
                    showOfflineError('No internet connection. Please check your network and try again.');
                } else {
                    showError('An error occurred while creating the user: ' + error.message);
                }
            })
            .finally(() => {
                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });

        // Role radio button styling
        document.querySelectorAll('.role-checkbox input[type="radio"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // Remove selected class from all role boxes
                document.querySelectorAll('.role-checkbox').forEach(box => {
                    box.classList.remove('selected');
                });

                // Add selected class to the selected role box
                if (this.checked) {
                    const roleBox = this.closest('.role-checkbox');
                    roleBox.classList.add('selected');
                }
            });
        });

        // Password confirmation validation
        document.getElementById('password_confirm').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;

            if (confirmPassword && password !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
                this.classList.add('is-invalid');
            } else {
                this.setCustomValidity('');
                this.classList.remove('is-invalid');
            }
        });

        // Utility functions
        function clearErrors() {
            document.querySelectorAll('.is-invalid').forEach(el => {
                el.classList.remove('is-invalid');
            });
            document.querySelectorAll('.invalid-feedback').forEach(el => {
                el.textContent = '';
            });
        }

        function displayErrors(errors) {
            Object.keys(errors).forEach(field => {
                const input = document.querySelector(`[name="${field}"]`);
                if (input) {
                    input.classList.add('is-invalid');
                    const feedback = input.parentNode.querySelector('.invalid-feedback');
                    if (feedback) {
                        feedback.textContent = errors[field];
                    }
                }
            });
        }

        function showSuccess(message) {
            const toast = createToast('success', message);
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        }

        function showError(message) {
            const toast = createToast('error', message);
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        }

        function createToast(type, message) {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            return toast;
        }

        // Placeholder functions for header buttons
        function showNotifications() {
            console.log('Show notifications panel');
        }

        function showUserMenu() {
            console.log('Show user menu');
        }

        // Mobile sidebar toggle
        const sidebar = document.getElementById('sidebar');
        const sidebarToggle = document.getElementById('sidebarToggle');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('show');
            });
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 991 && sidebar.classList.contains('show')) {
                if (!sidebar.contains(e.target) && !sidebarToggle?.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });
</script>
<?= $this->endSection() ?>
