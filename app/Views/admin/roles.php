<?= $this->extend('layouts/app') ?>

<?= $this->section('styles') ?>
<style>
        /* Role-specific styles only */
        .role-card {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
            transition: all var(--transition-fast);
            position: relative;
            overflow: hidden;
            margin-bottom: var(--space-md);
        }

        .role-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary);
        }

        .role-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-md);
        }

        .role-name {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-900);
        }

        .role-description {
            color: var(--gray-600);
            margin-bottom: var(--space-md);
        }

        .permission-badge {
            background: var(--primary);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            display: inline-block;
        }

        .user-count {
            background: var(--gray-100);
            color: var(--gray-700);
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-sm);
            font-size: 0.875rem;
        }

        .permission-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-md);
            margin-bottom: var(--space-md);
        }

        .permission-group {
            background: var(--gray-50);
            border-radius: var(--radius-md);
            padding: var(--space-md);
        }

        .permission-group-title {
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        .permission-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .permission-item input[type="checkbox"] {
            margin-right: 0.5rem;
        }

        .permission-item label {
            font-size: 0.875rem;
            color: var(--gray-700);
            margin: 0;
        }

        .role-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: var(--space-lg);
            margin-bottom: var(--space-xl);
        }

        @media (max-width: 768px) {
            .role-cards {
                grid-template-columns: 1fr;
                gap: var(--space-md);
            }
        }
    </style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-user-tag me-3"></i>
        Role Management
    </h1>
    <p class="page-subtitle">Manage user roles and permissions</p>
</div>

            <!-- Search and Actions -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <input type="text" id="roleSearch" class="form-control" placeholder="Search roles by name or description...">
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-primary" onclick="showCreateRoleModal()">
                                <i class="fas fa-plus me-2"></i>
                                Add New Role
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Roles Grid -->
            <div id="rolesContainer">
                <div id="roleCards" class="role-cards">
                    <!-- Roles will be loaded here via AJAX -->
                </div>

                <!-- Loading State -->
                <div id="loadingState" class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3 text-muted">Loading roles...</p>
                </div>

                <!-- Empty State -->
                <div id="emptyState" class="text-center py-5" style="display: none;">
                    <i class="fas fa-user-shield text-muted" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                    <h3 class="text-muted mb-3">No Roles Found</h3>
                    <p class="text-muted mb-4">No roles match your search criteria.</p>
                    <button class="btn btn-primary" onclick="showCreateRoleModal()">
                        <i class="fas fa-plus me-2"></i>
                        Create First Role
                    </button>
                </div>
            </div>


    <!-- Create/Edit Role Modal -->
    <div class="modal fade" id="roleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="roleModalTitle">Create Role</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="roleForm">
                        <input type="hidden" id="roleId" name="role_id">

                        <div class="mb-3">
                            <label for="roleName" class="form-label">Role Name</label>
                            <input type="text" class="form-control" id="roleName" name="name" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="mb-3">
                            <label for="roleDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="roleDescription" name="description" rows="3"></textarea>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Permissions</label>
                            <div id="permissionsContainer" class="permission-grid">
                                <!-- Permissions will be loaded here -->
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveRole()">Save Role</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Confirm Delete Modal -->
    <div class="modal fade" id="confirmDeleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this role? This action cannot be undone.</p>
                    <p><strong>Role:</strong> <span id="deleteRoleName"></span></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" onclick="confirmDeleteRole()">Delete Role</button>
                </div>
            </div>
        </div>
    </div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
        let roles = [];
        let filteredRoles = [];
        let currentRoleId = null;

        // CSS for spinner animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);

        // Load roles on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadRoles();

            // Search functionality
            document.getElementById('roleSearch').addEventListener('input', function() {
                filterRoles(this.value);
            });
        });

        // Load roles from server
        function loadRoles() {
            document.getElementById('loadingState').style.display = 'block';
            document.getElementById('roleCards').style.display = 'none';
            document.getElementById('emptyState').style.display = 'none';

            fetch('/admin/roles/data')
                .then(response => response.json())
                .then(data => {
                    if (data.success !== false) {
                        roles = data.data || [];
                        filteredRoles = [...roles];
                        renderRoles();
                    } else {
                        showError('Failed to load roles: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error loading roles:', error);
                    showError('Failed to load roles. Please try again.');
                })
                .finally(() => {
                    document.getElementById('loadingState').style.display = 'none';
                });
        }

        // Filter roles based on search
        function filterRoles(searchTerm) {
            if (!searchTerm.trim()) {
                filteredRoles = [...roles];
            } else {
                const term = searchTerm.toLowerCase();
                filteredRoles = roles.filter(role =>
                    role.role_name.toLowerCase().includes(term) ||
                    (role.description && role.description.toLowerCase().includes(term))
                );
            }
            renderRoles();
        }

        // Render roles in card layout
        function renderRoles() {
            const container = document.getElementById('roleCards');
            const emptyState = document.getElementById('emptyState');

            if (filteredRoles.length === 0) {
                container.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            container.style.display = 'grid';
            emptyState.style.display = 'none';

            container.innerHTML = filteredRoles.map(role => createRoleCard(role)).join('');

            // Add event listeners to action buttons
            addRoleActionListeners();
        }

        // Create role card HTML
        function createRoleCard(role) {
            const userCountText = role.user_count == 1 ? '1 user' : `${role.user_count} users`;
            const canDelete = role.user_count == 0 && role.role_name !== 'admin';

            return `
                <div class="role-card">
                    <div class="role-header">
                        <div>
                            <h3 class="role-name">
                                <i class="fas fa-user-shield" style="color: var(--primary); margin-right: var(--space-sm);"></i>
                                ${escapeHtml(role.role_name)}
                            </h3>
                            <p class="role-description">
                                ${role.description ? escapeHtml(role.description) : 'No description'}
                            </p>
                        </div>
                        <div>
                            ${role.actions}
                        </div>
                    </div>

                    <div class="mb-3">
                        ${role.permissions ? role.permissions.split(',').map(p => `<span class="permission-badge">${escapeHtml(p.trim())}</span>`).join('') : '<span class="text-muted">No permissions</span>'}
                    </div>

                    <div class="d-flex justify-content-between align-items-center">
                        <span class="user-count">
                            <i class="fas fa-users me-1"></i>
                            ${userCountText}
                        </span>
                        <small class="text-muted">
                            Created: ${role.created_at || 'N/A'}
                        </small>
                    </div>
                </div>
            `;
        }

        // Add event listeners to action buttons
        function addRoleActionListeners() {
            // View buttons
            document.querySelectorAll('a[href*="/admin/roles/view/"]').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // Allow default navigation
                });
            });

            // Edit buttons
            document.querySelectorAll('a[href*="/admin/roles/edit/"]').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // Allow default navigation
                });
            });

            // Delete buttons
            document.querySelectorAll('.delete-role').forEach(btn => {
                btn.addEventListener('click', function() {
                    const roleId = this.dataset.id;
                    const roleName = this.dataset.name;
                    deleteRole(roleId, roleName);
                });
            });
        }







        // Utility functions
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function showSuccess(message) {
            const toast = createToast('success', message);
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        }

        function showError(message) {
            const toast = createToast('error', message);
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        }

        function createToast(type, message) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;
                background: ${type === 'success' ? 'var(--success)' : 'var(--error)'};
                color: white; padding: var(--space-md) var(--space-lg);
                border-radius: var(--radius-md); box-shadow: var(--shadow-lg);
                display: flex; align-items: center; gap: var(--space-sm);
            `;
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                ${message}
                <button onclick="this.parentElement.remove()" style="background: none; border: none; color: white; margin-left: auto; cursor: pointer;">×</button>
            `;
            return toast;
        }

        // Edit role function
        function editRole(roleId) {
            const role = roles.find(r => r.id === roleId);
            if (!role) {
                showError('Role not found');
                return;
            }

            document.getElementById('roleModalTitle').textContent = 'Edit Role';
            document.getElementById('roleId').value = role.id;
            document.getElementById('roleName').value = role.role_name;
            document.getElementById('roleDescription').value = role.description || '';

            loadPermissions(role.permissions ? role.permissions.split(',').map(p => p.trim()) : []);
            new bootstrap.Modal(document.getElementById('roleModal')).show();
        }

        // Show create role modal
        function showCreateRoleModal() {
            document.getElementById('roleModalTitle').textContent = 'Create Role';
            document.getElementById('roleForm').reset();
            document.getElementById('roleId').value = '';
            loadPermissions();
            new bootstrap.Modal(document.getElementById('roleModal')).show();
        }

        // Load permissions for role modal
        function loadPermissions(selectedPermissions = []) {
            // Define all available permissions
            const allPermissions = [
                'user.create', 'user.read', 'user.update', 'user.delete',
                'role.create', 'role.read', 'role.update', 'role.delete',
                'system.read', 'system.update', 'system.backup', 'system.restore',
                'dashboard.read', 'dashboard.analytics',
                'notification.create', 'notification.read', 'notification.update', 'notification.delete',
                'audit.read', 'audit.export',
                'settings.read', 'settings.update',
                'admin.access'
            ];

            renderPermissions(allPermissions, selectedPermissions);
        }

        // Render permissions with all available options
        function renderPermissions(permissions, selectedPermissions = []) {
            const container = document.getElementById('permissionsContainer');

            // Group permissions by category
            const grouped = {};
            permissions.forEach(permission => {
                const category = permission.split('.')[0] || 'general';
                if (!grouped[category]) {
                    grouped[category] = [];
                }
                grouped[category].push(permission);
            });

            const permissionHTML = Object.keys(grouped).map(category => `
                <div class="permission-group">
                    <div class="permission-group-title">${category.charAt(0).toUpperCase() + category.slice(1)}</div>
                    ${grouped[category].map(permission => `
                        <div class="permission-item">
                            <input type="checkbox" id="perm_${permission}" name="permissions[]" value="${permission}"
                                   ${selectedPermissions.includes(permission) ? 'checked' : ''}>
                            <label for="perm_${permission}">${permission}</label>
                        </div>
                    `).join('')}
                </div>
            `).join('');

            container.innerHTML = permissionHTML;
        }

        // Save role
        function saveRole() {
            const form = document.getElementById('roleForm');
            const formData = new FormData(form);

            // Get selected permissions
            const permissions = Array.from(form.querySelectorAll('input[name="permissions[]"]:checked'))
                .map(cb => cb.value);

            // Clear previous permissions from FormData and add selected ones
            formData.delete('permissions[]');
            permissions.forEach(permission => {
                formData.append('permissions[]', permission);
            });

            const roleId = document.getElementById('roleId').value;
            const url = roleId ? `/admin/roles/update/${roleId}` : '/admin/roles/store';

            fetch(url, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                    loadRoles();
                    bootstrap.Modal.getInstance(document.getElementById('roleModal')).hide();
                } else {
                    showError(data.message);
                    if (data.errors) {
                        displayErrors(data.errors);
                    }
                }
            })
            .catch(error => {
                console.error('Error saving role:', error);
                showError('Failed to save role.');
            });
        }

        function displayErrors(errors) {
            Object.keys(errors).forEach(field => {
                const input = document.querySelector(`[name="${field}"]`);
                if (input) {
                    input.classList.add('is-invalid');
                    const feedback = input.parentNode.querySelector('.invalid-feedback');
                    if (feedback) {
                        feedback.textContent = errors[field];
                    }
                }
            });
        }

        // Delete role function
        function deleteRole(roleId, roleName) {
            currentRoleId = roleId;
            document.getElementById('deleteRoleName').textContent = roleName;
            new bootstrap.Modal(document.getElementById('confirmDeleteModal')).show();
        }

        // Confirm delete role
        function confirmDeleteRole() {
            if (!currentRoleId) return;

            fetch(`/admin/roles/delete/${currentRoleId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                    loadRoles();
                    bootstrap.Modal.getInstance(document.getElementById('confirmDeleteModal')).hide();
                } else {
                    showError(data.message);
                }
            })
            .catch(error => {
                console.error('Error deleting role:', error);
                showError('Failed to delete role.');
            });
        }

</script>
<?= $this->endSection() ?>
