<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?= csrf_hash() ?>">
    <title>CSRF Test - SmartFlo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>SmartFlo CSRF Testing</h1>
        
        <div class="test-section">
            <h2>1. Current CSRF Token</h2>
            <p>Meta Tag Token: <code id="metaToken"><?= csrf_hash() ?></code></p>
            <p>Form Token: <code id="formToken"><?= csrf_hash() ?></code></p>
            <button class="btn btn-primary" onclick="refreshToken()">Refresh CSRF Token</button>
            <div id="tokenResult" class="result"></div>
        </div>

        <div class="test-section">
            <h2>2. Test Project Creation Form</h2>
            <form id="testProjectForm">
                <?= csrf_field() ?>
                <div class="row">
                    <div class="col-md-6">
                        <label for="project_id" class="form-label">Project ID</label>
                        <input type="text" class="form-control" id="project_id" name="project_id" value="TEST-<?= time() ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label for="project_name" class="form-label">Project Name</label>
                        <input type="text" class="form-control" id="project_name" name="project_name" value="Test Project <?= date('Y-m-d H:i:s') ?>" required>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <label for="client_mobile" class="form-label">Client Mobile</label>
                        <input type="text" class="form-control" id="client_mobile" name="client_mobile" value="+91 98765 43210">
                    </div>
                    <div class="col-md-6">
                        <label for="start_date" class="form-label">Start Date</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="<?= date('Y-m-d') ?>" required>
                    </div>
                </div>
                <div class="mt-3">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="form-control" id="description" name="description">Test project description</textarea>
                </div>
                
                <!-- Hidden fields for task assignment -->
                <input type="hidden" name="task_category[]" value="office">
                <input type="hidden" name="task_type[]" value="planning">
                <input type="hidden" name="assigned_to[]" value="1">
                <input type="hidden" name="target_days[]" value="7">
                
                <button type="submit" class="btn btn-success mt-3">Create Test Project</button>
            </form>
            <div id="projectResult" class="result"></div>
        </div>

        <div class="test-section">
            <h2>3. Raw CSRF Test</h2>
            <button class="btn btn-warning" onclick="testRawCSRF()">Test Raw CSRF Request</button>
            <div id="rawResult" class="result"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Function to display results
        function displayResult(elementId, type, message, data = null) {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
            if (data) {
                element.textContent += '\n\nData: ' + JSON.stringify(data, null, 2);
            }
        }

        // Refresh CSRF token
        function refreshToken() {
            fetch('/auth/csrf-token', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.csrf_token) {
                    // Update meta tag
                    document.querySelector('meta[name="csrf-token"]').setAttribute('content', data.csrf_token);
                    // Update form token
                    document.querySelector('input[name="csrf_token"]').value = data.csrf_token;
                    // Update display
                    document.getElementById('metaToken').textContent = data.csrf_token;
                    document.getElementById('formToken').textContent = data.csrf_token;
                    displayResult('tokenResult', 'success', 'CSRF token refreshed successfully', data);
                } else {
                    displayResult('tokenResult', 'error', 'Failed to refresh CSRF token', data);
                }
            })
            .catch(error => {
                displayResult('tokenResult', 'error', 'Error: ' + error.message);
            });
        }

        // Test project creation
        document.getElementById('testProjectForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            // Log form data
            console.log('Form data being sent:');
            for (let [key, value] of formData.entries()) {
                console.log(`${key}: ${value}`);
            }
            
            fetch('/projects/create', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    displayResult('projectResult', 'success', 'Project created successfully!', data);
                } else {
                    displayResult('projectResult', 'error', 'Project creation failed: ' + data.message, data);
                }
            })
            .catch(error => {
                displayResult('projectResult', 'error', 'Network error: ' + error.message);
            });
        });

        // Test raw CSRF
        function testRawCSRF() {
            const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            
            fetch('/projects/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': token
                },
                body: JSON.stringify({
                    project_id: 'RAW-TEST-' + Date.now(),
                    project_name: 'Raw Test Project',
                    start_date: new Date().toISOString().split('T')[0],
                    task_category: ['office'],
                    task_type: ['planning'],
                    assigned_to: ['1'],
                    target_days: ['7']
                })
            })
            .then(response => {
                console.log('Raw response status:', response.status);
                return response.json();
            })
            .then(data => {
                displayResult('rawResult', data.success ? 'success' : 'error', 
                    'Raw CSRF test result: ' + (data.message || 'Unknown'), data);
            })
            .catch(error => {
                displayResult('rawResult', 'error', 'Raw CSRF test error: ' + error.message);
            });
        }
    </script>
</body>
</html>
