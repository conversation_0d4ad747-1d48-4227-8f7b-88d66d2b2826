<?= $this->extend('layouts/app') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Page Header -->
            <div class="page-header-create">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <h1 class="page-title mb-0">
                            <i class="fas fa-edit me-3"></i>
                            Edit Project: <?= esc($project['project_name']) ?>
                        </h1>
                    </div>
                    <div>
                        <a href="/projects" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Projects
                        </a>
                        <a href="/projects/view/<?= $project['id'] ?>" class="btn btn-outline-primary">
                            <i class="fas fa-eye me-2"></i>View Details
                        </a>
                    </div>
                </div>
            </div>

            <!-- Project Edit Form -->
            <div class="creation-form-container">
                <?php if (session('errors')): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach (session('errors') as $error): ?>
                        <li><?= esc($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>

                <?php if (session('error')): ?>
                <div class="alert alert-danger">
                    <?= session('error') ?>
                </div>
                <?php endif; ?>

                <form id="editProjectForm" method="POST" action="/projects/update/<?= $project['id'] ?>">
                    <?= csrf_field() ?>

                    <!-- Progress Steps -->
                    <div class="steps-container">
                        <div class="step-item active" data-step="1">
                            <div class="step-content">
                                <span class="step-number">1</span>
                                <span class="step-label">Project Details</span>
                            </div>
                        </div>
                        <div class="step-line"></div>
                        <div class="step-item" data-step="2">
                            <div class="step-content">
                                <span class="step-number">2</span>
                                <span class="step-label">Task Management</span>
                            </div>
                        </div>
                    </div>

                    <!-- Step 1: Project Details -->
                    <div class="form-section active" data-step="1">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="project_id" class="form-label">Project ID</label>
                                    <input type="text" class="form-control" id="project_id"
                                           value="<?= esc($project['project_id']) ?>" readonly>
                                    <small class="text-muted">Project ID cannot be changed</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="project_name" class="form-label required">Project Name</label>
                                    <input type="text" class="form-control" id="project_name" name="project_name"
                                           value="<?= esc(old('project_name', $project['project_name'])) ?>" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="start_date" class="form-label required">Start Date</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date"
                                           value="<?= esc(old('start_date', $project['start_date'])) ?>" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="client_mobile" class="form-label">Client Mobile</label>
                                    <input type="tel" class="form-control" id="client_mobile" name="client_mobile"
                                           value="<?= esc(old('client_mobile', $project['client_mobile'])) ?>"
                                           placeholder="+91 98765 43210">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="location" class="form-label">Project Location</label>
                                    <input type="text" class="form-control" id="location" name="location"
                                           value="<?= esc(old('location', $project['location'])) ?>"
                                           placeholder="https://maps.google.com/maps?q=location">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description" class="form-label">Project Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"
                                      placeholder="Detailed project description..."><?= esc(old('description', $project['description'])) ?></textarea>
                        </div>
                    </div>

                    <!-- Step 2: Task Management -->
                    <div class="form-section" data-step="2">
                        <div id="existing-tasks">
                            <!-- Existing tasks will be loaded here -->
                        </div>

                        <div class="text-center mb-3">
                            <button type="button" class="btn btn-outline-primary" id="add-new-task">
                                <i class="fas fa-plus me-2"></i>
                                Add New Task
                            </button>
                        </div>

                        <!-- Add Task Modal -->
                        <div class="modal fade" id="taskModal" tabindex="-1" aria-labelledby="taskModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="taskModalLabel">Add New Task</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <form id="taskForm">
                                            <input type="hidden" id="task-id" name="task_id">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="task-type" class="form-label required">Task Type</label>
                                                        <select class="form-select" id="task-type" name="task_type_id" required>
                                                            <option value="">Select Task Type</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="task-name" class="form-label required">Task Name</label>
                                                        <input type="text" class="form-control" id="task-name" name="task_name" required>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label for="task-description" class="form-label">Description</label>
                                                <textarea class="form-control" id="task-description" name="description" rows="3"></textarea>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="task-assignee" class="form-label required">Assigned To</label>
                                                        <select class="form-select" id="task-assignee" name="assigned_to" required>
                                                            <option value="">Select Assignee</option>
                                                            <?php foreach ($users as $user_option): ?>
                                                            <option value="<?= $user_option['id'] ?>"><?= esc($user_option['username']) ?></option>
                                                            <?php endforeach; ?>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="task-priority" class="form-label required">Priority</label>
                                                        <select class="form-select" id="task-priority" name="priority" required>
                                                            <option value="low">Low</option>
                                                            <option value="medium" selected>Medium</option>
                                                            <option value="high">High</option>
                                                            <option value="urgent">Urgent</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="task-target-days" class="form-label">Target Days</label>
                                                        <input type="number" class="form-control" id="task-target-days" name="target_days" min="1">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label for="task-depends-on" class="form-label">Depends On</label>
                                                <select class="form-select" id="task-depends-on" name="depends_on">
                                                    <option value="">No Dependencies</option>
                                                </select>
                                                <small class="text-muted">Select a task that must be completed before this task can start</small>
                                            </div>
                                        </form>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <button type="button" class="btn btn-primary" id="save-task">
                                            <span class="spinner-border spinner-border-sm me-2" style="display: none;"></span>
                                            Save Task
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Navigation -->
                    <div class="form-navigation">
                        <button type="button" class="btn btn-secondary" id="prev-btn" style="display: none;">
                            <i class="fas fa-arrow-left me-2"></i>
                            Previous
                        </button>
                        <div class="nav-spacer"></div>
                        <div class="created-info">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Created: <?= date('M d, Y H:i', strtotime($project['created_at'])) ?>
                            </small>
                        </div>
                        <button type="button" class="btn btn-outline-secondary" onclick="window.location.href='/projects'">
                            Cancel
                        </button>
                        <button type="button" class="btn btn-primary" id="next-btn">
                            Next
                            <i class="fas fa-arrow-right ms-2"></i>
                        </button>
                        <button type="submit" class="btn btn-success" id="submit-btn" style="display: none;">
                            <span class="spinner-border spinner-border-sm me-2" style="display: none;"></span>
                            <i class="fas fa-save me-2"></i>
                            Update Project
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentStep = 1;
    const totalSteps = 2;

    // Initialize form
    initializeForm();
    loadExistingTasks();
    loadTaskTypes();

    function initializeForm() {
        updateStepDisplay();

        // Navigation buttons
        document.getElementById('next-btn').addEventListener('click', nextStep);
        document.getElementById('prev-btn').addEventListener('click', prevStep);

        // Task management buttons
        document.getElementById('add-new-task').addEventListener('click', showAddTaskModal);
        document.getElementById('save-task').addEventListener('click', saveTask);

        // Form validation
        const form = document.getElementById('editProjectForm');
        form.addEventListener('submit', handleFormSubmit);
    }

    function updateStepDisplay() {
        // Update step indicators
        document.querySelectorAll('.step-item').forEach((step, index) => {
            const stepNumber = index + 1;
            step.classList.toggle('active', stepNumber === currentStep);
            step.classList.toggle('completed', stepNumber < currentStep);
        });

        // Update step lines
        document.querySelectorAll('.step-line').forEach((line, index) => {
            line.classList.toggle('completed', index + 1 < currentStep);
        });

        // Show/hide form sections
        document.querySelectorAll('.form-section').forEach((section, index) => {
            const stepNumber = index + 1;
            section.classList.toggle('active', stepNumber === currentStep);
        });

        // Update navigation buttons
        document.getElementById('prev-btn').style.display = currentStep > 1 ? 'block' : 'none';
        document.getElementById('next-btn').style.display = currentStep < totalSteps ? 'block' : 'none';
        document.getElementById('submit-btn').style.display = currentStep === totalSteps ? 'block' : 'none';
    }

    function nextStep() {
        if (currentStep < totalSteps) {
            currentStep++;
            updateStepDisplay();
        }
    }

    function prevStep() {
        if (currentStep > 1) {
            currentStep--;
            updateStepDisplay();
        }
    }

    function loadExistingTasks() {
        // Load existing tasks for this project
        fetch(`/projects/getTasks/<?= $project['id'] ?>`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayExistingTasks(data.tasks);
                }
            })
            .catch(error => {
                console.error('Error loading tasks:', error);
            });
    }

    function displayExistingTasks(tasks) {
        const container = document.getElementById('existing-tasks');
        if (tasks.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">No tasks found for this project.</p>';
            return;
        }

        container.innerHTML = tasks.map(task => `
            <div class="task-edit-card" data-task-id="${task.id}">
                <div class="task-header">
                    <h6 class="task-title">
                        <i class="fas fa-tasks me-2"></i>
                        ${task.task_name}
                    </h6>
                    <div class="task-actions">
                        <button type="button" class="btn btn-sm btn-outline-primary edit-task" data-task-id="${task.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger delete-task" data-task-id="${task.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="task-details">
                    <div class="row">
                        <div class="col-md-3">
                            <small class="text-muted">Assignee:</small><br>
                            <span>${task.assigned_username || 'Unassigned'}</span>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">Status:</small><br>
                            <span class="badge badge-${task.status}">${task.status.replace('_', ' ')}</span>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">Priority:</small><br>
                            <span>${task.priority || 'Medium'}</span>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">Target Days:</small><br>
                            <span>${task.target_days || 'N/A'}</span>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        // Add event listeners for edit and delete buttons
        container.querySelectorAll('.edit-task').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const taskId = e.target.closest('.edit-task').dataset.taskId;
                editTask(taskId);
            });
        });

        container.querySelectorAll('.delete-task').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const taskId = e.target.closest('.delete-task').dataset.taskId;
                deleteTask(taskId);
            });
        });
    }

    function handleFormSubmit(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const submitBtn = document.getElementById('submit-btn');
        const spinner = submitBtn.querySelector('.spinner-border');

        // Show loading state
        submitBtn.disabled = true;
        spinner.style.display = 'inline-block';

        fetch(e.target.action, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message and redirect
                alert('Project updated successfully!');
                window.location.href = '/projects';
            } else {
                alert('Error: ' + (data.message || 'Failed to update project'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the project');
        })
        .finally(() => {
            // Hide loading state
            submitBtn.disabled = false;
            spinner.style.display = 'none';
        });
    }

    // Task Management Functions
    function loadTaskTypes() {
        fetch('/projects/getTaskTypes')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const select = document.getElementById('task-type');
                    select.innerHTML = '<option value="">Select Task Type</option>';
                    data.task_types.forEach(type => {
                        select.innerHTML += `<option value="${type.id}">${type.name}</option>`;
                    });
                }
            })
            .catch(error => {
                console.error('Error loading task types:', error);
            });
    }

    function showAddTaskModal() {
        document.getElementById('taskModalLabel').textContent = 'Add New Task';
        document.getElementById('taskForm').reset();
        document.getElementById('task-id').value = '';
        updateTaskDependencies();
        new bootstrap.Modal(document.getElementById('taskModal')).show();
    }

    function editTask(taskId) {
        // Find task data from existing tasks
        fetch(`/projects/getTasks/<?= $project['id'] ?>`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const task = data.tasks.find(t => t.id == taskId);
                    if (task) {
                        document.getElementById('taskModalLabel').textContent = 'Edit Task';
                        document.getElementById('task-id').value = task.id;
                        document.getElementById('task-type').value = task.task_type_id;
                        document.getElementById('task-name').value = task.task_name;
                        document.getElementById('task-description').value = task.description || '';
                        document.getElementById('task-assignee').value = task.assigned_to;
                        document.getElementById('task-priority').value = task.priority;
                        document.getElementById('task-target-days').value = task.target_days || '';
                        document.getElementById('task-depends-on').value = task.depends_on || '';
                        updateTaskDependencies(taskId);
                        new bootstrap.Modal(document.getElementById('taskModal')).show();
                    }
                }
            })
            .catch(error => {
                console.error('Error loading task:', error);
                alert('Error loading task details');
            });
    }

    function updateTaskDependencies(excludeTaskId = null) {
        fetch(`/projects/getTasks/<?= $project['id'] ?>`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const select = document.getElementById('task-depends-on');
                    select.innerHTML = '<option value="">No Dependencies</option>';
                    data.tasks.forEach(task => {
                        if (task.id != excludeTaskId) {
                            select.innerHTML += `<option value="${task.id}">${task.task_name}</option>`;
                        }
                    });
                }
            })
            .catch(error => {
                console.error('Error loading task dependencies:', error);
            });
    }

    function saveTask() {
        const form = document.getElementById('taskForm');
        const formData = new FormData(form);
        const taskId = document.getElementById('task-id').value;
        const saveBtn = document.getElementById('save-task');
        const spinner = saveBtn.querySelector('.spinner-border');

        // Show loading state
        saveBtn.disabled = true;
        spinner.style.display = 'inline-block';

        const url = taskId ? `/projects/updateTask/${taskId}` : `/projects/addTask/<?= $project['id'] ?>`;
        const method = 'POST';

        fetch(url, {
            method: method,
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                bootstrap.Modal.getInstance(document.getElementById('taskModal')).hide();
                loadExistingTasks(); // Reload tasks
                alert(taskId ? 'Task updated successfully!' : 'Task added successfully!');
            } else {
                alert('Error: ' + (data.message || 'Failed to save task'));
                if (data.errors) {
                    console.error('Validation errors:', data.errors);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while saving the task');
        })
        .finally(() => {
            // Hide loading state
            saveBtn.disabled = false;
            spinner.style.display = 'none';
        });
    }

    function deleteTask(taskId) {
        if (!confirm('Are you sure you want to delete this task?')) {
            return;
        }

        fetch(`/projects/deleteTask/${taskId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadExistingTasks(); // Reload tasks
                alert('Task deleted successfully!');
            } else {
                alert('Error: ' + (data.message || 'Failed to delete task'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the task');
        });
    }
});
</script>
<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
/* Import the complete styling from create project page */
.page-header-create {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.creation-form-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    padding: 2.5rem;
    margin-bottom: 2rem;
}

/* Steps styling */
.steps-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 3rem;
    padding: 0 2rem;
}

.step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.step-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.step-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    border: 3px solid #e9ecef;
}

.step-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #6c757d;
    transition: all 0.3s ease;
}

.step-item.active .step-number {
    background: #007bff;
    color: white;
    border-color: #007bff;
    transform: scale(1.1);
}

.step-item.active .step-label {
    color: #007bff;
}

.step-item.completed .step-number {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.step-item.completed .step-label {
    color: #28a745;
}

.step-line {
    flex: 1;
    height: 3px;
    background: #e9ecef;
    margin: 0 1rem;
    position: relative;
    top: -25px;
    transition: all 0.3s ease;
}

.step-line.completed {
    background: #28a745;
}

/* Form sections */
.form-section {
    display: none;
}

.form-section.active {
    display: block;
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form styling */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    display: block;
}

.form-label.required::after {
    content: " *";
    color: #dc3545;
}

.form-control, .form-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background-color: #fff;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
    outline: none;
}

.form-control.is-invalid, .form-select.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.15);
}

.form-control.is-valid, .form-select.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.15);
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #28a745;
}

/* Navigation */
.form-navigation {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid #e9ecef;
}

.nav-spacer {
    flex: 1;
}

.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-color: #007bff;
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    border-color: #28a745;
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* Task edit cards */
.task-edit-card {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.task-edit-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.1);
}

.task-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
}

.task-title {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
}

.task-details {
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
}

.badge {
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
}

.badge-not_started {
    background-color: #6c757d;
    color: white;
}

.badge-in_progress {
    background-color: #007bff;
    color: white;
}

.badge-completed {
    background-color: #28a745;
    color: white;
}

.badge-on_hold {
    background-color: #ffc107;
    color: #212529;
}

/* Responsive design */
@media (max-width: 768px) {
    .creation-form-container {
        padding: 1.5rem;
    }

    .steps-container {
        padding: 0 1rem;
    }

    .step-number {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .form-navigation {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-spacer {
        display: none;
    }
}
</style>
<?= $this->endSection() ?>
