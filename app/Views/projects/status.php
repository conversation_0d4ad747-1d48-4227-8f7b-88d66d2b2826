<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-building me-2"></i>
                    Construction Projects
                </h1>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createProjectModal">
                    <i class="fas fa-plus me-2"></i>
                    New Project
                </button>
            </div>

            <!-- Project Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Projects</h6>
                                    <h3 class="mb-0" id="totalProjects">0</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-building fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Active Projects</h6>
                                    <h3 class="mb-0" id="activeProjects">0</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-hammer fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Completed</h6>
                                    <h3 class="mb-0" id="completedProjects">0</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Avg Progress</h6>
                                    <h3 class="mb-0" id="avgProgress">0%</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project Filters -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <select class="form-select" id="statusFilter" onchange="filterProjects()">
                                        <option value="">All Status</option>
                                        <option value="planning">Planning</option>
                                        <option value="in_progress">In Progress</option>
                                        <option value="on_hold">On Hold</option>
                                        <option value="completed">Completed</option>
                                        <option value="cancelled">Cancelled</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <input type="text" class="form-control" id="clientFilter" placeholder="Filter by client..." onkeyup="filterProjects()">
                                </div>
                                <div class="col-md-3">
                                    <input type="text" class="form-control" id="searchFilter" placeholder="Search projects..." onkeyup="filterProjects()">
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-outline-secondary" onclick="clearFilters()">
                                        <i class="fas fa-times me-2"></i>
                                        Clear Filters
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Projects List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        Construction Projects
                    </h5>
                </div>
                <div class="card-body">
                    <div id="projectsList">
                        <div class="text-center py-4">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Project Modal -->
<div class="modal fade" id="createProjectModal" tabindex="-1" aria-labelledby="createProjectModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createProjectModalLabel">Create New Project</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createProjectForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="clientName" class="form-label">Client Name</label>
                                <input type="text" class="form-control" id="clientName" name="client_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="projectName" class="form-label">Project Name</label>
                                <input type="text" class="form-control" id="projectName" name="project_name" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="location" class="form-label">Location</label>
                        <input type="text" class="form-control" id="location" name="location" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="startDate" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="startDate" name="start_date" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="estimatedCompletion" class="form-label">Estimated Completion</label>
                                <input type="date" class="form-control" id="estimatedCompletion" name="estimated_completion">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="budget" class="form-label">Budget</label>
                                <input type="number" class="form-control" id="budget" name="budget" step="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="planning">Planning</option>
                            <option value="in_progress">In Progress</option>
                            <option value="on_hold">On Hold</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                    <input type="hidden" name="csrf_token" value="<?= csrf_hash() ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="createProjectBtn">
                        <i class="fas fa-save me-2"></i>
                        Create Project
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.project-card {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.project-card:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
}

.status-planning {
    background-color: #e3f2fd;
    color: #1565c0;
}

.status-in_progress {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.status-on_hold {
    background-color: #fff3e0;
    color: #ef6c00;
}

.status-completed {
    background-color: #f3e5f5;
    color: #7b1fa2;
}

.status-cancelled {
    background-color: #ffebee;
    color: #c62828;
}

.progress-bar-container {
    background-color: #e9ecef;
    border-radius: 0.5rem;
    height: 8px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 0.5rem;
    transition: width 0.3s ease;
}

.client-badge {
    background-color: #f8f9fa;
    color: #495057;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
}
</style>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
let allProjects = [];

document.addEventListener('DOMContentLoaded', function() {
    loadProjects();
    loadStats();

    // Create project form
    document.getElementById('createProjectForm').addEventListener('submit', function(e) {
        e.preventDefault();
        createProject();
    });
});

function loadProjects() {
    fetch('/projects/status/get-projects', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            allProjects = data.projects;
            displayProjects(allProjects);
        } else {
            displaySampleProjects();
        }
    })
    .catch(error => {
        console.error('Error loading projects:', error);
        displaySampleProjects();
    });
}

function displaySampleProjects() {
    const sampleProjects = [
        {
            id: 1,
            client_name: 'ABC Construction Ltd',
            project_name: 'Residential Complex - Phase 1',
            location: 'Downtown District, City Center',
            description: '50-unit residential complex with modern amenities',
            start_date: '2024-01-15',
            estimated_completion: '2024-12-31',
            budget: 2500000.00,
            actual_cost: 1800000.00,
            status: 'in_progress',
            progress_percentage: 65,
            last_update_notes: 'Foundation work completed, starting structural work',
            created_by_username: 'Project Manager',
            created_at: '2024-01-10 09:00:00'
        },
        {
            id: 2,
            client_name: 'XYZ Developers',
            project_name: 'Commercial Plaza',
            location: 'Business District, Main Street',
            description: 'Multi-story commercial plaza with retail and office spaces',
            start_date: '2024-03-01',
            estimated_completion: '2025-06-30',
            budget: 5000000.00,
            actual_cost: 1200000.00,
            status: 'in_progress',
            progress_percentage: 25,
            last_update_notes: 'Site preparation completed, excavation in progress',
            created_by_username: 'Site Manager',
            created_at: '2024-02-25 14:30:00'
        },
        {
            id: 3,
            client_name: 'Green Homes Inc',
            project_name: 'Eco-Friendly Villas',
            location: 'Suburban Area, Green Valley',
            description: 'Sustainable housing project with solar panels and rainwater harvesting',
            start_date: '2023-09-01',
            estimated_completion: '2024-08-31',
            budget: 3200000.00,
            actual_cost: 3100000.00,
            status: 'completed',
            progress_percentage: 100,
            last_update_notes: 'Project completed successfully, handover done',
            created_by_username: 'Project Director',
            created_at: '2023-08-20 11:15:00'
        }
    ];

    allProjects = sampleProjects;
    displayProjects(sampleProjects);
}

function displayProjects(projects) {
    const container = document.getElementById('projectsList');

    if (projects.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">No projects found</p>';
        return;
    }

    container.innerHTML = projects.map(project => `
        <div class="project-card">
            <div class="row">
                <div class="col-md-8">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h5 class="mb-1">${project.project_name}</h5>
                            <div class="client-badge mb-2">${project.client_name}</div>
                        </div>
                        <div class="text-end">
                            <span class="status-badge status-${project.status}">${project.status.replace('_', ' ')}</span>
                        </div>
                    </div>

                    <p class="text-muted mb-2">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        ${project.location}
                    </p>

                    ${project.description ? `<p class="mb-3">${project.description}</p>` : ''}

                    <div class="row text-sm">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="fas fa-calendar-alt me-1"></i>
                                Start: ${new Date(project.start_date).toLocaleDateString()}
                            </small>
                        </div>
                        <div class="col-md-6">
                            ${project.estimated_completion ? `
                                <small class="text-muted">
                                    <i class="fas fa-calendar-check me-1"></i>
                                    Est. Completion: ${new Date(project.estimated_completion).toLocaleDateString()}
                                </small>
                            ` : ''}
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="text-center mb-3">
                        <h6 class="mb-1">Progress</h6>
                        <div class="progress-bar-container mb-2">
                            <div class="progress-bar" style="width: ${project.progress_percentage}%"></div>
                        </div>
                        <span class="fw-bold">${project.progress_percentage}%</span>
                    </div>

                    ${project.budget ? `
                        <div class="text-center mb-3">
                            <small class="text-muted d-block">Budget</small>
                            <span class="fw-bold">$${parseFloat(project.budget).toLocaleString()}</span>
                        </div>
                    ` : ''}

                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="viewProject(${project.id})">
                            <i class="fas fa-eye me-2"></i>
                            View Details
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="updateProgress(${project.id})">
                            <i class="fas fa-chart-line me-2"></i>
                            Update Progress
                        </button>
                    </div>
                </div>
            </div>

            ${project.last_update_notes ? `
                <div class="mt-3 pt-3 border-top">
                    <small class="text-muted">
                        <i class="fas fa-sticky-note me-1"></i>
                        Latest Update: ${project.last_update_notes}
                    </small>
                </div>
            ` : ''}
        </div>
    `).join('');
}

function loadStats() {
    fetch('/projects/status/get-stats', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateStats(data.stats);
        } else {
            // Use sample stats
            updateStats({
                total_projects: 3,
                active_projects: 2,
                completed_projects: 1,
                average_progress: 63.3
            });
        }
    })
    .catch(error => {
        console.error('Error loading stats:', error);
        // Use sample stats
        updateStats({
            total_projects: 3,
            active_projects: 2,
            completed_projects: 1,
            average_progress: 63.3
        });
    });
}

function updateStats(stats) {
    document.getElementById('totalProjects').textContent = stats.total_projects || 0;
    document.getElementById('activeProjects').textContent = stats.active_projects || 0;
    document.getElementById('completedProjects').textContent = stats.completed_projects || 0;
    document.getElementById('avgProgress').textContent = (stats.average_progress || 0) + '%';
}

function filterProjects() {
    const statusFilter = document.getElementById('statusFilter').value;
    const clientFilter = document.getElementById('clientFilter').value.toLowerCase();
    const searchFilter = document.getElementById('searchFilter').value.toLowerCase();

    let filteredProjects = allProjects.filter(project => {
        const matchesStatus = !statusFilter || project.status === statusFilter;
        const matchesClient = !clientFilter || project.client_name.toLowerCase().includes(clientFilter);
        const matchesSearch = !searchFilter ||
            project.project_name.toLowerCase().includes(searchFilter) ||
            project.location.toLowerCase().includes(searchFilter) ||
            (project.description && project.description.toLowerCase().includes(searchFilter));

        return matchesStatus && matchesClient && matchesSearch;
    });

    displayProjects(filteredProjects);
}

function clearFilters() {
    document.getElementById('statusFilter').value = '';
    document.getElementById('clientFilter').value = '';
    document.getElementById('searchFilter').value = '';
    displayProjects(allProjects);
}

function createProject() {
    const form = document.getElementById('createProjectForm');
    const formData = new FormData(form);
    const createBtn = document.getElementById('createProjectBtn');

    createBtn.disabled = true;
    createBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';

    fetch('/projects/status/create', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('createProjectModal')).hide();
            form.reset();
            loadProjects();
            loadStats();
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error creating project:', error);
        showAlert('danger', 'Error creating project');
    })
    .finally(() => {
        createBtn.disabled = false;
        createBtn.innerHTML = '<i class="fas fa-save me-2"></i>Create Project';
    });
}

function viewProject(projectId) {
    window.location.href = `/projects/view/${projectId}`;
}

function updateProgress(projectId) {
    const project = allProjects.find(p => p.id === projectId);
    if (!project) return;

    const newProgress = prompt(`Update progress for "${project.project_name}":\nCurrent: ${project.progress_percentage}%\n\nEnter new progress (0-100):`, project.progress_percentage);

    if (newProgress === null) return;

    const progress = parseInt(newProgress);
    if (isNaN(progress) || progress < 0 || progress > 100) {
        showAlert('danger', 'Please enter a valid progress percentage (0-100)');
        return;
    }

    const notes = prompt('Add update notes (optional):');

    fetch(`/projects/status/update-progress/${projectId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            progress_percentage: progress,
            notes: notes,
            csrf_token: '<?= csrf_hash() ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            loadProjects();
            loadStats();
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error updating progress:', error);
        showAlert('danger', 'Error updating progress');
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
<?= $this->endSection() ?>
