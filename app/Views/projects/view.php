<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>
<style>
.timeline-task-name {
    font-size: 1.1em;
    margin-bottom: 8px;
    color: #2c3e50;
}

.timeline-action-line {
    font-size: 0.95em;
    margin-bottom: 6px;
    line-height: 1.4;
}

.timeline-timestamp {
    font-size: 0.85em;
    color: #6c757d;
    margin-bottom: 8px;
}

.timeline-timestamp i {
    margin-right: 4px;
}

.timeline-notes-compact {
    margin-top: 8px;
    padding: 6px 10px;
    background-color: #f8f9fa;
    border-left: 3px solid #dee2e6;
    border-radius: 4px;
}

.timeline-content {
    padding: 12px 16px;
}

.timeline-item {
    margin-bottom: 20px;
}

/* Timeline Label UI Styles */
.timeline-user-label {
    background-color: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.85em;
    font-weight: 500;
    display: inline-block;
    margin-right: 4px;
}

.timeline-status-label {
    padding: 2px 8px;
    border-radius: 8px;
    font-size: 0.85em;
    font-weight: 500;
    display: inline-block;
    margin: 0 2px;
}

.timeline-old-status {
    background-color: #6c757d;
    color: white;
}

.timeline-new-status {
    background-color: #28a745;
    color: white;
}

.timeline-duration-label {
    background-color: #17a2b8;
    color: white;
    padding: 2px 8px;
    border-radius: 8px;
    font-size: 0.8em;
    font-weight: 500;
    display: inline-block;
    margin-left: 4px;
}

.timeline-total-label {
    background-color: #ffc107;
    color: #212529;
    padding: 2px 8px;
    border-radius: 8px;
    font-size: 0.8em;
    font-weight: 500;
    display: inline-block;
    margin-left: 4px;
}
</style>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Project Details</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="/projects">Projects</a></li>
                    <li class="breadcrumb-item active"><?= esc($project['project_name']) ?></li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="/projects" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Projects
            </a>
            <?php if ($user['roles'] === 'admin' || $project['created_by'] == $user['id']): ?>
            <a href="/projects/edit/<?= $project['id'] ?>" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Edit Project
            </a>
            <?php endif; ?>
        </div>
    </div>

    <div class="row">
        <!-- Project Information -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Project Information
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Show task name for assignees -->
                    <?php
                    $userTaskName = null;
                    $userTaskStatus = null;
                    if (!empty($teamMembers)) {
                        foreach ($teamMembers as $member) {
                            if ($member['id'] == $user['id'] && !empty($member['task_name']) && $member['task_name'] !== 'General Task') {
                                $userTaskName = $member['task_name'];
                                $userTaskStatus = $member['task_status'];
                                break;
                            }
                        }
                    } elseif ($project['assigned_to'] == $user['id'] && !empty($project['task_name']) && $project['task_name'] !== 'General Task') {
                        $userTaskName = $project['task_name'];
                        $userTaskStatus = $project['task_status'] ?? null;
                    }
                    ?>

                    <?php if ($userTaskName): ?>
                    <div class="alert alert-info mb-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-tasks me-2"></i>
                            <div>
                                <strong>Your Task:</strong> <?= esc($userTaskName) ?>
                                <?php if ($userTaskStatus === 'completed'): ?>
                                <span class="badge bg-success ms-2">✓ Completed</span>
                                <?php elseif ($project['status'] === 'in_progress'): ?>
                                <span class="badge bg-primary ms-2">In Progress</span>
                                <?php elseif ($project['status'] === 'on_hold'): ?>
                                <span class="badge bg-warning ms-2">On Hold</span>
                                <?php else: ?>
                                <span class="badge bg-secondary ms-2">Not Started</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Project ID</label>
                                <p class="mb-0"><?= esc($project['project_id']) ?></p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Project Name</label>
                                <p class="mb-0"><?= esc($project['project_name']) ?></p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Client Name</label>
                                <p class="mb-0"><?= esc($project['client_name']) ?></p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Location</label>
                                <p class="mb-0"><?= esc($project['location']) ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Status</label>
                                <p class="mb-0">
                                    <span class="badge bg-<?= getStatusColor($project['status']) ?>">
                                        <?= ucwords(str_replace('_', ' ', $project['status'])) ?>
                                    </span>
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Start Date</label>
                                <p class="mb-0"><?= date('M d, Y', strtotime($project['start_date'])) ?></p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Target Completion</label>
                                <p class="mb-0"><?= date('M d, Y', strtotime($project['target_completion'])) ?></p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Progress</label>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: <?= $project['progress_percentage'] ?>%"
                                         aria-valuenow="<?= $project['progress_percentage'] ?>" 
                                         aria-valuemin="0" aria-valuemax="100">
                                        <?= $project['progress_percentage'] ?>%
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <?php if (!empty($project['description'])): ?>
                    <div class="mt-3">
                        <label class="form-label fw-bold">Description</label>
                        <p class="mb-0"><?= nl2br(esc($project['description'])) ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Timeline -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>Project Timeline
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($timeline)): ?>
                    <div class="timeline">
                        <?php foreach ($timeline as $item): ?>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-<?= getStatusColor($item['status']) ?>">
                                <i class="fas fa-<?= getStatusIcon($item['status']) ?>"></i>
                            </div>
                            <div class="timeline-content">
                                <!-- Simplified Timeline Format -->
                                <?php if (!empty($item['task_name'])): ?>
                                <div class="timeline-task-name">
                                    <strong>Task: <?= esc($item['task_name']) ?></strong>
                                </div>
                                <?php endif; ?>

                                <div class="timeline-action-line">
                                    <?php
                                    // Extract old and new status from action
                                    $action = $item['action'];
                                    if (strpos($action, 'Status changed from') !== false) {
                                        // Parse "Status changed from X to Y" format
                                        preg_match('/Status changed from (.+?) to (.+?)$/', $action, $matches);
                                        if (count($matches) >= 3) {
                                            $oldStatus = $matches[1];
                                            $newStatus = $matches[2];
                                            echo '<span class="timeline-user-label">' . esc($item['user']) . '</span> changed from <span class="timeline-status-label timeline-old-status">' . esc($oldStatus) . '</span> to <span class="timeline-status-label timeline-new-status">' . esc($newStatus) . '</span>';

                                            // Add duration label if available
                                            if (!empty($item['duration'])) {
                                                echo ' - <span class="timeline-duration-label">' . esc($item['duration']) . '</span>';
                                            }

                                            // Add total time label if available
                                            if (!empty($item['total_task_time'])) {
                                                echo ' - <span class="timeline-total-label">Total: ' . esc($item['total_task_time']) . '</span>';
                                            }
                                        } else {
                                            echo '<span class="timeline-user-label">' . esc($item['user']) . '</span> ' . esc($action);
                                        }
                                    } else {
                                        echo '<span class="timeline-user-label">' . esc($item['user']) . '</span> ' . esc($action);
                                    }
                                    ?>
                                </div>

                                <div class="timeline-timestamp">
                                    <i class="fas fa-clock"></i> <?= date('M d, Y, h:i:s A', strtotime($item['timestamp'])) ?>
                                </div>

                                <?php if (!empty($item['notes'])): ?>
                                <div class="timeline-notes-compact">
                                    <small class="text-muted"><?= esc($item['notes']) ?></small>
                                </div>
                                <?php endif; ?>

                                <!-- File Attachment Display -->
                                <?php if (!empty($item['file_path']) || !empty($item['has_attachment'])): ?>
                                <div class="timeline-attachment">
                                    <div class="attachment-header">
                                        <i class="fas fa-paperclip me-2"></i>
                                        <strong>Attachment:</strong>
                                    </div>
                                    <div class="attachment-item">
                                        <?php
                                        $fileName = basename($item['file_path'] ?? '');
                                        $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                                        $isImage = in_array($fileExtension, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
                                        ?>

                                        <?php if ($isImage): ?>
                                        <!-- Image Preview -->
                                        <div class="attachment-image-preview">
                                            <img src="<?= base_url($item['file_path']) ?>"
                                                 alt="Attachment"
                                                 class="img-thumbnail attachment-thumbnail"
                                                 onclick="showImageModal('<?= base_url($item['file_path']) ?>', '<?= esc($fileName) ?>')">
                                            <div class="attachment-info">
                                                <span class="attachment-name"><?= esc($fileName) ?></span>
                                                <a href="<?= base_url($item['file_path']) ?>"
                                                   target="_blank"
                                                   class="btn btn-sm btn-outline-primary ms-2">
                                                    <i class="fas fa-external-link-alt"></i> View Full Size
                                                </a>
                                            </div>
                                        </div>
                                        <?php else: ?>
                                        <!-- File Download -->
                                        <div class="attachment-file">
                                            <div class="file-icon">
                                                <i class="fas fa-<?= getFileIcon($fileExtension) ?> fa-2x"></i>
                                            </div>
                                            <div class="attachment-info">
                                                <span class="attachment-name"><?= esc($fileName) ?></span>
                                                <small class="text-muted d-block"><?= strtoupper($fileExtension) ?> File</small>
                                                <a href="<?= base_url($item['file_path']) ?>"
                                                   target="_blank"
                                                   class="btn btn-sm btn-outline-primary mt-1">
                                                    <i class="fas fa-download"></i> Download
                                                </a>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                        <h6>No Timeline Data</h6>
                        <p class="text-muted">Timeline will appear as project progresses</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Team Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>Team
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Project Creator</label>
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <div>
                                <p class="mb-0"><?= esc($creator['username'] ?? 'Unknown') ?></p>
                                <small class="text-muted"><?= esc($creator['email'] ?? '') ?></small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Team Information - Enhanced for Admin -->
                    <?php if (!empty($teamMembers)): ?>
                    <?php
                    // Group team members by user to show all tasks per user
                    $groupedMembers = [];
                    $uniqueUserCount = 0;
                    $seenUserIds = [];

                    foreach ($teamMembers as $member) {
                        $userId = $member['id'];
                        if (!isset($groupedMembers[$userId])) {
                            $groupedMembers[$userId] = [
                                'user_info' => $member,
                                'tasks' => []
                            ];
                            if (!in_array($userId, $seenUserIds)) {
                                $uniqueUserCount++;
                                $seenUserIds[] = $userId;
                            }
                        }
                        $groupedMembers[$userId]['tasks'][] = $member;
                    }
                    ?>
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-users me-2"></i>Team Information
                            <span class="badge bg-primary ms-2"><?= $uniqueUserCount ?> member<?= $uniqueUserCount > 1 ? 's' : '' ?></span>
                            <span class="badge bg-secondary ms-1"><?= count($teamMembers) ?> task<?= count($teamMembers) > 1 ? 's' : '' ?></span>
                        </label>

                        <div class="team-members-grid">
                            <?php foreach ($groupedMembers as $userId => $memberData): ?>
                            <?php $member = $memberData['user_info']; ?>
                            <div class="team-member-card">
                                <div class="member-header">
                                    <div class="member-avatar">
                                        <div class="avatar-circle bg-info">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    </div>
                                    <div class="member-info">
                                        <h6 class="member-name mb-0"><?= esc($member['username']) ?></h6>
                                        <small class="member-email text-muted"><?= esc($member['email']) ?></small>
                                        <?php if (!empty($member['first_name']) || !empty($member['last_name'])): ?>
                                        <small class="member-fullname text-muted d-block">
                                            <?= esc(trim(($member['first_name'] ?? '') . ' ' . ($member['last_name'] ?? ''))) ?>
                                        </small>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Show all tasks for this user -->
                                <div class="member-tasks-list">
                                    <?php foreach ($memberData['tasks'] as $task): ?>
                                    <div class="member-task-info">
                                        <div class="task-assignment">
                                            <span class="task-label">Task:</span>
                                            <span class="task-name <?= ($task['task_status'] === 'completed' || $task['status'] === 'completed') ? 'completed-task-name' : '' ?>">
                                                <?= esc($task['task_name']) ?>
                                            </span>
                                        </div>
                                        <div class="task-status-badge">
                                            <?php
                                            $taskStatus = $task['task_status'] ?? $task['status'] ?? 'not_started';
                                            if ($taskStatus === 'completed'): ?>
                                            <span class="badge bg-success">✓ Completed</span>
                                            <?php elseif ($taskStatus === 'in_progress'): ?>
                                            <span class="badge bg-primary">🔄 In Progress</span>
                                            <?php elseif ($taskStatus === 'on_hold'): ?>
                                            <span class="badge bg-warning">⏸️ On Hold</span>
                                            <?php else: ?>
                                            <span class="badge bg-light text-dark">⏳ Not Started</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php else: ?>
                    <div class="mb-3">
                        <label class="form-label fw-bold">Assigned To</label>
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm bg-success rounded-circle d-flex align-items-center justify-content-center me-2">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <div>
                                <p class="mb-0"><?= esc($assignee['username'] ?? 'Unassigned') ?></p>
                                <small class="text-muted"><?= esc($assignee['email'] ?? '') ?></small>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Project Stats -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Quick Stats
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="stat-item">
                                <h4 class="stat-number"><?= getDaysRemaining($project['target_completion']) ?></h4>
                                <p class="stat-label">Days Left</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <h4 class="stat-number"><?= getDaysElapsed($project['start_date']) ?></h4>
                                <p class="stat-label">Days Elapsed</p>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="mb-2">
                        <small class="text-muted">Created:</small>
                        <span class="float-end"><?= date('M d, Y', strtotime($project['created_at'])) ?></span>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">Last Updated:</small>
                        <span class="float-end"><?= date('M d, Y H:i', strtotime($project['updated_at'])) ?></span>
                    </div>
                    <?php if (!empty($project['client_mobile'])): ?>
                    <div class="mb-2">
                        <small class="text-muted">Client Mobile:</small>
                        <span class="float-end"><?= esc($project['client_mobile']) ?></span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">Attachment Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Attachment" class="img-fluid">
            </div>
            <div class="modal-footer">
                <a id="modalDownloadLink" href="" target="_blank" class="btn btn-primary">
                    <i class="fas fa-download"></i> Download Original
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?php
// Helper functions
function getStatusColor($status) {
    switch ($status) {
        case 'not_started': return 'secondary';
        case 'planning': return 'info';
        case 'in_progress': return 'primary';
        case 'on_hold': return 'warning';
        case 'completed': return 'success';
        case 'review': return 'warning';
        case 'sent_for_review': return 'warning';
        case 'under_review': return 'warning';
        case 'need_revision': return 'danger';
        case 'client_accepted': return 'success';
        case 'rejected': return 'danger';
        default: return 'secondary';
    }
}

function getStatusIcon($status) {
    switch ($status) {
        case 'not_started': return 'circle';
        case 'planning': return 'clipboard-list';
        case 'in_progress': return 'play';
        case 'on_hold': return 'pause';
        case 'completed': return 'check';
        case 'review': return 'search';
        case 'sent_for_review': return 'paper-plane';
        case 'under_review': return 'search';
        case 'need_revision': return 'edit';
        case 'client_accepted': return 'thumbs-up';
        case 'rejected': return 'times';
        default: return 'circle';
    }
}

function getDaysRemaining($targetDate) {
    $target = new DateTime($targetDate);
    $now = new DateTime();
    $diff = $now->diff($target);
    return $diff->invert ? 0 : $diff->days;
}

function getDaysElapsed($startDate) {
    $start = new DateTime($startDate);
    $now = new DateTime();
    $diff = $start->diff($now);
    return $diff->days;
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) return 'Just now';
    if ($time < 3600) return floor($time/60) . ' minute' . (floor($time/60) > 1 ? 's' : '') . ' ago';
    if ($time < 86400) return floor($time/3600) . ' hour' . (floor($time/3600) > 1 ? 's' : '') . ' ago';
    if ($time < 2592000) return floor($time/86400) . ' day' . (floor($time/86400) > 1 ? 's' : '') . ' ago';
    return date('M d, Y', strtotime($datetime));
}

function getFileIcon($extension) {
    switch (strtolower($extension)) {
        case 'pdf': return 'file-pdf';
        case 'doc':
        case 'docx': return 'file-word';
        case 'xls':
        case 'xlsx': return 'file-excel';
        case 'ppt':
        case 'pptx': return 'file-powerpoint';
        case 'zip':
        case 'rar':
        case '7z': return 'file-archive';
        case 'txt': return 'file-alt';
        case 'mp4':
        case 'avi':
        case 'mov': return 'file-video';
        case 'mp3':
        case 'wav': return 'file-audio';
        default: return 'file';
    }
}
?>
<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.timeline-title {
    margin-bottom: 5px;
    font-weight: 600;
}

.timeline-meta {
    font-size: 12px;
    margin-bottom: 8px;
}

.timeline-meta-enhanced {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 12px;
    padding: 10px;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.timeline-exact-time,
.timeline-user,
.timeline-task,
.timeline-assignee,
.timeline-session-duration,
.timeline-total-time,
.timeline-relative-time {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.timeline-exact-time {
    background: rgba(0, 123, 255, 0.1);
    color: #004085;
    border-color: rgba(0, 123, 255, 0.2);
}

.timeline-user {
    background: rgba(108, 117, 125, 0.1);
    color: #495057;
    border-color: rgba(108, 117, 125, 0.2);
}

.timeline-task {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
    border-color: rgba(255, 193, 7, 0.2);
}

.timeline-assignee {
    background: rgba(220, 53, 69, 0.1);
    color: #721c24;
    border-color: rgba(220, 53, 69, 0.2);
}

.timeline-session-duration {
    background: rgba(23, 162, 184, 0.1);
    color: #0c5460;
    border-color: rgba(23, 162, 184, 0.2);
}

.timeline-total-time {
    background: rgba(111, 66, 193, 0.1);
    color: #4c2a85;
    border-color: rgba(111, 66, 193, 0.2);
    font-weight: 600;
}

.timeline-relative-time {
    background: rgba(40, 167, 69, 0.1);
    color: #155724;
    border-color: rgba(40, 167, 69, 0.2);
}

.timeline-notes {
    margin-bottom: 0;
    font-size: 14px;
}

/* Timeline Attachment Styles */
.timeline-attachment {
    margin-top: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}

.attachment-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 13px;
    color: #495057;
}

.attachment-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.attachment-image-preview {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
}

.attachment-thumbnail {
    max-width: 80px;
    max-height: 80px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.attachment-thumbnail:hover {
    transform: scale(1.05);
}

.attachment-file {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
}

.file-icon {
    color: #6c757d;
    text-align: center;
    min-width: 40px;
}

.attachment-info {
    flex: 1;
}

.attachment-name {
    font-weight: 500;
    color: #212529;
    font-size: 14px;
}

.avatar-sm {
    width: 40px;
    height: 40px;
}

.stat-item {
    padding: 10px;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
    color: #007bff;
}

.stat-label {
    font-size: 12px;
    text-transform: uppercase;
    color: #6c757d;
    margin-bottom: 0;
}

/* Enhanced Team Information Styles */
.team-members-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
    margin-top: 12px;
}

.team-member-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 16px;
    transition: all 0.3s ease;
}

.team-member-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
    transform: translateY(-2px);
}

.member-header {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
}

.member-avatar .avatar-circle {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.member-info {
    flex: 1;
}

.member-name {
    font-size: 16px;
    font-weight: 600;
    color: #212529;
    margin-bottom: 4px;
}

.member-email {
    font-size: 13px;
    color: #6c757d;
}

.member-fullname {
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
}

.member-tasks-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.member-task-info {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    padding: 12px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.member-task-info:hover {
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(0, 123, 255, 0.2);
    transform: translateY(-1px);
}

.task-assignment {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.task-label {
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    min-width: 40px;
}

.task-name {
    font-size: 14px;
    font-weight: 500;
    color: #212529;
    flex: 1;
}

.completed-task-name {
    text-decoration: line-through !important;
    text-decoration-color: rgba(0, 0, 0, 0.3) !important;
    text-decoration-thickness: 1px !important;
    opacity: 0.7 !important;
}

.task-status-badge {
    display: flex;
    justify-content: flex-start;
}

.task-status-badge .badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 6px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .team-members-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .member-header {
        gap: 10px;
    }

    .member-avatar .avatar-circle {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
}
</style>

<script>
function showImageModal(imageSrc, fileName) {
    document.getElementById('modalImage').src = imageSrc;
    document.getElementById('imageModalLabel').textContent = fileName || 'Attachment Preview';
    document.getElementById('modalDownloadLink').href = imageSrc;

    const modal = new bootstrap.Modal(document.getElementById('imageModal'));
    modal.show();
}
</script>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Update relative time every minute
function updateRelativeTimes() {
    const relativeTimeElements = document.querySelectorAll('.relative-time[data-timestamp]');
    relativeTimeElements.forEach(element => {
        const timestamp = element.getAttribute('data-timestamp');
        if (timestamp) {
            element.textContent = formatRelativeTime(timestamp);
        }
    });
}

function formatRelativeTime(dateString) {
    if (!dateString) return 'N/A';

    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) {
        return 'Just now';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 604800) {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
        return date.toLocaleDateString();
    }
}

// Update every minute
setInterval(updateRelativeTimes, 60000);

// Initial update
document.addEventListener('DOMContentLoaded', updateRelativeTimes);
</script>
<?= $this->endSection() ?>
