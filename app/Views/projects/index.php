<?= $this->extend('layouts/app') ?>

<?= $this->section('content') ?>

<!-- Hidden CSRF Token for AJAX requests -->
<input type="hidden" name="csrf_token" value="<?= csrf_hash() ?>" id="csrf_token">

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Consolidated Navigation Bar -->
            <div class="consolidated-nav">
                <div class="nav-content">
                    <!-- Page Title and Statistics -->
                    <div class="nav-header">

                        <!-- Compact Statistics - Role Based -->
                        <div class="nav-stats compact" id="navStats">
                            <?php if ($user['roles'] === 'admin'): ?>
                            <!-- Admin sees all project stats -->
                            <div class="stat-item">
                                <span class="stat-number" id="totalProjects">-</span>
                                <span class="stat-label">Total</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="inProgressProjects">-</span>
                                <span class="stat-label">Active</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="completedProjects">-</span>
                                <span class="stat-label">Done</span>
                            </div>
                            <?php else: ?>
                            <!-- Assignees see their personal stats -->
                            <div class="stat-item">
                                <span class="stat-number" id="myTotalProjects">-</span>
                                <span class="stat-label">Total</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="myActiveProjects">-</span>
                                <span class="stat-label">Active</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="myCompletedProjects">-</span>
                                <span class="stat-label">Done</span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Filters - Hidden by default, shown with toggle -->
                    <div class="nav-filters" id="navFilters" style="display: none;">
                        <?php if ($user['roles'] === 'admin'): ?>
                        <!-- Admin sees assignee filter (default to all) -->
                        <select class="form-select-nav" id="assigneeFilter" onchange="loadProjects()">
                            <option value="all" selected>All Assignees</option>
                            <!-- Assignee options will be loaded dynamically -->
                        </select>
                        <?php else: ?>
                        <!-- Regular users see project filter -->
                        <select class="form-select-nav" id="projectFilter" onchange="loadProjects()">
                            <option value="assigned" selected>Assigned to Me</option>
                            <option value="created">Created by Me</option>
                            <option value="all">All Projects</option>
                        </select>
                        <?php endif; ?>

                        <select class="form-select-nav" id="statusFilter" onchange="loadProjects()">
                            <option value="">All Status</option>
                            <option value="not_started">Not Started</option>
                            <option value="planning">Planning</option>
                            <option value="in_progress">In Progress</option>
                            <option value="on_hold">On Hold</option>
                            <option value="completed">Completed</option>
                            <option value="review">Under Review</option>
                        </select>
                        <div class="search-nav">
                            <i class="fas fa-search"></i>
                            <input type="text" class="form-control-nav" id="searchInput" placeholder="Search projects..." onkeyup="searchProjects()">
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="nav-actions">
                        <!-- Filter Toggle Button -->
                        <button type="button" class="btn-nav btn-nav-outline" onclick="toggleFilters()">
                            <i class="fas fa-filter me-1"></i>
                            <span class="filter-text">Filters</span>
                        </button>

                        <?php if (in_array($user['roles'], ['admin', 'manager', 'projects'])): ?>

                        <button type="button" class="btn-nav btn-nav-outline" data-bs-toggle="modal" data-bs-target="#bulkImportModal">
                            <i class="fas fa-file-excel me-1"></i>
                            Import
                        </button>
                        <a href="/projects/create" class="btn-nav btn-nav-primary">
                            <i class="fas fa-plus me-1"></i>
                            New Project
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>



            <!-- Projects Content Area - 80% of screen space -->
            <div class="projects-content-area">
                <div id="projectsList">
                    <div class="col-12 text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading projects...</p>
                    </div>
                </div>
            </div>


        </div>
    </div>
</div>

<!-- Create Project Modal Removed - Using Page-Based Approach -->
<?php if (in_array($user['roles'], ['admin', 'manager', 'projects'])): ?>
<!-- Modal removed - using page-based approach at /projects/create -->

<!-- Bulk Import Modal -->
<div class="modal fade" id="bulkImportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Import Projects</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="bulkImportForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="excelFile" class="form-label">Excel File</label>
                        <input type="file" class="form-control" id="excelFile" name="excel_file" accept=".xls,.xlsx" required>
                        <div class="form-text">
                            Upload an Excel file with project data. Supported formats: .xls, .xlsx
                            <br>
                            <a href="#" onclick="downloadSampleFile()" class="text-primary">
                                <i class="fas fa-download me-1"></i>
                                Download Sample Excel File
                            </a>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <h6>Excel Format Requirements:</h6>
                        <ul class="mb-0">
                            <li>Column A: Project ID (e.g., PRJ-2024-001)</li>
                            <li>Column B: Project Name</li>
                            <li>Column C: Client Name</li>
                            <li>Column D: Client Mobile (optional)</li>
                            <li>Column E: Location</li>
                            <li>Column F: Description (optional)</li>
                            <li>Column G: Start Date (YYYY-MM-DD)</li>
                            <li>Column H: Target Completion (YYYY-MM-DD)</li>
                            <li>Column I: Assigned User ID</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>
                        Import Projects
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Project Details Modal -->
<div class="modal fade" id="projectDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-gradient-primary text-white">
                <div class="d-flex align-items-center">
                    <div class="modal-icon me-3">
                        <i class="fas fa-building fa-2x"></i>
                    </div>
                    <div>
                        <h4 class="modal-title mb-0" id="projectDetailsTitle">Project Details</h4>
                        <small class="opacity-75" id="projectDetailsSubtitle">View project information and task progress</small>
                    </div>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>

            <div class="modal-body p-0">
                <!-- Project Info Header -->
                <div class="project-info-header p-4 bg-light border-bottom">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center mb-2">
                                <span class="project-id-badge me-3" id="detailProjectId">PRJ-2024-001</span>
                                <span class="status-badge" id="detailProjectStatus">In Progress</span>
                            </div>
                            <h5 class="mb-1" id="detailProjectName">Project Name</h5>
                            <p class="text-muted mb-2" id="detailClientName">Client Name</p>
                            <p class="text-muted mb-0" id="detailLocation">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                Location
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="project-progress-circle mb-2">
                                <div class="progress-circle" id="detailProgressCircle">
                                    <span class="progress-text">0%</span>
                                </div>
                            </div>
                            <small class="text-muted">Overall Progress</small>
                        </div>
                    </div>
                </div>

                <!-- Tabs Navigation -->
                <ul class="nav nav-tabs nav-fill" id="projectDetailsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="tasks-tab" data-bs-toggle="tab" data-bs-target="#tasks" type="button" role="tab">
                            <i class="fas fa-tasks me-2"></i>
                            Tasks
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="timeline-tab" data-bs-toggle="tab" data-bs-target="#timeline" type="button" role="tab">
                            <i class="fas fa-clock me-2"></i>
                            Timeline
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="info-tab" data-bs-toggle="tab" data-bs-target="#info" type="button" role="tab">
                            <i class="fas fa-info-circle me-2"></i>
                            Information
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="client-access-tab" data-bs-toggle="tab" data-bs-target="#client-access" type="button" role="tab">
                            <i class="fas fa-qrcode me-2"></i>
                            Client Access
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="projectDetailsTabContent">
                    <!-- Tasks Tab -->
                    <div class="tab-pane fade show active" id="tasks" role="tabpanel">
                        <div class="p-4">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h6 class="mb-0">Project Tasks</h6>
                                <div class="task-stats" id="taskStats">
                                    <span class="badge bg-secondary me-1">0 Total</span>
                                    <span class="badge bg-primary me-1">0 In Progress</span>
                                    <span class="badge bg-success">0 Completed</span>
                                </div>
                            </div>

                            <div id="projectTasksList">
                                <!-- Tasks will be loaded here -->
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading tasks...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timeline Tab -->
                    <div class="tab-pane fade" id="timeline" role="tabpanel">
                        <div class="p-4">
                            <h6 class="mb-4">Project Timeline</h6>
                            <div id="projectTimeline">
                                <!-- Timeline will be loaded here -->
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading timeline...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Information Tab -->
                    <div class="tab-pane fade" id="info" role="tabpanel">
                        <div class="p-4">
                            <h6 class="mb-4">Project Information</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item mb-3">
                                        <label class="form-label fw-bold">Description</label>
                                        <p class="text-muted" id="detailDescription">Project description will appear here</p>
                                    </div>
                                    <div class="info-item mb-3">
                                        <label class="form-label fw-bold">Start Date</label>
                                        <p class="text-muted" id="detailStartDate">-</p>
                                    </div>
                                    <div class="info-item mb-3">
                                        <label class="form-label fw-bold">Target Completion</label>
                                        <p class="text-muted" id="detailTargetDate">-</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item mb-3">
                                        <label class="form-label fw-bold">Assigned To</label>
                                        <p class="text-muted" id="detailAssignedTo">-</p>
                                    </div>
                                    <div class="info-item mb-3">
                                        <label class="form-label fw-bold">Created By</label>
                                        <p class="text-muted" id="detailCreatedBy">-</p>
                                    </div>
                                    <div class="info-item mb-3">
                                        <label class="form-label fw-bold">Created Date</label>
                                        <p class="text-muted" id="detailCreatedDate">-</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Client Access Tab -->
                    <div class="tab-pane fade" id="client-access" role="tabpanel">
                        <div class="p-4">
                            <h6 class="mb-4">Client Access Management</h6>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Generate a QR code for clients to view project status without logging in. Access expires after 30 days.
                            </div>

                            <div id="clientAccessContent">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="mb-3">
                                            <i class="fas fa-qrcode me-2"></i>
                                            QR Code Access
                                        </h6>
                                        <p class="text-muted mb-4">
                                            Share this QR code with your client to give them instant access to project status and updates.
                                        </p>

                                        <div class="qr-code-container text-center mb-4">
                                            <div id="qrCodeDisplay" class="qr-code-placeholder">
                                                <i class="fas fa-qrcode fa-5x text-muted"></i>
                                                <p class="mt-2 text-muted">QR Code will appear here</p>
                                            </div>
                                        </div>

                                        <div class="d-grid gap-2">
                                            <button class="btn btn-primary" onclick="generateQRCode()" id="generateQRBtn">
                                                <i class="fas fa-qrcode me-2"></i>
                                                Generate QR Code
                                            </button>
                                            <button class="btn btn-outline-secondary" onclick="downloadQRCode()" id="downloadQRBtn" style="display: none;">
                                                <i class="fas fa-download me-2"></i>
                                                Download QR Code
                                            </button>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h6 class="mb-3">
                                            <i class="fas fa-link me-2"></i>
                                            Direct Access Link
                                        </h6>
                                        <p class="text-muted mb-3">
                                            Alternatively, share this direct link with your client.
                                        </p>

                                        <div class="input-group mb-3">
                                            <input type="text" class="form-control" id="clientAccessLink" readonly placeholder="Link will be generated...">
                                            <button class="btn btn-outline-secondary" onclick="copyAccessLink()">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>

                                        <div class="alert alert-info">
                                            <h6 class="alert-heading">
                                                <i class="fas fa-info-circle me-2"></i>
                                                Client Access Features
                                            </h6>
                                            <ul class="mb-0">
                                                <li>View project progress and status</li>
                                                <li>See completed tasks and milestones</li>
                                                <li>Access project timeline</li>
                                                <li>View project photos and updates</li>
                                                <li>No login required</li>
                                            </ul>
                                        </div>

                                        <div class="mt-3">
                                            <small class="text-muted">
                                                <i class="fas fa-shield-alt me-1"></i>
                                                This link is secure and unique to this project. Access expires after 30 days.
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>
                    Close
                </button>
                <button type="button" class="btn btn-primary" id="updateProjectStatusBtn">
                    <i class="fas fa-edit me-2"></i>
                    Update Status
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal - Redesigned -->
<div class="modal fade" id="statusUpdateModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content status-modal-redesign">
            <div class="status-modal-header">
                <div class="status-modal-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="status-modal-title-section">
                    <h5 class="status-modal-title">Update Project Status</h5>
                    <p class="status-modal-subtitle">Record your progress with optional notes</p>
                </div>
                <button type="button" class="btn-close-custom" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="statusUpdateForm" onsubmit="submitStatusUpdate(event)" enctype="multipart/form-data">
                <div class="status-modal-body">
                    <div class="status-info-card">
                        <div class="status-info-content">
                            <div class="status-change-info">
                                <span class="status-change-label">Changing to:</span>
                                <span class="status-change-value" id="statusUpdateText">In Progress</span>
                            </div>
                            <div class="status-timestamp">
                                <i class="fas fa-clock me-1"></i>
                                <span id="statusTimestamp"></span>
                            </div>
                        </div>
                    </div>

                    <input type="hidden" id="updateProjectId" name="project_id">

                    <div class="form-group-redesign">
                        <label for="statusComment" class="form-label-redesign">
                            <i class="fas fa-comment-alt me-2"></i>
                            Add a note <span class="optional-badge" id="commentRequiredBadge">Optional</span>
                        </label>
                        <textarea class="form-control-redesign" id="statusComment" name="notes" rows="3"
                                  placeholder="What's the current progress? Any updates to share?"></textarea>
                    </div>

                    <div class="form-group-redesign">
                        <label for="statusFile" class="form-label-redesign">
                            <i class="fas fa-paperclip me-2"></i>
                            Attach file <span class="optional-badge">Optional</span>
                        </label>
                        <div class="file-upload-redesign">
                            <input type="file" class="file-input-redesign" id="statusFile" name="status_file"
                                   accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx">
                            <div class="file-upload-placeholder">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <span>Click to upload or drag file here</span>
                                <small>Images, PDF, Word, Excel (Max 10MB)</small>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Status Section (shown when completing project) -->
                    <div class="mb-3" id="paymentStatusSection" style="display: none;">
                        <label class="form-label">
                            <i class="fas fa-credit-card me-2"></i>
                            Payment Status *
                        </label>
                        <div class="row">
                            <div class="col-md-6">
                                <select class="form-select" id="paymentStatus" name="payment_status">
                                    <option value="pending">Payment Pending</option>
                                    <option value="partial">Partial Payment Received</option>
                                    <option value="completed">Payment Completed</option>
                                    <option value="overdue">Payment Overdue</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <input type="number" class="form-control" id="paymentAmount" name="payment_amount"
                                       placeholder="Amount received" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Payment details are required before marking project as completed
                        </div>
                    </div>

                    <!-- Payment Notes -->
                    <div class="mb-3" id="paymentNotesSection" style="display: none;">
                        <label for="paymentNotes" class="form-label">Payment Notes</label>
                        <textarea class="form-control" id="paymentNotes" name="payment_notes" rows="2"
                                  placeholder="Additional payment details, invoice numbers, etc."></textarea>
                    </div>
                </div>
                <div class="status-modal-footer">
                    <button type="button" class="btn-redesign btn-cancel" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>
                        Cancel
                    </button>
                    <button type="submit" class="btn-redesign btn-submit">
                        <i class="fas fa-check me-2"></i>
                        Update Status
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Task Status Update Modal -->
<div class="modal fade" id="taskStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Task Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="taskStatusForm">
                <div class="modal-body">
                    <input type="hidden" id="updateTaskId" name="task_id">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Task</label>
                        <p class="text-muted mb-0" id="taskStatusName">Task name</p>
                    </div>
                    <div class="mb-3">
                        <label for="newTaskStatus" class="form-label">New Status *</label>
                        <select class="form-select" id="newTaskStatus" name="status" required>
                            <option value="not_started">Not Started</option>
                            <option value="in_progress">In Progress</option>
                            <option value="on_hold">On Hold</option>
                            <option value="completed">Completed</option>
                        </select>
                    </div>

                    <!-- Manager Review Options (shown only for sent_for_review tasks) -->
                    <div class="mb-3" id="managerReviewOptions" style="display: none;">
                        <label class="form-label">Manager Review Action *</label>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-outline-warning" onclick="setManagerReviewStatus('need_revision')">
                                <i class="fas fa-edit me-2"></i>Need Revision
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="setManagerReviewStatus('client_accepted')">
                                <i class="fas fa-check-circle me-2"></i>Client Accepted
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="setManagerReviewStatus('client_rejected')">
                                <i class="fas fa-times-circle me-2"></i>Client Rejected
                            </button>
                        </div>
                        <input type="hidden" id="managerReviewAction" name="manager_action">
                    </div>
                    <div class="mb-3">
                        <label for="taskNotes" class="form-label">Notes</label>
                        <textarea class="form-control" id="taskNotes" name="notes" rows="3"
                                  placeholder="Optional notes about this status change..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        Update Task Status
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Manager Task Status Update Modal - Redesigned -->
<div class="modal fade" id="managerTaskStatusModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content status-modal-redesign">
            <div class="status-modal-header">
                <div class="status-modal-icon">
                    <i class="fas fa-tasks"></i>
                </div>
                <div class="status-modal-title">
                    <h4>Update Completed Task Status</h4>
                    <p class="status-modal-subtitle">
                        Review and update status for completed tasks of this project
                    </p>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>

            <form id="managerTaskStatusForm" onsubmit="submitManagerTaskStatus(event)" enctype="multipart/form-data">
                <input type="hidden" id="managerProjectId" name="project_id">

                <div class="modal-body">
                    <!-- Completed Task Selection -->
                    <div class="status-form-group">
                        <label class="status-form-label">
                            <i class="fas fa-check-circle me-2"></i>
                            Select Completed Task
                        </label>
                        <select class="status-form-select" id="managerTaskSelect" name="project_id" onchange="updateManagerTaskInfo()" required>
                            <option value="">Loading completed tasks...</option>
                        </select>
                        <small class="form-text text-muted mt-1">Choose from all available completed tasks</small>
                    </div>

                    <!-- Task Info Display -->
                    <div id="managerTaskInfo" class="status-info-card" style="display: none;">
                        <div class="status-info-row">
                            <div class="status-info-item">
                                <span class="status-info-label">Task Status:</span>
                                <span id="managerCurrentStatus" class="status-info-value"></span>
                            </div>
                            <div class="status-info-item">
                                <span class="status-info-label">Assigned To:</span>
                                <span id="managerTaskAssignee" class="status-info-value"></span>
                            </div>
                        </div>
                        <div class="status-info-description">
                            <span class="status-info-label">Task Description:</span>
                            <span id="managerTaskDescription" class="status-info-value"></span>
                        </div>
                        <div class="status-info-revision" id="managerRevisionInfo" style="display: none;">
                            <span class="status-info-label">Revision Count:</span>
                            <span id="managerRevisionCount" class="status-info-value">0</span>
                        </div>
                    </div>

                    <!-- Status Selection -->
                    <div class="status-form-group">
                        <label class="status-form-label">
                            <i class="fas fa-flag me-2"></i>
                            New Status
                        </label>
                        <select class="status-form-select" id="managerNewStatus" name="status" required disabled>
                            <option value="">Select status...</option>
                            <option value="sent_for_review">📤 Sent for Review</option>
                            <option value="revision_needed">🔄 Need Revision</option>
                            <option value="client_accepted">✅ Client Accepted</option>
                            <option value="rejected">❌ Rejected</option>
                        </select>
                    </div>

                    <!-- Notes -->
                    <div class="status-form-group">
                        <label class="status-form-label">
                            <i class="fas fa-sticky-note me-2"></i>
                            Notes
                            <span class="optional-badge">Optional</span>
                        </label>
                        <textarea class="status-form-textarea" id="managerTaskNotes" name="notes" rows="3"
                                  placeholder="Add notes about this status change..."></textarea>
                    </div>

                    <!-- File Attachment -->
                    <div class="status-form-group">
                        <label class="status-form-label">
                            <i class="fas fa-paperclip me-2"></i>
                            Attachment
                            <span class="optional-badge">Optional</span>
                        </label>
                        <input type="file" class="status-form-file" id="managerTaskFile" name="status_file"
                               accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx">
                        <div class="status-form-help">
                            Attach images, documents, or files related to this status update
                        </div>
                    </div>
                </div>

                <div class="status-modal-footer">
                    <button type="button" class="status-btn-secondary" data-bs-dismiss="modal">
                        Cancel
                    </button>
                    <button type="submit" class="status-btn-primary" id="managerTaskSubmitBtn" disabled>
                        <i class="fas fa-save me-2"></i>
                        Update Status
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Project Confirmation Modal -->
<div class="modal fade" id="deleteProjectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Delete Project
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="deleteProjectForm" onsubmit="submitDeleteProject(event)">
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning!</strong> This action cannot be undone.
                    </div>

                    <p>Are you sure you want to delete the project <strong id="deleteProjectName"></strong>?</p>

                    <div class="mb-3">
                        <label for="deleteReason" class="form-label">Reason for deletion <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="deleteReason" rows="3"
                                placeholder="Please explain why this project is being deleted..."
                                required minlength="10"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete Project</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Timeline Modal -->
<div class="modal fade" id="timelineModal" tabindex="-1" aria-labelledby="timelineModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="timelineModalLabel">
                    <i class="fas fa-history me-2"></i>
                    Project Timeline
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="timelineContent">
                    <!-- Timeline content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Share Link Modal -->
<div class="modal fade" id="shareLinkModal" tabindex="-1" aria-labelledby="shareLinkModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="shareLinkModalLabel">
                    <i class="fas fa-share-alt me-2"></i>
                    Generate Client Share Link
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="shareRole" class="form-label">Client Access Level</label>
                    <select class="form-control" id="shareRole">
                        <option value="view">View Only - Can see project updates</option>
                        <option value="comment">Comment - Can view and add comments</option>
                        <option value="approve">Approve - Can view, comment and approve milestones</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="shareLink" class="form-label">Share Link</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="shareLink" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyShareLink()">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                    </div>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    This link allows clients to view project updates without logging in. The link expires in 30 days.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="saveShareLink()">Generate Link</button>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
/* Modern SmartFlo Design System - CACHE BUST v2.1 - FORCE REFRESH */
:root {
    --smartflo-primary: #2563eb;
    --smartflo-secondary: #64748b;
    --smartflo-success: #059669;
    --smartflo-warning: #d97706;
    --smartflo-danger: #dc2626;
    --smartflo-info: #0891b2;
    --smartflo-light: #f8fafc;
    --smartflo-dark: #1e293b;
    --smartflo-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --smartflo-gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --smartflo-gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --smartflo-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --smartflo-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --smartflo-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --smartflo-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Page Background */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container-fluid {
    background: transparent;
    padding: 2rem 1rem;
}

/* Consolidated Navigation Bar - Maximum Space Efficiency */
.consolidated-nav {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 0.5rem 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

/* Page Header */
.nav-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.page-title {
    color: white;
    margin: 0;
    font-weight: 600;
    font-size: 1.3rem;
}

/* Compact Stats */
.nav-stats.compact {
    display: flex;
    gap: 1rem;
}

.nav-stats.compact .stat-item {
    text-align: center;
    min-width: 50px;
}

/* Live Status Indicators - 20% Bigger and Center Aligned with Project ID */
.live-indicator {
    font-size: 1.2rem; /* Increased by 20% from 1rem */
    margin-right: 0.5rem;
    animation: bigPulse 2s infinite;
    filter: drop-shadow(0 0 4px currentColor);
    width: 14.4px; /* Increased by 20% from 12px */
    height: 14.4px; /* Increased by 20% from 12px */
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: relative;
    vertical-align: middle; /* Center align with project ID text */
    margin-top: -2px; /* Fine-tune vertical alignment */
}

.live-indicator.live-running {
    color: #28a745;
    background: #28a745;
}

.live-indicator.live-hold {
    color: #fd7e14;
    background: #fd7e14;
}

/* Big pulse animation for live indicators */
@keyframes bigPulse {
    0% {
        opacity: 1;
        transform: scale(1);
        box-shadow: 0 0 0 0 currentColor;
    }
    50% {
        opacity: 0.8;
        transform: scale(1.4);
        box-shadow: 0 0 0 8px transparent;
    }
    100% {
        opacity: 1;
        transform: scale(1);
        box-shadow: 0 0 0 0 currentColor;
    }
}

.nav-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1.5rem;
    min-height: 50px;
}

/* Navigation Statistics */
.nav-stats {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-item .stat-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: white;
    line-height: 1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.stat-item .stat-label {
    font-size: 0.7rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.2rem;
}

/* Navigation Filters */
.nav-filters {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    flex: 1;
    justify-content: center;
}

.form-select-nav {
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
    background: rgba(255, 255, 255, 0.9);
    min-width: 120px;
    transition: all 0.3s ease;
}

.form-select-nav:focus {
    border-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
    outline: none;
}

.navbar-title {
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    padding: 0.4rem 0.6rem;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    min-width: 120px;
    text-align: center;
    display: inline-block;
}

.search-nav {
    position: relative;
    min-width: 200px;
}

.search-nav i {
    position: absolute;
    left: 0.6rem;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    font-size: 0.8rem;
}

.form-control-nav {
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 0.4rem 0.6rem 0.4rem 2rem;
    font-size: 0.8rem;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    width: 100%;
}

.form-control-nav:focus {
    border-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
    outline: none;
}

.btn-nav-reset {
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
    background: rgba(255, 255, 255, 0.9);
    color: #667eea;
    transition: all 0.3s ease;
    cursor: pointer;
    height: 34px;
    min-width: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.btn-nav-reset:hover {
    background: white;
    color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Navigation Actions */
.nav-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}



.btn-nav {
    padding: 0.4rem 0.8rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.8rem;
    border: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    text-decoration: none;
    cursor: pointer;
}

.btn-nav-outline {
    background: rgba(255, 255, 255, 0.9);
    color: #667eea;
    border: 1px solid rgba(255, 255, 255, 0.3);
    font-weight: 600;
}

.btn-nav-outline:hover {
    background: white;
    color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.btn-nav-primary {
    background: rgba(255, 255, 255, 0.9);
    color: #667eea;
}

.btn-nav-primary:hover {
    background: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.page-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.25rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    line-height: 1.2;
}

.page-subtitle {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0;
    font-weight: 400;
}

.header-right-content {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
    justify-content: flex-end;
}

/* Compact Filters */
.compact-filters {
    margin-bottom: 1rem;
}

.filters-row {
    display: flex;
    gap: 1rem;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-select-compact {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    background: white;
    min-width: 140px;
    transition: all 0.3s ease;
}

.form-select-compact:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    outline: none;
}

.search-compact {
    position: relative;
    flex: 1;
    max-width: 300px;
}

.search-compact i {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    font-size: 0.875rem;
}

.form-control-compact {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 0.5rem 0.75rem 0.5rem 2.25rem;
    font-size: 0.875rem;
    background: white;
    transition: all 0.3s ease;
    width: 100%;
}

.form-control-compact:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    outline: none;
}

.btn-compact {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-compact:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(100, 116, 139, 0.4);
}

/* Modern Project Card Design */
.project-card-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.project-card-modern:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

/* Old status indicator removed as requested */

/* Card Header */
.card-header-modern {
    padding: 1.5rem 1.5rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
}

.project-title-section {
    flex: 1;
}

.project-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--smartflo-dark);
    margin: 0 0 0.5rem 0;
    line-height: 1.3;
}

.project-number {
    color: var(--smartflo-primary);
    font-weight: 800;
}

.project-name {
    color: var(--smartflo-dark);
}

.project-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    font-size: 0.85rem;
    color: var(--smartflo-secondary);
}

.client-info,
.location-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.client-info i,
.location-info i {
    font-size: 0.8rem;
    color: var(--smartflo-primary);
}

.days-left-section {
    flex-shrink: 0;
}

/* Card Body */
.card-body-modern {
    padding: 0 1.5rem 1rem;
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 1.5rem;
    align-items: center;
}

/* Action Buttons Section */
.action-buttons-section {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Latest Update Section */
.latest-update-section {
    text-align: right;
}

.update-header {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.5rem;
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--smartflo-secondary);
    margin-bottom: 0.5rem;
}

.update-header i {
    font-size: 0.75rem;
    color: var(--smartflo-primary);
}

.update-content {
    font-size: 0.85rem;
    color: var(--smartflo-secondary);
}

/* Card Footer */
.card-footer-modern {
    padding: 1rem 1.5rem;
    background: rgba(248, 250, 252, 0.8);
    border-top: 1px solid rgba(226, 232, 240, 0.5);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Modern Action Buttons */
.status-action-btn-modern {
    position: relative;
    padding: 0.75rem 1.25rem;
    border: none;
    border-radius: 14px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 90px;
    min-height: 48px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    text-decoration: none;
}

.status-action-btn-modern:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.status-action-btn-modern:active:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-action-btn-modern:disabled {
    cursor: not-allowed;
    opacity: 0.6;
}

/* Modern Button Variants */
.status-action-btn-modern.play-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border: 2px solid rgba(59, 130, 246, 0.3);
}

.status-action-btn-modern.resume-btn {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border: 2px solid rgba(245, 158, 11, 0.3);
}

.status-action-btn-modern.play-btn:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
}

.status-action-btn-modern.resume-btn:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    box-shadow: 0 8px 24px rgba(245, 158, 11, 0.4);
}

.status-action-btn-modern.pause-btn {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border: 2px solid rgba(245, 158, 11, 0.3);
}

.status-action-btn-modern.pause-btn:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    box-shadow: 0 8px 24px rgba(245, 158, 11, 0.4);
}

.status-action-btn-modern.complete-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: 2px solid rgba(16, 185, 129, 0.3);
}

.status-action-btn-modern.complete-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    box-shadow: 0 8px 24px rgba(16, 185, 129, 0.4);
}

/* Status Badges */
.status-completed-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    color: #166534;
    border-radius: 14px;
    font-size: 0.85rem;
    font-weight: 600;
    border: 2px solid #86efac;
}

.status-readonly-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border-radius: 14px;
    font-size: 0.85rem;
    font-weight: 600;
    border: 2px solid;
}

.status-readonly-badge.status-not_started,
.status-readonly-badge.status-planning {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #475569;
    border-color: #cbd5e1;
}

.status-readonly-badge.status-in_progress {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1d4ed8;
    border-color: #93c5fd;
}

.status-readonly-badge.status-on_hold {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #b45309;
    border-color: #fcd34d;
}

.status-readonly-badge.status-review {
    background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
    color: #6d28d9;
    border-color: #c4b5fd;
}

/* Edit/Delete Buttons */
.timeline-btn-modern,
.edit-btn-modern,
.delete-btn-modern {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.timeline-btn-modern {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #475569;
    border: 1px solid #e2e8f0;
}

.timeline-btn-modern:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    color: #334155;
    transform: translateY(-1px);
}

.edit-btn-modern {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1d4ed8;
    border: 1px solid #93c5fd;
}

.edit-btn-modern:hover {
    background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
    color: #1e40af;
    transform: translateY(-1px);
}

.delete-btn-modern {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #dc2626;
    border: 1px solid #f87171;
}

.delete-btn-modern:hover {
    background: linear-gradient(135deg, #fecaca 0%, #f87171 100%);
    color: #b91c1c;
    transform: translateY(-1px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .nav-content {
        flex-direction: column;
        gap: 1rem;
        min-height: auto;
        padding: 0.5rem 0;
    }

    .nav-stats {
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
        order: 1;
    }

    .nav-filters {
        flex-direction: column;
        gap: 0.75rem;
        width: 100%;
        order: 3;
    }

    .form-select-nav,
    .search-nav {
        width: 100%;
        max-width: none;
        min-width: auto;
    }

    .nav-actions {
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
        order: 2;
    }

    .nav-actions .btn-nav {
        width: 100%;
        justify-content: center;
    }



    .stat-item .stat-number {
        font-size: 1.1rem;
    }

    .stat-item .stat-label {
        font-size: 0.65rem;
    }

    .projects-content-area {
        width: 100%;
        padding: 0;
    }

    /* Grid layout handled by main responsive CSS */

    .project-card {
        min-height: 250px;
        max-height: 280px;
    }
}

.header-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    justify-content: flex-end;
}

/* Glass Morphism Buttons */
.btn-glass {
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
}

.btn-glass.btn-outline-light {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

.btn-glass.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-glass.btn-primary {
    background: var(--smartflo-gradient-primary);
    color: white;
    border: none;
}

.btn-glass.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

/* Compact Statistics Cards */
.stats-section {
    margin-bottom: 1.5rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--smartflo-shadow-lg);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
    height: 80px;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all 0.3s ease;
}

.stat-card-primary::before {
    background: var(--smartflo-gradient-primary);
}

.stat-card-info::before {
    background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);
}

.stat-card-warning::before {
    background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
}

.stat-card-success::before {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stat-card-body {
    padding: 0.75rem 1rem;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.stat-card-primary .stat-icon {
    background: var(--smartflo-gradient-primary);
}

.stat-card-info .stat-icon {
    background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);
}

.stat-card-warning .stat-icon {
    background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
}

.stat-card-success .stat-icon {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
}

.stat-content {
    flex-grow: 1;
    margin-left: 1rem;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--smartflo-dark);
    margin-bottom: 0;
    line-height: 1;
}

.stat-label {
    font-size: 0.75rem;
    color: var(--smartflo-secondary);
    margin-bottom: 0;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-trend {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--smartflo-success);
}

.stat-trend i {
    font-size: 0.625rem;
}

/* Compact Filters Section */
.filters-section {
    margin-bottom: 1rem;
}

.filters-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--smartflo-shadow-lg);
    overflow: hidden;
}

.filters-header {
    background: rgba(37, 99, 235, 0.1);
    padding: 0.5rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.filters-title {
    margin: 0;
    color: var(--smartflo-primary);
    font-weight: 600;
    font-size: 0.875rem;
}

.filters-body {
    padding: 1rem;
}

.form-select-modern,
.form-control-modern {
    border: 2px solid rgba(37, 99, 235, 0.1);
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.875rem;
}

.form-select-modern:focus,
.form-control-modern:focus {
    border-color: var(--smartflo-primary);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    background: white;
}

.search-input-group {
    position: relative;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--smartflo-secondary);
    z-index: 2;
}

.search-input-group .form-control-modern {
    padding-left: 2.5rem;
}



/* Projects Content Area - Full width, no separate scrolling */
.projects-content-area {
    width: 100%;
    padding: 0;
    position: relative;
}

/* Grid layout is defined in the responsive section below */

/* Empty State Styling */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    text-align: center;
    color: #64748b;
    grid-column: 1 / -1;
}

.empty-state i {
    font-size: 4rem;
    color: #cbd5e1;
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #475569;
    margin-bottom: 0.5rem;
}

.empty-state p {
    font-size: 1rem;
    color: #64748b;
    margin-bottom: 2rem;
    max-width: 400px;
}

.empty-state .btn {
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 12px;
}

/* Compact project cards for better space utilization */
.project-card {
    height: auto;
    min-height: 280px;
    max-height: 320px;
    overflow: hidden;
}

/* Ultra-Modern Modal Design */
.modern-modal {
    border: none;
    border-radius: 24px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.98);
    overflow: hidden;
}

.modern-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem;
    border: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header-content {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.modal-icon-modern {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    backdrop-filter: blur(10px);
}

.modal-title-section {
    color: white;
}

.modern-modal-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modern-modal-subtitle {
    font-size: 1rem;
    margin: 0;
    opacity: 0.9;
    font-weight: 400;
}

.modern-close-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.modern-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

/* Modern Modal Body */
.modern-modal-body {
    padding: 2rem;
    background: white;
    max-height: 70vh;
    overflow-y: auto;
}

/* Modern Progress Steps */
.modern-progress-steps {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 16px;
}

.step-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.step-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #cbd5e1;
    transition: all 0.3s ease;
    position: relative;
}

.step-indicator.active .step-dot {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.step-indicator.completed .step-dot {
    background: #10b981;
    transform: scale(1.1);
}

.step-text {
    font-size: 0.75rem;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.step-indicator.active .step-text {
    color: #667eea;
}

.step-line {
    width: 60px;
    height: 2px;
    background: #e2e8f0;
    margin: 0 1rem;
    transition: all 0.3s ease;
}

.step-line.completed {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

/* Modern Form Steps */
.modern-form-step {
    display: none;
    animation: fadeInUp 0.3s ease;
}

.modern-form-step.active {
    display: block;
}

.step-header-modern {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.step-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.step-description {
    font-size: 0.875rem;
    color: #64748b;
    margin: 0;
}

/* Fixed Floating Labels - No Overlap Issues */
.form-floating {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-floating > .form-control {
    height: calc(3.5rem + 2px);
    line-height: 1.25;
    padding: 1.2rem 0.75rem 0.4rem 0.75rem;
    background-color: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 1rem;
}

.form-floating > .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background-color: white;
    outline: 0;
}

.form-floating > label {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 1.2rem 0.75rem 0.4rem 0.75rem;
    color: #64748b;
    font-weight: 600;
    font-size: 0.875rem;
    pointer-events: none;
    transition: all 0.3s ease;
    transform-origin: 0 0;
    z-index: 3;
    background: transparent;
    border: none;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: 1;
    transform: scale(0.85) translateY(-0.9rem) translateX(0.15rem);
    color: #667eea;
    background: white;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    width: auto;
    height: auto;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 5;
}

/* Fix textarea floating labels */
.form-floating > textarea.form-control {
    min-height: 120px;
    padding-top: 1.5rem;
    resize: vertical;
}

.form-floating > textarea.form-control ~ label {
    padding-top: 1.5rem;
}

/* Ensure labels don't interfere with input text */
.form-floating > .form-control::placeholder {
    color: transparent;
}

.form-floating > .form-control:focus::placeholder {
    color: #6c757d;
}

/* Modern Modal Footer */
.modern-modal-footer {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1.5rem 2rem;
    border-top: 1px solid #e2e8f0;
}

.footer-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-actions-right {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Modern Buttons */
.modern-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.875rem;
    border: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.modern-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.modern-btn:hover::before {
    left: 100%;
}

.modern-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.modern-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

.modern-btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.modern-btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.6);
}

.modern-btn-secondary {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(100, 116, 139, 0.4);
}

.modern-btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(100, 116, 139, 0.6);
}

.modern-btn-outline {
    background: transparent;
    color: #64748b;
    border: 2px solid #e2e8f0;
}

.modern-btn-outline:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
    transform: translateY(-1px);
}

/* Task Management Section */
.task-management-section {
    background: #f8fafc;
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
}

.tasks-list {
    max-height: 400px;
    overflow-y: auto;
}

.task-item {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.task-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.task-title-input {
    flex: 1;
    border: none;
    background: transparent;
    font-weight: 600;
    font-size: 1rem;
    color: #1e293b;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.task-title-input:focus {
    background: #f8fafc;
    outline: none;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.task-remove-btn {
    background: #fee2e2;
    color: #dc2626;
    border: none;
    border-radius: 8px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.task-remove-btn:hover {
    background: #fecaca;
    transform: scale(1.1);
}

.task-details {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1rem;
}

.task-field {
    display: flex;
    flex-direction: column;
}

.task-field label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #64748b;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.task-field select,
.task-field input {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 0.5rem;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.task-field select:focus,
.task-field input:focus {
    border-color: #667eea;
    outline: none;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Professional gradient color scheme for project states */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    border: none;
    color: white;
    text-shadow: 0 1px 3px rgba(0,0,0,0.2);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.status-not_started {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
}

.status-in_progress {
    background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
    box-shadow: 0 2px 4px rgba(13, 110, 253, 0.3);
}

.status-on_hold {
    background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
    box-shadow: 0 2px 4px rgba(253, 126, 20, 0.3);
}

.status-completed {
    background: linear-gradient(135deg, #198754 0%, #146c43 100%);
    box-shadow: 0 2px 4px rgba(25, 135, 84, 0.3);
}

.project-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.project-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.stat-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.progress-bar-custom {
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-fill.not_started {
    background: linear-gradient(90deg, #6c757d 0%, #495057 100%);
}

.progress-fill.in_progress {
    background: linear-gradient(90deg, #0d6efd 0%, #0a58ca 100%);
}

.progress-fill.on_hold {
    background: linear-gradient(90deg, #fd7e14 0%, #e55a00 100%);
}

.progress-fill.completed {
    background: linear-gradient(90deg, #198754 0%, #146c43 100%);
}

.client-badge {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-block;
    border: 1px solid #dee2e6;
}

.action-btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.action-btn:hover {
    transform: translateY(-1px);
}

.btn-play {
    background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
    border: none;
    color: white;
}

.btn-hold {
    background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
    border: none;
    color: white;
}

.btn-complete {
    background: linear-gradient(135deg, #198754 0%, #146c43 100%);
    border: none;
    color: white;
}

.time-tracking {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 0.75rem;
    margin-top: 1rem;
}

.time-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
    font-size: 0.875rem;
}

.time-label {
    color: #6c757d;
    font-weight: 500;
}

.time-value {
    color: #495057;
    font-weight: 600;
}

/* Enhanced Modal Styles */
.bg-gradient-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
}

.modal-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-steps {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    margin-bottom: 2rem;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 2px;
    background: #e9ecef;
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background: #0d6efd;
    color: white;
    transform: scale(1.1);
}

.step.completed .step-number {
    background: #198754;
    color: white;
}

.step-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6c757d;
    transition: color 0.3s ease;
}

.step.active .step-label {
    color: #0d6efd;
    font-weight: 600;
}

.step.completed .step-label {
    color: #198754;
}

.form-step {
    display: none;
    animation: fadeInUp 0.3s ease;
}

.form-step.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-header {
    text-align: center;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

/* Enhanced Form Floating Styles */
.form-floating {
    position: relative;
}

.form-floating > .form-control {
    height: calc(3.5rem + 2px);
    line-height: 1.25;
    padding: 1rem 0.75rem 0.25rem 0.75rem;
    background-color: #fff;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.form-floating > .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    outline: 0;
}

.form-floating > .form-control:not(:placeholder-shown),
.form-floating > .form-control:focus {
    padding-top: 1.625rem;
    padding-bottom: 0.625rem;
}

.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-control:focus ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    color: #0d6efd;
}

.form-floating > label {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    padding: 1rem 0.75rem;
    pointer-events: none;
    border: 2px solid transparent;
    transform-origin: 0 0;
    transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
    color: #6c757d;
    font-weight: 500;
}

.form-floating > .form-control::placeholder {
    color: transparent;
}

.form-floating > .form-control:focus::placeholder {
    color: #6c757d;
}

/* Textarea specific styles */
.form-floating > textarea.form-control {
    padding-top: 1.625rem;
    padding-bottom: 0.625rem;
    resize: vertical;
    min-height: calc(3.5rem + 2px);
}

.form-floating > textarea.form-control ~ label {
    padding-top: 1rem;
}

/* Date input specific styles */
.form-floating > input[type="date"].form-control {
    padding-top: 1.625rem;
    padding-bottom: 0.625rem;
}

.form-floating > input[type="date"].form-control:not(:placeholder-shown) ~ label,
.form-floating > input[type="date"].form-control:focus ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

.user-preview-card .user-avatar-preview {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #0d6efd, #6610f2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.25rem;
    margin: 0 auto;
}

/* Modern Project Cards with Glass Morphism */
.project-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    box-shadow: var(--smartflo-shadow-xl);
    padding: 2rem;
    margin-bottom: 2rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: var(--smartflo-gradient-primary);
    opacity: 0;
    transition: all 0.3s ease;
}

.project-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.project-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border-color: rgba(37, 99, 235, 0.3);
}

.project-card:hover::before {
    opacity: 1;
}

.project-card:hover::after {
    opacity: 1;
}

/* Responsive Grid Layout for Project Cards */
#projectsList {
    display: grid;
    gap: 1.5rem;
    align-items: stretch;
    padding: 0;
}

/* Big screens (1400px+): 4 cards per row */
@media (min-width: 1400px) {
    #projectsList {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Medium Laptops (1024px-1399px): 3 cards per row */
@media (min-width: 1024px) and (max-width: 1399px) {
    #projectsList {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Tablet (768px-1023px): 2 cards per row */
@media (min-width: 768px) and (max-width: 1023px) {
    #projectsList {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
}

/* Mobile (below 768px): 1 card per row, full width */
@media (max-width: 767px) {
    #projectsList {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0;
        margin: 0;
    }
}

.project-card h5 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.3;
    /* Truncate long titles */
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 2.6rem;
}

.project-card p {
    /* Truncate long descriptions */
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 4.5rem;
}

.project-card .client-badge {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

/* Ensure card content is properly distributed */
.project-card > div:last-child {
    margin-top: auto;
}

/* ===== COMPACT PROJECT CARDS - MINIMALIST DESIGN ===== */
.project-card-compact {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(0, 0, 0, 0.04);
    margin-bottom: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-height: 120px;
}

.project-card-compact:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    border-color: rgba(0, 0, 0, 0.08);
}

/* Status strip removed as requested */

/* Card Content Layout */
.card-content {
    padding: 1.5rem;
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
}

/* Project Info Section */
.project-info {
    min-width: 0;
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.project-id {
    font-size: 0.8rem;
    font-weight: 700;
    color: #64748b;
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.days-left {
    font-size: 0.8rem;
    font-weight: 600;
}

.days-overdue {
    color: #dc2626;
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    animation: pulse 2s infinite;
}

.days-today {
    color: #ea580c;
    background: linear-gradient(135deg, #fed7aa, #fdba74);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
}

.days-urgent {
    color: #f59e0b;
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
}

.days-normal {
    color: #64748b;
    font-weight: 500;
}

.project-name {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Project Actions Section */
.project-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
}

.action-btn-compact {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.6rem 1rem;
    border: none;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    min-width: 90px;
    min-height: 36px;
    color: white;
}

.action-btn-compact i {
    font-size: 0.9rem;
}

/* Action Button Variants */
.play-btn {
    background: linear-gradient(135deg, #10b981, #059669);
    box-shadow: 0 2px 10px rgba(16, 185, 129, 0.3);
}

.play-btn:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
    color: white;
}

.hold-btn {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    box-shadow: 0 2px 10px rgba(245, 158, 11, 0.3);
}

.hold-btn:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
    color: white;
}

.complete-btn {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);
}

.complete-btn:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
    color: white;
}

.review-btn {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    box-shadow: 0 2px 10px rgba(139, 92, 246, 0.3);
}

.review-btn:hover {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4);
    color: white;
}

.delete-btn {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    box-shadow: 0 2px 10px rgba(239, 68, 68, 0.3);
}

.delete-btn:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
    color: white;
}

/* Project Updates Section */
.project-updates {
    text-align: right;
    min-width: 0;
}

.latest-update {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.update-text {
    font-size: 0.85rem;
    color: #475569;
    line-height: 1.4;
    margin-bottom: 0.25rem;
}

.update-time {
    font-size: 0.75rem;
    color: #94a3b8;
    font-weight: 500;
}

.no-update {
    font-size: 0.85rem;
    color: #94a3b8;
    font-style: italic;
}

.timeline-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.timeline-btn,
.share-btn {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    padding: 0.5rem 0.75rem;
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    color: #64748b;
    border: 1px solid #cbd5e1;
    border-radius: 8px;
    font-size: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
}

.timeline-btn:hover,
.share-btn:hover {
    background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
    color: #475569;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.timeline-btn i,
.share-btn i {
    font-size: 0.7rem;
}

/* Timeline Display */
.timeline-container {
    position: relative;
    padding: 1rem 0;
}

.timeline-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 12px;
    top: 24px;
    bottom: -24px;
    width: 2px;
    background: #e2e8f0;
}

.timeline-marker {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    flex-shrink: 0;
    position: relative;
    z-index: 1;
}

.timeline-marker.status-not_started,
.timeline-marker.status-planning {
    background: linear-gradient(135deg, #64748b, #475569);
}

.timeline-marker.status-in_progress {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.timeline-marker.status-on_hold {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.timeline-marker.status-review {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.timeline-marker.status-completed {
    background: linear-gradient(135deg, #10b981, #059669);
}

.timeline-content {
    flex: 1;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1rem;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.timeline-status {
    font-weight: 600;
    color: #1e293b;
    font-size: 0.9rem;
}

.timeline-date {
    font-size: 0.8rem;
    color: #64748b;
    font-weight: 500;
}

.timeline-user {
    font-size: 0.8rem;
    color: #64748b;
    margin-bottom: 0.25rem;
}

.timeline-duration {
    font-size: 0.8rem;
    color: #059669;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.timeline-notes {
    font-size: 0.85rem;
    color: #475569;
    line-height: 1.5;
    background: white;
    padding: 0.75rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.project-title-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.75rem;
}

.project-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
    line-height: 1.3;
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.project-meta-row {
    display: flex;
    gap: 1.5rem;
    align-items: center;
    flex-wrap: wrap;
}

.client-name,
.project-id {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #64748b;
    font-weight: 500;
}

.client-name i,
.project-id i {
    color: #94a3b8;
    font-size: 0.8rem;
}

/* Urgency Indicators */
.urgency-indicator {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    animation: pulse 2s infinite;
    flex-shrink: 0;
}

.urgency-indicator.overdue {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    box-shadow: 0 4px 20px rgba(220, 38, 38, 0.3);
}

.urgency-indicator.critical {
    background: linear-gradient(135deg, #ea580c, #c2410c);
    box-shadow: 0 4px 20px rgba(234, 88, 12, 0.3);
}

.urgency-indicator.urgent {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    box-shadow: 0 4px 20px rgba(245, 158, 11, 0.3);
}

/* Status Display */
.status-display {
    flex-shrink: 0;
}

.status-badge-modern {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.status-badge-modern.status-not_started,
.status-badge-modern.status-planning {
    background: linear-gradient(135deg, #64748b, #475569);
}

.status-badge-modern.status-in_progress {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.status-badge-modern.status-on_hold {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.status-badge-modern.status-completed {
    background: linear-gradient(135deg, #10b981, #059669);
}

/* Card Body */
.card-body-modern {
    padding: 0 2rem 2rem 2rem;
    display: grid;
    grid-template-columns: 1fr 200px 1fr;
    gap: 2rem;
    align-items: start;
}

/* Details Column */
.details-column {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.detail-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.detail-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #64748b;
    font-size: 1rem;
    flex-shrink: 0;
}

.detail-content {
    flex: 1;
    min-width: 0;
}

.detail-label {
    display: block;
    font-size: 0.75rem;
    font-weight: 600;
    color: #94a3b8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.detail-value {
    display: block;
    font-size: 0.95rem;
    font-weight: 600;
    color: #334155;
    line-height: 1.4;
}

/* Date Status Indicators */
.date-status {
    display: block;
    font-size: 0.8rem;
    font-weight: 500;
    margin-top: 0.25rem;
}

.date-status.overdue { color: #dc2626; }
.date-status.today { color: #ea580c; }
.date-status.urgent { color: #f59e0b; }
.date-status.normal { color: #64748b; }

/* Content Column */
.content-column {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    text-align: center;
}

/* Progress Circle */
.progress-circle-container {
    position: relative;
}

.progress-circle {
    position: relative;
    width: 80px;
    height: 80px;
}

.progress-ring {
    transform: rotate(-90deg);
}

.progress-ring-background {
    fill: none;
    stroke: #e2e8f0;
    stroke-width: 6;
}

.progress-ring-fill {
    fill: none;
    stroke-width: 6;
    stroke-linecap: round;
    transition: stroke-dashoffset 0.5s ease;
}

.progress-ring-fill.status-not_started,
.progress-ring-fill.status-planning {
    stroke: #64748b;
}

.progress-ring-fill.status-in_progress {
    stroke: #3b82f6;
}

.progress-ring-fill.status-on_hold {
    stroke: #f59e0b;
}

.progress-ring-fill.status-completed {
    stroke: #10b981;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.progress-number {
    display: block;
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    line-height: 1;
}

.progress-label {
    display: block;
    font-size: 0.7rem;
    font-weight: 500;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.25rem;
}

/* Project Description */
.project-description-modern {
    font-size: 0.9rem;
    color: #64748b;
    line-height: 1.6;
    text-align: center;
    max-width: 100%;
}

/* Latest Update */
.latest-update-modern {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1rem;
    width: 100%;
    text-align: left;
}

.latest-update-modern .update-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.8rem;
    font-weight: 600;
    color: #3b82f6;
}

.latest-update-modern .update-time {
    margin-left: auto;
    color: #94a3b8;
    font-weight: 500;
}

.latest-update-modern .update-text {
    font-size: 0.85rem;
    color: #64748b;
    line-height: 1.5;
}

.no-updates {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #94a3b8;
    font-style: italic;
}

/* Actions Column */
.actions-column {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: stretch;
}

.action-buttons-container {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 0.875rem 1.25rem;
    border: none;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    min-height: 48px;
}

.action-btn i {
    font-size: 1rem;
}

.action-btn span {
    font-size: 0.875rem;
    font-weight: 600;
}

/* Action Button Variants */
.action-start {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.25);
}

.action-start:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(16, 185, 129, 0.35);
    color: white;
}

.action-hold {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    box-shadow: 0 4px 20px rgba(245, 158, 11, 0.25);
}

.action-hold:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(245, 158, 11, 0.35);
    color: white;
}

.action-complete {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.25);
}

.action-complete:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(59, 130, 246, 0.35);
    color: white;
}

.action-resume {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.25);
}

.action-resume:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(16, 185, 129, 0.35);
    color: white;
}

.action-view {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    color: #475569;
    border: 2px solid #e2e8f0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.action-view:hover {
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    color: #334155;
    transform: translateY(-2px);
    border-color: #cbd5e1;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.action-delete {
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
    color: #dc2626;
    border: 2px solid #fecaca;
    box-shadow: 0 2px 10px rgba(220, 38, 38, 0.1);
    padding: 0.875rem;
    min-height: 48px;
}

.action-delete:hover {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #b91c1c;
    transform: translateY(-2px);
    border-color: #f87171;
    box-shadow: 0 4px 20px rgba(220, 38, 38, 0.2);
}

.action-delete span {
    display: none;
}

/* Completed Indicator */
.completed-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 0.875rem 1.25rem;
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    color: #166534;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 600;
    border: 2px solid #86efac;
    min-height: 48px;
}

.completed-indicator i {
    color: #16a34a;
    font-size: 1rem;
}

/* Quick Info */
.quick-info {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.info-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.info-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #94a3b8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 0.8rem;
    font-weight: 600;
    color: #475569;
    text-align: right;
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
}

.project-title-section {
    flex: 1;
}

.project-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--smartflo-dark);
    margin: 0 0 0.5rem 0;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.project-meta {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.client-badge {
    background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
    color: #0277bd;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    border: 1px solid rgba(2, 119, 189, 0.2);
}

.project-id-badge {
    background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
    color: #7c3aed;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    border: 1px solid rgba(124, 58, 237, 0.2);
}

/* Urgency Badges */
.urgency-badge {
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    animation: pulse 2s infinite;
}

.urgency-badge.overdue {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #dc2626;
    border: 1px solid rgba(220, 38, 38, 0.3);
}

.urgency-badge.urgent {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #d97706;
    border: 1px solid rgba(217, 119, 6, 0.3);
}

.urgency-badge.soon {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #2563eb;
    border: 1px solid rgba(37, 99, 235, 0.3);
}

/* Days Until Target */
.days-overdue { color: #dc2626; font-weight: 600; }
.days-today { color: #f59e0b; font-weight: 600; }
.days-soon { color: #2563eb; font-weight: 600; }
.days-normal { color: #64748b; font-weight: 500; }

.project-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.location-info,
.date-info {
    font-size: 0.875rem;
    color: var(--smartflo-secondary);
    display: flex;
    align-items: center;
}

.location-info i,
.date-info i {
    color: var(--smartflo-primary);
    width: 16px;
}

/* Progress Section */
.progress-section {
    margin-top: 0.5rem;
}

/* Time Tracking Section */
.time-tracking-section {
    margin-top: 1rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.time-tracking-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.time-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--smartflo-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.current-status-time {
    font-size: 1rem;
    font-weight: 700;
    color: var(--smartflo-primary);
    margin-bottom: 0.25rem;
}

.total-project-time {
    font-size: 0.75rem;
    color: #64748b;
}

/* Status History Preview */
.status-history-preview {
    margin-top: 1rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.history-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.history-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--smartflo-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.history-item:last-child {
    border-bottom: none;
}

.history-status {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--smartflo-dark);
}

.history-time {
    font-size: 0.75rem;
    color: #64748b;
}

/* ===== ANIMATIONS & EFFECTS ===== */
@keyframes pulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Apply entrance animation to cards */
.project-card-modern {
    animation: slideInUp 0.6s ease-out;
}

.project-card-modern:nth-child(2) { animation-delay: 0.1s; }
.project-card-modern:nth-child(3) { animation-delay: 0.2s; }
.project-card-modern:nth-child(4) { animation-delay: 0.3s; }
.project-card-modern:nth-child(5) { animation-delay: 0.4s; }

/* Hover effects for interactive elements */
.detail-icon:hover {
    background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
    transform: scale(1.1);
    transition: all 0.3s ease;
}

.progress-circle:hover {
    transform: scale(1.1);
    transition: all 0.3s ease;
}

/* Button press effect */
.action-btn:active {
    transform: translateY(1px) scale(0.98);
}

/* Status badge hover effect */
.status-badge-modern:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
}

/* Enhanced Status Badge Gradients */
.status-badge.status-not_started,
.status-badge.status-planning {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(100, 116, 139, 0.3);
}

.status-badge.status-in_progress {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.status-badge.status-on_hold {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.status-badge.status-completed {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.progress-label {
    font-size: 0.75rem;
    color: var(--smartflo-secondary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.progress-percentage {
    font-size: 0.875rem;
    font-weight: 700;
    color: var(--smartflo-dark);
}

.progress-bar-horizontal {
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.progress-fill-horizontal {
    height: 100%;
    border-radius: 3px;
    transition: all 0.3s ease;
    position: relative;
}

.progress-start { background: linear-gradient(90deg, #94a3b8 0%, #64748b 100%); }
.progress-low { background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%); }
.progress-medium { background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%); }
.progress-high { background: linear-gradient(90deg, #10b981 0%, #059669 100%); }
.progress-completed { background: linear-gradient(90deg, #059669 0%, #047857 100%); }
.progress-hold { background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%); }

/* Center Section: Content */
.project-content-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    min-width: 0;
}

.project-description {
    font-size: 0.875rem;
    color: var(--smartflo-secondary);
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Update Cards */
.latest-update-horizontal {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid rgba(14, 165, 233, 0.2);
    border-radius: 12px;
    padding: 0.75rem;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.latest-update-horizontal .update-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.latest-update-horizontal .update-content {
    flex: 1;
    min-width: 0;
}

.latest-update-horizontal .update-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #0284c7;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: 0.25rem;
}

.latest-update-horizontal .update-text {
    font-size: 0.875rem;
    color: var(--smartflo-secondary);
    display: block;
    margin-bottom: 0.25rem;
    line-height: 1.4;
}

.latest-update-horizontal .update-time {
    font-size: 0.75rem;
    color: #64748b;
    font-weight: 500;
}

.no-update-horizontal {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: #94a3b8;
    font-style: italic;
}

/* Right Section: Action Buttons */
.project-actions-section {
    flex: 0 0 140px;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
}

.btn-action {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    min-height: 36px;
    position: relative;
    overflow: hidden;
}

.btn-action i {
    font-size: 0.875rem;
}

.btn-action span {
    font-size: 0.75rem;
    font-weight: 600;
}

/* Start Button - Blue Theme */
.btn-start {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.btn-start:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    color: white;
}

/* Hold Button */
.btn-hold {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.btn-hold:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
    color: white;
}

/* Complete Button - Green Theme */
.btn-complete {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.btn-complete:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    color: white;
}

/* Resume Button - Orange Theme */
.btn-resume {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.btn-resume:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
    color: white;
}

/* View Button */
.btn-view {
    background: rgba(100, 116, 139, 0.1);
    color: #475569;
    border: 1px solid rgba(100, 116, 139, 0.2);
}

.btn-view:hover {
    background: rgba(100, 116, 139, 0.15);
    color: #334155;
    transform: translateY(-2px);
    border-color: rgba(100, 116, 139, 0.3);
}

/* Delete Button */
.btn-delete {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
    padding: 0.5rem;
    min-height: 32px;
}

.btn-delete:hover {
    background: rgba(239, 68, 68, 0.15);
    color: #b91c1c;
    transform: translateY(-2px);
    border-color: rgba(239, 68, 68, 0.3);
}

.btn-delete span {
    display: none;
}

/* Completed Badge */
.completed-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    color: #166534;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    border: 1px solid rgba(22, 101, 52, 0.2);
}

.completed-badge i {
    color: #16a34a;
}

/* ===== MOBILE RESPONSIVE STYLES FOR COMPACT CARDS ===== */
@media (max-width: 768px) {
    .project-card-compact {
        margin-bottom: 0.75rem;
        min-height: auto;
    }

    .card-content {
        padding: 1rem;
        grid-template-columns: 1fr;
        gap: 1rem;
        text-align: center;
    }

    .project-info {
        order: 1;
    }

    .project-header {
        flex-direction: column;
        gap: 0.5rem;
        align-items: center;
    }

    .project-name {
        font-size: 1.1rem;
        white-space: normal;
        text-align: center;
    }

    .project-actions {
        order: 2;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.4rem;
    }

    .action-btn-compact {
        flex: 1;
        min-width: 80px;
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
        min-height: 32px;
    }

    .project-updates {
        order: 3;
        text-align: center;
    }

    .latest-update {
        margin-bottom: 0.75rem;
        padding: 0.6rem;
        min-height: 50px;
    }

    .update-text {
        font-size: 0.8rem;
    }

    .update-time {
        font-size: 0.7rem;
    }

    .timeline-actions {
        justify-content: center;
        gap: 0.4rem;
    }

    .timeline-btn,
    .share-btn {
        padding: 0.4rem 0.6rem;
        font-size: 0.7rem;
    }
}

    .card-header-modern {
        padding: 1.5rem 1.5rem 1rem 1.5rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .project-title-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .project-name {
        font-size: 1.25rem;
        white-space: normal;
        overflow: visible;
        text-overflow: unset;
    }

    .urgency-indicator {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .project-meta-row {
        gap: 1rem;
    }

    .status-badge-modern {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
        align-self: flex-start;
    }

    .card-body-modern {
        padding: 0 1.5rem 1.5rem 1.5rem;
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .details-column {
        order: 1;
        gap: 1rem;
    }

    .detail-item {
        gap: 0.75rem;
    }

    .detail-icon {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .detail-label {
        font-size: 0.7rem;
    }

    .detail-value {
        font-size: 0.85rem;
    }

    .content-column {
        order: 2;
        gap: 1rem;
    }

    .progress-circle {
        width: 70px;
        height: 70px;
    }

    .progress-circle svg {
        width: 70px;
        height: 70px;
    }

    .progress-number {
        font-size: 1.1rem;
    }

    .progress-label {
        font-size: 0.65rem;
    }

    .project-description-modern {
        font-size: 0.85rem;
    }

    .latest-update-modern {
        padding: 0.75rem;
    }

    .latest-update-modern .update-header {
        font-size: 0.75rem;
        gap: 0.4rem;
    }

    .latest-update-modern .update-text {
        font-size: 0.8rem;
    }

    .actions-column {
        order: 3;
        gap: 1rem;
    }

    .action-buttons-container {
        flex-direction: row;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .action-btn {
        flex: 1;
        min-width: 0;
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
        min-height: 44px;
    }

    .action-btn i {
        font-size: 0.9rem;
    }

    .action-btn span {
        font-size: 0.75rem;
    }

    .action-delete {
        flex: 0 0 44px;
        padding: 0.75rem;
    }

    .quick-info {
        padding: 0.75rem;
    }

    .info-item {
        padding: 0.4rem 0;
    }

    .info-label {
        font-size: 0.7rem;
    }

    .info-value {
        font-size: 0.75rem;
    }
}

    .project-info-section {
        flex: 1;
        width: 100%;
    }

    .project-header {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }

    .project-title-section {
        width: 100%;
    }

    .project-meta {
        margin-bottom: 0.5rem;
    }

    .project-content-section {
        width: 100%;
        order: 2;
    }

    .project-actions-section {
        flex: none;
        width: 100%;
        flex-direction: row;
        gap: 0.5rem;
        order: 3;
    }

    .btn-action {
        flex: 1;
        min-height: 40px;
        font-size: 0.8rem;
    }

    .btn-action span {
        font-size: 0.7rem;
    }

    .btn-delete {
        flex: 0 0 40px;
    }

    .progress-section {
        margin-top: 0.75rem;
    }

    .time-tracking-section,
    .status-history-preview {
        margin-top: 0.5rem;
        padding: 0.5rem;
    }

    .time-tracking-header,
    .history-header {
        margin-bottom: 0.25rem;
    }

    .current-status-time {
        font-size: 0.9rem;
    }

    .urgency-badge {
        font-size: 0.65rem;
        padding: 0.15rem 0.5rem;
    }

    .latest-update-horizontal {
        padding: 0.5rem;
        gap: 0.5rem;
    }

    .latest-update-horizontal .update-icon {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }
}

@media (max-width: 576px) {
    .project-card-modern {
        margin-bottom: 0.75rem;
        border-radius: 16px;
    }

    .card-header-modern {
        padding: 1.25rem 1.25rem 0.75rem 1.25rem;
    }

    .project-name {
        font-size: 1.1rem;
    }

    .project-meta-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .client-name,
    .project-id {
        font-size: 0.8rem;
    }

    .urgency-indicator {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }

    .status-badge-modern {
        padding: 0.5rem 0.875rem;
        font-size: 0.75rem;
    }

    .card-body-modern {
        padding: 0 1.25rem 1.25rem 1.25rem;
        gap: 1.25rem;
    }

    .detail-icon {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }

    .detail-label {
        font-size: 0.65rem;
    }

    .detail-value {
        font-size: 0.8rem;
    }

    .progress-circle {
        width: 60px;
        height: 60px;
    }

    .progress-circle svg {
        width: 60px;
        height: 60px;
    }

    .progress-number {
        font-size: 1rem;
    }

    .progress-label {
        font-size: 0.6rem;
    }

    .project-description-modern {
        font-size: 0.8rem;
        line-height: 1.5;
    }

    .latest-update-modern {
        padding: 0.6rem;
    }

    .latest-update-modern .update-header {
        font-size: 0.7rem;
        margin-bottom: 0.4rem;
    }

    .latest-update-modern .update-text {
        font-size: 0.75rem;
        line-height: 1.4;
    }

    .action-buttons-container {
        gap: 0.4rem;
    }

    .action-btn {
        padding: 0.6rem 0.8rem;
        font-size: 0.75rem;
        min-height: 40px;
        border-radius: 10px;
    }

    .action-btn i {
        font-size: 0.8rem;
    }

    .action-btn span {
        font-size: 0.7rem;
    }

    .action-delete {
        flex: 0 0 40px;
        padding: 0.6rem;
    }

    .completed-indicator {
        padding: 0.6rem 0.8rem;
        font-size: 0.75rem;
        min-height: 40px;
    }

    .quick-info {
        padding: 0.6rem;
    }

    .info-item {
        padding: 0.35rem 0;
    }

    .info-label {
        font-size: 0.65rem;
    }

    .info-value {
        font-size: 0.7rem;
    }
}

/* Latest Update Highlighting */
.latest-update-card {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 1px solid rgba(13, 110, 253, 0.2);
    border-radius: 12px;
    padding: 1rem;
    margin-top: 1rem;
    position: relative;
    overflow: hidden;
}

.latest-update-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #0d6efd, #6610f2);
}

.update-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #0d6efd, #6610f2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    margin-right: 0.75rem;
    flex-shrink: 0;
    animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.4);
    }
    50% {
        box-shadow: 0 0 0 8px rgba(13, 110, 253, 0);
    }
}

.update-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.update-label {
    font-weight: 600;
    color: #0d6efd;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.update-time {
    font-size: 0.75rem;
    color: #6c757d;
    background: rgba(255,255,255,0.8);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    margin-left: auto;
}

.update-content {
    color: #495057;
    font-size: 0.875rem;
    line-height: 1.4;
    font-weight: 500;
}

.no-update-card {
    background: rgba(108, 117, 125, 0.1);
    border: 1px dashed rgba(108, 117, 125, 0.3);
    border-radius: 8px;
    padding: 0.75rem;
    margin-top: 1rem;
    text-align: center;
    font-size: 0.875rem;
}

/* Enhanced Status Badges */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    border: none;
    color: white;
    text-shadow: 0 1px 3px rgba(0,0,0,0.2);
    position: relative;
    overflow: hidden;
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s ease;
}

.status-badge:hover::before {
    left: 100%;
}

/* Enhanced Client Badge */
.client-badge {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    color: #495057;
    padding: 0.375rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-block;
    border: 2px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.client-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: #0d6efd;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    /* Hide statistics cards on mobile to save space */
    .stats-section {
        display: none;
    }

    /* Simplify header for mobile */
    .page-header {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .page-title {
        font-size: 1.75rem;
    }

    .header-actions {
        justify-content: center;
        margin-top: 1rem;
    }

    .header-actions .btn {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
    }

    /* Mobile: Full width cards with minimal margins globally */
    body, .container, .container-fluid {
        padding-left: 8px !important;
        padding-right: 8px !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
    }

    #projectsList {
        grid-template-columns: 1fr !important;
        gap: 0.75rem;
        padding: 0 !important;
        margin: 0 !important;
    }

    .projects-content-area {
        padding: 0 !important;
        margin: 0 !important;
    }

    .project-card-final {
        margin-left: 0 !important;
        margin-right: 0 !important;
        width: 100% !important;
        padding: 12px !important; /* Reduced padding for mobile */
    }

    /* Reduce header and filter padding on mobile */
    .header-section, .filter-section {
        padding-left: 8px !important;
        padding-right: 8px !important;
    }

    /* Ensure content fits screen better */
    .main-content {
        padding: 8px !important;
    }



    .project-card {
        padding: 1.5rem;
        margin-bottom: 1rem;
        border-radius: 16px;
    }

    .project-card h5 {
        font-size: 1rem;
        min-height: 2rem;
    }

    /* Simplify filters for mobile */
    .filters-section .row > div {
        margin-bottom: 0.75rem;
    }

    .filters-section .btn {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
    }

    /* Hide less important filter options */
    .filters-section .col-md-3:nth-child(3),
    .filters-section .col-md-3:nth-child(4) {
        display: none;
    }

    /* Modal adjustments */
    .progress-steps {
        gap: 1rem;
    }

    .progress-steps::before {
        width: 50%;
    }

    .step-label {
        font-size: 0.75rem;
    }

    .modal-xl {
        max-width: 95%;
    }

    /* Prioritize project content */
    .project-content-priority {
        order: -1;
    }

    /* Compact action buttons */
    .action-btn {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }

    /* Latest update card mobile optimization */
    .latest-update-card {
        padding: 0.75rem;
        margin-top: 0.75rem;
    }

    .update-icon {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
        margin-right: 0.5rem;
    }

    .update-content {
        font-size: 0.8rem;
    }
}

/* Loading and success animations */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Form validation styles */
.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-valid {
    border-color: #198754;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5 6.46 3.56 4.23 5.79z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Project Details Modal Styles */
.project-id-badge {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.project-info-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.project-progress-circle {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto;
}

.progress-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: conic-gradient(#0d6efd 0deg, #e9ecef 0deg);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.progress-circle::before {
    content: '';
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: white;
}

.progress-text {
    position: relative;
    z-index: 2;
    font-weight: 600;
    color: #495057;
}

/* Task Card Styles */
.task-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    position: relative;
}

.task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.task-card.task-not_started {
    border-left: 4px solid #6c757d;
}

.task-card.task-in_progress {
    border-left: 4px solid #0d6efd;
}

.task-card.task-on_hold {
    border-left: 4px solid #fd7e14;
}

.task-card.task-completed {
    border-left: 4px solid #198754;
    opacity: 0.8;
}

.task-type-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
    margin-right: 1rem;
}

.task-priority {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.task-priority.low {
    background: #d1ecf1;
    color: #0c5460;
}

.task-priority.medium {
    background: #fff3cd;
    color: #856404;
}

.task-priority.high {
    background: #f8d7da;
    color: #721c24;
}

.task-priority.urgent {
    background: #dc3545;
    color: white;
}

.task-dependency {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.875rem;
}

/* Timeline Styles */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -1.75rem;
    top: 1.5rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #0d6efd;
    border: 3px solid white;
    box-shadow: 0 0 0 3px #e9ecef;
}

.timeline-item.completed::before {
    background: #198754;
}

.timeline-item.in_progress::before {
    background: #0d6efd;
    animation: pulse 2s infinite;
}

.timeline-item.on_hold::before {
    background: #fd7e14;
}

.timeline-item.not_started::before {
    background: #6c757d;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 3px #e9ecef, 0 0 0 6px rgba(13, 110, 253, 0.3);
    }
    50% {
        box-shadow: 0 0 0 3px #e9ecef, 0 0 0 10px rgba(13, 110, 253, 0.1);
    }
    100% {
        box-shadow: 0 0 0 3px #e9ecef, 0 0 0 6px rgba(13, 110, 253, 0.3);
    }
}

.timeline-content {
    margin-left: 1rem;
}

.timeline-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.timeline-title {
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.timeline-meta {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.timeline-description {
    color: #6c757d;
    margin-bottom: 1rem;
}

.timeline-actions {
    display: flex;
    gap: 0.5rem;
}

/* Tab Styles */
.nav-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
    padding: 1rem 1.5rem;
}

.nav-tabs .nav-link:hover {
    border-bottom-color: #dee2e6;
    color: #495057;
}

.nav-tabs .nav-link.active {
    border-bottom-color: #0d6efd;
    color: #0d6efd;
    background: none;
}



/* Info Item Styles */
.info-item label {
    color: #495057;
    margin-bottom: 0.25rem;
}

.info-item p {
    margin-bottom: 0;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
}

/* Task Stats Badges */
.task-stats .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .project-progress-circle {
        width: 60px;
        height: 60px;
    }

    .progress-circle {
        width: 60px;
        height: 60px;
    }

    .progress-circle::before {
        width: 45px;
        height: 45px;
    }

    .timeline {
        padding-left: 1.5rem;
    }

    .timeline::before {
        left: 0.75rem;
    }

    .timeline-item::before {
        left: -1.25rem;
    }

    .task-type-icon {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }
}

/* ===== REDESIGNED PROJECT CARDS - PROFESSIONAL & COMPACT ===== */

/* Redesigned Card Content Layout */
.card-content-redesigned {
    padding: 1.25rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    min-height: 140px;
}

/* Project Header Section - ID and Name on same line */
.project-header-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.project-title-line {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.project-id-badge {
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    color: #475569;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid #cbd5e1;
    flex-shrink: 0;
}

.project-title-separator {
    color: #94a3b8;
    font-weight: 600;
    font-size: 1rem;
}

.project-name-inline {
    font-size: 1.1rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
    line-height: 1.3;
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.project-meta-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.days-left-display {
    font-size: 0.85rem;
    font-weight: 600;
}

.days-left-display .days-overdue {
    color: #dc2626;
    background: rgba(220, 38, 38, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    border: 1px solid rgba(220, 38, 38, 0.2);
}

.days-left-display .days-today {
    color: #ea580c;
    background: rgba(234, 88, 12, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    border: 1px solid rgba(234, 88, 12, 0.2);
}

.days-left-display .days-urgent {
    color: #f59e0b;
    background: rgba(245, 158, 11, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.days-left-display .days-normal {
    color: #64748b;
    background: rgba(100, 116, 139, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    border: 1px solid rgba(100, 116, 139, 0.2);
}

.client-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: #64748b;
    font-weight: 500;
}

.client-info i {
    color: #94a3b8;
    font-size: 0.75rem;
}

/* Main Content Grid */
.card-main-grid {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 1.5rem;
    align-items: start;
}

/* Status and Time Section */
.status-time-section {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.latest-update-compact {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 0.8rem;
}

.latest-update-compact .update-text {
    color: #475569;
    line-height: 1.4;
    margin-bottom: 0.25rem;
}

.latest-update-compact .update-time {
    color: #94a3b8;
    font-size: 0.75rem;
    font-weight: 500;
}

.latest-update-compact .no-update {
    color: #94a3b8;
    font-style: italic;
    text-align: center;
}

/* Project Actions Center */
.project-actions-center {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
    justify-content: center;
    min-width: 120px;
}

/* Management Section */
.management-section {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-end;
}

.timeline-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-end;
}

/* Management Buttons */
.management-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    color: #64748b;
    border: 1px solid #cbd5e1;
    border-radius: 8px;
    font-size: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    min-width: 80px;
    justify-content: center;
}

.management-btn:hover {
    background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
    color: #475569;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.management-btn i {
    font-size: 0.7rem;
}

/* Specific management button styles */
.edit-btn:hover {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: #1d4ed8;
    border-color: #3b82f6;
}

.reassign-btn:hover {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    color: #92400e;
    border-color: #f59e0b;
}

.share-btn:hover {
    background: linear-gradient(135deg, #d1fae5, #a7f3d0);
    color: #065f46;
    border-color: #10b981;
}

.delete-btn:hover {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #991b1b;
    border-color: #ef4444;
}

/* Enhanced Status Action Buttons */
.status-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 10px;
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    min-height: 44px;
    min-width: 100px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-action-btn .btn-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.status-action-btn .btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.status-action-btn i {
    font-size: 0.875rem;
}

.status-action-btn .btn-text {
    font-size: 0.875rem;
    font-weight: 600;
}

/* Play/Start Button - Blue Theme */
.status-action-btn.play-btn {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.25);
}

.status-action-btn.play-btn:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(59, 130, 246, 0.35);
    color: white;
}

/* Resume Button - Orange Theme */
.status-action-btn.resume-btn {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    box-shadow: 0 4px 20px rgba(245, 158, 11, 0.25);
}

.status-action-btn.resume-btn:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(245, 158, 11, 0.35);
    color: white;
}

/* Hold/Pause Button */
.status-action-btn.hold-btn {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    box-shadow: 0 4px 20px rgba(245, 158, 11, 0.25);
}

.status-action-btn.hold-btn:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(245, 158, 11, 0.35);
    color: white;
}

/* Complete Button - Green Theme */
.status-action-btn.complete-btn {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.25);
}

.status-action-btn.complete-btn:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(16, 185, 129, 0.35);
    color: white;
}

/* Review Button */
.status-action-btn.review-btn {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    box-shadow: 0 4px 20px rgba(139, 92, 246, 0.25);
}

.status-action-btn.review-btn:hover {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(139, 92, 246, 0.35);
    color: white;
}

/* Button Loading State */
.status-action-btn.loading .btn-content {
    opacity: 0;
}

.status-action-btn.loading .btn-loading {
    display: block;
}

.status-action-btn:not(.loading) .btn-loading {
    display: none;
}

/* Mobile responsive for redesigned cards */
@media (max-width: 768px) {
    .card-main-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        text-align: center;
    }

    .management-section {
        align-items: center;
    }

    .timeline-actions {
        flex-direction: row;
        justify-content: center;
        align-items: center;
    }

    .project-title-line {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .project-name-inline {
        white-space: normal;
        text-overflow: unset;
        overflow: visible;
    }

    .status-action-btn {
        min-width: 80px;
        padding: 0.6rem 0.8rem;
        font-size: 0.8rem;
    }

    .status-action-btn .btn-text {
        font-size: 0.8rem;
    }
}

/* Old custom card CSS removed - using project-card-final now */

/* Old header CSS removed - using card-header-final now */

/* Old action buttons CSS removed - using action-buttons-final now */

/* Old action button CSS removed - using assignee-btn now */

/* Old status and client row CSS removed - using bottom-row-final now */

.menu-dots-header {
    flex-shrink: 0;
}

.dots-btn {
    background: transparent;
    border: none;
    color: #6c757d;
    font-size: 16px;
    padding: 6px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dots-btn:hover {
    background: #f8f9fa;
    color: #495057;
    transform: scale(1.1);
}

/* ===== MODERN PROJECT CARD DESIGN - COMPACT SPACING ===== */
.project-card-final {
    background: #ffffff !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 12px !important;
    padding: 12px !important; /* Reduced from 16px to 12px for more compact cards */
    margin-bottom: 12px !important; /* Reduced from 16px to 12px */
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08);
}

.project-card-final:hover {
    border: 2px solid #d0d0d0 !important; /* 2px on hover as specified */
    box-shadow: 0 4px 12px rgba(0,0,0,0.12) !important; /* Soft shadow on hover */
    transform: translateY(-2px) !important; /* Smooth lift effect */
}

.project-card-final::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: all 0.4s ease;
    opacity: 0.8;
}

.project-card-final:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #d0d0d0;
}

.project-card-final:hover::before {
    width: 8px;
    opacity: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

/* Modern status-based gradient borders */
.project-card-final.status-not_started::before {
    background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
}

.project-card-final.status-planning::before {
    background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
}

.project-card-final.status-in_progress::before {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

.project-card-final.status-on_hold::before {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
}

.project-card-final.status-completed::before {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
}

.project-card-final.status-review::before {
    background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
}

/* Hover effects with enhanced gradients */
.project-card-final.status-not_started:hover::before {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 50%, #bdc3c7 100%);
}

.project-card-final.status-in_progress:hover::before {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #00cec9 100%);
}

.project-card-final.status-on_hold:hover::before {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 50%, #fd79a8 100%);
}

.project-card-final.status-completed:hover::before {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 50%, #55efc4 100%);
}

.project-card-final.status-review:hover::before {
    background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 50%, #fd79a8 100%);
}

/* Modern Header Row - Compact Spacing */
.card-header-final {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px; /* Reduced from 16px to 10px */
    padding-bottom: 10px; /* Reduced from 16px to 10px */
    border-bottom: 1px solid #f5f5f5;
}

.project-title-final {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
    min-width: 0;
}

.project-title-main {
    display: flex;
    align-items: center; /* Changed from baseline to center for better icon alignment */
    gap: 4px;
    width: 100%; /* Use maximum available width */
    flex: 1;
    min-width: 0; /* Allow content to shrink */
}

/* Days Left Below Project Title - Compact */
.days-left-below-title {
    margin-top: 2px; /* Reduced from 4px to 2px */
    margin-bottom: 4px; /* Reduced from 8px to 4px */
}

.days-left-urgency {
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
}

.days-left-urgency.overdue {
    color: #dc2626;
    background: rgba(220, 38, 38, 0.1);
    border: 1px solid rgba(220, 38, 38, 0.2);
}

.days-left-urgency.today {
    color: #ea580c;
    background: rgba(234, 88, 12, 0.1);
    border: 1px solid rgba(234, 88, 12, 0.2);
}

.days-left-urgency.critical {
    color: #f59e0b;
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.days-left-urgency.urgent {
    color: #f97316;
    background: rgba(249, 115, 22, 0.1);
    border: 1px solid rgba(249, 115, 22, 0.2);
}

.days-left-urgency.normal {
    color: #64748b;
    background: rgba(100, 116, 139, 0.1);
    border: 1px solid rgba(100, 116, 139, 0.2);
}

/* Task Running Time Display */
.task-running-time {
    color: #10b981;
    font-weight: 500;
    font-size: 10px;
    margin-left: 8px;
    opacity: 0.9;
    display: inline-block;
    padding: 1px 4px;
    background: rgba(16, 185, 129, 0.1);
    border-radius: 4px;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

/* Completed Tasks Section */
.completed-tasks-section {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e5e7eb;
}

.completed-tasks-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 4px 0;
}

.completed-tasks-title {
    font-size: 11px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.completed-tasks-list {
    opacity: 0.8;
}

.completed-task-line {
    background: rgba(16, 185, 129, 0.02);
    border-radius: 4px;
    margin-bottom: 2px;
    padding: 2px 4px;
}

/* User Project Status Section - Inline */
.user-project-status-inline {
    margin-left: 8px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    opacity: 0.8;
}

.user-project-status-inline i {
    font-size: 10px;
}

.status-text-inline {
    font-size: 11px;
    font-weight: 500;
    color: #6b7280;
}

.revision-count-inline {
    font-size: 10px;
    color: #9ca3af;
    font-weight: 400;
}

.user-project-status-inline.status-waiting .status-text-inline {
    color: #d97706;
}

.user-project-status-inline.status-review .status-text-inline {
    color: #2563eb;
}

.user-project-status-inline.status-revision .status-text-inline {
    color: #d97706;
}

.user-project-status-inline.status-accepted .status-text-inline {
    color: #059669;
}

.user-project-status-inline.status-rejected .status-text-inline {
    color: #dc2626;
}

/* Manager Comments Below Task */
.manager-comments-below {
    margin-top: 6px;
    padding: 6px 8px;
    background: rgba(249, 250, 251, 0.8);
    border-radius: 4px;
    border-left: 2px solid #e5e7eb;
}

.comments-header-below {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 3px;
}

.comments-header-below span {
    font-size: 10px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
}

.comments-text-below {
    font-size: 11px;
    color: #4b5563;
    line-height: 1.3;
    padding: 2px 4px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 3px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Manager Comments Inline (if needed) */
.manager-comments-inline {
    margin-top: 4px;
    padding: 4px 6px;
    background: rgba(249, 250, 251, 0.6);
    border-radius: 3px;
    border-left: 2px solid #e5e7eb;
    font-size: 10px;
}

.comments-header-inline {
    display: flex;
    align-items: center;
    gap: 3px;
    margin-bottom: 2px;
}

.comments-header-inline span {
    font-size: 9px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
}

.comments-text-inline {
    font-size: 10px;
    color: #4b5563;
    line-height: 1.2;
    padding: 2px 3px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 2px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* User Tasks Separator */
.user-tasks-separator {
    position: relative;
    margin: 24px 0;
    text-align: center;
}

.separator-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}



.task-name-small {
    font-size: 12px;
    color: #6c757d;
    font-weight: 400;
    margin-top: 2px;
}

.assignee-name-small {
    font-size: 11px;
    color: #495057;
    font-weight: 500;
    margin-top: 2px;
}

.live-indicator {
    color: #28a745;
    font-size: 9.6px; /* Increased by 20% from 8px */
    margin-left: 8px;
    animation: pulse 2s infinite;
    vertical-align: middle; /* Center align with text */
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Task name and assignee name styling */
.task-name-small {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
    font-weight: 500;
}

.assignee-name-small {
    font-size: 11px;
    color: #495057;
    margin-top: 2px;
    font-weight: 400;
}

/* Admin consolidated view styling - Compact Spacing */
.tasks-summary-admin {
    margin-top: 4px; /* Reduced from 8px to 4px */
    padding: 4px 0; /* Reduced from 8px to 4px */
}

.task-assignee-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px; /* Reduced from 8px to 4px */
    font-size: 10px;
    padding: 4px 0; /* Reduced from 8px to 4px */
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    width: 100%; /* Use maximum available width */
}

.task-info {
    display: flex;
    align-items: center;
    gap: 4px;
    flex: 1;
    min-width: 0; /* Allow content to shrink */
    width: 100%; /* Use maximum available width */
}

.task-info i {
    font-size: 10px;
    width: 12px;
    text-align: center;
}

.assignees-only {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 10px;
    color: #666;
    font-style: italic;
    margin-top: 4px;
}

.task-assignee-line .task-name-small {
    color: #6c757d;
    font-weight: 500;
    margin: 0;
}

.task-assignee-line .assignee-name-small {
    color: #495057;
    font-weight: 400;
    margin: 0;
    font-size: 10px;
}

.task-assignee-line .assignee-name-small::before {
    content: "→ ";
    color: #28a745;
    font-weight: bold;
    animation: moveForward 2s infinite;
    margin-right: 2px;
}

/* Active assignee styling with name blinking effect */
.assignee-name-small.active-assignee {
    color: #28a745 !important;
    font-weight: 600;
    animation: nameBlinking 2s infinite;
    background: linear-gradient(90deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
    padding: 2px 6px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

/* Completed task styling - Narrow black line with 30% opacity */
.completed-task,
.task-name-small.completed-task,
.task-assignee-line .task-name-small.completed-task,
.tasks-summary-admin .task-name-small.completed-task,
span.completed-task,
.task-name.completed-task {
    text-decoration: line-through !important;
    text-decoration-color: rgba(0, 0, 0, 0.3) !important;
    text-decoration-thickness: 1px !important;
    text-decoration-style: solid !important;
    opacity: 0.7 !important;
}

/* Additional completed task styling for better visibility */
.completed-task {
    position: relative;
}

/* Removed ::after pseudo-element to avoid double strikethrough lines */

/* Force strikethrough with inline styles backup */
.force-strikethrough {
    text-decoration: line-through !important;
    text-decoration-color: rgba(0, 0, 0, 0.3) !important;
    text-decoration-thickness: 1px !important;
    opacity: 0.7 !important;
}

/* Debug styling to identify completed tasks */
.completed-task-debug {
    background-color: rgba(40, 167, 69, 0.1) !important;
    border: 1px dashed #28a745 !important;
    padding: 2px 4px !important;
    border-radius: 4px !important;
}

/* Task completion summary CSS removed as the feature was removed */

/* Moving forward animation for active assignee arrow */
@keyframes moveForward {
    0% {
        transform: translateX(0px);
        opacity: 0.7;
    }
    50% {
        transform: translateX(3px);
        opacity: 1;
    }
    100% {
        transform: translateX(0px);
        opacity: 0.7;
    }
}

/* Name blinking animation for active assignee */
@keyframes nameBlinking {
    0% {
        color: #28a745;
        background: linear-gradient(90deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
        opacity: 1;
    }
    25% {
        color: #20c997;
        background: linear-gradient(90deg, rgba(32, 201, 151, 0.15), rgba(32, 201, 151, 0.08));
        opacity: 0.8;
    }
    50% {
        color: #17a2b8;
        background: linear-gradient(90deg, rgba(23, 162, 184, 0.2), rgba(23, 162, 184, 0.1));
        opacity: 0.6;
    }
    75% {
        color: #20c997;
        background: linear-gradient(90deg, rgba(32, 201, 151, 0.15), rgba(32, 201, 151, 0.08));
        opacity: 0.8;
    }
    100% {
        color: #28a745;
        background: linear-gradient(90deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
        opacity: 1;
    }
}

/* Admin status display removed as requested */

.status-indicator {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

.status-text {
    font-size: 13px;
    font-weight: 500;
    color: #495057;
}

/* Pulse animation for active projects */
.pulse-animation .status-indicator {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}

/* Waiting for client response indicator */
.waiting-client-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(23, 162, 184, 0.1);
    border: 1px solid rgba(23, 162, 184, 0.3);
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
}

.waiting-client-indicator i {
    animation: pulse-slow 2s infinite;
}

@keyframes pulse-slow {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Modern Timeline Styles */
.modern-timeline-container {
    position: relative;
    padding: 20px 0;
}

.modern-timeline-item {
    position: relative;
    padding-left: 50px;
    margin-bottom: 30px;
}

.modern-timeline-item::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 30px;
    bottom: -30px;
    width: 2px;
    background: #e9ecef;
}

.modern-timeline-item:last-child::before {
    display: none;
}

.timeline-marker {
    position: absolute;
    left: 0;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    z-index: 1;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.timeline-action {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.timeline-time {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.timeline-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
    font-size: 12px;
    color: #6c757d;
}

.timeline-user,
.timeline-duration {
    display: flex;
    align-items: center;
    gap: 4px;
}

.timeline-notes {
    font-size: 14px;
    color: #495057;
    line-height: 1.4;
}

.timeline-loading {
    text-align: center;
    padding: 40px 20px;
}

.loading-spinner .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #6c757d;
    margin: 0;
}

.empty-timeline {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-timeline i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-timeline h6 {
    margin-bottom: 8px;
    color: #495057;
}

.project-number {
    font-size: 20px;
    font-weight: 600;
    color: #2d3748;
    letter-spacing: 0.3px;
}

.project-name {
    font-size: 20px;
    font-weight: 600;
    color: #2d3748;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;
    flex: 1; /* Use maximum available width */
    min-width: 0; /* Allow text to truncate */
}

/* Action Buttons - Compact Spacing */
.action-buttons-final {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0; /* Removed bottom margin to save space */
    gap: 12px; /* Reduced from 16px to 12px */
    min-height: 32px; /* Reduced from 40px to 32px */
    padding: 6px 0; /* Reduced from 8px to 6px */
    border-bottom: none; /* Removed border to save space */
}

.assignee-buttons-container {
    display: flex;
    gap: 4px; /* Reduced from 8px to 4px to minimize space */
    flex-wrap: wrap;
}

.status-dropdown-container {
    display: flex;
    align-items: center;
}

.status-update-button-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Force override for status update button */
.status-update-btn {
    font-size: 11px !important;
    padding: 4px 8px !important;
    border-radius: 6px !important;
    border: 1px solid #dee2e6 !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    color: #495057 !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
    transition: all 0.2s ease !important;
    font-weight: 500 !important;
    white-space: nowrap !important;
    text-decoration: none !important;
}

.status-update-btn:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%) !important;
    border-color: #adb5bd !important;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15) !important;
    transform: translateY(-1px) !important;
    color: #495057 !important;
    text-decoration: none !important;
}

.status-update-btn:active {
    transform: translateY(0) !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
}

.status-update-btn i {
    font-size: 10px !important;
}

/* Force button styling override */
button.status-update-btn,
.btn.status-update-btn {
    font-size: 11px !important;
    padding: 4px 8px !important;
    border-radius: 6px !important;
    border: 1px solid #dee2e6 !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    color: #495057 !important;
}

/* Status label for admin/manager cards */
.status-label-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-label {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    font-weight: 500;
    white-space: nowrap;
}

.status-label.status-active {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-color: #2196f3;
    color: #1976d2;
}

.status-label.status-in_progress {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border-color: #4caf50;
    color: #388e3c;
}

.status-label.status-on_hold {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    border-color: #ff9800;
    color: #f57c00;
}

/* Revision indicator and button */
.revision-indicator {
    font-size: 11px;
    margin-bottom: 8px;
    padding: 4px 8px;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffc107;
    border-radius: 6px;
    color: #856404;
}

.revision-btn {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
    border-color: #ffc107 !important;
    color: #856404 !important;
}

.revision-btn:hover {
    background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%) !important;
    border-color: #e0a800 !important;
    color: #856404 !important;
}

/* Status Modal CSS Classes */
.status-modal-redesign {
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    border: none;
}

.status-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    border-radius: 12px 12px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.status-modal-icon {
    width: 48px;
    height: 48px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    font-size: 20px;
}

.status-modal-title h4 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.status-modal-subtitle {
    margin: 4px 0 0 0;
    font-size: 14px;
    opacity: 0.9;
}

.status-form-group {
    margin-bottom: 20px;
}

.status-form-label {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 14px;
}

.status-form-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    transition: all 0.3s ease;
}

.status-form-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.status-form-textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    transition: all 0.3s ease;
    resize: vertical;
    min-height: 80px;
}

.status-form-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.status-form-file {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    transition: all 0.3s ease;
}

.status-form-file:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.status-form-help {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
}

.status-info-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.status-info-row {
    display: flex;
    gap: 20px;
    margin-bottom: 12px;
}

.status-info-item {
    flex: 1;
}

.status-info-label {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
    display: block;
    margin-bottom: 4px;
}

.status-info-value {
    font-size: 14px;
    color: #2c3e50;
    font-weight: 600;
}

.status-info-description {
    margin-top: 12px;
}

.status-info-revision {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #dee2e6;
}

.optional-badge {
    background: #6c757d;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
    margin-left: 8px;
}

.status-modal-footer {
    padding: 20px 24px;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.status-btn-secondary {
    padding: 10px 20px;
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.status-btn-secondary:hover {
    background: #5a6268;
}

.status-btn-primary {
    padding: 10px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.status-btn-primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.status-btn-primary:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.assignee-btn {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
    cursor: pointer;
    box-shadow: none;
}

.assignee-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.assignee-btn.start-btn {
    background: #3b82f6;
    color: #ffffff;
    border-color: #3b82f6;
}

.assignee-btn.start-btn:hover {
    background: #2563eb;
    border-color: #2563eb;
}

.assignee-btn.play-pause-btn {
    background: #007bff;
    color: #ffffff;
    border-color: #007bff;
}

.assignee-btn.play-pause-btn:hover {
    background: #0056b3;
    border-color: #0056b3;
}

.assignee-btn.complete-btn {
    background: #10b981;
    color: #ffffff;
    border-color: #10b981;
}

.assignee-btn.complete-btn:hover {
    background: #059669;
    border-color: #059669;
}

/* Old dropdown styles removed - now using button approach */

/* Modern Bottom Row - Compact Spacing (Hidden for user cards) */
.bottom-row-final {
    display: flex;
    align-items: center;
    gap: 12px; /* Reduced from 16px to 12px */
    padding-top: 10px; /* Reduced from 16px to 10px */
    border-top: 1px solid #f0f0f0;
    margin-top: 6px; /* Reduced from 8px to 6px */
}

.timeline-icon-btn {
    background: #6c757d;
    border: none;
    color: #ffffff;
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
    box-shadow: none;
}

.timeline-icon-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.latest-update-text {
    flex: 1;
    font-size: 13px;
    color: #718096;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
    font-weight: 500;
}

.days-left-final {
    font-size: 12px;
    font-weight: 600;
    color: #4a5568;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border: 1px solid #e2e8f0;
    padding: 8px 16px;
    border-radius: 20px;
    white-space: nowrap;
    flex-shrink: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    letter-spacing: 0.3px;
}

/* Modern Timeline Styles */
.timeline-loading {
    text-align: center;
    padding: 40px 20px;
}

.loading-spinner {
    margin-bottom: 20px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-timeline {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-timeline i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.modern-timeline-container {
    position: relative;
    padding: 20px 0;
}

.modern-timeline-container::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #e9ecef, #dee2e6);
}

.modern-timeline-item {
    position: relative;
    padding: 0 0 24px 70px;
    margin-bottom: 16px;
}

.modern-timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: -40px;
    top: 0;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    border: 3px solid white;
}

.timeline-content {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.timeline-action {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #212529;
}

.timeline-time {
    font-size: 12px;
    color: #6c757d;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 12px;
}

.timeline-meta {
    display: flex;
    gap: 16px;
    margin-bottom: 8px;
    font-size: 13px;
    color: #6c757d;
}

.timeline-meta span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.timeline-notes {
    background: #f8f9fa;
    border-left: 3px solid #007bff;
    padding: 8px 12px;
    border-radius: 0 8px 8px 0;
    font-size: 14px;
    color: #495057;
    margin-top: 8px;
}

/* Timeline status badges with borders */
.timeline-status-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    border: 1.5px solid;
    text-transform: capitalize;
    margin: 0 2px;
}

.timeline-status-badge.status-not-started {
    color: #6c757d;
    background-color: rgba(108, 117, 125, 0.1);
    border-color: #6c757d;
}

.timeline-status-badge.status-in-progress {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
    border-color: #007bff;
}

.timeline-status-badge.status-on-hold {
    color: #fd7e14;
    background-color: rgba(253, 126, 20, 0.1);
    border-color: #fd7e14;
}

.timeline-status-badge.status-completed {
    color: #28a745;
    background-color: rgba(40, 167, 69, 0.1);
    border-color: #28a745;
}

.timeline-status-badge.status-review {
    color: #17a2b8;
    background-color: rgba(23, 162, 184, 0.1);
    border-color: #17a2b8;
}

.timeline-status-badge.status-corrections {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
    border-color: #dc3545;
}

.timeline-status-badge.status-accepted {
    color: #28a745;
    background-color: rgba(40, 167, 69, 0.15);
    border-color: #28a745;
}

/* Enhanced timeline details */
.timeline-details {
    margin: 8px 0;
    padding: 8px 0;
    border-left: 2px solid #e9ecef;
    padding-left: 12px;
}

.timeline-task-info,
.timeline-assignee-info,
.timeline-assigner-info {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 4px;
    font-size: 0.85rem;
    color: #495057;
}

.timeline-task-info i,
.timeline-assignee-info i,
.timeline-assigner-info i {
    width: 14px;
    color: #6c757d;
}

.timeline-meta {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 12px;
    padding: 10px;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Enhanced timeline duration display */
.timeline-exact-time,
.timeline-work-duration,
.timeline-relative-time {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.timeline-exact-time {
    background: rgba(0, 123, 255, 0.1);
    color: #004085;
    border-color: rgba(0, 123, 255, 0.2);
}

.timeline-work-duration {
    background: rgba(40, 167, 69, 0.1);
    color: #155724;
    border-color: rgba(40, 167, 69, 0.2);
}

.timeline-relative-time {
    background: rgba(108, 117, 125, 0.1);
    color: #495057;
    border-color: rgba(108, 117, 125, 0.2);
}

.timeline-time-ago,
.timeline-work-duration {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8rem;
    color: #6c757d;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.timeline-work-duration {
    background: #e3f2fd;
    color: #1976d2;
}

/* Real-time Duration Display */
.real-time-duration-display {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
    border: 1px solid rgba(40, 167, 69, 0.2);
    border-radius: 8px;
    padding: 8px 12px;
    margin: 8px 0;
    font-size: 13px;
}

.duration-header {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 4px;
    font-weight: 600;
    color: #155724;
}

.duration-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
}

.live-duration {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    font-size: 14px;
    color: #28a745;
    background: rgba(255, 255, 255, 0.8);
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.total-time-info {
    font-size: 11px;
    color: #6c757d;
    font-style: italic;
}

/* ===== REDESIGNED STATUS MODAL ===== */
.status-modal-redesign {
    border: none;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    overflow: hidden;
}

/* ===== MANAGER STATUS LABELS ===== */
.manager-status-label {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    margin: 8px 0;
    border: 1px solid transparent;
    transition: all 0.3s ease;
}

.manager-status-label.pending-review {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #6c757d;
    border-color: #dee2e6;
}

.manager-status-label.sent-for-review {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1976d2;
    border-color: #2196f3;
}

.manager-status-label.revision-needed {
    background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
    color: #f57c00;
    border-color: #ff9800;
}

.manager-status-label.client-accepted {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    color: #388e3c;
    border-color: #4caf50;
}

.manager-status-label.rejected {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    color: #d32f2f;
    border-color: #f44336;
}

.manager-status-label.unknown-status {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    color: #7b1fa2;
    border-color: #9c27b0;
}

.manager-status-label:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* ===== ROLE-BASED CARD VARIATIONS ===== */
.project-card-final.card-assignee {
    border-left: 4px solid #10b981;
    background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
}

.project-card-final.card-assignee:hover {
    border-left: 4px solid #059669;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.project-card-final.card-admin {
    border-left: 4px solid #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #ffffff 100%);
}

.project-card-final.card-admin:hover {
    border-left: 4px solid #2563eb;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.project-card-final.card-manager {
    border-left: 4px solid #f59e0b;
    background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
}

.project-card-final.card-manager:hover {
    border-left: 4px solid #d97706;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
}

/* ===== TASK NAME CONTAINER ===== */
.task-name-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 2px; /* Reduced to 2px to minimize space */
    flex: 2; /* Increased from 1 to 2 for more space */
    min-width: 0; /* Allow content to shrink */
}

.task-name-container .task-name-small {
    flex: 1;
    min-width: 0; /* Allow text to truncate */
}

/* ===== DEPENDENCY INDICATORS ===== */
.dependency-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    font-size: 0.75rem;
    margin-left: 0.5rem;
    transition: all 0.3s ease;
}

.dependency-indicator.locked {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #dc2626;
    border: 1px solid #f87171;
}

.dependency-indicator.depends-on {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #d97706;
    border: 1px solid #f59e0b;
}

.dependency-indicator:hover {
    transform: scale(1.1);
}

/* ===== TASK DEPENDENCY DISPLAY ===== */
.task-dependencies {
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 3px solid #e2e8f0;
}

.dependency-chain {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: #64748b;
}

.dependency-arrow {
    color: #94a3b8;
}

.dependency-task {
    padding: 0.25rem 0.5rem;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #e2e8f0;
}

.dependency-task.completed {
    background: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

.dependency-task.locked {
    background: #fef2f2;
    border-color: #fecaca;
    color: #991b1b;
}

.dependency-header {
    display: flex;
    align-items: center;
    font-size: 0.8rem;
    font-weight: 600;
    color: #64748b;
    margin-bottom: 0.5rem;
}

.dependency-chains {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

/* ===== ENHANCED TASK DISPLAY STYLES ===== */
.task-assignee-line.enhanced {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0; /* Standardized to 8px (0.5rem) */
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.task-info-enhanced {
    display: flex;
    align-items: center;
    flex: 2; /* Increased from 1 to 2 for more space */
    gap: 4px; /* Reduced from 8px to 4px to minimize space */
    min-width: 0; /* Allow content to shrink */
    width: 100%; /* Use maximum available width */
}

.task-icons {
    display: flex;
    gap: 2px; /* Reduced from 8px to 2px to minimize space */
    align-items: center;
    flex-shrink: 0; /* Prevent icons from shrinking */
}

.task-icons i {
    font-size: 0.875rem;
}

.task-assignee-info {
    display: flex;
    align-items: center;
    gap: 4px; /* Reduced from 8px to 4px to minimize space */
    flex: 1; /* Changed from flex-shrink: 0 to flex: 1 for more space */
    min-width: fit-content; /* Ensure assignee name is readable */
}

.task-action-btn {
    padding: 0.0625rem 0.125rem;
    font-size: 0.7rem;
    border: none;
    background: transparent;
    color: #6c757d;
    transition: all 0.2s ease;
    margin: 0 0.125rem;
}

.task-action-btn:hover {
    color: #007bff;
    background: rgba(0, 123, 255, 0.1);
}

.manager-actions {
    margin-top: 0.75rem;
    padding-top: 0.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.manager-status-btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* Extra small button */
.btn-xs {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
    line-height: 1.2;
    border-radius: 0.25rem;
}

/* Task action buttons */
.task-action-btn {
    padding: 0.0625rem 0.125rem;
    font-size: 0.65rem;
    border-radius: 0.2rem;
    margin: 0 0.125rem;
}

/* Send for Review button - compact with reduced height */
.send-review-btn {
    padding: 0.125rem 0.375rem !important;
    font-size: 0.65rem !important;
    min-width: auto !important;
    line-height: 1.2 !important;
    height: auto !important;
    min-height: 20px !important;
}

.send-review-btn i {
    font-size: 1.3em !important;
    margin: 0 !important;
}

/* Completed tasks separator and bottom status */
.completed-tasks-separator {
    margin: 0.75rem 0 0.5rem 0;
}

.task-separator-line {
    border: none;
    border-top: 1px solid var(--gray-300);
    margin: 0;
    opacity: 0.6;
}

.bottom-status-section {
    padding: 0.5rem 0;
    border-left: 3px solid var(--success);
    padding-left: 0.75rem;
    background: rgba(40, 167, 69, 0.05);
    border-radius: 0 4px 4px 0;
    margin-top: 0.5rem;
}

.waiting-status-bottom {
    margin-top: 0.25rem;
    padding-left: 1.25rem;
}

.waiting-status-bottom small {
    font-size: 0.75rem;
    opacity: 0.8;
}

/* User status horizontal layout */
.user-status-horizontal {
    background: rgba(248, 249, 250, 0.8);
    border-radius: 6px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    font-size: 0.85rem;
}

.user-status-horizontal .status-left {
    font-weight: 500;
}

.user-status-horizontal .status-right {
    font-size: 0.8rem;
}

/* Task name with icon styling for completed tasks */
.task-name-with-icon {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.task-name-with-icon .fas.fa-lock {
    color: #9ca3af !important;
    font-size: 0.9em;
}

.task-name-with-icon .completed-task {
    color: #9ca3af !important;
    opacity: 0.7;
}

.user-status-horizontal .btn-xs {
    padding: 0.125rem 0.375rem;
    font-size: 0.7rem;
    line-height: 1.2;
}

.task-status-icons {
    display: flex;
    gap: 0.0625rem;
    margin-left: 0.125rem;
}

.task-icon-btn {
    padding: 0.0625rem 0.125rem;
    font-size: 0.6rem;
    border-radius: 0.2rem;
    min-width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.task-icon-btn:hover {
    transform: scale(1.1);
}

/* ===== ROLE-BASED CARD CONTENT VARIATIONS ===== */
.card-assignee .project-title-main {
    font-weight: 600;
}

.card-admin .tasks-summary-admin {
    padding-left: 0.75rem;
    margin-top: 0.5rem;
}

.card-manager .tasks-summary-admin {
    padding-left: 0.75rem;
    margin-top: 0.5rem;
}

/* Enhanced task display for different roles */
.card-assignee .task-name-small {
    font-size: 0.9rem;
    font-weight: 500;
}

/* Locked task styling */
.task-name-small.locked-task {
    color: #dc3545 !important;
    opacity: 0.7;
    font-style: italic;
}

.task-assignee-line .locked-task {
    background: rgba(220, 53, 69, 0.05);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: 1px solid rgba(220, 53, 69, 0.2);
}

/* No tasks message styling */
.no-tasks-message {
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.no-tasks-content {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    font-size: 11px;
    color: #6c757d;
}

.project-assignee-info {
    font-size: 10px;
    color: #6c757d;
}

.no-task-assigned {
    padding: 8px 0;
    text-align: center;
}

.no-task-content {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4px;
    font-size: 11px;
    color: #6c757d;
}

.project-status-info {
    font-size: 10px;
    color: #6c757d;
}

.card-admin .task-assignee-line {
    padding: 0.25rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.card-admin .task-assignee-line:last-child {
    border-bottom: none;
}

.card-manager .task-assignee-line {
    padding: 0.25rem 0;
    background: linear-gradient(90deg, transparent 0%, #fffbeb 50%, transparent 100%);
    border-radius: 4px;
    margin-bottom: 0.25rem;
}

.status-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    border: none;
}

.status-modal-icon {
    width: 48px;
    height: 48px;
    background: rgba(255,255,255,0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.status-modal-title-section {
    flex: 1;
}

.status-modal-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    line-height: 1.2;
}

.status-modal-subtitle {
    margin: 4px 0 0 0;
    font-size: 14px;
    opacity: 0.9;
    font-weight: 400;
}

.btn-close-custom {
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-close-custom:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.05);
}

.status-modal-body {
    padding: 24px;
    background: #ffffff;
}

.status-info-card {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 24px;
}

.status-info-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-change-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.status-change-label {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-change-value {
    font-size: 16px;
    color: #1e293b;
    font-weight: 600;
}

.status-timestamp {
    font-size: 12px;
    color: #64748b;
    display: flex;
    align-items: center;
    gap: 4px;
}

.form-group-redesign {
    margin-bottom: 20px;
}

.form-label-redesign {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.optional-badge {
    background: #e5e7eb;
    color: #6b7280;
    font-size: 11px;
    font-weight: 500;
    padding: 2px 8px;
    border-radius: 12px;
    margin-left: 8px;
}

.required-badge {
    background: #fecaca;
    color: #dc2626;
    font-size: 11px;
    font-weight: 500;
    padding: 2px 8px;
    border-radius: 12px;
    margin-left: 8px;
}

.form-control-redesign {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 12px 16px;
    font-size: 14px;
    transition: all 0.2s ease;
    resize: vertical;
    min-height: 80px;
    width: 100%;
    box-sizing: border-box;
}

.form-control-redesign:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.file-upload-redesign {
    position: relative;
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.file-upload-redesign:hover {
    border-color: #667eea;
    background: #f8fafc;
}

.file-input-redesign {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: #6b7280;
}

.file-upload-placeholder i {
    font-size: 24px;
    color: #9ca3af;
}

.file-upload-placeholder span {
    font-size: 14px;
    font-weight: 500;
}

.file-upload-placeholder small {
    font-size: 12px;
    color: #9ca3af;
}

.status-modal-footer {
    background: #f8fafc;
    padding: 20px 24px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    border-top: 1px solid #e5e7eb;
}

.btn-redesign {
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-cancel {
    background: #f3f4f6;
    color: #374151;
}

.btn-cancel:hover {
    background: #e5e7eb;
    transform: translateY(-1px);
}

.btn-submit {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-submit:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Desktop - Show full filter text */
@media (min-width: 769px) {
    .filter-text {
        display: inline;
    }
}

/* Mobile Responsive - Keep Same Layout */
@media (max-width: 768px) {
    .project-card-final {
        padding: 12px;
        margin-bottom: 12px;
        border-radius: 12px;
    }

    /* Hide filters by default on mobile */
    .nav-filters {
        display: none !important;
    }

    /* Compact navigation on mobile */
    .nav-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .nav-stats.compact {
        gap: 0.5rem;
    }

    .nav-stats.compact .stat-item {
        min-width: 40px;
    }

    .page-title {
        font-size: 1.1rem;
    }

    .card-header-final {
        margin-bottom: 12px;
        padding-bottom: 8px;
    }

    .project-title-final {
        gap: 2px;
    }

    .project-number,
    .project-name {
        font-size: 16px;
    }

    .task-name-small {
        font-size: 11px;
        margin-top: 2px;
    }

    .assignee-name-small {
        font-size: 10px;
        margin-top: 1px;
    }

    .action-buttons-final {
        margin-bottom: 10px;
        gap: 6px;
        min-height: 32px;
        padding: 4px 0;
    }

    .assignee-btn {
        padding: 4px 8px;
        font-size: 11px;
        min-height: 32px;
    }

    .assignee-btn i {
        font-size: 10px;
    }

    .assignee-btn span {
        font-size: 10px;
    }

    .bottom-row-final {
        padding-top: 8px;
        gap: 6px;
    }

    .timeline-icon-btn {
        width: 28px;
        height: 28px;
        font-size: 10px;
    }

    .latest-update-text {
        font-size: 11px;
        line-height: 1.2;
    }

    .days-left-final {
        font-size: 10px;
        padding: 4px 6px;
        min-width: 50px;
    }

    .live-indicator {
        font-size: 7.2px; /* Increased by 20% from 6px */
        margin-left: 4px;
        vertical-align: middle; /* Center align with text */
    }

    .menu-dots-header .dots-btn {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    /* Ensure cards maintain horizontal layout on mobile */
    .projects-content-area .col-12 {
        padding: 0 8px;
    }

    /* Compact navigation on mobile */
    .nav-stats {
        gap: 8px;
    }

    .stat-item {
        min-width: 60px;
    }

    .stat-number {
        font-size: 14px;
    }

    .stat-label {
        font-size: 10px;
    }

    /* Hide filter text on mobile, show only icon */
    .filter-text {
        display: none;
    }
}

    .modern-timeline-container::before {
        left: 20px;
    }

    .modern-timeline-item {
        padding-left: 50px;
    }

    .timeline-marker {
        left: -30px;
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .timeline-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .timeline-meta {
        flex-direction: column;
        gap: 6px;
        padding: 8px;
    }

    .timeline-exact-time,
    .timeline-work-duration,
    .timeline-relative-time {
        font-size: 11px;
        padding: 3px 6px;
    }

    .status-modal-header {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .status-modal-body {
        padding: 20px;
    }

    .status-info-content {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .status-modal-footer {
        padding: 16px 20px;
        flex-direction: column;
    }
}

/* Revision Number Styling */
.revision-number {
    font-size: 0.8em;
    color: #6c757d;
    font-weight: 500;
    margin-left: 4px;
}

/* Task Menu Button Styling */
.task-menu-btn {
    padding: 1px 4px;
    font-size: 0.7em;
    border-radius: 3px;
    transition: all 0.2s ease;
    min-width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.task-menu-btn:hover {
    background-color: #f8f9fa;
    border-color: #6c757d;
}

.task-menu-btn i {
    font-size: 0.8em;
}

.dropdown-menu {
    min-width: 160px;
    font-size: 0.9em;
}

.dropdown-item {
    padding: 6px 12px;
    transition: background-color 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item i {
    width: 16px;
    text-align: center;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
let allProjects = [];
let allUsers = [];
let currentFilter = 'assigned';
let currentStep = 1;
let maxSteps = 4;

// Toggle filters visibility
function toggleFilters() {
    const filtersDiv = document.getElementById('navFilters');
    const isVisible = filtersDiv.style.display !== 'none';

    if (isVisible) {
        filtersDiv.style.display = 'none';
    } else {
        filtersDiv.style.display = 'flex';
    }
}

document.addEventListener('DOMContentLoaded', function() {
    loadProjects();
    loadStats();
    loadUsers();

    // Check for highlight parameter in URL
    const urlParams = new URLSearchParams(window.location.search);
    const highlightProjectId = urlParams.get('highlight');
    if (highlightProjectId) {
        // Wait for projects to load, then highlight the specific project
        setTimeout(() => {
            highlightProject(highlightProjectId);
        }, 1000);
    }

    // Load assignees for admin filter if user is admin
    const userRole = '<?= $user['roles'] ?>';
    if (userRole === 'admin') {
        loadAssignees();
    }

    initializeTooltips();
    initializeRealTimeUpdates();

    // Form event listeners
    // Note: Create project form moved to separate page (/projects/create)

    document.getElementById('bulkImportForm').addEventListener('submit', function(e) {
        e.preventDefault();
        bulkImportProjects();
    });

    document.getElementById('statusUpdateForm').addEventListener('submit', function(e) {
        e.preventDefault();
        submitStatusUpdate(e);
    });

    document.getElementById('taskStatusForm').addEventListener('submit', function(e) {
        e.preventDefault();
        submitTaskStatusUpdate();
    });

    // Tab change handlers for project details modal
    document.getElementById('timeline-tab').addEventListener('click', function() {
        const projectId = document.getElementById('updateProjectStatusBtn').onclick.toString().match(/\d+/);
        if (projectId) {
            loadProjectTimeline(projectId[0]);
        }
    });

    document.getElementById('client-access-tab').addEventListener('click', function() {
        const projectId = document.getElementById('updateProjectStatusBtn').onclick.toString().match(/\d+/);
        if (projectId) {
            loadClientAccess(projectId[0]);
        }
    });

    // Modal step navigation
    document.getElementById('nextStep').addEventListener('click', function() {
        if (validateCurrentStep()) {
            nextStep();
        }
    });

    document.getElementById('prevStep').addEventListener('click', function() {
        prevStep();
    });

    // Real-time validation
    setupRealTimeValidation();

    // Date change handlers
    document.getElementById('startDate').addEventListener('change', calculateDuration);
    document.getElementById('targetCompletion').addEventListener('change', calculateDuration);

    // User selection handler
    document.getElementById('assignedTo').addEventListener('change', updateUserPreview);
});

// Load projects based on current filter
function loadProjects(forceRefresh = false) {
    const userRole = '<?= $user['roles'] ?>';
    const isAdmin = userRole === 'admin';

    // Clear cache if force refresh is requested
    if (forceRefresh) {
        window.projectsCache = null;
        console.log('Force refresh requested, clearing project cache');
    }

    // Get filter values based on user role
    const assigneeFilter = document.getElementById('assigneeFilter')?.value || 'all';
    const projectFilter = document.getElementById('projectFilter')?.value || 'assigned';
    const statusFilter = document.getElementById('statusFilter')?.value || '';

    // Use appropriate filter based on role
    let filterValue, filterType;
    if (isAdmin) {
        // Admin can filter by assignee or project
        filterValue = assigneeFilter;
        filterType = 'assignee';
    } else {
        // Regular users can filter by project type (assigned, created, all)
        filterValue = projectFilter;
        filterType = 'project';
    }

    currentFilter = filterValue;

    let url = `/projects/getProjects?filter=${filterValue}&filterType=${filterType}`;
    if (statusFilter) {
        url += `&status=${statusFilter}`;
    }

    // Add cache busting parameters
    url += `&_t=${Date.now()}`;
    if (forceRefresh) {
        url += '&_force=1';
    }

    console.log('Loading projects with filters:', { filterType, filterValue, statusFilter, userRole });

    fetch(url, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        },
        cache: 'no-store'
    })
    .then(response => {
        if (response.status === 401) {
            // Authentication required - redirect to login
            window.location.href = '/auth/login';
            return;
        }
        return response.json();
    })
    .then(data => {
        if (!data) return; // Handle redirect case

        if (data.success) {
            allProjects = data.projects;
            window.currentProjects = data.projects; // Store globally for modal access
            displayProjects(allProjects);
            updateStatsDisplay(data.stats);

            // Initialize progress circles after displaying projects
            setTimeout(() => {
                initializeProgressCircles();
            }, 100);
        } else {
            if (data.redirect) {
                window.location.href = data.redirect;
                return;
            }
            showAlert('error', data.message || 'Error loading projects');
        }
    })
    .catch(error => {
        console.error('Error loading projects:', error);
        showAlert('error', 'Error loading projects. Please refresh the page.');
    });
}

// Load project statistics
function loadStats() {
    const userRole = '<?= $user['roles'] ?>';
    const userId = <?= $user['id'] ?>;

    fetch('/projects/getStats', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('📊 Statistics loaded:', data.stats);
            updateStatsDisplay(data.stats);
        } else {
            console.error('Failed to load statistics:', data.message);
        }
    })
    .catch(error => {
        console.error('Error loading stats:', error);
        // Fallback: Load statistics from projects data
        loadStatsFromProjects();
    });
}

// Fallback function to calculate stats from loaded projects
function loadStatsFromProjects() {
    const userRole = '<?= $user['roles'] ?>';
    const userId = <?= $user['id'] ?>;

    if (!allProjects || allProjects.length === 0) {
        console.log('No projects data available for stats calculation');
        return;
    }

    let stats = {};

    if (userRole === 'admin') {
        stats = {
            total_projects: allProjects.length,
            active_projects: allProjects.filter(p => p.status === 'in_progress').length,
            completed_projects: allProjects.filter(p => p.status === 'completed').length,
            on_hold_projects: allProjects.filter(p => p.status === 'on_hold').length
        };
    } else {
        // Assignee stats
        const myProjects = allProjects.filter(p => parseInt(p.assigned_to) === parseInt(userId));
        const completedProjects = myProjects.filter(p => p.status === 'completed').length;
        const totalProjects = myProjects.length;
        const efficiency = totalProjects > 0 ? Math.round((completedProjects / totalProjects) * 100) : 0;

        stats = {
            my_total_projects: totalProjects,
            my_active_projects: myProjects.filter(p => p.status === 'in_progress').length,
            my_completed_projects: completedProjects,
            my_efficiency: efficiency
        };
    }

    console.log('📊 Calculated stats from projects:', stats);
    updateStatsDisplay(stats);
}

// Load users for assignment dropdown
function loadUsers() {
    fetch('/projects/getUsers', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            allUsers = data.users;
            populateUserDropdown();
        }
    })
    .catch(error => {
        console.error('Error loading users:', error);
    });
}

// Load assignees for admin filter
function loadAssignees() {
    const assigneeFilter = document.getElementById('assigneeFilter');
    if (!assigneeFilter) return;

    fetch('/projects/getAssignees', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Clear existing options except "All Projects"
            assigneeFilter.innerHTML = '<option value="all">All Projects</option>';

            // Add assignee options
            data.assignees.forEach(assignee => {
                const option = document.createElement('option');
                option.value = assignee.id;
                option.textContent = assignee.name;
                assigneeFilter.appendChild(option);
            });
        }
    })
    .catch(error => {
        console.error('Error loading assignees:', error);
        // Fallback: populate with test users
        assigneeFilter.innerHTML = `
            <option value="all">All Projects</option>
            <option value="1">Test User 1</option>
            <option value="2">Test User 2</option>
        `;
    });
}

// Modal functionality removed - using page-based approach

// Initialize tooltips
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Reset modal to initial state
function resetModal() {
    currentStep = 1;
    updateStepDisplay();
    clearFormValidation();
    // Note: Create project form moved to separate page (/projects/create)
    // document.getElementById('createProjectForm').reset();
    // document.getElementById('userPreview').style.display = 'none';

    // Set default dates
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('startDate').value = today;

    // Generate default project ID
    generateProjectId();
}

// Generate default project ID
function generateProjectId() {
    const year = new Date().getFullYear();
    const randomNum = Math.floor(Math.random() * 999) + 1;
    const projectId = `PRJ-${year}-${randomNum.toString().padStart(3, '0')}`;
    document.getElementById('projectId').value = projectId;
}

// Modern step navigation functions
function nextStep() {
    const currentStepElement = document.querySelector('.modern-form-step.active');
    const currentStepNumber = parseInt(currentStepElement.dataset.step);

    if (validateStep(currentStepNumber)) {
        // Hide current step
        currentStepElement.classList.remove('active');
        document.querySelector(`.step-indicator[data-step="${currentStepNumber}"]`).classList.remove('active');

        // Show next step
        const nextStepNumber = currentStepNumber + 1;
        const nextStep = document.querySelector(`.modern-form-step[data-step="${nextStepNumber}"]`);
        const nextStepIndicator = document.querySelector(`.step-indicator[data-step="${nextStepNumber}"]`);

        if (nextStep) {
            nextStep.classList.add('active');
            nextStepIndicator.classList.add('active');

            // Update buttons
            updateNavigationButtons(nextStepNumber);

            // Mark previous step as completed
            const prevIndicator = document.querySelector(`.step-indicator[data-step="${currentStepNumber}"]`);
            prevIndicator.classList.add('completed');

            // Update step line
            const stepLines = document.querySelectorAll('.step-line');
            if (stepLines[currentStepNumber - 1]) {
                stepLines[currentStepNumber - 1].classList.add('completed');
            }
        }
    }
}

function prevStep() {
    const currentStepElement = document.querySelector('.modern-form-step.active');
    const currentStepNumber = parseInt(currentStepElement.dataset.step);

    if (currentStepNumber > 1) {
        // Hide current step
        currentStepElement.classList.remove('active');
        document.querySelector(`.step-indicator[data-step="${currentStepNumber}"]`).classList.remove('active');

        // Show previous step
        const prevStepNumber = currentStepNumber - 1;
        const prevStep = document.querySelector(`.modern-form-step[data-step="${prevStepNumber}"]`);
        const prevStepIndicator = document.querySelector(`.step-indicator[data-step="${prevStepNumber}"]`);

        if (prevStep) {
            prevStep.classList.add('active');
            prevStepIndicator.classList.add('active');

            // Update buttons
            updateNavigationButtons(prevStepNumber);

            // Remove completed state from current step
            const currentIndicator = document.querySelector(`.step-indicator[data-step="${currentStepNumber}"]`);
            currentIndicator.classList.remove('completed');

            // Update step line
            const stepLines = document.querySelectorAll('.step-line');
            if (stepLines[prevStepNumber - 1]) {
                stepLines[prevStepNumber - 1].classList.remove('completed');
            }
        }
    }
}

// Update navigation buttons based on current step
function updateNavigationButtons(stepNumber) {
    const prevBtn = document.getElementById('prevStep');
    const nextBtn = document.getElementById('nextStep');
    const submitBtn = document.getElementById('submitProject');

    // Show/hide previous button
    if (stepNumber === 1) {
        prevBtn.style.display = 'none';
    } else {
        prevBtn.style.display = 'inline-flex';
    }

    // Show/hide next/submit buttons
    if (stepNumber === 4) { // Last step
        nextBtn.style.display = 'none';
        submitBtn.style.display = 'inline-flex';
    } else {
        nextBtn.style.display = 'inline-flex';
        submitBtn.style.display = 'none';
    }

    // Load default tasks when reaching step 3
    if (stepNumber === 3) {
        loadDefaultTasks();
    }
}

// Validate current step
function validateStep(stepNumber) {
    const currentStep = document.querySelector(`.modern-form-step[data-step="${stepNumber}"]`);
    const requiredFields = currentStep.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');

            // Additional validation for specific fields
            if (field.name === 'project_id' && !/^[A-Z]{3}-\d{4}-\d{3}$/.test(field.value)) {
                field.classList.add('is-invalid');
                isValid = false;
            }

            if (field.name === 'client_mobile' && field.value && !/^[\+]?[0-9\s\-\(\)]{10,20}$/.test(field.value)) {
                field.classList.add('is-invalid');
                isValid = false;
            }
        }
    });

    return isValid;
}

// Validate all steps before submission
function validateAllSteps() {
    let allValid = true;

    // Validate each step
    for (let step = 1; step <= 4; step++) {
        if (!validateStep(step)) {
            allValid = false;
        }
    }

    return allValid;
}

// Collect task data from the form
function collectTaskData() {
    const tasks = [];
    const taskItems = document.querySelectorAll('.task-item');

    taskItems.forEach((taskItem, index) => {
        const taskData = {
            name: taskItem.querySelector('.task-title-input')?.value || '',
            type: taskItem.querySelector(`select[name*="[type]"]`)?.value || 'custom',
            priority: taskItem.querySelector(`select[name*="[priority]"]`)?.value || 'medium',
            assigned_to: taskItem.querySelector(`select[name*="[assigned_to]"]`)?.value || '',
            due_date: taskItem.querySelector(`input[name*="[due_date]"]`)?.value || '',
            estimated_hours: taskItem.querySelector(`input[name*="[estimated_hours]"]`)?.value || 0,
            dependencies: Array.from(taskItem.querySelectorAll(`select[name*="[dependencies]"] option:checked`)).map(opt => opt.value)
        };

        if (taskData.name.trim()) {
            tasks.push(taskData);
        }
    });

    return tasks;
}

// Old wizard functions removed - using page-based approach

// Task Management Functions
let taskCounter = 0;
const defaultTasks = [
    { name: 'Planning & Design', type: 'planning', priority: 'high' },
    { name: '3D Design & Modeling', type: 'design', priority: 'high' },
    { name: 'Permit Approval', type: 'approval', priority: 'medium' },
    { name: 'Site Preparation', type: 'construction', priority: 'high' },
    { name: 'Foundation Work', type: 'construction', priority: 'high' },
    { name: 'Construction', type: 'construction', priority: 'high' },
    { name: 'Final Inspection', type: 'inspection', priority: 'medium' },
    { name: 'Project Completion', type: 'completion', priority: 'high' }
];

function loadDefaultTasks() {
    const tasksList = document.getElementById('tasksList');
    tasksList.innerHTML = '';

    defaultTasks.forEach((task, index) => {
        addTaskToList(task.name, task.type, task.priority, index);
    });

    taskCounter = defaultTasks.length;
}

function addNewTask() {
    addTaskToList('New Task', 'custom', 'medium', taskCounter);
    taskCounter++;
}

function addTaskToList(taskName, taskType, priority, index) {
    const tasksList = document.getElementById('tasksList');

    const taskHtml = `
        <div class="task-item" data-task-id="${index}">
            <div class="task-header">
                <input type="text" class="task-title-input" value="${taskName}"
                       name="tasks[${index}][name]" placeholder="Task name">
                <button type="button" class="task-remove-btn" onclick="removeTask(${index})">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="task-details">
                <div class="task-field">
                    <label>Task Type</label>
                    <select name="tasks[${index}][type]" class="task-type-select">
                        <option value="planning" ${taskType === 'planning' ? 'selected' : ''}>Planning</option>
                        <option value="design" ${taskType === 'design' ? 'selected' : ''}>Design</option>
                        <option value="approval" ${taskType === 'approval' ? 'selected' : ''}>Approval</option>
                        <option value="construction" ${taskType === 'construction' ? 'selected' : ''}>Construction</option>
                        <option value="inspection" ${taskType === 'inspection' ? 'selected' : ''}>Inspection</option>
                        <option value="completion" ${taskType === 'completion' ? 'selected' : ''}>Completion</option>
                        <option value="custom" ${taskType === 'custom' ? 'selected' : ''}>Custom</option>
                    </select>
                </div>
                <div class="task-field">
                    <label>Priority</label>
                    <select name="tasks[${index}][priority]" class="task-priority-select">
                        <option value="low" ${priority === 'low' ? 'selected' : ''}>Low</option>
                        <option value="medium" ${priority === 'medium' ? 'selected' : ''}>Medium</option>
                        <option value="high" ${priority === 'high' ? 'selected' : ''}>High</option>
                        <option value="urgent" ${priority === 'urgent' ? 'selected' : ''}>Urgent</option>
                    </select>
                </div>
                <div class="task-field">
                    <label>Assigned To</label>
                    <select name="tasks[${index}][assigned_to]" class="task-assignee-select">
                        <option value="">Select User...</option>
                        <!-- Users will be loaded here -->
                    </select>
                </div>
            </div>
            <div class="task-details mt-2">
                <div class="task-field">
                    <label>Due Date</label>
                    <input type="date" name="tasks[${index}][due_date]" class="task-due-date">
                </div>
                <div class="task-field">
                    <label>Dependencies</label>
                    <select name="tasks[${index}][dependencies]" class="task-dependencies-select" multiple>
                        <!-- Other tasks will be loaded here -->
                    </select>
                </div>
                <div class="task-field">
                    <label>Estimated Hours</label>
                    <input type="number" name="tasks[${index}][estimated_hours]"
                           placeholder="Hours" min="1" class="task-hours">
                </div>
            </div>
        </div>
    `;

    tasksList.insertAdjacentHTML('beforeend', taskHtml);

    // Load users for the new task
    loadUsersForTask(index);
    updateTaskDependencies();
}

function removeTask(taskId) {
    const taskElement = document.querySelector(`[data-task-id="${taskId}"]`);
    if (taskElement) {
        taskElement.remove();
        updateTaskDependencies();
    }
}

function loadUsersForTask(taskIndex) {
    const select = document.querySelector(`select[name="tasks[${taskIndex}][assigned_to]"]`);

    fetch('/projects/getUsers')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                select.innerHTML = '<option value="">Select User...</option>';
                data.users.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user.id;
                    option.textContent = `${user.username} (${user.roles})`;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading users for task:', error);
        });
}

function updateTaskDependencies() {
    const allTasks = document.querySelectorAll('.task-item');

    allTasks.forEach((taskElement, index) => {
        const dependencySelect = taskElement.querySelector('.task-dependencies-select');
        const currentTaskName = taskElement.querySelector('.task-title-input').value;

        dependencySelect.innerHTML = '';

        allTasks.forEach((otherTask, otherIndex) => {
            if (index !== otherIndex) {
                const otherTaskName = otherTask.querySelector('.task-title-input').value;
                const option = document.createElement('option');
                option.value = otherIndex;
                option.textContent = otherTaskName;
                dependencySelect.appendChild(option);
            }
        });
    });
}

function updateStepDisplay() {
    // Update step indicators
    document.querySelectorAll('.step').forEach((step, index) => {
        const stepNum = index + 1;
        step.classList.remove('active', 'completed');

        if (stepNum < currentStep) {
            step.classList.add('completed');
        } else if (stepNum === currentStep) {
            step.classList.add('active');
        }
    });

    // Update form steps
    document.querySelectorAll('.form-step').forEach((step, index) => {
        step.classList.remove('active');
        if (index + 1 === currentStep) {
            step.classList.add('active');
        }
    });

    // Update navigation buttons
    const prevBtn = document.getElementById('prevStep');
    const nextBtn = document.getElementById('nextStep');
    const submitBtn = document.getElementById('submitProject');

    prevBtn.style.display = currentStep > 1 ? 'block' : 'none';

    if (currentStep === maxSteps) {
        nextBtn.style.display = 'none';
        submitBtn.style.display = 'block';
    } else {
        nextBtn.style.display = 'block';
        submitBtn.style.display = 'none';
    }
}

// Populate user dropdown with enhanced formatting
function populateUserDropdown() {
    const dropdown = document.getElementById('assignedTo');
    dropdown.innerHTML = '<option value="">Select a team member...</option>';

    if (allUsers.length === 0) {
        dropdown.innerHTML = '<option value="">No users available</option>';
        return;
    }

    allUsers.forEach(user => {
        const option = document.createElement('option');
        option.value = user.id;
        option.textContent = user.display_name;
        option.dataset.username = user.username;
        option.dataset.email = user.email;
        option.dataset.roles = user.roles;
        dropdown.appendChild(option);
    });
}

// Display projects
function displayProjects(projects) {
    const container = document.getElementById('projectsList');
    const userRole = '<?= $user['roles'] ?>';
    const isAdmin = userRole === 'admin';

    console.log('Displaying projects:', projects.length, projects);

    if (projects.length === 0) {
        const user = window.user || {};
        const canCreateProjects = user.roles && ['admin', 'manager', 'projects'].includes(user.roles);
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-building"></i>
                <h3>No Projects Found</h3>
                <p>You don't have any projects yet. ${canCreateProjects ? 'Create your first project to get started with SmartFlo project management.' : 'Contact your administrator to get assigned to projects.'}</p>
                ${canCreateProjects ?
                    '<a href="/projects/create" class="btn btn-primary"><i class="fas fa-plus me-2"></i>Create Your First Project</a>' :
                    '<button class="btn btn-outline-primary" onclick="resetFilters()"><i class="fas fa-refresh me-2"></i>Reset Filters</button>'
                }
            </div>
        `;
        return;
    }

    // Process projects based on user role
    let processedProjects;
    const userId = <?= $user['id'] ?>;

    if (isAdmin) {
        // Admin sees consolidated view - group by main project ID
        processedProjects = consolidateProjectsForAdmin(projects);
    } else {
        // Assignees see individual cards for their tasks only
        // Backend already filters correctly, so we don't need additional filtering here

        // Separate active and completed tasks for user cards
        const activeTasks = [];
        const completedTasks = [];

        projects.forEach(project => {
            const isCompleted = project.status === 'completed' || project.task_status === 'completed';
            if (isCompleted) {
                completedTasks.push(project);
            } else {
                activeTasks.push(project);
            }
        });

        // Sort active tasks by target completion date
        const sortedActiveTasks = activeTasks.sort((a, b) => {
            const dateA = new Date(a.target_completion);
            const dateB = new Date(b.target_completion);
            return dateA - dateB;
        });

        // Sort completed tasks by completion date (most recent first)
        const sortedCompletedTasks = completedTasks.sort((a, b) => {
            const dateA = new Date(a.updated_at);
            const dateB = new Date(b.updated_at);
            return dateB - dateA;
        });

        // Apply additional dependency filtering for non-admin users
        const userId = <?= $user['id'] ?>;
        const userRole = '<?= $user['roles'] ?>';
        const isAdmin = userRole === 'admin' || userRole === 'manager';

        if (!isAdmin) {
            // Enhanced dependency filtering for non-admin users
            const filteredActiveTasks = sortedActiveTasks.filter(project => {
                // Debug logging for dependency filtering
                console.log('🔍 Checking task dependency:', {
                    taskName: project.task_name,
                    projectName: project.project_name,
                    dependsOn: project.depends_on,
                    dependencyStatus: project.dependency_status,
                    dependencyTaskName: project.dependency_task_name,
                    isLocked: project.is_locked,
                    status: project.status,
                    taskStatus: project.task_status
                });

                // Allow tasks with no dependencies
                if (!project.depends_on && !project.is_locked) {
                    console.log('✅ Allowing task (no dependencies):', project.task_name);
                    return true;
                }

                // Block explicitly locked tasks
                if (project.is_locked === true || project.is_locked === 1) {
                    console.log('🔒 Blocking locked task:', project.task_name);
                    return false;
                }

                // Check dependency completion status
                if (project.depends_on) {
                    const dependencyCompleted = project.dependency_status === 'completed';
                    const managerApproved = project.dependency_manager_status === 'client_accepted';
                    console.log('🔍 Dependency check details:', {
                        taskName: project.task_name,
                        dependsOn: project.depends_on,
                        dependencyStatus: project.dependency_status,
                        dependencyManagerStatus: project.dependency_manager_status,
                        dependencyTaskName: project.dependency_task_name,
                        dependencyCompleted: dependencyCompleted,
                        managerApproved: managerApproved,
                        assignedTo: project.assigned_to,
                        currentUserId: userId
                    });

                    if (dependencyCompleted && managerApproved) {
                        console.log('✅ Allowing task (dependency completed and approved):', project.task_name, 'depends on:', project.dependency_task_name);
                        return true;
                    } else {
                        console.log('🔒 Blocking task (dependency not completed or not approved):', project.task_name, 'depends on:', project.dependency_task_name, 'status:', project.dependency_status, 'manager_status:', project.dependency_manager_status);
                        return false;
                    }
                }

                // Default: allow task if no clear dependency blocking
                console.log('✅ Allowing task (default):', project.task_name);
                return true;
            });

            const filteredCompletedTasks = sortedCompletedTasks.filter(project => {
                // Always show completed tasks (they were accessible when completed)
                return true;
            });

            console.log('📊 Dependency filtering results:', {
                originalActiveTasks: sortedActiveTasks.length,
                filteredActiveTasks: filteredActiveTasks.length,
                blockedTasks: sortedActiveTasks.length - filteredActiveTasks.length,
                completedTasks: filteredCompletedTasks.length
            });

            processedProjects = [...filteredActiveTasks, ...filteredCompletedTasks];
        } else {
            // Combine: active tasks first, then completed tasks
            processedProjects = [...sortedActiveTasks, ...sortedCompletedTasks];
        }
    }

    // For non-admin users, add separator between active and completed tasks
    let htmlContent = '';
    if (!isAdmin && processedProjects.length > 0) {
        const activeTasks = processedProjects.filter(p => !(p.status === 'completed' || p.task_status === 'completed'));
        const completedTasks = processedProjects.filter(p => p.status === 'completed' || p.task_status === 'completed');

        // Render active tasks
        activeTasks.forEach(project => {
            console.log('Rendering active project card for:', project.project_name, project);
            htmlContent += generateProjectCard(project, isAdmin);
        });

        // Separator removed as requested - no visual separation between active and completed tasks

        // Render completed tasks
        completedTasks.forEach(project => {
            console.log('Rendering completed project card for:', project.project_name, project);
            htmlContent += generateProjectCard(project, isAdmin);
        });

        container.innerHTML = htmlContent;
    } else {
        // Admin view or no separation needed
        container.innerHTML = processedProjects.map(project => {
            console.log('Rendering project card for:', project.project_name, project);
            return generateProjectCard(project, isAdmin);
        }).join('');
    }
}

// Generate individual project card HTML
function generateProjectCard(project, isAdmin) {
    return `
        <div class="project-card-final ${getProjectCardStatusClass(project)} ${getRoleBasedCardClass(project)}" data-project-id="${project.id}" data-project-data='${JSON.stringify(project)}'>
            <!-- Header Row: Project ID.Name and 3 Dots Menu -->
            <div class="card-header-final">
                <div class="project-title-final">
                    <div class="project-title-main">
                        ${getLiveStatusIcon(project.task_status || project.status)}
                        <span class="project-number">${getMainProjectId(project.project_id)}.</span>
                        <span class="project-name">${project.project_name}</span>
                    </div>
                    <div class="days-left-below-title">
                        ${getDaysLeftDisplayWithUrgency(project.target_completion, project)}
                    </div>
                    ${getTaskAndAssigneeDisplay(project, isAdmin)}
                </div>
                <div class="menu-dots-header">
                    <button class="dots-btn" onclick="showProjectMenu(${project.id})" title="More options">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>

            <!-- Action Buttons Row -->
            <div class="action-buttons-final">
                ${getAssigneeButtons(project)}
                ${getAssignerDropdown(project)}
            </div>

            <!-- Manager Status Label -->
            ${getManagerStatusLabel(project)}

            <!-- Task Dependencies Display -->
            ${getTaskDependenciesDisplay(project)}

            <!-- Real-time Duration Display for Assignees -->
            ${getRealTimeDurationDisplay(project)}

            <!-- Bottom Row: Removed to save space -->
        </div>
    `;
}

// Consolidate projects for admin view - group by main project ID
function consolidateProjectsForAdmin(projects) {
    const projectGroups = {};

    projects.forEach(project => {
        const mainId = getMainProjectId(project.project_id);

        if (!projectGroups[mainId]) {
            projectGroups[mainId] = {
                ...project,
                project_id: mainId,
                tasks: [],
                assignees: [],
                all_statuses: []
            };
        }

        // Add task and assignee info with better data handling
        if (project.task_name) {
            // Check if this task already exists to avoid duplicates
            const existingTask = projectGroups[mainId].tasks.find(t =>
                t.task_id === project.task_id && t.assignee_id === project.assigned_to
            );

            if (!existingTask) {
                projectGroups[mainId].tasks.push({
                    name: project.task_name,
                    assignee: project.task_assigned_username || project.assigned_username || 'Unassigned',
                    assignee_id: project.task_assigned_to || project.assigned_to,
                    status: project.task_status || project.status || 'not_started', // Use task_status as primary
                    task_status: project.task_status || 'not_started',
                    id: project.id,
                    task_id: project.task_id || `task_${project.id}`,
                    created_at: project.created_at,
                    updated_at: project.updated_at
                });
            } else {
                // Update existing task with latest data - prioritize task_status
                existingTask.status = project.task_status || project.status || 'not_started';
                existingTask.task_status = project.task_status || 'not_started';
                existingTask.updated_at = project.updated_at;
            }
        }

        // Track unique assignees - prioritize task assignees over project assignees
        const assigneeId = project.task_assigned_to || project.assigned_to;
        const assigneeUsername = project.task_assigned_username || project.assigned_username;
        const assigneeEmail = project.task_assigned_email || project.assigned_email || '';

        if (assigneeUsername && !projectGroups[mainId].assignees.some(a => a.id === assigneeId)) {
            projectGroups[mainId].assignees.push({
                id: assigneeId,
                username: assigneeUsername,
                email: assigneeEmail
            });
        }

        // Ensure project has assignee information even without tasks
        if (!projectGroups[mainId].assigned_username && project.assigned_username) {
            projectGroups[mainId].assigned_username = project.assigned_username;
            projectGroups[mainId].assigned_to = project.assigned_to;
        }

        // Track all statuses for overall project status calculation
        projectGroups[mainId].all_statuses.push(project.status);

        // Also track task statuses for live icon calculation
        if (project.task_status) {
            if (!projectGroups[mainId].task_statuses) {
                projectGroups[mainId].task_statuses = [];
            }
            projectGroups[mainId].task_statuses.push(project.task_status);
        }

        // Use the most recent update
        if (new Date(project.updated_at) > new Date(projectGroups[mainId].updated_at)) {
            projectGroups[mainId].last_update_notes = project.last_update_notes;
            projectGroups[mainId].updated_at = project.updated_at;
        }
    });

    // Convert back to array and determine overall status
    return Object.values(projectGroups).map(group => {
        // Calculate task completion counts
        if (group.tasks && group.tasks.length > 0) {
            group.total_tasks = group.tasks.length;
            group.completed_tasks = group.tasks.filter(task => task.task_status === 'completed').length;

            // Sort tasks: completed tasks go to the end
            group.tasks.sort((a, b) => {
                if (a.task_status === 'completed' && b.task_status !== 'completed') return 1;
                if (a.task_status !== 'completed' && b.task_status === 'completed') return -1;
                return 0;
            });
        } else {
            group.total_tasks = 0;
            group.completed_tasks = 0;
        }

        // Determine overall project status based on task statuses
        const statuses = group.all_statuses;
        const taskStatuses = group.task_statuses || [];

        // Use task statuses for more accurate status calculation
        if (taskStatuses.includes('in_progress')) {
            group.status = 'in_progress';
            group.task_status = 'in_progress'; // Set for live icon
        } else if (taskStatuses.every(s => s === 'completed') && taskStatuses.length > 0) {
            group.status = 'completed';
            group.task_status = 'completed';
        } else if (taskStatuses.includes('on_hold')) {
            group.status = 'on_hold';
            group.task_status = 'on_hold';
        } else if (statuses.includes('in_progress')) {
            group.status = 'in_progress';
            group.task_status = 'in_progress'; // Set for live icon
        } else if (statuses.every(s => s === 'completed')) {
            group.status = 'completed';
            group.task_status = 'completed';
        } else if (statuses.includes('on_hold')) {
            group.status = 'on_hold';
            group.task_status = 'on_hold';
        } else {
            group.status = statuses[0] || 'not_started';
            group.task_status = taskStatuses[0] || 'not_started';
        }

        // Debug logging for admin project status
        console.log('🔍 Admin project status debug:', {
            projectName: group.project_name,
            projectId: group.project_id,
            allStatuses: statuses,
            finalStatus: group.status,
            taskStatus: group.task_status
        });

        return group;
    }).sort((a, b) => {
        // Sort projects: completed projects go to the end
        if (a.status === 'completed' && b.status !== 'completed') return 1;
        if (a.status !== 'completed' && b.status === 'completed') return -1;

        // For non-completed projects, sort by target completion date
        if (a.status !== 'completed' && b.status !== 'completed') {
            const dateA = new Date(a.target_completion);
            const dateB = new Date(b.target_completion);
            return dateA - dateB;
        }

        // For completed projects, sort by completion date (most recent first)
        if (a.status === 'completed' && b.status === 'completed') {
            const dateA = new Date(a.updated_at);
            const dateB = new Date(b.updated_at);
            return dateB - dateA;
        }

        return 0;
    });
}

// Get main project ID (extract only digits)
function getMainProjectId(projectId) {
    if (!projectId) return 'N/A';
    // Extract only digits from the project ID
    const digits = projectId.toString().match(/\d+/g);
    return digits ? digits.join('') : 'N/A';
}

// Get live status icon for admin view - only show for active running projects
function getLiveStatusIcon(status) {
    // Debug logging to see what status values we're getting
    console.log('🔴 Live status icon debug:', {
        originalStatus: status,
        statusType: typeof status
    });

    // Handle various status formats
    const normalizedStatus = status ? status.toLowerCase().replace(/[_\s]/g, '_') : '';

    console.log('🔴 Normalized status:', normalizedStatus);

    // Only show green live icon for active/running projects
    if (normalizedStatus === 'in_progress' || normalizedStatus === 'inprogress' || normalizedStatus === 'running') {
        console.log('✅ Returning green running icon');
        return '<i class="fas fa-circle live-indicator live-running" title="Project Running" style="color: #10b981; margin-right: 6px; font-size: 0.8em;"></i>';
    }

    // No icon for other statuses (completed, on_hold, not_started, etc.)
    console.log('🔴 No live icon for status:', status, 'normalized:', normalizedStatus);
    return '';
}

// Get task and assignee display based on role
function getTaskAndAssigneeDisplay(project, isAdmin) {
    let display = '';

    if (isAdmin) {
        // Admin sees all tasks and assignees with enhanced layout
        display += '<div class="tasks-summary-admin">';

        // Check if project has tasks
        if (project.tasks && project.tasks.length > 0) {
            // Separate tasks into active and completed
            const activeTasks = [];
            const completedTasks = [];

        project.tasks.forEach(task => {
            const isCompleted = task.task_status === 'completed' || task.status === 'completed';
            if (isCompleted) {
                completedTasks.push(task);
            } else {
                activeTasks.push(task);
            }
        });

        // Sort active tasks by availability and status
        const sortedActiveTasks = activeTasks.sort((a, b) => {
            // Helper function to check if task is available (not locked by dependencies)
            // Now requires both completion AND manager approval
            const isTaskAvailable = (task) => {
                if (!task.depends_on) {
                    return true; // No dependencies
                }
                // Requires both completion AND manager approval (client_accepted)
                return task.dependency_status === 'completed' && task.dependency_manager_status === 'client_accepted';
            };

            const aAvailable = isTaskAvailable(a);
            const bAvailable = isTaskAvailable(b);
            const aInProgress = a.task_status === 'in_progress' || a.status === 'in_progress';
            const bInProgress = b.task_status === 'in_progress' || b.status === 'in_progress';

            // 1. In-progress tasks first (highest priority)
            if (aInProgress && !bInProgress) return -1;
            if (!aInProgress && bInProgress) return 1;

            // 2. Available tasks before locked tasks
            if (aAvailable && !bAvailable) return -1;
            if (!aAvailable && bAvailable) return 1;

            // 3. Within same availability/status group, sort by task order
            return (a.task_order || 0) - (b.task_order || 0);
        });

        // Sort completed tasks by completion date (most recent first)
        const sortedCompletedTasks = completedTasks.sort((a, b) => {
            const dateA = new Date(a.completed_at || a.updated_at);
            const dateB = new Date(b.completed_at || b.updated_at);
            return dateB - dateA;
        });

        // Display active tasks first
        sortedActiveTasks.forEach(task => {
            const isActive = task.status === 'in_progress';
            const isCompleted = task.status === 'completed' || task.task_status === 'completed';
            const isOnHold = task.status === 'on_hold';
            const isPending = task.status === 'not_started' || task.status === 'planning';

            // Enhanced task type icons (removed fa task icons as requested)
            let taskTypeIcon = '';
            if (task.task_type_name) {
                const taskType = task.task_type_name.toLowerCase();
                if (taskType.includes('design') || taskType.includes('3d')) {
                    taskTypeIcon = '<i class="fas fa-cube text-primary" title="3D Design"></i>';
                } else if (taskType.includes('plan')) {
                    taskTypeIcon = '<i class="fas fa-drafting-compass text-info" title="Planning"></i>';
                } else if (taskType.includes('permit')) {
                    taskTypeIcon = '<i class="fas fa-file-contract text-warning" title="Permit"></i>';
                } else if (taskType.includes('construction')) {
                    taskTypeIcon = '<i class="fas fa-hard-hat text-danger" title="Construction"></i>';
                } else if (taskType.includes('completion')) {
                    taskTypeIcon = '<i class="fas fa-flag-checkered text-success" title="Completion"></i>';
                } else {
                    // Remove fa task icon as requested
                    taskTypeIcon = '';
                }
            } else {
                // Remove fa task icon as requested
                taskTypeIcon = '';
            }

            // Check if task is locked due to dependencies
            const isLocked = task.is_locked || (task.depends_on && task.dependency_status !== 'completed');
            const isAvailable = !task.depends_on || task.dependency_status === 'completed';

            // Enhanced status indicator with dependency and availability icons
            let statusIndicator = '';
            let dependencyIndicator = '';

            // Primary status indicator (shows before task name) - prioritize status over dependency
            if (isLocked) {
                dependencyIndicator = '<i class="fas fa-lock text-danger me-2" title="Task can start only after dependencies are completed" style="font-size: 1.1em;"></i>';
            } else if (isActive) {
                dependencyIndicator = '<i class="fas fa-play-circle text-success me-2" title="In Progress" style="font-size: 1.1em;"></i>';
            } else if (isCompleted) {
                dependencyIndicator = '<i class="fas fa-check-circle text-success me-2" title="Completed" style="font-size: 1.1em;"></i>';
            } else if (isOnHold) {
                dependencyIndicator = '<i class="fas fa-pause-circle text-warning me-2" title="On Hold" style="font-size: 1.1em;"></i>';
            } else if (isAvailable) {
                dependencyIndicator = '<i class="fas fa-clock text-muted me-2" title="Task can start immediately" style="opacity: 0.6; font-size: 1.1em;"></i>';
            }

            // Secondary status indicator (shows after task name) - only for additional info
            // No longer needed since status is shown before task name

            // Manager status indicator
            let managerStatusIndicator = '';
            if (isCompleted && task.manager_status) {
                const managerConfig = getManagerStatusConfig(task.manager_status);
                managerStatusIndicator = `<i class="${managerConfig.icon} ${managerConfig.colorClass}" title="${managerConfig.text}"></i>`;
            }

            const arrowClass = isActive ? 'assignee-name-small active-assignee' : 'assignee-name-small';
            const taskClass = isCompleted ? 'task-name-small completed-task' : (isLocked ? 'task-name-small locked-task' : 'task-name-small');

            // Check for urgency indicators - show if other users depend on this task
            let urgencyIndicator = '';
            if (task.has_dependent_tasks && !isCompleted) {
                urgencyIndicator = '<i class="fas fa-exclamation-triangle text-warning ms-2" title="Other tasks are waiting for this to complete" style="font-size: 1.1em;"></i>';
            }

            display += `<div class="task-assignee-line enhanced">
                <div class="task-info-enhanced">
                    <div class="task-icons">
                        ${taskTypeIcon}
                        ${managerStatusIndicator}
                    </div>
                    <div class="task-name-container">
                        ${dependencyIndicator}<span class="${taskClass} ${isCompleted ? 'completed-task force-strikethrough' : ''}"
                              style="${isCompleted ? 'text-decoration: line-through !important; text-decoration-color: rgba(0, 0, 0, 0.3) !important; text-decoration-thickness: 1px !important; opacity: 0.7;' : ''}"
                              data-completed="${isCompleted}">${task.name}${getRevisionNumber(task)}</span>${urgencyIndicator}
                    </div>
                </div>
                <div class="task-assignee-info">
                    <span class="${arrowClass}" title="Assigned to: ${task.assignee || task.assigned_username || 'Unassigned'}">${task.assignee || task.assigned_username || 'Unassigned'}</span>
                    ${getTaskActionButton(task, project.id, isAdmin, project)}
                </div>
            </div>`;
        });

        // Add completed tasks section if there are any
        if (sortedCompletedTasks.length > 0) {
            display += `
                <div class="completed-tasks-section">
                    <div class="completed-tasks-header">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        <span class="completed-tasks-title">Completed Tasks (${sortedCompletedTasks.length})</span>
                    </div>
                    <div class="completed-tasks-list">
            `;

            sortedCompletedTasks.forEach(task => {
                const isCompleted = true; // All tasks in this section are completed
                const taskClass = 'task-name-small completed-task';
                const arrowClass = 'assignee-name-small';

                // Manager status indicator
                let managerStatusIndicator = '';
                if (task.manager_status) {
                    const managerConfig = getManagerStatusConfig(task.manager_status);
                    managerStatusIndicator = `<i class="${managerConfig.icon} ${managerConfig.colorClass}" title="${managerConfig.text}"></i>`;
                }

                const dependencyIndicator = '<i class="fas fa-check-circle text-success me-2" title="Completed" style="font-size: 1.1em;"></i>';

                display += `<div class="task-assignee-line enhanced completed-task-line">
                    <div class="task-info-enhanced">
                        <div class="task-icons">
                            ${managerStatusIndicator}
                        </div>
                        <div class="task-name-container">
                            ${dependencyIndicator}<span class="${taskClass} completed-task force-strikethrough"
                                  style="text-decoration: line-through !important; text-decoration-color: rgba(0, 0, 0, 0.3) !important; text-decoration-thickness: 1px !important; opacity: 0.7;"
                                  data-completed="true">${task.name}${getRevisionNumber(task)}</span>
                        </div>
                    </div>
                    <div class="task-assignee-info">
                        <span class="${arrowClass}" title="Assigned to: ${task.assignee || task.assigned_username || 'Unassigned'}">${task.assignee || task.assigned_username || 'Unassigned'}</span>
                        ${getTaskActionButton(task, project.id, isAdmin, project)}
                    </div>
                </div>`;
            });

            display += `
                    </div>
                </div>
            `;
        }

        } else {
            // No tasks found for this project - show appropriate message
            display += `
                <div class="no-tasks-message">
                    <div class="no-tasks-content">
                        <i class="fas fa-info-circle text-muted me-2"></i>
                        <span class="text-muted">No tasks created yet</span>
                    </div>
                    <div class="project-assignee-info">
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i>
                            Assigned to: ${project.assigned_username || 'Unassigned'}
                        </small>
                    </div>
                </div>
            `;
        }

        display += '</div>';

        // Manager status update buttons are now task-specific (shown in getTaskActionButton)
    } else if (project.task_name) {
        // Assignees see their own task with status
        // Fix: Check both project.status and project.task_status for completion
        const isCompleted = project.status === 'completed' || project.task_status === 'completed';
        const isActive = project.status === 'in_progress';
        const isLocked = project.is_locked || (project.depends_on && project.dependency_status !== 'completed');
        const taskClass = isCompleted ? 'task-name-small completed-task' : (isLocked ? 'task-name-small locked-task' : 'task-name-small');

        // Debug logging
        console.log('Single task strikethrough debug:', {
            projectName: project.project_name,
            taskName: project.task_name,
            projectStatus: project.status,
            taskStatus: project.task_status,
            isCompleted: isCompleted,
            taskClass: taskClass
        });

        // Enhanced status icon logic (same as admin cards) - bigger icons before task name
        let statusIcon = '';
        const isOnHold = project.status === 'on_hold';
        const isAvailable = !project.depends_on || project.dependency_status === 'completed';

        if (isLocked) {
            statusIcon = '<i class="fas fa-lock text-danger me-2" title="Task can start only after dependencies are completed" style="font-size: 1.1em;"></i>';
        } else if (isActive) {
            statusIcon = '<i class="fas fa-play-circle text-success me-2" title="In Progress" style="font-size: 1.1em;"></i>';
        } else if (isCompleted) {
            statusIcon = '<i class="fas fa-check-circle text-success me-2" title="Completed" style="font-size: 1.1em;"></i>';
        } else if (isOnHold) {
            statusIcon = '<i class="fas fa-pause-circle text-warning me-2" title="On Hold" style="font-size: 1.1em;"></i>';
        } else if (isAvailable) {
            statusIcon = '<i class="fas fa-clock text-muted me-2" title="Task can start immediately" style="opacity: 0.6; font-size: 1.1em;"></i>';
        }

        // For completed tasks, show the task name with strikethrough (no lock icon)
        if (isCompleted) {
            display += `
                <div class="task-name-with-icon mb-2">
                    <span class="${taskClass} completed-task force-strikethrough"
                          style="text-decoration: line-through !important; text-decoration-color: rgba(0, 0, 0, 0.3) !important; text-decoration-thickness: 1px !important; opacity: 0.7; color: #9ca3af;"
                          data-completed="${isCompleted}">${project.task_name}</span>
                </div>
            `;

            // Add horizontal status section for completed tasks
            display += getUserProjectStatusHorizontal(project);
        } else {
            display += `<div class="${taskClass}"
                         data-completed="${isCompleted}">${statusIcon}${project.task_name}</div>`;
        }

        // Add manager comments below if available (for completed tasks only)
        if (isCompleted && project.manager_comments) {
            display += `
                <div class="manager-comments-below">
                    <div class="comments-header-below">
                        <i class="fas fa-comment text-muted"></i>
                        <span>Manager Comments:</span>
                    </div>
                    <div class="comments-text-below">${project.manager_comments}</div>
                </div>
            `;
        }
    } else if (isAdmin && project.assignees && project.assignees.length > 0) {
        // Show assignees even if no specific tasks
        display += '<div class="assignees-only">';
        display += '<i class="fas fa-users"></i> ';
        display += project.assignees.map(a => a.username || a).join(', ');
        display += '</div>';
    } else if (!isAdmin) {
        // Non-admin user with no specific task assigned - show project info
        display += `
            <div class="no-task-assigned">
                <div class="no-task-content">
                    <i class="fas fa-clock text-muted me-2"></i>
                    <span class="text-muted">Waiting for task assignment</span>
                </div>
                <div class="project-status-info">
                    <small class="text-muted">
                        Project Status: ${project.status ? project.status.replace('_', ' ').toUpperCase() : 'PENDING'}
                    </small>
                </div>
            </div>
        `;
    }

    return display;
}

// Get user project status section for completed tasks
function getUserProjectStatusSection(project) {
    const userRole = '<?= $user['roles'] ?>';
    const isAdmin = userRole === 'admin' || userRole === 'manager';

    // Only show for non-admin users
    if (isAdmin) return '';

    // Project status information - use task_manager_status for task-level status
    const managerStatus = project.task_manager_status;
    const revisionCount = project.revision_count || 0;
    const managerComments = project.manager_comments;

    // Determine current project status
    let currentStatus = '';
    let statusIcon = '';
    let statusClass = '';
    let showStartButton = false;

    if (!managerStatus) {
        currentStatus = 'Completed';
        statusIcon = '<i class="fas fa-check-circle text-success"></i>';
        statusClass = 'status-completed';
    } else if (managerStatus === 'sent_for_review') {
        currentStatus = 'Review needed';
        statusIcon = '<i class="fas fa-eye text-info"></i>';
        statusClass = 'status-review';
    } else if (managerStatus === 'need_revision') {
        currentStatus = 'Revision needed';
        statusIcon = '<i class="fas fa-edit text-warning"></i>';
        statusClass = 'status-revision';
        showStartButton = true;
    } else if (managerStatus === 'client_accepted') {
        currentStatus = 'Client accepted';
        statusIcon = '<i class="fas fa-check-circle text-success"></i>';
        statusClass = 'status-accepted';
    } else if (managerStatus === 'rejected') {
        currentStatus = 'Client rejected';
        statusIcon = '<i class="fas fa-times-circle text-danger"></i>';
        statusClass = 'status-rejected';
        showStartButton = true;
    }

    // Build inline status section (same line as completed task)
    let statusDisplay = `
        <span class="user-project-status-inline ${statusClass}">
            ${statusIcon}
            <span class="status-text-inline">${currentStatus}</span>
            ${revisionCount > 0 ? `<span class="revision-count-inline">(Rev. ${revisionCount})</span>` : ''}
            ${showStartButton ? `<button class="btn btn-xs btn-primary ms-1" onclick="startRevision(${project.id})" title="Start Revision"><i class="fas fa-play"></i></button>` : ''}
        </span>
    `;

    // Note: "Waiting to submit to client" is now shown at the bottom of the card
    // instead of inline with the status

    return statusDisplay;
}

// Get user project status section for completed tasks - horizontal layout
function getUserProjectStatusHorizontal(project) {
    const userRole = '<?= $user['roles'] ?>';
    const isAdmin = userRole === 'admin' || userRole === 'manager';

    // Debug logging
    console.log('getUserProjectStatusHorizontal called:', {
        project: project,
        userRole: userRole,
        isAdmin: isAdmin,
        managerStatus: project.manager_status
    });

    // Only show for non-admin users
    if (isAdmin) return '';

    // Project status information - use task_manager_status for task-level status
    const managerStatus = project.task_manager_status;
    const revisionCount = project.revision_count || 0;

    // Determine current project status
    let leftStatus = '';
    let rightStatus = '';
    let showStartButton = false;

    if (!managerStatus) {
        leftStatus = `
            <span class="text-muted d-flex align-items-center" style="font-weight: 500;">
                <i class="fas fa-clock me-2" style="color: #9ca3af; font-size: 1.1em;"></i>
                Waiting to submit to client
            </span>
        `;
        rightStatus = '';
    } else if (managerStatus === 'sent_for_review') {
        leftStatus = `
            <span class="text-info d-flex align-items-center" style="font-weight: 500;">
                <i class="fas fa-hourglass-half me-2" style="color: #3b82f6; font-size: 1.1em;"></i>
                Under review
            </span>
        `;
        rightStatus = '';
    } else if (managerStatus === 'need_revision') {
        leftStatus = `
            <span class="text-warning d-flex align-items-center" style="font-weight: 500;">
                <i class="fas fa-edit me-2" style="color: #f59e0b; font-size: 1.1em;"></i>
                Revision needed
            </span>
        `;
        rightStatus = `
            <button class="btn btn-xs btn-primary" onclick="startRevision(${project.id})" title="Start Revision">
                <i class="fas fa-play me-1"></i>Start Revision
            </button>
        `;
        showStartButton = true;
    } else if (managerStatus === 'client_accepted') {
        leftStatus = `
            <span class="text-success d-flex align-items-center" style="font-weight: 500;">
                <i class="fas fa-check-circle me-2" style="color: #10b981; font-size: 1.1em;"></i>
                Client accepted
            </span>
        `;
        rightStatus = '';
    } else if (managerStatus === 'rejected') {
        leftStatus = `
            <span class="text-danger d-flex align-items-center" style="font-weight: 500;">
                <i class="fas fa-times-circle me-2" style="color: #ef4444; font-size: 1.1em;"></i>
                Rejected by client
            </span>
        `;
        rightStatus = `
            <button class="btn btn-xs btn-primary" onclick="startRevision(${project.id})" title="Start Revision">
                <i class="fas fa-play me-1"></i>Start Revision
            </button>
        `;
        showStartButton = true;
    }

    // Build horizontal status section to match the exact design
    return `
        <div class="user-status-horizontal d-flex justify-content-between align-items-center mt-2 p-2" style="background: transparent; border: none; font-size: 0.9rem;">
            <div class="status-left d-flex align-items-center">
                ${leftStatus}
                ${revisionCount > 0 ? `<small class="text-muted ms-2">(Rev. ${revisionCount})</small>` : ''}
            </div>
            <div class="status-right">
                ${rightStatus}
            </div>
        </div>
    `;
}

// Get project card status class - only completed when admin marks as complete
function getProjectCardStatusClass(project) {
    // Project is only considered completed when admin explicitly marks it as completed
    // Individual task completion should not change project card color
    if (project.status === 'completed') {
        return 'status-completed';
    } else if (project.status === 'in_progress') {
        return 'status-in_progress';
    } else if (project.status === 'on_hold') {
        return 'status-on_hold';
    } else {
        return 'status-not_started';
    }
}

// Task completion summary function removed as the feature was removed

// Get horizontal action buttons for new card layout
function getHorizontalActionButtons(project) {
    const canUpdate = project.assigned_to == <?= $user['id'] ?> || <?= in_array($user['roles'], ['admin', 'manager']) ? 'true' : 'false' ?>;

    let buttons = '';

    // Primary action buttons based on status
    if (canUpdate) {
        if (project.status === 'planning' || project.status === 'not_started') {
            buttons += `
                <button class="btn-action btn-start" onclick="updateStatus(${project.id}, 'in_progress')" title="Start Project">
                    <i class="fas fa-play"></i>
                    <span>Start</span>
                </button>
            `;
        } else if (project.status === 'in_progress') {
            buttons += `
                <button class="btn-action btn-hold" onclick="updateStatus(${project.id}, 'on_hold')" title="Hold Project">
                    <i class="fas fa-pause"></i>
                    <span>Hold</span>
                </button>
                <button class="btn-action btn-complete" onclick="updateStatus(${project.id}, 'completed')" title="Complete Project">
                    <i class="fas fa-check"></i>
                    <span>Complete</span>
                </button>
            `;
        } else if (project.status === 'on_hold') {
            buttons += `
                <button class="btn-action btn-resume" onclick="updateStatus(${project.id}, 'in_progress')" title="Resume Project">
                    <i class="fas fa-play"></i>
                    <span>Resume</span>
                </button>
            `;
        } else if (project.status === 'completed') {
            buttons += `
                <div class="completed-badge">
                    <i class="fas fa-check-circle"></i>
                    <span>Completed</span>
                </div>
            `;
        }
    }

    // Secondary actions
    buttons += `
        <button class="btn-action btn-view" onclick="viewProject(${project.id})" title="View Details">
            <i class="fas fa-eye"></i>
            <span>Details</span>
        </button>
    `;

    // Admin delete button
    if (<?= $user['roles'] === 'admin' ? 'true' : 'false' ?>) {
        buttons += `
            <button class="btn-action btn-delete" onclick="deleteProject(${project.id}, '${project.project_name}')" title="Delete Project">
                <i class="fas fa-trash"></i>
            </button>
        `;
    }

    return buttons;
}

// Get progress color based on status and percentage
function getProgressColor(status, percentage) {
    if (status === 'completed') return 'progress-completed';
    if (status === 'on_hold') return 'progress-hold';
    if (percentage >= 75) return 'progress-high';
    if (percentage >= 50) return 'progress-medium';
    if (percentage >= 25) return 'progress-low';
    return 'progress-start';
}

// Legacy function for backward compatibility
function getActionButtons(project) {
    return getHorizontalActionButtons(project);
}

// Format status text for display
function formatStatusText(status) {
    const statusMap = {
        'not_started': 'Not Started',
        'planning': 'Planning',
        'in_progress': 'In Progress',
        'on_hold': 'On Hold',
        'completed': 'Completed'
    };
    return statusMap[status] || status.replace('_', ' ');
}

// Get urgency badge based on target completion date
function getUrgencyBadge(project) {
    const targetDate = new Date(project.target_completion);
    const today = new Date();
    const daysUntilTarget = Math.ceil((targetDate - today) / (1000 * 60 * 60 * 24));

    if (project.status === 'completed') return '';

    if (daysUntilTarget < 0) {
        return '<span class="urgency-badge overdue">Overdue</span>';
    } else if (daysUntilTarget <= 7) {
        return '<span class="urgency-badge urgent">Urgent</span>';
    } else if (daysUntilTarget <= 30) {
        return '<span class="urgency-badge soon">Due Soon</span>';
    }
    return '';
}

// Get days until target completion
function getDaysUntilTarget(targetDate) {
    const target = new Date(targetDate);
    const today = new Date();
    const daysUntilTarget = Math.ceil((target - today) / (1000 * 60 * 60 * 24));

    if (daysUntilTarget < 0) {
        return `<span class="days-overdue">(${Math.abs(daysUntilTarget)} days overdue)</span>`;
    } else if (daysUntilTarget === 0) {
        return '<span class="days-today">(Due today)</span>';
    } else if (daysUntilTarget <= 30) {
        return `<span class="days-soon">(${daysUntilTarget} days left)</span>`;
    }
    return `<span class="days-normal">(${daysUntilTarget} days left)</span>`;
}

// Get time tracking display
function getTimeTrackingDisplay(project) {
    if (!project.time_tracking) return '';

    const currentStatus = project.status;
    const timeInCurrentStatus = getCurrentStatusTime(project);

    return `
        <div class="time-tracking-section">
            <div class="time-tracking-header">
                <i class="fas fa-clock me-2"></i>
                <span class="time-label">Time in ${formatStatusText(currentStatus)}</span>
            </div>
            <div class="current-status-time">${timeInCurrentStatus}</div>
            <div class="total-project-time">
                <small class="text-muted">Total project time: ${formatDuration(project.days_since_start * 24 * 60 * 60)}</small>
            </div>
        </div>
    `;
}

// Get current status time
function getCurrentStatusTime(project) {
    const statusStartTimes = {
        'not_started': project.created_at,
        'planning': project.created_at,
        'in_progress': project.in_progress_at || project.created_at,
        'on_hold': project.on_hold_at || project.created_at,
        'completed': project.completed_at || project.created_at
    };

    const startTime = new Date(statusStartTimes[project.status]);
    const currentTime = project.status === 'completed' ? new Date(project.completed_at) : new Date();
    const timeDiff = currentTime - startTime;

    return formatDuration(timeDiff / 1000);
}

// Format duration in seconds to human readable format
function formatDuration(seconds) {
    if (seconds < 60) {
        return Math.round(seconds) + ' seconds';
    } else if (seconds < 3600) {
        return Math.round(seconds / 60) + ' minutes';
    } else if (seconds < 86400) {
        return Math.round(seconds / 3600 * 10) / 10 + ' hours';
    } else {
        return Math.round(seconds / 86400 * 10) / 10 + ' days';
    }
}

// Get urgency indicator for modern design
function getUrgencyIndicator(project) {
    const targetDate = new Date(project.target_completion);
    const today = new Date();
    const daysUntilTarget = Math.ceil((targetDate - today) / (1000 * 60 * 60 * 24));

    if (project.status === 'completed') return '';

    if (daysUntilTarget < 0) {
        return '<div class="urgency-indicator overdue"><i class="fas fa-exclamation-triangle"></i></div>';
    } else if (daysUntilTarget <= 3) {
        return '<div class="urgency-indicator critical"><i class="fas fa-fire"></i></div>';
    } else if (daysUntilTarget <= 7) {
        return '<div class="urgency-indicator urgent"><i class="fas fa-clock"></i></div>';
    }
    return '';
}

// Get status icon
function getStatusIcon(status) {
    const icons = {
        'not_started': '<i class="fas fa-pause-circle"></i>',
        'planning': '<i class="fas fa-clipboard-list"></i>',
        'in_progress': '<i class="fas fa-play-circle"></i>',
        'on_hold': '<i class="fas fa-pause-circle"></i>',
        'completed': '<i class="fas fa-check-circle"></i>'
    };
    return icons[status] || '<i class="fas fa-circle"></i>';
}

// Get manager status label for all projects
function getManagerStatusLabel(project) {
    // Check if project has any completed tasks or is completed
    let hasCompletedTasks = false;

    // Check if project itself is completed
    if (project.status === 'completed') {
        hasCompletedTasks = true;
    }

    // Check if project has any completed tasks (for multi-task projects)
    if (project.tasks && Array.isArray(project.tasks)) {
        hasCompletedTasks = project.tasks.some(task =>
            task.status === 'completed' || task.task_status === 'completed'
        );
    }

    // Only show manager status if there are completed tasks
    if (!hasCompletedTasks) {
        return '';
    }

    // Get manager status from project data
    const managerStatus = project.manager_status;

    if (!managerStatus) {
        // No manager review yet - don't show anything
        return '';
    }

    // Show current manager status
    const statusConfig = getManagerStatusConfig(managerStatus);

    return `
        <div class="manager-status-label ${statusConfig.class}">
            <i class="${statusConfig.icon} me-1"></i>
            <span>${statusConfig.text}</span>
        </div>
    `;
}

// Get role-based card class for styling variations
function getRoleBasedCardClass(project) {
    const userId = <?= $user['id'] ?>;
    const userRole = '<?= $user['roles'] ?>';

    const isAssignee = project.assigned_to == userId;
    const isAdmin = userRole === 'admin';
    const isManager = userRole === 'manager';

    if (isAssignee) {
        return 'card-assignee';
    } else if (isAdmin) {
        return 'card-admin';
    } else if (isManager) {
        return 'card-manager';
    }

    return 'card-viewer';
}

// Get dependency status indicator
function getDependencyStatusIndicator(project) {
    // Check if project has dependency information
    if (!project.depends_on && !project.is_locked) {
        return '';
    }

    let indicator = '';

    // Show lock status
    if (project.is_locked) {
        indicator += `
            <span class="dependency-indicator locked" title="Task is locked - waiting for dependency">
                <i class="fas fa-lock"></i>
            </span>
        `;
    }

    // Show dependency information
    if (project.depends_on) {
        indicator += `
            <span class="dependency-indicator depends-on" title="Depends on: ${project.dependency_task_name || 'Previous task'}">
                <i class="fas fa-link"></i>
            </span>
        `;
    }

    return indicator;
}

// Get task dependencies display for project cards
function getTaskDependenciesDisplay(project) {
    const userId = <?= $user['id'] ?>;
    const userRole = '<?= $user['roles'] ?>';
    const isAdmin = userRole === 'admin';
    const isManager = userRole === 'manager';

    // Only show dependencies for admin/manager (removed for users)
    if (!isAdmin && !isManager) {
        return '';
    }

    // Check if project has tasks with dependencies
    let hasDependencies = false;
    let dependencyInfo = '';

    if (project.tasks && Array.isArray(project.tasks)) {
        const tasksWithDependencies = project.tasks.filter(task =>
            task.depends_on || task.is_locked
        );

        if (tasksWithDependencies.length > 0) {
            hasDependencies = true;
            dependencyInfo = `
                <div class="task-dependencies">
                    <div class="dependency-header">
                        <i class="fas fa-sitemap me-1"></i>
                        <span>Task Dependencies</span>
                    </div>
                    <div class="dependency-chains">
                        ${tasksWithDependencies.map(task => `
                            <div class="dependency-chain">
                                <div class="dependency-task ${task.status === 'completed' ? 'completed' : (task.is_locked ? 'locked' : '')}">
                                    ${task.name}
                                    ${task.is_locked ? '<i class="fas fa-lock ms-1" title="Locked"></i>' : ''}
                                </div>
                                ${task.dependency_task_name ? `
                                    <i class="fas fa-arrow-left dependency-arrow"></i>
                                    <div class="dependency-task ${task.dependency_status === 'completed' ? 'completed' : ''}">
                                        ${task.dependency_task_name}
                                    </div>
                                ` : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
    } else if (project.depends_on || project.is_locked) {
        // Single task project with dependency
        hasDependencies = true;
        dependencyInfo = `
            <div class="task-dependencies">
                <div class="dependency-header">
                    <i class="fas fa-link me-1"></i>
                    <span>Task Dependency</span>
                </div>
                <div class="dependency-chain">
                    <div class="dependency-task ${project.status === 'completed' ? 'completed' : (project.is_locked ? 'locked' : '')}">
                        ${project.task_name || project.project_name}
                        ${project.is_locked ? '<i class="fas fa-lock ms-1" title="Locked"></i>' : ''}
                    </div>
                    ${project.dependency_task_name ? `
                        <i class="fas fa-arrow-left dependency-arrow"></i>
                        <div class="dependency-task ${project.dependency_status === 'completed' ? 'completed' : ''}">
                            ${project.dependency_task_name}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    return hasDependencies ? dependencyInfo : '';
}

// Get revision number display for task
function getRevisionNumber(task) {
    const currentRevision = task.current_revision || 0;
    return ` <span class="revision-number">(R${currentRevision})</span>`;
}

// Get task action button for admin/manager
function getTaskActionButton(task, projectId, isAdmin, project = null) {
    const userRole = '<?= $user['roles'] ?>';
    const isManager = isAdmin || userRole === 'manager';

    if (!isManager) return '';

    const isCompleted = task.status === 'completed' || task.task_status === 'completed';
    const taskId = task.id || task.task_id || 'unknown';

    // Only show buttons for completed tasks
    if (!isCompleted) return '';

    // Check if task has been sent for review (task-level manager status)
    const taskManagerStatus = task.task_manager_status || 'not_reviewed';
    const isSubmitted = taskManagerStatus === 'sent_for_review';

    // Debug logging for task ID resolution
    console.log('🔍 Task ID Debug:', {
        taskName: task.name || task.task_name,
        taskId: taskId,
        taskIdField: task.id,
        taskTaskIdField: task.task_id,
        projectId: projectId,
        isCompleted: isCompleted,
        isSubmitted: isSubmitted
    });

    // Create 3-dot menu for completed tasks
    const menuId = `taskMenu_${taskId}`;

    if (isCompleted && !isSubmitted) {
        // Show send for review option for completed but not submitted tasks
        return `<div class="dropdown">
            <button class="btn btn-xs btn-outline-secondary task-menu-btn" type="button"
                    data-bs-toggle="dropdown" aria-expanded="false" title="Task Actions">
                <i class="fas fa-ellipsis-h"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item" href="#" onclick="updateTaskManagerStatus(${taskId}, 'sent_for_review'); return false;">
                    <i class="fas fa-paper-plane text-primary me-2"></i>Send for Review
                </a></li>
            </ul>
        </div>`;
    } else if (isSubmitted) {
        // Show revision options for submitted tasks
        return `<div class="dropdown">
            <button class="btn btn-xs btn-outline-secondary task-menu-btn" type="button"
                    data-bs-toggle="dropdown" aria-expanded="false" title="Task Actions">
                <i class="fas fa-ellipsis-h"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item" href="#" onclick="updateTaskManagerStatus(${taskId}, 'need_revision'); return false;">
                    <i class="fas fa-undo text-warning me-2"></i>Need Revision
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="updateTaskManagerStatus(${taskId}, 'client_accepted'); return false;">
                    <i class="fas fa-check text-success me-2"></i>Client Accepted
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="updateTaskManagerStatus(${taskId}, 'rejected'); return false;">
                    <i class="fas fa-times text-danger me-2"></i>Client Rejected
                </a></li>
            </ul>
        </div>`;
    }

    return '';
}

// Get manager status configuration
function getManagerStatusConfig(status) {
    const configs = {
        'sent_for_review': {
            text: 'Sent for Review',
            icon: 'fas fa-eye',
            class: 'sent-for-review',
            colorClass: 'text-info'
        },
        'need_revision': {
            text: 'Need Revision',
            icon: 'fas fa-edit',
            class: 'need-revision',
            colorClass: 'text-warning'
        },
        'client_accepted': {
            text: 'Client Accepted',
            icon: 'fas fa-thumbs-up',
            class: 'client-accepted',
            colorClass: 'text-success'
        },
        'rejected': {
            text: 'Rejected',
            icon: 'fas fa-times-circle',
            class: 'rejected',
            colorClass: 'text-danger'
        }
    };

    return configs[status] || {
        text: status ? status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'Unknown',
        icon: 'fas fa-question-circle',
        class: 'unknown-status',
        colorClass: 'text-muted'
    };
}

// Original manager status configuration function
function getManagerStatusConfigOld(status) {
    const configs = {
        'sent_for_review': {
            class: 'sent-for-review',
            icon: 'fas fa-paper-plane',
            text: 'Sent for Review'
        },
        'revision_needed': {
            class: 'revision-needed',
            icon: 'fas fa-edit',
            text: 'Revision Needed'
        },
        'client_accepted': {
            class: 'client-accepted',
            icon: 'fas fa-check-circle',
            text: 'Client Accepted'
        },
        'rejected': {
            class: 'rejected',
            icon: 'fas fa-times-circle',
            text: 'Rejected'
        }
    };

    return configs[status] || {
        class: 'unknown-status',
        icon: 'fas fa-question-circle',
        text: status ? status.replace('_', ' ').toUpperCase() : 'Unknown Status'
    };
}

// Get target date status
function getTargetDateStatus(targetDate) {
    const target = new Date(targetDate);
    const today = new Date();
    const daysUntilTarget = Math.ceil((target - today) / (1000 * 60 * 60 * 24));

    if (daysUntilTarget < 0) {
        return `<span class="date-status overdue">${Math.abs(daysUntilTarget)} days overdue</span>`;
    } else if (daysUntilTarget === 0) {
        return '<span class="date-status today">Due today</span>';
    } else if (daysUntilTarget <= 7) {
        return `<span class="date-status urgent">${daysUntilTarget} days left</span>`;
    } else if (daysUntilTarget <= 30) {
        return `<span class="date-status normal">${daysUntilTarget} days left</span>`;
    }
    return `<span class="date-status normal">${daysUntilTarget} days left</span>`;
}

// Get project duration
function getProjectDuration(project) {
    const startDate = new Date(project.start_date || project.created_at);
    const today = new Date();
    const diffTime = Math.abs(today - startDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '1 day';
    if (diffDays < 30) return `${diffDays} days`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months`;
    return `${Math.floor(diffDays / 365)} years`;
}

// Get latest update display
function getLatestUpdateDisplay(project) {
    if (!project.last_update_notes) {
        return ''; // Return empty string instead of "no updates" message
    }

    return `
        <div class="latest-update-modern">
            <div class="update-header">
                <i class="fas fa-bell"></i>
                <span>Latest Update</span>
                <span class="update-time">${formatRelativeTime(project.last_updated || project.updated_at)}</span>
            </div>
            <div class="update-text">${project.last_update_notes.substring(0, 80)}${project.last_update_notes.length > 80 ? '...' : ''}</div>
        </div>
    `;
}

// Get real-time duration display for assignees
function getRealTimeDurationDisplay(project) {
    const currentUserId = <?= $user['id'] ?>;
    const isAssignee = project.assigned_to == currentUserId;

    // Only show for assignees when project is in progress
    if (!isAssignee || project.status !== 'in_progress') {
        return '';
    }

    // Get the start time from the project data
    const startTime = project.current_session_start || new Date().toISOString();

    return `
        <div class="real-time-duration-display" data-project-id="${project.id}" data-start-time="${startTime}">
            <div class="duration-header">
                <i class="fas fa-stopwatch text-success"></i>
                <span class="duration-label">Working Time:</span>
            </div>
            <div class="duration-content">
                <span class="live-duration" data-start="${startTime}">0m 0s</span>
                <span class="total-time-info" id="total-time-${project.id}">
                    ${getTotalWorkTimeDisplay(project)}
                </span>
            </div>
        </div>
    `;
}

// Get total work time display
function getTotalWorkTimeDisplay(project) {
    // This will be calculated from timeline data
    // For now, show placeholder
    return '<small>Total: calculating...</small>';
}

// Get days left display
function getDaysLeftDisplay(targetDate) {
    const target = new Date(targetDate);
    const today = new Date();
    const daysLeft = Math.ceil((target - today) / (1000 * 60 * 60 * 24));

    if (daysLeft < 0) {
        return `${Math.abs(daysLeft)} days overdue`;
    } else if (daysLeft === 0) {
        return 'Due today';
    } else if (daysLeft <= 7) {
        return `${daysLeft} days left`;
    } else {
        return `${daysLeft} days left`;
    }
}

// Get task running time display - tracks actual work sessions
function getTaskRunningTime(project) {
    // Check if task is currently running (in_progress status)
    const isRunning = project.status === 'in_progress' || project.task_status === 'in_progress';

    // Get total work time from timeline sessions
    let totalWorkTime = 0;
    let currentSessionTime = 0;
    let sessionCount = 0;

    // Parse timeline sessions if it's a JSON string
    let timelineSessions = [];
    if (project.timeline_sessions) {
        try {
            if (typeof project.timeline_sessions === 'string') {
                timelineSessions = JSON.parse(project.timeline_sessions);
            } else if (Array.isArray(project.timeline_sessions)) {
                timelineSessions = project.timeline_sessions;
            }
        } catch (e) {
            console.warn('Failed to parse timeline_sessions:', e);
            timelineSessions = [];
        }
    }

    // Calculate total work time from sessions
    if (timelineSessions && Array.isArray(timelineSessions)) {
        timelineSessions.forEach(session => {
            if (session.duration_seconds) {
                totalWorkTime += parseInt(session.duration_seconds);
                sessionCount++;
            }
        });
    }

    // If currently running, add current session time
    if (isRunning) {
        const currentSessionStart = project.current_session_start || project.in_progress_at || project.updated_at;
        if (currentSessionStart) {
            const start = new Date(currentSessionStart);
            const now = new Date();
            currentSessionTime = Math.floor((now - start) / 1000); // in seconds
        }
    }

    // Total time including current session
    const totalSeconds = totalWorkTime + currentSessionTime;

    if (totalSeconds === 0) {
        return isRunning ? `<span class="task-running-time">• Just started</span>` : '';
    }

    // Convert to hours and minutes
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);

    let timeDisplay = '';
    if (hours > 0) {
        timeDisplay = `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
        timeDisplay = `${minutes}m`;
    } else {
        timeDisplay = `${totalSeconds}s`;
    }

    // Add session count if multiple sessions
    let sessionInfo = '';
    if (sessionCount > 1) {
        sessionInfo = ` (${sessionCount} sessions)`;
    } else if (sessionCount === 1 && isRunning) {
        sessionInfo = ` (session ${sessionCount + 1})`;
    }

    const statusText = isRunning ? 'Working' : 'Total';
    return `<span class="task-running-time">• ${statusText} ${timeDisplay}${sessionInfo}</span>`;
}

// Get days left display with urgency-based colors for below project name
function getDaysLeftDisplayWithUrgency(targetDate, project = null) {
    let daysLeftDisplay = '';

    if (targetDate) {
        const target = new Date(targetDate);
        const today = new Date();
        const daysLeft = Math.ceil((target - today) / (1000 * 60 * 60 * 24));

        if (daysLeft < 0) {
            daysLeftDisplay = `<span class="days-left-urgency overdue">${Math.abs(daysLeft)} days overdue</span>`;
        } else if (daysLeft === 0) {
            daysLeftDisplay = '<span class="days-left-urgency today">Due today</span>';
        } else if (daysLeft <= 3) {
            daysLeftDisplay = `<span class="days-left-urgency critical">${daysLeft} days left</span>`;
        } else if (daysLeft <= 7) {
            daysLeftDisplay = `<span class="days-left-urgency urgent">${daysLeft} days left</span>`;
        } else if (daysLeft <= 30) {
            daysLeftDisplay = `<span class="days-left-urgency normal">${daysLeft} days left</span>`;
        } else {
            daysLeftDisplay = `<span class="days-left-urgency normal">${daysLeft} days left</span>`;
        }
    }

    // Add running time if project is provided and task is running
    const runningTime = project ? getTaskRunningTime(project) : '';

    return daysLeftDisplay + (runningTime ? ' ' + runningTime : '');
}

// Get compact action buttons with role-based visibility
function getCompactActionButtons(project) {
    const userId = <?= $user['id'] ?>;
    const userRole = '<?= $user['roles'] ?>';

    // Define user permissions
    const isAssignee = project.assigned_to == userId;
    const isCreator = project.created_by == userId;
    const isAdmin = userRole === 'admin';
    const isManager = userRole === 'manager';

    // Permission levels
    const canUpdateStatus = isAssignee || isCreator || isAdmin || isManager;
    const canEdit = isCreator || isAdmin;
    const canDelete = isCreator || isAdmin;
    const canReassign = isCreator || isAdmin;

    console.log('Role-based permissions for', project.project_name, ':', {
        isAssignee, isCreator, isAdmin, isManager,
        canUpdateStatus, canEdit, canDelete, canReassign
    });

    let buttons = '';

    // Status control buttons (for assignees, creators, and admins)
    if (canUpdateStatus) {
        if (project.status === 'planning' || project.status === 'not_started') {
            buttons += `
                <button class="status-action-btn play-btn"
                        onclick="initiateStatusChange(${project.id}, 'in_progress', this)"
                        title="Start Project - Begin active work">
                    <div class="btn-content">
                        <i class="fas fa-play"></i>
                        <span class="btn-text">Start</span>
                    </div>
                    <div class="btn-loading" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                </button>
            `;
        } else if (project.status === 'in_progress') {
            buttons += `
                <button class="status-action-btn hold-btn"
                        onclick="initiateStatusChange(${project.id}, 'on_hold', this)"
                        title="Hold Project - Pause active work">
                    <div class="btn-content">
                        <i class="fas fa-pause"></i>
                        <span class="btn-text">Hold</span>
                    </div>
                    <div class="btn-loading" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                </button>
                <button class="status-action-btn complete-btn"
                        onclick="initiateStatusChange(${project.id}, 'completed', this)"
                        title="Complete Project - Finish all work">
                    <div class="btn-content">
                        <i class="fas fa-check"></i>
                        <span class="btn-text">Complete</span>
                    </div>
                    <div class="btn-loading" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                </button>
            `;
        } else if (project.status === 'on_hold') {
            buttons += `
                <button class="status-action-btn resume-btn"
                        onclick="initiateStatusChange(${project.id}, 'in_progress', this)"
                        title="Resume Project - Continue active work">
                    <div class="btn-content">
                        <i class="fas fa-play"></i>
                        <span class="btn-text">Resume</span>
                    </div>
                    <div class="btn-loading" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                </button>
            `;
        } else if (project.status === 'review') {
            buttons += `
                <button class="status-action-btn complete-btn"
                        onclick="initiateStatusChange(${project.id}, 'completed', this)"
                        title="Complete Project - Approve and finish">
                    <div class="btn-content">
                        <i class="fas fa-check"></i>
                        <span class="btn-text">Complete</span>
                    </div>
                    <div class="btn-loading" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                </button>
                <button class="status-action-btn hold-btn"
                        onclick="initiateStatusChange(${project.id}, 'on_hold', this)"
                        title="Hold Project - Pause for revisions">
                    <div class="btn-content">
                        <i class="fas fa-pause"></i>
                        <span class="btn-text">Hold</span>
                    </div>
                    <div class="btn-loading" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                </button>
            `;
        }
    }

    return buttons;
}

// Get management buttons based on user role and permissions
function getManagementButtons(project) {
    const userId = <?= $user['id'] ?>;
    const userRole = '<?= $user['roles'] ?>';

    const isCreator = project.created_by == userId;
    const isAdmin = userRole === 'admin';
    const canEdit = isCreator || isAdmin;
    const canDelete = isCreator || isAdmin;
    const canReassign = isCreator || isAdmin;
    const canShare = userRole === 'admin' || userRole === 'manager';

    let buttons = '';

    // Edit project button (creator and admin only)
    if (canEdit) {
        buttons += `
            <button class="management-btn edit-btn" onclick="editProject(${project.id})" title="Edit Project">
                <i class="fas fa-edit"></i>
                Edit
            </button>
        `;
    }

    // Reassign project button (creator and admin only)
    if (canReassign) {
        buttons += `
            <button class="management-btn reassign-btn" onclick="reassignProject(${project.id})" title="Reassign Project">
                <i class="fas fa-user-edit"></i>
                Reassign
            </button>
        `;
    }

    // Share link button (admin and manager only)
    if (canShare) {
        buttons += `
            <button class="management-btn share-btn" onclick="generateShareLink(${project.id})" title="Generate Client Link">
                <i class="fas fa-share-alt"></i>
                Share
            </button>
        `;
    }

    // Delete button (creator and admin only)
    if (canDelete) {
        buttons += `
            <button class="management-btn delete-btn" onclick="showDeleteModal(${project.id}, '${project.project_name}')" title="Delete Project">
                <i class="fas fa-trash"></i>
                Delete
            </button>
        `;
    }

    return buttons;
}

// Get role-based action buttons (assignees only see play, pause, complete)
function getRoleBasedActionButtons(project) {
    const userId = <?= $user['id'] ?>;
    const userRole = '<?= $user['roles'] ?>';

    // Define user permissions
    const isAssignee = project.assigned_to == userId;
    const isCreator = project.created_by == userId;
    const isAdmin = userRole === 'admin';
    const isManager = userRole === 'manager';

    // Assignees, creators, admins, and managers can control status
    const canControlStatus = isAssignee || isCreator || isAdmin || isManager;

    let buttons = '';

    // Status control buttons (assignees only)
    if (canControlStatus) {
        if (project.status === 'planning' || project.status === 'not_started') {
            buttons += `
                <button class="action-btn-custom play-btn"
                        onclick="initiateStatusChange(${project.id}, 'in_progress', this)"
                        title="Start Project"
                        data-project-id="${project.id}"
                        data-status="in_progress">
                    <i class="fas fa-play"></i>
                    <span>Play</span>
                </button>
            `;
        } else if (project.status === 'in_progress') {
            buttons += `
                <button class="action-btn-custom pause-btn"
                        onclick="initiateStatusChange(${project.id}, 'on_hold', this)"
                        title="Pause Project"
                        data-project-id="${project.id}"
                        data-status="on_hold">
                    <i class="fas fa-pause"></i>
                    <span>Pause</span>
                </button>
                <button class="action-btn-custom complete-btn"
                        onclick="initiateStatusChange(${project.id}, 'completed', this)"
                        title="Complete Project"
                        data-project-id="${project.id}"
                        data-status="completed">
                    <i class="fas fa-check"></i>
                    <span>Complete</span>
                </button>
            `;
        } else if (project.status === 'on_hold') {
            buttons += `
                <button class="action-btn-custom resume-btn"
                        onclick="initiateStatusChange(${project.id}, 'in_progress', this)"
                        title="Resume Project"
                        data-project-id="${project.id}"
                        data-status="in_progress">
                    <i class="fas fa-play"></i>
                    <span>Resume</span>
                </button>
            `;
        } else if (project.status === 'completed') {
            buttons += `
                <div class="completed-badge-custom">
                    <i class="fas fa-check-circle"></i>
                    <span>Completed</span>
                </div>
            `;
        }
    } else {
        // Non-authorized users see read-only status
        buttons += `
            <div class="readonly-status-custom status-${project.status}">
                <i class="fas fa-${getStatusIconName(project.status)}"></i>
                <span>${formatStatusText(project.status)}</span>
            </div>
        `;
    }

    return buttons;
}

// Get assignee buttons (play/pause/complete with time tracking)
function getAssigneeButtons(project) {
    const userId = <?= $user['id'] ?>;
    const userRole = '<?= $user['roles'] ?>';

    // Convert both to numbers for comparison to avoid type issues
    const projectAssignedTo = parseInt(project.assigned_to);
    const taskAssignedTo = parseInt(project.task_assigned_to || project.assigned_to);
    const currentUserId = parseInt(userId);

    // User is assignee if they're assigned to the project OR the specific task
    const isAssignee = projectAssignedTo === currentUserId || taskAssignedTo === currentUserId;
    const isAdmin = userRole === 'admin';
    const isManager = userRole === 'manager';



    // FIXED LOGIC: Assignees ALWAYS see action buttons, even if they are admin
    if (!isAssignee) {
        // If admin but not assignee, show nothing (status display removed)
        if (isAdmin) {
            return '';
        }
        return '';
    }
    let buttons = '';

    // For task-based assignments, use task_status; for project assignments, use project status
    const statusToCheck = project.task_status || project.status;

    // For task-based assignments, use task_id; for project assignments, use project id
    const idToUse = project.task_id || project.id;

    // Check if project needs revision (special case)
    if (project.revision_count && project.revision_count > 0 && statusToCheck === 'active') {
        buttons = `
            <div class="assignee-buttons-container">
                <div class="revision-indicator">
                    <i class="fas fa-exclamation-triangle text-warning"></i>
                    <span class="text-warning">Revision Required (${project.revision_count})</span>
                </div>
                <button class="assignee-btn start-btn revision-btn" onclick="startRevision('${idToUse}', this)" title="Start Revision" data-project-id="${idToUse}" data-action="start-revision">
                    <i class="fas fa-edit"></i>
                    <span>Start Revision</span>
                </button>
            </div>
        `;
    } else if (statusToCheck === 'sent_for_review' || statusToCheck === 'review') {
        buttons = `
            <div class="assignee-buttons-container">
                <div class="waiting-client-indicator">
                    <i class="fas fa-clock text-info"></i>
                    <span class="text-info">Waiting for client response</span>
                </div>
            </div>
        `;
    } else if (statusToCheck === 'planning' || statusToCheck === 'not_started') {
        buttons = `
            <div class="assignee-buttons-container">
                <button class="assignee-btn start-btn" onclick="togglePlayPause('${idToUse}', 'play', this)" title="Start Task" data-project-id="${idToUse}" data-action="start">
                    <i class="fas fa-play"></i>
                    <span>Start</span>
                </button>
            </div>
        `;
    } else if (statusToCheck === 'in_progress') {
        buttons = `
            <div class="assignee-buttons-container">
                <button class="assignee-btn play-pause-btn" onclick="togglePlayPause('${idToUse}', 'pause', this)" title="Pause Task" data-project-id="${idToUse}" data-action="pause">
                    <i class="fas fa-pause"></i>
                    <span>Pause</span>
                </button>
                <button class="assignee-btn complete-btn" onclick="completeTask('${idToUse}', this)" title="Finish Task" data-project-id="${idToUse}" data-action="complete">
                    <i class="fas fa-check"></i>
                    <span>Finish</span>
                </button>
            </div>
        `;
    } else if (statusToCheck === 'on_hold') {
        buttons = `
            <div class="assignee-buttons-container">
                <button class="assignee-btn play-pause-btn" onclick="togglePlayPause('${idToUse}', 'resume', this)" title="Resume Task" data-project-id="${idToUse}" data-action="resume">
                    <i class="fas fa-play"></i>
                    <span>Resume</span>
                </button>
                <button class="assignee-btn complete-btn" onclick="completeTask('${idToUse}', this)" title="Finish Task" data-project-id="${idToUse}" data-action="complete">
                    <i class="fas fa-check"></i>
                    <span>Finish</span>
                </button>
            </div>
        `;
    } else if (statusToCheck === 'completed') {
        buttons = `
            <div class="assignee-buttons-container">
                <div class="status-completed-indicator">
                    <i class="fas fa-check-circle text-success"></i>
                    <span class="text-success">Completed</span>
                </div>
            </div>
        `;
    } else {
        // FALLBACK: If no buttons generated but user is assignee, show default start button
        buttons = `
            <div class="assignee-buttons-container">
                <button class="assignee-btn start-btn" onclick="togglePlayPause('${idToUse}', 'play', this)" title="Start Task" data-project-id="${idToUse}" data-action="start">
                    <i class="fas fa-play"></i>
                    <span>Start</span>
                </button>
            </div>
        `;
    }

    // CRITICAL: Ensure we always return buttons for assignees
    if (buttons.trim() === '' || buttons.length < 10) {
        buttons = `
            <div class="assignee-buttons-container">
                <button class="assignee-btn start-btn" onclick="togglePlayPause('${idToUse}', 'play', this)" title="Start Task" data-project-id="${idToUse}" data-action="start">
                    <i class="fas fa-play"></i>
                    <span>Start</span>
                </button>
            </div>
        `;
    }

    return buttons;
}

// Get admin status display with pulsing animation
// Admin status display function removed as requested

// Get assigner/admin dropdown for status updates
function getAssignerDropdown(project) {
    const userId = <?= $user['id'] ?>;
    const userRole = '<?= $user['roles'] ?>';

    const isCreator = project.created_by == userId;
    const isAdmin = userRole === 'admin';
    const isManager = userRole === 'manager';

    const canManageStatus = isCreator || isAdmin || isManager;

    if (!canManageStatus) {
        return ''; // Only assigners/admins see dropdown
    }

    // Check if project has any completed tasks
    let hasCompletedTasks = false;

    // Check if project itself is completed
    if (project.status === 'completed') {
        hasCompletedTasks = true;
    }

    // Check if project has any completed tasks (for multi-task projects)
    if (project.tasks && Array.isArray(project.tasks)) {
        hasCompletedTasks = project.tasks.some(task =>
            task.status === 'completed' || task.task_status === 'completed'
        );
    }

    // Remove Update Task Status button as requested
    // Show button if ANY task is completed
    if (hasCompletedTasks) {
        return ''; // Return empty string instead of button
    } else {
        const statusText = project.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
        const statusIcon = project.status === 'in_progress' ? '🔄' :
                          project.status === 'on_hold' ? '⏸️' :
                          project.status === 'active' ? '🔄' : '📋';

        const statusLabel = `
            <div class="status-label-container">
                <span class="status-label status-${project.status}">
                    ${statusIcon} ${statusText}
                </span>
            </div>
        `;
        return statusLabel;
    }
}

// Get latest update in single line with ellipsis
function getLatestUpdateSingleLine(project) {
    const latestUpdate = project.latest_update || 'No updates yet';
    const maxLength = 50;

    if (latestUpdate.length > maxLength) {
        return latestUpdate.substring(0, maxLength) + '...';
    }

    return latestUpdate;
}

// Get edit buttons (creators and admins only)
function getEditButtons(project) {
    const userId = <?= $user['id'] ?>;
    const userRole = '<?= $user['roles'] ?>';

    const isCreator = project.created_by == userId;
    const isAdmin = userRole === 'admin';

    // Only creators and admins can edit/delete
    const canEdit = isCreator || isAdmin;
    const canDelete = isCreator || isAdmin;

    let buttons = '';

    // Edit button (creators and admins)
    if (canEdit) {
        buttons += `
            <button class="edit-btn-modern" onclick="editProject(${project.id})" title="Edit Project">
                <i class="fas fa-edit"></i>
                <span>Edit</span>
            </button>
        `;
    }

    // Delete button (creators and admins)
    if (canDelete) {
        buttons += `
            <button class="delete-btn-modern" onclick="showDeleteModal(${project.id}, '${project.project_name}')" title="Delete Project">
                <i class="fas fa-trash"></i>
                <span>Delete</span>
            </button>
        `;
    }

    return buttons;
}

// Get status icon name for CSS classes
function getStatusIconName(status) {
    const icons = {
        'not_started': 'pause',
        'planning': 'clipboard-list',
        'in_progress': 'play',
        'on_hold': 'pause',
        'completed': 'check',
        'review': 'eye'
    };
    return icons[status] || 'circle';
}

// Format status text for display
function formatStatusText(status) {
    const statusTexts = {
        'not_started': 'Not Started',
        'planning': 'Planning',
        'in_progress': 'In Progress',
        'on_hold': 'On Hold',
        'completed': 'Completed',
        'review': 'Under Review'
    };
    return statusTexts[status] || status.replace('_', ' ');
}

// Edit project function
function editProject(projectId) {
    window.location.href = `/projects/edit/${projectId}`;
}

// Get compact latest update
function getCompactLatestUpdate(project) {
    if (!project.last_update_notes) {
        return ''; // Return empty string instead of "no updates" message
    }

    const timestamp = project.last_updated || project.updated_at;
    return `
        <div class="update-text">${project.last_update_notes.substring(0, 60)}${project.last_update_notes.length > 60 ? '...' : ''}</div>
        <div class="update-time" data-timestamp="${timestamp}">${formatRelativeTime(timestamp)}</div>
    `;
}

// Get share link button (admin/manager only)
function getShareLinkButton(project) {
    const canShare = <?= in_array($user['roles'], ['admin', 'manager']) ? 'true' : 'false' ?>;

    if (!canShare) return '';

    return `
        <button class="share-btn" onclick="generateShareLink(${project.id})" title="Generate Client Link">
            <i class="fas fa-share-alt"></i>
            Share
        </button>
    `;
}

// Update project status
function updateStatus(projectId, newStatus) {
    document.getElementById('updateProjectId').value = projectId;
    document.getElementById('newStatus').value = newStatus;
    document.getElementById('statusComment').value = '';

    // Show/hide payment fields based on status
    togglePaymentFields(newStatus);

    const modal = new bootstrap.Modal(document.getElementById('statusUpdateModal'));
    modal.show();
}

// Toggle payment fields visibility
function togglePaymentFields(status) {
    const paymentSection = document.getElementById('paymentStatusSection');
    const paymentNotesSection = document.getElementById('paymentNotesSection');

    if (status === 'completed') {
        paymentSection.style.display = 'block';
        paymentNotesSection.style.display = 'block';

        // Make payment status required
        document.getElementById('paymentStatus').required = true;
    } else {
        paymentSection.style.display = 'none';
        paymentNotesSection.style.display = 'none';

        // Remove payment status requirement
        document.getElementById('paymentStatus').required = false;
    }
}

// Listen for status changes in the modal
document.addEventListener('DOMContentLoaded', function() {
    const statusSelect = document.getElementById('newStatus');
    if (statusSelect) {
        statusSelect.addEventListener('change', function() {
            togglePaymentFields(this.value);
        });
    }
});

// Submit status update with file upload support
function submitStatusUpdate(event) {
    event.preventDefault();

    const form = document.getElementById('statusUpdateForm');
    const formData = new FormData(form);

    // Get project ID from hidden input
    const hiddenInput = document.getElementById('updateProjectId');
    const projectId = hiddenInput ? hiddenInput.value : null;

    console.log('🔧 Project ID from hidden input:', projectId);
    console.log('🔧 Hidden input element:', hiddenInput);

    if (!projectId || projectId === 'undefined' || projectId === '' || projectId === 'null') {
        console.error('❌ Project ID is missing or invalid!');
        showAlert('error', 'Project ID is missing. Please refresh and try again.');
        return;
    }

    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
    submitBtn.disabled = true;

    // Determine the correct endpoint based on action type
    const actionType = formData.get('action_type');
    let updateUrl;

    if (actionType === 'assignee_update') {
        // For assignee updates, projectId is actually taskId
        updateUrl = `${window.location.origin}/projects/updateTaskStatus/${projectId}`;
        console.log('🔧 Task Update URL:', updateUrl);
    } else {
        // For manager updates, use project status endpoint
        updateUrl = `${window.location.origin}/projects/updateManagerStatus/${projectId}`;
        console.log('🔧 Manager Update URL:', updateUrl);
    }

    console.log('🔧 Action Type:', actionType, 'Update URL:', updateUrl);

    fetch(updateUrl, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('Status update response:', data);

        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('statusUpdateModal')).hide();

            // Invalidate cache immediately
            if (typeof invalidateCache === 'function') {
                invalidateCache('projects');
            }

            // Immediate UI update instead of full reload
            const newStatus = formData.get('status');
            if (newStatus) {
                updateProjectCardAfterStatusChange(projectId, newStatus);

                // Update task completion display in real-time
                updateTaskCompletionDisplay(projectId, newStatus);
            }
            loadStats(); // Only reload stats

            // Immediate full reload for consistency with cache invalidation
            if (typeof forceRefreshWithCacheInvalidation === 'function') {
                forceRefreshWithCacheInvalidation();
            } else {
                setTimeout(() => {
                    loadProjects(true); // Force refresh
                }, 100);
            }
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error updating status:', error);
        showAlert('error', 'Error updating project status');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// Helper function to update project card immediately after status change
function updateProjectCardAfterStatusChange(projectId, newStatus) {
    console.log('🔄 Updating project card immediately:', { projectId, newStatus });

    // Find the project card
    const projectCards = document.querySelectorAll('.project-card-final');
    let targetCard = null;

    projectCards.forEach(card => {
        const buttons = card.querySelectorAll('[data-project-id]');
        buttons.forEach(button => {
            if (button.dataset.projectId === String(projectId)) {
                targetCard = card;
            }
        });
    });

    if (targetCard) {
        // Update status class - only change card color when admin marks as complete
        targetCard.className = targetCard.className.replace(/status-\w+/g, '');

        // Find the project in allProjects to get complete data
        const projectData = allProjects ? allProjects.find(p => p.id == projectId) : null;
        if (projectData) {
            projectData.status = newStatus;
            targetCard.classList.add(getProjectCardStatusClass(projectData));
        } else {
            // Fallback to simple status class
            targetCard.classList.add(`status-${newStatus}`);
        }

        // Update live status icon
        const liveIcon = targetCard.querySelector('.live-indicator');
        if (liveIcon) {
            liveIcon.className = 'fas fa-circle live-indicator';
            if (newStatus === 'in_progress') {
                liveIcon.classList.add('live-running');
            } else if (newStatus === 'on_hold') {
                liveIcon.classList.add('live-hold');
            }
        }

        // Update action buttons
        const buttonsContainer = targetCard.querySelector('.assignee-buttons-container');
        if (buttonsContainer) {
            // Regenerate buttons based on new status
            const userId = <?= $user['id'] ?>;
            const isAssignee = targetCard.querySelector('[data-project-id]')?.dataset.projectId === String(projectId);

            if (isAssignee) {
                buttonsContainer.innerHTML = generateButtonsForStatus(projectId, newStatus);
            }
        }

        // Add enhanced visual feedback
        targetCard.style.transform = 'scale(1.02)';
        targetCard.style.transition = 'all 0.3s ease';
        targetCard.style.boxShadow = '0 8px 25px rgba(0,123,255,0.3)';

        setTimeout(() => {
            targetCard.style.transform = 'scale(1)';
            targetCard.style.boxShadow = '';
        }, 300);

        // Update the project in allProjects array for consistency
        if (allProjects && Array.isArray(allProjects)) {
            const projectIndex = allProjects.findIndex(p => p.id == projectId);
            if (projectIndex !== -1) {
                allProjects[projectIndex].status = newStatus;
                console.log('✅ Updated project in allProjects array');
            }
        }

        console.log('✅ Project card updated immediately');
    } else {
        console.log('⚠️ Project card not found for immediate update');
        // Force reload if card not found
        setTimeout(() => {
            loadProjects();
        }, 100);
    }
}

// Helper function to generate buttons for a specific status
function generateButtonsForStatus(projectId, status) {
    if (status === 'planning' || status === 'not_started') {
        return `<button class="assignee-btn start-btn" onclick="togglePlayPause('${projectId}', 'play', this)" title="Start Task" data-project-id="${projectId}" data-action="start">
                    <i class="fas fa-play"></i>
                    <span>Start</span>
                </button>`;
    } else if (status === 'in_progress') {
        return `<button class="assignee-btn play-pause-btn" onclick="togglePlayPause('${projectId}', 'pause', this)" title="Pause Task" data-project-id="${projectId}" data-action="pause">
                    <i class="fas fa-pause"></i>
                    <span>Pause</span>
                </button>
                <button class="assignee-btn complete-btn" onclick="completeTask('${projectId}', this)" title="Finish Task" data-project-id="${projectId}" data-action="complete">
                    <i class="fas fa-check"></i>
                    <span>Finish</span>
                </button>`;
    } else if (status === 'on_hold') {
        return `<button class="assignee-btn play-pause-btn" onclick="togglePlayPause('${projectId}', 'resume', this)" title="Resume Task" data-project-id="${projectId}" data-action="resume">
                    <i class="fas fa-play"></i>
                    <span>Resume</span>
                </button>
                <button class="assignee-btn complete-btn" onclick="completeTask('${projectId}', this)" title="Finish Task" data-project-id="${projectId}" data-action="complete">
                    <i class="fas fa-check"></i>
                    <span>Finish</span>
                </button>`;
    } else if (status === 'completed') {
        return `<div class="status-completed-indicator">
                    <i class="fas fa-check-circle text-success"></i>
                    <span class="text-success">Completed</span>
                </div>`;
    }
    return '';
}

// Legacy function for backward compatibility
function updateProjectStatus() {
    const form = document.getElementById('statusUpdateForm');
    const event = new Event('submit');
    submitStatusUpdate(event);
}

// Old createProject function removed - using page-based approach

// Bulk import projects
function bulkImportProjects() {
    const formData = new FormData(document.getElementById('bulkImportForm'));

    fetch('/projects/bulkImport', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('bulkImportModal')).hide();
            document.getElementById('bulkImportForm').reset();
            loadProjects();
            loadStats();
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error importing projects:', error);
        showAlert('error', 'Error importing projects');
    });
}

// View project details - redirect to project view page
function viewProject(projectId) {
    window.location.href = `/projects/view/${projectId}`;
}

// Load project details
function loadProjectDetails(projectId) {
    fetch(`/projects/getProject/${projectId}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            populateProjectDetails(data.project);
        } else {
            showAlert('error', data.message || 'Error loading project details');
        }
    })
    .catch(error => {
        console.error('Error loading project details:', error);
        showAlert('error', 'Error loading project details');
    });
}

// Populate project details in modal
function populateProjectDetails(project) {
    document.getElementById('projectDetailsTitle').textContent = project.project_name;
    document.getElementById('projectDetailsSubtitle').textContent = `${project.client_name} • ${project.location}`;
    document.getElementById('detailProjectId').textContent = project.project_id || 'N/A';
    document.getElementById('detailProjectStatus').textContent = project.status.replace('_', ' ');
    document.getElementById('detailProjectStatus').className = `status-badge status-${project.status}`;
    document.getElementById('detailProjectName').textContent = project.project_name;
    document.getElementById('detailClientName').textContent = project.client_name;
    document.getElementById('detailLocation').textContent = project.location;
    document.getElementById('detailDescription').textContent = project.description || 'No description provided';
    document.getElementById('detailStartDate').textContent = formatDate(project.start_date);
    document.getElementById('detailTargetDate').textContent = formatDate(project.target_completion);
    document.getElementById('detailAssignedTo').textContent = project.assigned_username || 'Not assigned';
    document.getElementById('detailCreatedBy').textContent = project.created_by_username || 'Unknown';
    document.getElementById('detailCreatedDate').textContent = formatDate(project.created_at);

    // Update progress circle
    updateProgressCircle(project.progress_percentage || 0);

    // Store project ID for status updates
    document.getElementById('updateProjectStatusBtn').onclick = () => updateStatus(project.id, project.status);
}

// Update progress circle
function updateProgressCircle(percentage) {
    const circle = document.getElementById('detailProgressCircle');
    const text = circle.querySelector('.progress-text');

    text.textContent = `${percentage}%`;

    // Update conic gradient
    const degrees = (percentage / 100) * 360;
    circle.style.background = `conic-gradient(#0d6efd ${degrees}deg, #e9ecef ${degrees}deg)`;
}

// Load project tasks
function loadProjectTasks(projectId) {
    fetch(`/projects/getProjectTasks/${projectId}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayProjectTasks(data.tasks, data.stats);
        } else {
            showAlert('error', data.message || 'Error loading project tasks');
        }
    })
    .catch(error => {
        console.error('Error loading project tasks:', error);
        showAlert('error', 'Error loading project tasks');
    });
}

// Display project tasks
function displayProjectTasks(tasks, stats) {
    const container = document.getElementById('projectTasksList');
    const statsContainer = document.getElementById('taskStats');

    // Update task statistics
    statsContainer.innerHTML = `
        <span class="badge bg-secondary me-1">${stats.total_tasks} Total</span>
        <span class="badge bg-primary me-1">${stats.in_progress} In Progress</span>
        <span class="badge bg-success">${stats.completed} Completed</span>
    `;

    if (tasks.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                <h6 class="text-muted">No tasks found</h6>
                <p class="text-muted">Tasks will be created automatically when the project is set up.</p>
            </div>
        `;
        return;
    }

    container.innerHTML = tasks.map(task => `
        <div class="task-card task-${task.status}">
            <div class="task-priority ${task.priority}">${task.priority}</div>
            <div class="d-flex align-items-start">
                <div class="task-type-icon" style="background-color: ${task.task_type_color}">
                    <i class="${task.task_type_icon}"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="mb-0">${task.task_name}</h6>
                        <span class="status-badge status-${task.status}">${task.status.replace('_', ' ')}</span>
                    </div>
                    <p class="text-muted mb-2">${task.description || 'No description'}</p>
                    <div class="task-meta">
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i>
                            ${task.assigned_username}
                        </small>
                        ${task.due_date ? `
                            <small class="text-muted ms-3">
                                <i class="fas fa-calendar me-1"></i>
                                Due: ${formatDate(task.due_date)}
                            </small>
                        ` : ''}
                    </div>
                    ${task.depends_on_task_name ? `
                        <div class="task-dependency mt-2">
                            <i class="fas fa-link me-1"></i>
                            Depends on: ${task.depends_on_task_name}
                        </div>
                    ` : ''}
                    <div class="task-actions mt-3">
                        ${getTaskActionButtons(task)}
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// Get task action buttons
function getTaskActionButtons(task) {
    const canUpdate = task.assigned_to == <?= $user['id'] ?> || <?= ($user['roles'] === 'admin' || $user['roles'] === 'manager') ? 'true' : 'false' ?>;

    if (!canUpdate) {
        return '<small class="text-muted">View only</small>';
    }

    let buttons = '';

    if (task.status === 'not_started') {
        buttons += `
            <button class="btn btn-sm btn-primary me-2" onclick="updateTaskStatus(${task.id}, 'in_progress', '${task.task_name}', ${JSON.stringify(task).replace(/"/g, '&quot;')})">
                <i class="fas fa-play me-1"></i>
                Start
            </button>
        `;
    } else if (task.status === 'in_progress') {
        buttons += `
            <button class="btn btn-sm btn-warning me-2" onclick="updateTaskStatus(${task.id}, 'on_hold', '${task.task_name}', ${JSON.stringify(task).replace(/"/g, '&quot;')})">
                <i class="fas fa-pause me-1"></i>
                Hold
            </button>
            <button class="btn btn-sm btn-success me-2" onclick="updateTaskStatus(${task.id}, 'completed', '${task.task_name}', ${JSON.stringify(task).replace(/"/g, '&quot;')})">
                <i class="fas fa-check me-1"></i>
                Complete
            </button>
        `;
    } else if (task.status === 'on_hold') {
        buttons += `
            <button class="btn btn-sm btn-primary me-2" onclick="updateTaskStatus(${task.id}, 'in_progress', '${task.task_name}', ${JSON.stringify(task).replace(/"/g, '&quot;')})">
                <i class="fas fa-play me-1"></i>
                Resume
            </button>
        `;
    } else if (task.status === 'completed') {
        // Check if task has been sent for review and user is admin/manager
        const isManager = <?= ($user['roles'] === 'admin' || $user['roles'] === 'manager') ? 'true' : 'false' ?>;
        const taskManagerStatus = task.task_manager_status || 'not_reviewed';

        if (taskManagerStatus === 'sent_for_review' && isManager) {

            buttons += `
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button"
                            data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-clipboard-check me-1"></i>
                        Review Actions
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="#" onclick="updateTaskManagerStatus(${task.id}, 'need_revision')">
                                <i class="fas fa-edit text-warning me-2"></i>
                                Need Revision
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" onclick="updateTaskManagerStatus(${task.id}, 'client_accepted')">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                Client Accepted
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" onclick="updateTaskManagerStatus(${task.id}, 'rejected')">
                                <i class="fas fa-times-circle text-danger me-2"></i>
                                Rejected
                            </a>
                        </li>
                    </ul>
                </div>
            `;
        } else if (taskManagerStatus === 'not_reviewed' && isManager) {
            // Show send for review button for completed tasks not yet reviewed
            buttons += `
                <button class="btn btn-sm btn-success me-2" onclick="updateTaskManagerStatus(${task.id}, 'sent_for_review')">
                    <i class="fas fa-paper-plane me-1"></i>
                    Send for Review
                </button>
            `;
        }
    }





    buttons += `
        <button class="btn btn-sm btn-outline-secondary" onclick="updateTaskStatus(${task.id}, '${task.status}', '${task.task_name}', ${JSON.stringify(task).replace(/"/g, '&quot;')})">
            <i class="fas fa-edit me-1"></i>
            Edit
        </button>
    `;

    return buttons;
}

// Update task status
function updateTaskStatus(taskId, suggestedStatus, taskName, taskData = null) {
    document.getElementById('updateTaskId').value = taskId;
    document.getElementById('newTaskStatus').value = suggestedStatus;
    document.getElementById('taskStatusName').textContent = taskName;
    document.getElementById('taskNotes').value = '';

    // Clear manager review action
    document.getElementById('managerReviewAction').value = '';

    // Check if this task is sent for review and user is manager
    const isManager = <?= ($user['roles'] === 'admin' || $user['roles'] === 'manager') ? 'true' : 'false' ?>;
    const taskManagerStatus = taskData?.task_manager_status || 'not_reviewed';

    // Show/hide manager review options
    const reviewOptions = document.getElementById('managerReviewOptions');
    const statusSelect = document.getElementById('newTaskStatus').parentElement;

    if (taskManagerStatus === 'sent_for_review' && isManager) {
        reviewOptions.style.display = 'block';
        statusSelect.style.display = 'none';
    } else {
        reviewOptions.style.display = 'none';
        statusSelect.style.display = 'block';
    }

    const modal = new bootstrap.Modal(document.getElementById('taskStatusModal'));
    modal.show();
}

// Set manager review status
function setManagerReviewStatus(action) {
    document.getElementById('managerReviewAction').value = action;

    // Update button states
    const buttons = document.querySelectorAll('#managerReviewOptions button');
    buttons.forEach(btn => {
        btn.classList.remove('btn-warning', 'btn-success', 'btn-danger');
        btn.classList.add('btn-outline-warning', 'btn-outline-success', 'btn-outline-danger');
    });

    // Highlight selected button
    const selectedBtn = document.querySelector(`#managerReviewOptions button[onclick*="${action}"]`);
    if (selectedBtn) {
        selectedBtn.classList.remove('btn-outline-warning', 'btn-outline-success', 'btn-outline-danger');
        if (action === 'need_revision') {
            selectedBtn.classList.add('btn-warning');
        } else if (action === 'client_accepted') {
            selectedBtn.classList.add('btn-success');
        } else if (action === 'client_rejected') {
            selectedBtn.classList.add('btn-danger');
        }
    }
}

// Search projects
function searchProjects() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();

    if (searchTerm === '') {
        displayProjects(allProjects);
        return;
    }

    const filteredProjects = allProjects.filter(project =>
        project.project_name.toLowerCase().includes(searchTerm) ||
        project.client_name.toLowerCase().includes(searchTerm) ||
        project.location.toLowerCase().includes(searchTerm) ||
        (project.description && project.description.toLowerCase().includes(searchTerm))
    );

    displayProjects(filteredProjects);
}

// Reset filters
function resetFilters() {
    document.getElementById('projectFilter').value = 'assigned';
    document.getElementById('statusFilter').value = '';
    document.getElementById('searchInput').value = '';
    loadProjects();
}

// Highlight a specific project card
function highlightProject(projectId) {
    // Find the project card by project ID
    const projectCards = document.querySelectorAll('.project-card-final');
    let targetCard = null;

    projectCards.forEach(card => {
        const cardData = card.getAttribute('data-project-data');
        if (cardData) {
            try {
                const project = JSON.parse(cardData);
                if (project.id == projectId) {
                    targetCard = card;
                }
            } catch (e) {
                console.warn('Failed to parse project data:', e);
            }
        }
    });

    if (targetCard) {
        // Scroll to the card
        targetCard.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Add highlight effect
        targetCard.style.border = '3px solid #007bff';
        targetCard.style.boxShadow = '0 0 20px rgba(0, 123, 255, 0.3)';
        targetCard.style.transform = 'scale(1.02)';
        targetCard.style.transition = 'all 0.3s ease';

        // Remove highlight after 3 seconds
        setTimeout(() => {
            targetCard.style.border = '';
            targetCard.style.boxShadow = '';
            targetCard.style.transform = '';

            // Remove highlight parameter from URL
            const url = new URL(window.location);
            url.searchParams.delete('highlight');
            window.history.replaceState({}, '', url);
        }, 3000);

        console.log('✅ Project highlighted:', projectId);
    } else {
        console.warn('⚠️ Project card not found for highlighting:', projectId);
    }
}

// Update statistics display
function updateStatsDisplay(stats) {
    const userRole = '<?= $user['roles'] ?>';

    if (userRole === 'admin') {
        // Admin sees all project stats
        document.getElementById('totalProjects').textContent = stats.total_projects || 0;
        document.getElementById('inProgressProjects').textContent = stats.active_projects || 0;
        document.getElementById('onHoldProjects').textContent = stats.on_hold_projects || 0;
        document.getElementById('completedProjects').textContent = stats.completed_projects || 0;
    } else {
        // Assignees see their personal stats with efficiency
        document.getElementById('myTotalProjects').textContent = stats.my_total_projects || 0;
        document.getElementById('myActiveProjects').textContent = stats.my_active_projects || 0;
        document.getElementById('myCompletedProjects').textContent = stats.my_completed_projects || 0;

        // Calculate and display efficiency percentage
        const efficiency = stats.my_efficiency || 0;
        document.getElementById('myEfficiency').textContent = efficiency + '%';
    }
}

// Create a simple test project for debugging
function createTestProject() {
    const testProject = {
        id: 999,
        project_id: 'TEST-001',
        project_name: 'Test Project',
        client_name: 'Test Client',
        location: 'Test Location',
        status: 'not_started',
        assigned_to: <?= $user['id'] ?>,
        created_by: <?= $user['id'] ?>,
        target_completion: '2024-12-31',
        last_update_notes: 'Test project for debugging',
        task_name: 'Foundation Work',
        assigned_username: 'Test User'
    };

    console.log('🔧 Creating test project:', testProject);
    displayProjects([testProject]);
}

// Utility functions
function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString();
}

function formatNumber(number) {
    return new Intl.NumberFormat().format(number);
}

function formatRelativeTime(dateString) {
    if (!dateString) return 'N/A';

    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) {
        return 'Just now';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 604800) {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
        return date.toLocaleDateString();
    }
}

// Format exact date and time for timeline
function formatExactDateTime(dateString) {
    if (!dateString) return 'N/A';

    const date = new Date(dateString);
    const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
    };

    return date.toLocaleDateString('en-US', options);
}

// Show status update modal with comment requirement
function showStatusModal(projectId, newStatus) {
    const modal = document.getElementById('statusUpdateModal');
    const form = document.getElementById('statusUpdateForm');
    const statusText = document.getElementById('statusUpdateText');
    const commentField = document.getElementById('statusComment');
    const projectIdField = document.getElementById('updateProjectId');

    // Set form data
    form.dataset.projectId = projectId;
    form.dataset.newStatus = newStatus;
    if (projectIdField) {
        projectIdField.value = projectId;
    }

    // Update modal text
    const statusNames = {
        'not_started': 'Not Started',
        'planning': 'Planning',
        'in_progress': 'In Progress',
        'on_hold': 'On Hold',
        'completed': 'Completed',
        'review': 'Under Review'
    };

    statusText.textContent = statusNames[newStatus] || newStatus;
    commentField.value = '';
    commentField.focus();

    // Show payment section for completion
    const paymentSection = document.getElementById('paymentSection');
    if (paymentSection) {
        paymentSection.style.display = newStatus === 'completed' ? 'block' : 'none';
    }

    // Show modal
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

// Show delete confirmation modal (admin only)
function showDeleteModal(projectId, projectName) {
    const modal = document.getElementById('deleteProjectModal');
    const form = document.getElementById('deleteProjectForm');
    const projectNameSpan = document.getElementById('deleteProjectName');
    const deleteReasonField = document.getElementById('deleteReason');

    form.dataset.projectId = projectId;
    projectNameSpan.textContent = projectName;
    if (deleteReasonField) {
        deleteReasonField.value = '';
        deleteReasonField.focus();
    }

    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

// Timeline modal removed - now using direct project view link

// Generate share link for client access
function generateShareLink(projectId) {
    const modal = document.getElementById('shareLinkModal');
    const linkInput = document.getElementById('shareLink');
    const roleSelect = document.getElementById('shareRole');

    // Set project ID for later use
    modal.dataset.projectId = projectId;

    // Generate unique share link
    const shareToken = generateShareToken();
    const shareUrl = `${window.location.origin}/projects/client/${projectId}/${shareToken}`;

    linkInput.value = shareUrl;

    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

// Generate random share token
function generateShareToken() {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// Create timeline modal if it doesn't exist
function createTimelineModal() {
    const modalHtml = `
        <div class="modal fade" id="timelineModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-history me-2"></i>
                            Project Timeline
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="timelineContent"></div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

// Mock timeline function removed - using real API data

// Display modern compact timeline
function displayModernTimeline(timeline) {
    const content = document.getElementById('timelineContent');

    if (!timeline || timeline.length === 0) {
        content.innerHTML = `
            <div class="empty-timeline">
                <i class="fas fa-clock"></i>
                <h6>No Timeline Data</h6>
                <p class="text-muted">Timeline will appear as project progresses</p>
            </div>
        `;
        return;
    }

    const timelineHtml = timeline.map((item, index) => {
        // Parse metadata for detailed information
        let metadata = {};
        try {
            metadata = typeof item.metadata === 'string' ? JSON.parse(item.metadata) : (item.metadata || {});
        } catch (e) {
            metadata = {};
        }

        // Format detailed timeline entry
        const assigneeName = metadata.assignee_name || 'Unknown Assignee';
        const taskName = metadata.task_name || 'General Task';
        const assignerName = metadata.assigner_name || item.user;
        const workDuration = metadata.duration_formatted || null;

        return `
            <div class="modern-timeline-item" data-status="${item.status}">
                <div class="timeline-marker" style="background: ${item.color}">
                    <i class="fas fa-${getTimelineIcon(item.status)}"></i>
                </div>
                <div class="timeline-content">
                    <div class="timeline-header">
                        <h6 class="timeline-action">${formatTimelineAction(item.action)}</h6>
                        <span class="timeline-time" data-timestamp="${item.timestamp}">${formatRelativeTime(item.timestamp)}</span>
                    </div>
                    <div class="timeline-details">
                        <div class="timeline-task-info">
                            <i class="fas fa-tasks"></i>
                            <strong>Task:</strong> ${taskName}
                        </div>
                        <div class="timeline-assignee-info">
                            <i class="fas fa-user-tag"></i>
                            <strong>Assignee:</strong> ${assigneeName}
                        </div>
                        <div class="timeline-assigner-info">
                            <i class="fas fa-user-cog"></i>
                            <strong>Changed by:</strong> ${assignerName}
                        </div>
                    </div>
                    <div class="timeline-meta">
                        <span class="timeline-exact-time" data-timestamp="${item.timestamp}">
                            <i class="fas fa-calendar-alt"></i>
                            <strong>When:</strong> ${formatExactDateTime(item.timestamp)}
                        </span>
                        ${metadata.duration_formatted ? `
                            <span class="timeline-work-duration">
                                <i class="fas fa-stopwatch"></i>
                                <strong>Work Duration:</strong> ${metadata.duration_formatted}
                            </span>
                        ` : ''}
                        <span class="timeline-relative-time">
                            <i class="fas fa-clock"></i>
                            <strong>Time Ago:</strong> ${formatRelativeTime(item.timestamp)}
                        </span>
                    </div>
                    ${item.notes ? `<div class="timeline-notes">${item.notes}</div>` : ''}
                </div>
            </div>
        `;
    }).join('');

    content.innerHTML = `
        <div class="modern-timeline-container">
            ${timelineHtml}
        </div>
    `;
}

// Get timeline icon based on status
function getTimelineIcon(status) {
    const icons = {
        'not_started': 'circle',
        'planning': 'clipboard-list',
        'in_progress': 'play',
        'on_hold': 'pause',
        'completed': 'check',
        'review': 'eye'
    };
    return icons[status] || 'circle';
}

// Copy share link to clipboard
function copyShareLink() {
    const linkInput = document.getElementById('shareLink');
    linkInput.select();
    linkInput.setSelectionRange(0, 99999); // For mobile devices

    try {
        document.execCommand('copy');
        showAlert('success', 'Share link copied to clipboard!');
    } catch (err) {
        showAlert('error', 'Failed to copy link. Please copy manually.');
    }
}

// Save share link (generate and store)
function saveShareLink() {
    const projectId = document.getElementById('shareLinkModal').dataset.projectId;
    const role = document.getElementById('shareRole').value;
    const linkInput = document.getElementById('shareLink');

    // Here you would typically make an API call to save the share link
    // For now, we'll just update the link with the selected role
    const shareToken = generateShareToken();
    const shareUrl = `${window.location.origin}/projects/client/${projectId}/${shareToken}?role=${role}`;

    linkInput.value = shareUrl;
    showAlert('success', 'Share link generated successfully!');
}

// Duplicate function removed - using main submitStatusUpdate function

// Submit delete project
function submitDeleteProject(event) {
    event.preventDefault();

    const form = event.target;
    const projectId = form.dataset.projectId;
    const reason = document.getElementById('deleteReason').value;

    if (reason.length < 10) {
        showAlert('error', 'Deletion reason must be at least 10 characters long');
        return;
    }

    // Submit the delete request
    const formData = new FormData();
    formData.append('reason', reason);

    fetch(`/projects/delete/${projectId}`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Project deleted successfully!');
            bootstrap.Modal.getInstance(document.getElementById('deleteProjectModal')).hide();
            loadProjects(); // Reload projects
        } else {
            showAlert('error', data.message || 'Error deleting project');
        }
    })
    .catch(error => {
        console.error('Error deleting project:', error);
        showAlert('error', 'Error deleting project');
    });
}

// Form validation functions
function validateCurrentStep() {
    const currentStepElement = document.querySelector(`.form-step[data-step="${currentStep}"]`);
    const inputs = currentStepElement.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;

    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });

    // Additional step-specific validation
    if (currentStep === 1) {
        isValid = validateStep1() && isValid;
    } else if (currentStep === 2) {
        isValid = validateStep2() && isValid;
    } else if (currentStep === 3) {
        isValid = validateStep3() && isValid;
    }

    return isValid;
}

function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let message = '';

    // Clear previous validation
    field.classList.remove('is-invalid', 'is-valid');
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    if (feedback) feedback.textContent = '';

    // Required field validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        message = 'This field is required';
    }

    // Field-specific validation
    if (isValid && value) {
        switch (field.name) {
            case 'project_id':
                if (!/^[A-Z]{3}-\d{4}-\d{3}$/.test(value)) {
                    isValid = false;
                    message = 'Project ID must follow format: PRJ-YYYY-XXX';
                }
                break;
            case 'client_mobile':
                if (value && !/^[\+]?[0-9\s\-\(\)]{10,20}$/.test(value)) {
                    isValid = false;
                    message = 'Please enter a valid phone number';
                }
                break;
            case 'start_date':
                const startDate = new Date(value);
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                if (startDate < today) {
                    isValid = false;
                    message = 'Start date cannot be in the past';
                }
                break;
            case 'target_completion':
                const targetDate = new Date(value);
                const startDateValue = document.getElementById('startDate').value;
                if (startDateValue && targetDate <= new Date(startDateValue)) {
                    isValid = false;
                    message = 'Target completion must be after start date';
                }
                break;
        }
    }

    // Apply validation styling
    if (isValid && value) {
        field.classList.add('is-valid');
    } else if (!isValid) {
        field.classList.add('is-invalid');
        if (feedback) feedback.textContent = message;
    }

    return isValid;
}

function validateStep1() {
    // Check for duplicate project ID
    const projectId = document.getElementById('projectId').value;
    // This would typically check against the server, but for now we'll assume it's valid
    return true;
}

function validateStep2() {
    return true;
}

function validateStep3() {
    const assignedTo = document.getElementById('assignedTo').value;
    return assignedTo !== '';
}

function setupRealTimeValidation() {
    // Note: Create project form moved to separate page (/projects/create)
    // Real-time validation is handled on the create project page
}

function clearFormValidation() {
    // Note: Create project form moved to separate page (/projects/create)
    // Form validation clearing is handled on the create project page
}

// Calculate and display project duration
function calculateDuration() {
    const startDate = document.getElementById('startDate').value;
    const targetDate = document.getElementById('targetCompletion').value;
    const display = document.getElementById('durationDisplay');

    if (startDate && targetDate) {
        const start = new Date(startDate);
        const target = new Date(targetDate);
        const diffTime = target - start;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays > 0) {
            const weeks = Math.floor(diffDays / 7);
            const days = diffDays % 7;
            let durationText = '';

            if (weeks > 0) {
                durationText += `${weeks} week${weeks > 1 ? 's' : ''}`;
                if (days > 0) durationText += ` and ${days} day${days > 1 ? 's' : ''}`;
            } else {
                durationText = `${days} day${days > 1 ? 's' : ''}`;
            }

            display.textContent = `Project duration: ${durationText} (${diffDays} days total)`;
            display.className = 'text-success';
        } else {
            display.textContent = 'Target completion must be after start date';
            display.className = 'text-danger';
        }
    } else {
        display.textContent = 'Select dates to see duration';
        display.className = 'text-muted';
    }
}

// Update user preview card
function updateUserPreview() {
    const select = document.getElementById('assignedTo');
    const preview = document.getElementById('userPreview');

    if (select.value) {
        const option = select.selectedOptions[0];
        const username = option.dataset.username;
        const email = option.dataset.email;
        const roles = option.dataset.roles;

        preview.querySelector('.user-avatar-preview').textContent = username.charAt(0).toUpperCase();
        preview.querySelector('.card-title').textContent = username;
        preview.querySelector('.text-muted').textContent = `${email} • ${roles}`;
        preview.style.display = 'block';
    } else {
        preview.style.display = 'none';
    }
}

// Submit task status update
function submitTaskStatusUpdate() {
    const form = document.getElementById('taskStatusForm');
    const formData = new FormData(form);
    const taskId = formData.get('task_id');
    const managerAction = formData.get('manager_action');

    // If this is a manager review action, use the updateTaskManagerStatus endpoint
    if (managerAction) {
        const reviewFormData = new FormData();
        reviewFormData.append('status', managerAction);
        reviewFormData.append('notes', formData.get('notes') || `Manager action: ${managerAction}`);

        // Add CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                         document.querySelector('input[name="csrf_token"]')?.value;
        if (csrfToken) {
            reviewFormData.append('csrf_token', csrfToken);
        }

        fetch(`/projects/updateTaskManagerStatus/${taskId}`, {
            method: 'POST',
            body: reviewFormData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                bootstrap.Modal.getInstance(document.getElementById('taskStatusModal')).hide();
                loadProjects();
            } else {
                showAlert('error', data.message || 'Failed to update task status');
            }
        })
        .catch(error => {
            console.error('Error updating task manager status:', error);
            showAlert('error', 'Error updating task status');
        });
        return;
    }

    // Regular task status update
    fetch(`/projects/updateTaskStatus/${taskId}`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('taskStatusModal')).hide();

            // Reload the main projects list to show updated task completion status
            loadProjects();

            // Also reload tasks in modal if open
            const projectId = document.getElementById('updateProjectStatusBtn')?.onclick?.toString().match(/\d+/);
            if (projectId) {
                loadProjectTasks(projectId[0]);
            }
        } else {
            showAlert('error', data.message || 'Failed to update task status');
        }
    })
    .catch(error => {
        console.error('Error updating task status:', error);
        showAlert('error', 'Error updating task status');
    });
}

// Load project timeline
function loadProjectTimeline(projectId) {
    const container = document.getElementById('projectTimeline');

    // Show loading
    container.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading timeline...</span>
            </div>
        </div>
    `;

    fetch(`/projects/getProjectTimeline/${projectId}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayProjectTimeline(data.timeline);
        } else {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                    <h6 class="text-muted">Error loading timeline</h6>
                    <p class="text-muted">${data.message}</p>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error loading timeline:', error);
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-exclamation-triangle fa-2x text-danger mb-3"></i>
                <h6 class="text-muted">Network Error</h6>
                <p class="text-muted">Failed to load project timeline</p>
            </div>
        `;
    });
}

// Display project timeline
function displayProjectTimeline(timeline) {
    const container = document.getElementById('projectTimeline');

    if (timeline.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                <h6 class="text-muted">No timeline data</h6>
                <p class="text-muted">Timeline will be populated as tasks progress.</p>
            </div>
        `;
        return;
    }

    container.innerHTML = `
        <div class="timeline">
            ${timeline.map(task => `
                <div class="timeline-item ${task.status}">
                    <div class="timeline-content">
                        <div class="timeline-header">
                            <h6 class="timeline-title">${task.task_name}</h6>
                            <span class="status-badge status-${task.status}">${task.status.replace('_', ' ')}</span>
                        </div>
                        <div class="timeline-meta">
                            <i class="${task.task_type_icon} me-2" style="color: ${task.task_type_color}"></i>
                            ${task.task_type_name} • Assigned to ${task.assigned_username}
                        </div>
                        ${task.description ? `
                            <div class="timeline-description">
                                ${task.description}
                            </div>
                        ` : ''}
                        <div class="timeline-actions">
                            ${task.start_date ? `
                                <small class="text-muted me-3">
                                    <i class="fas fa-play me-1"></i>
                                    Started: ${formatDate(task.start_date)}
                                </small>
                            ` : ''}
                            ${task.completed_date ? `
                                <small class="text-success">
                                    <i class="fas fa-check me-1"></i>
                                    Completed: ${formatDateTime(task.completed_date)}
                                </small>
                            ` : ''}
                            ${task.due_date && task.status !== 'completed' ? `
                                <small class="text-warning">
                                    <i class="fas fa-calendar me-1"></i>
                                    Due: ${formatDate(task.due_date)}
                                </small>
                            ` : ''}
                        </div>
                        ${task.notes ? `
                            <div class="mt-2 p-2 bg-light rounded">
                                <small class="text-muted">
                                    <i class="fas fa-sticky-note me-1"></i>
                                    ${task.notes}
                                </small>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

// Format date and time
function formatDateTime(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
}

// Delete project function
function deleteProject(projectId, projectName) {
    document.getElementById('deleteProjectName').textContent = projectName;
    document.getElementById('deleteProjectId').textContent = `ID: ${projectId}`;

    const modal = new bootstrap.Modal(document.getElementById('deleteProjectModal'));
    modal.show();

    // Set up confirmation handler
    document.getElementById('confirmDeleteProject').onclick = function() {
        confirmDeleteProject(projectId);
    };
}

// Confirm project deletion
function confirmDeleteProject(projectId) {
    const btn = document.getElementById('confirmDeleteProject');
    const spinner = btn.querySelector('.spinner-border');

    // Show loading state
    btn.disabled = true;
    spinner.style.display = 'inline-block';

    fetch(`/projects/delete/${projectId}`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('deleteProjectModal')).hide();
            loadProjects();
            loadStats();
        } else {
            showAlert('error', data.message || 'Failed to delete project');
        }
    })
    .catch(error => {
        console.error('Error deleting project:', error);
        showAlert('error', 'Error deleting project');
    })
    .finally(() => {
        // Reset button state
        btn.disabled = false;
        spinner.style.display = 'none';
    });
}

// Load client access management
function loadClientAccess(projectId) {
    const container = document.getElementById('clientAccessContent');

    // Show loading
    container.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    `;

    // Check if project already has client access
    fetch(`/projects/getProject/${projectId}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayClientAccessOptions(projectId, data.project);
        } else {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error loading client access information
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error loading client access:', error);
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Network error loading client access
            </div>
        `;
    });
}

// Display client access options
function displayClientAccessOptions(projectId, project) {
    const container = document.getElementById('clientAccessContent');
    const hasAccess = project.client_access_token && project.client_access_expires;
    const isExpired = hasAccess && new Date(project.client_access_expires) < new Date();

    if (hasAccess && !isExpired) {
        // Show existing access
        const clientUrl = `${window.location.origin}/client/project/${project.client_access_token}`;
        const qrUrl = `https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=${encodeURIComponent(clientUrl)}`;

        container.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <i class="fas fa-check-circle me-2"></i>
                            Active Client Access
                        </div>
                        <div class="card-body">
                            <p class="mb-3">Client access is currently active and will expire on:</p>
                            <p class="fw-bold text-success">${formatDate(project.client_access_expires)}</p>

                            <div class="mb-3">
                                <label class="form-label">Client Access URL:</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" value="${clientUrl}" readonly id="clientAccessUrl">
                                    <button class="btn btn-outline-secondary" onclick="copyToClipboard('clientAccessUrl')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <a href="/projects/downloadQR/${projectId}" class="btn btn-primary" target="_blank">
                                    <i class="fas fa-download me-2"></i>
                                    Download QR Code
                                </a>
                                <button class="btn btn-warning" onclick="revokeClientAccess(${projectId})">
                                    <i class="fas fa-ban me-2"></i>
                                    Revoke Access
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 text-center">
                    <h6 class="mb-3">QR Code</h6>
                    <img src="${qrUrl}" alt="QR Code" class="img-fluid border rounded" style="max-width: 250px;">
                    <p class="text-muted mt-2 small">Scan with phone camera to access project status</p>
                </div>
            </div>
        `;
    } else {
        // Show generate access option
        container.innerHTML = `
            <div class="text-center">
                <div class="card border-primary">
                    <div class="card-body">
                        <i class="fas fa-qrcode fa-3x text-primary mb-3"></i>
                        <h5>Generate Client Access</h5>
                        <p class="text-muted mb-4">
                            Create a secure QR code that allows your client to view project status
                            without needing to log in. Access will be valid for 30 days.
                        </p>

                        <button class="btn btn-primary btn-lg" onclick="generateClientAccess(${projectId})">
                            <i class="fas fa-plus me-2"></i>
                            Generate QR Code
                        </button>

                        ${isExpired ? `
                            <div class="alert alert-warning mt-3">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Previous access has expired
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    }
}

// Generate client access
function generateClientAccess(projectId) {
    const btn = event.target;
    const originalText = btn.innerHTML;

    btn.disabled = true;
    btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Generating...';

    fetch(`/projects/generateQR/${projectId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Client access generated successfully!');
            loadClientAccess(projectId); // Reload to show the new access
        } else {
            showAlert('error', data.message || 'Failed to generate client access');
        }
    })
    .catch(error => {
        console.error('Error generating client access:', error);
        showAlert('error', 'Error generating client access');
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
}

// Revoke client access
function revokeClientAccess(projectId) {
    if (!confirm('Are you sure you want to revoke client access? The current QR code will stop working.')) {
        return;
    }

    fetch(`/projects/revokeAccess/${projectId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            loadClientAccess(projectId); // Reload to show revoked state
        } else {
            showAlert('error', data.message || 'Failed to revoke client access');
        }
    })
    .catch(error => {
        console.error('Error revoking client access:', error);
        showAlert('error', 'Error revoking client access');
    });
}

// QR Code Generation Functions
let currentProjectId = null;

function generateQRCode() {
    const projectId = getCurrentProjectId();
    if (!projectId) {
        showAlert('error', 'Project ID not found');
        return;
    }

    const btn = document.getElementById('generateQRBtn');
    const originalText = btn.innerHTML;

    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating...';

    // Generate client access first
    fetch(`/projects/generateAccess/${projectId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const clientUrl = `${window.location.origin}/client/project/${data.access_token}`;

            // Update the access link
            document.getElementById('clientAccessLink').value = clientUrl;

            // Generate QR code using a QR code API
            generateQRCodeImage(clientUrl);

            // Show download button
            document.getElementById('downloadQRBtn').style.display = 'block';

            showAlert('success', 'QR Code generated successfully!');
        } else {
            showAlert('error', data.message || 'Failed to generate QR code');
        }
    })
    .catch(error => {
        console.error('Error generating QR code:', error);
        showAlert('error', 'Error generating QR code');
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
}

function generateQRCodeImage(url) {
    // Using QR Server API for QR code generation
    const qrApiUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(url)}`;

    const qrDisplay = document.getElementById('qrCodeDisplay');
    qrDisplay.innerHTML = `
        <img src="${qrApiUrl}" alt="QR Code" class="img-fluid" style="max-width: 200px; border: 1px solid #e2e8f0; border-radius: 8px;">
        <p class="mt-2 text-muted small">Scan with any QR code reader</p>
    `;

    // Store the QR code URL for download
    window.currentQRCodeUrl = qrApiUrl;
}

function downloadQRCode() {
    if (!window.currentQRCodeUrl) {
        showAlert('error', 'No QR code to download');
        return;
    }

    const link = document.createElement('a');
    link.href = window.currentQRCodeUrl;
    link.download = `project-${getCurrentProjectId()}-qrcode.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showAlert('success', 'QR Code downloaded!');
}

function copyAccessLink() {
    const linkInput = document.getElementById('clientAccessLink');
    if (!linkInput.value) {
        showAlert('error', 'No access link to copy. Generate QR code first.');
        return;
    }

    linkInput.select();
    linkInput.setSelectionRange(0, 99999);
    document.execCommand('copy');
    showAlert('success', 'Access link copied to clipboard!');
}

function getCurrentProjectId() {
    // Try to get project ID from the modal
    const updateBtn = document.getElementById('updateProjectStatusBtn');
    if (updateBtn && updateBtn.onclick) {
        const match = updateBtn.onclick.toString().match(/\d+/);
        if (match) {
            return match[0];
        }
    }

    // Fallback: try to get from URL or other sources
    const urlMatch = window.location.pathname.match(/\/projects\/(\d+)/);
    if (urlMatch) {
        return urlMatch[1];
    }

    return currentProjectId;
}

// Download sample Excel file
function downloadSampleFile() {
    // Create sample data
    const sampleData = [
        ['Project ID', 'Project Name', 'Client Name', 'Client Mobile', 'Location', 'Description', 'Start Date', 'Target Completion', 'Assigned User ID'],
        ['PRJ-2024-001', 'Residential Complex A', 'ABC Construction Ltd', '******-123-4567', 'Downtown District, City Center', 'Modern residential complex with 50 units', '2024-01-15', '2024-12-31', '1'],
        ['PRJ-2024-002', 'Office Building B', 'XYZ Developers', '******-987-6543', 'Business Park, North Side', 'Commercial office building with parking', '2024-02-01', '2024-11-30', '2'],
        ['PRJ-2024-003', 'Shopping Mall C', 'Mall Developers Inc', '******-456-7890', 'Shopping District, West End', 'Large shopping mall with entertainment zone', '2024-03-01', '2025-02-28', '1']
    ];

    // Convert to CSV format
    const csvContent = sampleData.map(row =>
        row.map(cell => `"${cell}"`).join(',')
    ).join('\n');

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'smartflo_projects_sample.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showAlert('success', 'Sample file downloaded! You can open it in Excel and modify as needed.');
}

// Copy to clipboard function
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999);
    document.execCommand('copy');
    showAlert('success', 'URL copied to clipboard!');
}

// Format relative time with real-time updates
function formatRelativeTime(dateString) {
    if (!dateString) return 'Unknown';

    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) {
        return 'Just now';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 604800) {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
        return formatDate(dateString);
    }
}

// Format timeline action with bordered status names
function formatTimelineAction(action) {
    if (!action) return '';

    // Define status patterns and their corresponding styles
    const statusPatterns = [
        { pattern: /\b(Not Started|not started)\b/gi, class: 'status-not-started' },
        { pattern: /\b(In Progress|in progress)\b/gi, class: 'status-in-progress' },
        { pattern: /\b(On Hold|on hold)\b/gi, class: 'status-on-hold' },
        { pattern: /\b(Completed|completed)\b/gi, class: 'status-completed' },
        { pattern: /\b(Sent for Review|sent for review)\b/gi, class: 'status-review' },
        { pattern: /\b(Need Corrections|need corrections)\b/gi, class: 'status-corrections' },
        { pattern: /\b(Client Accepted|client accepted)\b/gi, class: 'status-accepted' }
    ];

    let formattedAction = action;

    // Apply borders to status names
    statusPatterns.forEach(({ pattern, class: statusClass }) => {
        formattedAction = formattedAction.replace(pattern, (match) => {
            return `<span class="timeline-status-badge ${statusClass}">${match}</span>`;
        });
    });

    return formattedAction;
}



// Real-time updates functionality
function initializeRealTimeUpdates() {
    // Auto-refresh projects and stats every 10 seconds (faster refresh)
    setInterval(() => {
        refreshProjectData();
    }, 10000);

    // Check for updates every 3 seconds (much faster check)
    setInterval(() => {
        checkForUpdates();
    }, 3000);

    // Update timeline times every 10 seconds
    setInterval(() => {
        updateTimelineTimestamps();
    }, 10000);

    // Update real-time durations every second
    setInterval(() => {
        updateRealTimeDurations();
    }, 1000);

    // Initialize cache invalidation system
    initializeCacheInvalidation();

    console.log('Real-time updates initialized with enhanced cache management');
}

// Update timeline timestamps in real-time
function updateTimelineTimestamps() {
    // Update timeline times in modal if open
    const timelineElements = document.querySelectorAll('.timeline-time[data-timestamp]');
    timelineElements.forEach(element => {
        const timestamp = element.getAttribute('data-timestamp');
        if (timestamp) {
            element.textContent = formatRelativeTime(timestamp);
        }
    });

    // Update timeline relative time elements
    const relativeTimeElements = document.querySelectorAll('.timeline-relative-time');
    relativeTimeElements.forEach(element => {
        const metaContainer = element.closest('.timeline-meta');
        if (metaContainer) {
            const exactTimeElement = metaContainer.querySelector('.timeline-exact-time');
            if (exactTimeElement) {
                const timestamp = exactTimeElement.getAttribute('data-timestamp');
                if (timestamp) {
                    element.innerHTML = `<i class="fas fa-clock"></i> <strong>Time Ago:</strong> ${formatRelativeTime(timestamp)}`;
                }
            }
        }
    });

    // Update timeline duration elements (legacy support)
    const durationElements = document.querySelectorAll('.timeline-duration[data-timestamp]');
    durationElements.forEach(element => {
        const timestamp = element.getAttribute('data-timestamp');
        if (timestamp) {
            const duration = calculateDurationFromTimestamp(timestamp);
            element.innerHTML = `<i class="fas fa-clock"></i> ${duration}`;
        }
    });

    // Update project card latest update times
    const projectUpdateTimes = document.querySelectorAll('.update-time[data-timestamp]');
    projectUpdateTimes.forEach(element => {
        const timestamp = element.getAttribute('data-timestamp');
        if (timestamp) {
            element.textContent = formatRelativeTime(timestamp);
        }
    });
}

// Calculate duration from timestamp
function calculateDurationFromTimestamp(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) {
        return 'Just now';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes}m ago`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours}h ago`;
    } else {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days}d ago`;
    }
}

// Update real-time durations for active projects
function updateRealTimeDurations() {
    const durationElements = document.querySelectorAll('.live-duration[data-start]');

    durationElements.forEach(element => {
        const startTime = element.getAttribute('data-start');
        if (startTime && startTime !== 'null' && startTime !== '') {
            const start = new Date(startTime);
            const now = new Date();
            const diffInSeconds = Math.floor((now - start) / 1000);

            // Only update if the time is valid and positive
            if (!isNaN(start.getTime()) && diffInSeconds >= 0) {
                element.textContent = formatDurationFromSeconds(diffInSeconds);
            }
        }
    });
}

// Format duration from seconds to readable format
function formatDurationFromSeconds(seconds) {
    if (seconds < 0) return '0s';

    if (seconds < 60) {
        return `${seconds}s`;
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}m ${remainingSeconds}s`;
    } else if (seconds < 86400) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}h ${minutes}m`;
    } else {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        return `${days}d ${hours}h`;
    }
}

function refreshProjectData() {
    console.log('Auto-refreshing project data...');

    // Refresh projects list
    loadProjects();

    // Refresh statistics
    loadStats();

    // Show subtle notification
    showUpdateNotification();
}

function checkForUpdates() {
    // Lightweight check for any updates
    fetch('/projects/checkUpdates', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.hasUpdates) {
            // If there are updates, refresh the data
            refreshProjectData();
        }
    })
    .catch(error => {
        // Silently handle errors for background checks
        console.log('Background update check failed:', error);
    });
}

function showUpdateNotification() {
    // Removed top corner pulsing icon as requested
    console.log('Projects updated silently');
}

// Manual refresh function
function manualRefresh() {
    const refreshBtn = event.target;
    const originalContent = refreshBtn.innerHTML;

    refreshBtn.disabled = true;
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    // Clear all cache and force refresh
    if (typeof forceRefreshWithCacheInvalidation === 'function') {
        forceRefreshWithCacheInvalidation();
    } else {
        // Fallback to regular refresh
        refreshProjectData();
    }

    setTimeout(() => {
        refreshBtn.disabled = false;
        refreshBtn.innerHTML = originalContent;
        showAlert('success', 'Projects refreshed successfully with cache cleared!');
    }, 1000);
}

// Enhanced status change function with loading states
function initiateStatusChange(projectId, newStatus, buttonElement) {
    console.log('Status change initiated:', { projectId, newStatus, buttonElement });

    // Show loading state
    buttonElement.classList.add('loading');

    // Show status modal with enhanced UI
    showEnhancedStatusModal(projectId, newStatus, buttonElement);
}

// Show enhanced status modal
function showEnhancedStatusModal(projectId, newStatus, buttonElement) {
    const modal = document.getElementById('statusUpdateModal');
    if (!modal) {
        console.error('Status update modal not found');
        buttonElement.classList.remove('loading');
        return;
    }

    // Set form values
    document.getElementById('updateProjectId').value = projectId;
    document.getElementById('newStatus').value = newStatus;
    document.getElementById('statusComment').value = '';

    // Update modal title and content based on status
    updateModalContent(newStatus);

    // Show/hide payment fields
    togglePaymentFields(newStatus);

    // Store button reference for loading state management
    modal.dataset.triggerButton = buttonElement.outerHTML;

    // Show modal
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Remove loading state when modal is shown
    modal.addEventListener('shown.bs.modal', function() {
        buttonElement.classList.remove('loading');
    }, { once: true });

    // Remove loading state if modal is cancelled
    modal.addEventListener('hidden.bs.modal', function() {
        buttonElement.classList.remove('loading');
    }, { once: true });
}

// Update modal content based on status
function updateModalContent(newStatus) {
    const modalTitle = document.querySelector('#statusUpdateModal .modal-title');
    const commentLabel = document.querySelector('label[for="statusComment"]');
    const commentPlaceholder = document.getElementById('statusComment');

    const statusConfig = {
        'in_progress': {
            title: '▶️ Start Project',
            label: 'Reason for starting project',
            placeholder: 'Describe what work will begin and any initial preparations...'
        },
        'on_hold': {
            title: '⏸️ Hold Project',
            label: 'Reason for holding project',
            placeholder: 'Explain why the project needs to be paused and expected duration...'
        },
        'completed': {
            title: '✅ Complete Project',
            label: 'Project completion notes',
            placeholder: 'Describe the completed work and any final details...'
        },
        'review': {
            title: '🔍 Send for Review',
            label: 'Review request details',
            placeholder: 'Describe what needs to be reviewed and any specific requirements...'
        }
    };

    const config = statusConfig[newStatus] || {
        title: 'Update Project Status',
        label: 'Update notes',
        placeholder: 'Enter details about this status change...'
    };

    if (modalTitle) modalTitle.textContent = config.title;
    if (commentLabel) commentLabel.textContent = config.label + ' *';
    if (commentPlaceholder) commentPlaceholder.placeholder = config.placeholder;
}

// Edit project function (creator and admin only)
function editProject(projectId) {
    // Redirect to edit project page
    window.location.href = `/projects/edit/${projectId}`;
}

// Reassign project function (creator and admin only)
function reassignProject(projectId) {
    // Show reassign modal
    showReassignModal(projectId);
}

// Show reassign modal
function showReassignModal(projectId) {
    const modal = document.getElementById('reassignProjectModal');
    if (!modal) {
        // Create reassign modal if it doesn't exist
        createReassignModal();
        return showReassignModal(projectId);
    }

    const form = document.getElementById('reassignProjectForm');
    form.dataset.projectId = projectId;

    // Load available users for reassignment
    loadAvailableUsers();

    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

// Load available users for reassignment
function loadAvailableUsers() {
    fetch('/projects/getAvailableUsers', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            populateUserSelect(data.users);
        } else {
            showAlert('error', 'Failed to load available users');
        }
    })
    .catch(error => {
        console.error('Error loading users:', error);
        showAlert('error', 'Error loading available users');
    });
}

// Populate user select dropdown
function populateUserSelect(users) {
    const select = document.getElementById('reassignToUser');
    if (!select) return;

    select.innerHTML = '<option value="">Select user...</option>';
    users.forEach(user => {
        const option = document.createElement('option');
        option.value = user.id;
        option.textContent = `${user.first_name} ${user.last_name} (${user.roles})`;
        select.appendChild(option);
    });
}

// Submit reassignment
function submitReassignment(event) {
    event.preventDefault();

    const form = event.target;
    const projectId = form.dataset.projectId;
    const newUserId = document.getElementById('reassignToUser').value;
    const reason = document.getElementById('reassignReason').value.trim();
    const submitButton = form.querySelector('button[type="submit"]');

    if (!newUserId) {
        showAlert('error', 'Please select a user to reassign to');
        return;
    }

    if (reason.length < 10) {
        showAlert('error', 'Reassignment reason must be at least 10 characters long');
        return;
    }

    // Show loading state
    const originalButtonText = submitButton.innerHTML;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Reassigning...';
    submitButton.disabled = true;

    const formData = new FormData();
    formData.append('new_user_id', newUserId);
    formData.append('reason', reason);

    fetch(`/projects/reassign/${projectId}`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Project reassigned successfully!');
            bootstrap.Modal.getInstance(document.getElementById('reassignProjectModal')).hide();
            loadProjects(); // Reload projects
        } else {
            showAlert('error', data.message || 'Error reassigning project');
        }
    })
    .catch(error => {
        console.error('Error reassigning project:', error);
        showAlert('error', 'Error reassigning project');
    })
    .finally(() => {
        submitButton.innerHTML = originalButtonText;
        submitButton.disabled = false;
    });
}

// Create reassign modal dynamically
function createReassignModal() {
    const modalHtml = `
        <div class="modal fade" id="reassignProjectModal" tabindex="-1" aria-labelledby="reassignProjectModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="reassignProjectModalLabel">
                            <i class="fas fa-user-edit me-2"></i>
                            Reassign Project
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="reassignProjectForm" onsubmit="submitReassignment(event)">
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Reassigning this project will transfer ownership and responsibility to another user.
                            </div>

                            <div class="mb-3">
                                <label for="reassignToUser" class="form-label">Assign to User <span class="text-danger">*</span></label>
                                <select class="form-control" id="reassignToUser" required>
                                    <option value="">Loading users...</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="reassignReason" class="form-label">Reason for reassignment <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="reassignReason" rows="3"
                                        placeholder="Please explain why this project is being reassigned..."
                                        required minlength="10"></textarea>
                                <div class="form-text">Minimum 10 characters required</div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">Reassign Project</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Show project menu (for dots button)
function showProjectMenu(projectId) {
    const project = allProjects.find(p => p.id == projectId);
    if (!project) return;

    const userId = <?= $user['id'] ?>;
    const userRole = '<?= $user['roles'] ?>';

    // Define permissions
    const isCreator = project.created_by == userId;
    const isAdmin = userRole === 'admin';
    const isManager = userRole === 'manager';
    const canEdit = isCreator || isAdmin;
    const canDelete = isCreator || isAdmin;
    const canShare = isAdmin || isManager;

    // Create context menu
    const menu = document.createElement('div');
    menu.className = 'project-context-menu';
    menu.style.cssText = `
        position: fixed;
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0,0,0,0.12);
        z-index: 1000;
        min-width: 200px;
        padding: 8px 0;
        font-size: 14px;
    `;

    let menuItems = `
        <div class="context-menu-item" onclick="viewProject(${projectId})" style="padding: 12px 16px; cursor: pointer; display: flex; align-items: center; gap: 10px; transition: background 0.2s; color: #495057;">
            <i class="fas fa-eye" style="width: 16px;"></i> View Details
        </div>
    `;

    if (canEdit) {
        menuItems += `
            <div class="context-menu-item" onclick="editProject(${projectId})" style="padding: 12px 16px; cursor: pointer; display: flex; align-items: center; gap: 10px; transition: background 0.2s; color: #495057;">
                <i class="fas fa-edit" style="width: 16px;"></i> Edit Project
            </div>
        `;
    }

    if (canShare) {
        menuItems += `
            <div class="context-menu-item" onclick="generateShareLink(${projectId})" style="padding: 12px 16px; cursor: pointer; display: flex; align-items: center; gap: 10px; transition: background 0.2s; color: #495057;">
                <i class="fas fa-share-alt" style="width: 16px;"></i> Share Link
            </div>
        `;
    }

    if (canEdit || canDelete) {
        menuItems += `<hr style="margin: 8px 0; border: none; border-top: 1px solid #e9ecef;">`;
    }

    if (canDelete) {
        menuItems += `
            <div class="context-menu-item danger" onclick="showDeleteModal(${projectId}, '${project.project_name}')" style="padding: 12px 16px; cursor: pointer; display: flex; align-items: center; gap: 10px; transition: background 0.2s; color: #dc3545;">
                <i class="fas fa-trash" style="width: 16px;"></i> Delete Project
            </div>
        `;
    }

    menu.innerHTML = menuItems;

    // Add hover effects
    const items = menu.querySelectorAll('.context-menu-item');
    items.forEach(item => {
        item.addEventListener('mouseenter', () => {
            item.style.backgroundColor = item.classList.contains('danger') ? '#fee2e2' : '#f8f9fa';
        });
        item.addEventListener('mouseleave', () => {
            item.style.backgroundColor = 'transparent';
        });
    });

    // Position menu near the clicked button
    const event = window.event || {};
    menu.style.left = (event.clientX || 100) + 'px';
    menu.style.top = (event.clientY || 100) + 'px';

    document.body.appendChild(menu);

    // Remove menu when clicking outside
    setTimeout(() => {
        document.addEventListener('click', function removeMenu() {
            if (menu.parentNode) {
                menu.parentNode.removeChild(menu);
            }
            document.removeEventListener('click', removeMenu);
        });
    }, 100);
}

// Duplicate function removed - using main showTimeline function above

// View project details
function viewProject(projectId) {
    window.location.href = `/projects/view/${projectId}`;
}

// Generate share link for client
function generateShareLink(projectId) {
    console.log('Generate share link for project:', projectId);

    // Show loading state
    showAlert('info', 'Generating share link...');

    // Generate QR code and get share link
    fetch(`/projects/generateQR/${projectId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Create share link modal
            const shareUrl = `${window.location.origin}/client/project/${data.access_token}`;
            showShareLinkModal(shareUrl, data.qr_code_url, projectId);
        } else {
            showAlert('error', data.message || 'Failed to generate share link');
        }
    })
    .catch(error => {
        console.error('Error generating share link:', error);
        showAlert('error', 'Error generating share link');
    });
}

// Show share link modal with QR code and copy functionality
function showShareLinkModal(shareUrl, qrCodeUrl, projectId) {
    const modalHtml = `
        <div class="modal fade" id="shareLinkModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-share-alt me-2"></i>Share Project Link
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>QR Code</h6>
                                <div class="qr-code-container mb-3">
                                    <img src="${qrCodeUrl}" alt="QR Code" class="img-fluid" style="max-width: 200px;">
                                </div>
                                <p class="text-muted small">Scan with phone camera</p>
                            </div>
                            <div class="col-md-6">
                                <h6>Share Link</h6>
                                <div class="input-group mb-3">
                                    <input type="text" class="form-control" id="shareUrlInput" value="${shareUrl}" readonly>
                                    <button class="btn btn-outline-primary" type="button" onclick="copyShareLink()">
                                        <i class="fas fa-copy"></i> Copy
                                    </button>
                                </div>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-success" onclick="openShareLink('${shareUrl}')">
                                        <i class="fas fa-external-link-alt me-2"></i>Open Link
                                    </button>
                                    <button class="btn btn-warning" onclick="revokeShareLink(${projectId})">
                                        <i class="fas fa-ban me-2"></i>Revoke Access
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            Clients can use this link to view project status without logging in.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('shareLinkModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('shareLinkModal'));
    modal.show();

    // Clean up when modal is hidden
    document.getElementById('shareLinkModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// Copy share link to clipboard
function copyShareLink() {
    const input = document.getElementById('shareUrlInput');
    input.select();
    input.setSelectionRange(0, 99999); // For mobile devices

    try {
        document.execCommand('copy');
        showAlert('success', 'Share link copied to clipboard!');
    } catch (err) {
        // Fallback for modern browsers
        navigator.clipboard.writeText(input.value).then(() => {
            showAlert('success', 'Share link copied to clipboard!');
        }).catch(() => {
            showAlert('error', 'Failed to copy link');
        });
    }
}

// Open share link in new tab
function openShareLink(url) {
    window.open(url, '_blank');
}

// Revoke share link access
function revokeShareLink(projectId) {
    if (!confirm('Are you sure you want to revoke client access to this project?')) {
        return;
    }

    fetch(`/projects/revokeAccess/${projectId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Client access revoked successfully');
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('shareLinkModal'));
            modal.hide();
        } else {
            showAlert('error', data.message || 'Failed to revoke access');
        }
    })
    .catch(error => {
        console.error('Error revoking access:', error);
        showAlert('error', 'Error revoking access');
    });
}

// Single toggle function for play/pause with immediate feedback
function togglePlayPause(projectId, action, buttonElement) {
    console.log('🔧 togglePlayPause called with:', {
        projectId,
        action,
        projectIdType: typeof projectId,
        projectIdValue: projectId
    });

    // Ensure projectId is valid
    if (!projectId || projectId === 'undefined' || projectId === undefined) {
        console.error('❌ Invalid project ID in togglePlayPause:', projectId);
        showAlert('error', 'Invalid project ID. Please refresh the page and try again.');
        return;
    }

    // Provide immediate visual feedback
    if (buttonElement) {
        buttonElement.disabled = true;
        const originalHTML = buttonElement.innerHTML;
        buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

        // Store original state for potential rollback
        buttonElement.dataset.originalHtml = originalHTML;
    }

    const actionTime = new Date().toISOString();

    if (action === 'play') {
        showTaskCommentModal(projectId, 'in_progress', 'Task started', buttonElement);
    } else if (action === 'pause') {
        showTaskCommentModal(projectId, 'on_hold', 'Task paused', buttonElement);
    } else if (action === 'resume') {
        showTaskCommentModal(projectId, 'in_progress', 'Task resumed', buttonElement);
    }
}

function completeTask(projectId, buttonElement) {
    console.log('Completing task:', projectId);

    // Provide immediate visual feedback
    if (buttonElement) {
        buttonElement.disabled = true;
        const originalHTML = buttonElement.innerHTML;
        buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

        // Store original state for potential rollback
        buttonElement.dataset.originalHtml = originalHTML;
    }

    // Record completion time and update status
    const completeTime = new Date().toISOString();

    // Show comment modal for task completion
    showTaskCommentModal(projectId, 'completed', 'Task completed', buttonElement);
}

// Legacy functions for backward compatibility
function startTask(projectId, buttonElement) {
    togglePlayPause(projectId, 'play', buttonElement);
}

function pauseTask(projectId, buttonElement) {
    togglePlayPause(projectId, 'pause', buttonElement);
}

function resumeTask(projectId, buttonElement) {
    togglePlayPause(projectId, 'resume', buttonElement);
}

// Show task comment modal with time tracking
function showTaskCommentModal(projectId, newStatus, action, buttonElement) {
    console.log('🔧 showTaskCommentModal called:', { projectId, newStatus, action });

    const modal = document.getElementById('statusUpdateModal');
    const form = document.getElementById('statusUpdateForm');

    if (!modal || !form) {
        console.error('Modal or form not found');
        return;
    }

    // Update modal content
    const statusUpdateText = document.getElementById('statusUpdateText');
    const statusTimestamp = document.getElementById('statusTimestamp');
    const updateProjectId = document.getElementById('updateProjectId');
    const commentField = document.getElementById('statusComment');
    const commentBadge = document.getElementById('commentRequiredBadge');

    // Set current time
    const currentTime = new Date();
    const timeString = currentTime.toLocaleString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });

    // Update modal content
    if (statusUpdateText) statusUpdateText.textContent = action;
    if (statusTimestamp) statusTimestamp.textContent = timeString;

    // CRITICAL: Ensure project ID is set correctly
    if (updateProjectId) {
        updateProjectId.value = String(projectId);
        console.log('🔧 Set updateProjectId.value to:', updateProjectId.value);
    } else {
        console.error('❌ updateProjectId element not found! Creating it...');
        // Create the hidden input if it doesn't exist
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.id = 'updateProjectId';
        hiddenInput.name = 'project_id';
        hiddenInput.value = String(projectId);
        form.appendChild(hiddenInput);
        console.log('🔧 Created updateProjectId with value:', projectId);
    }

    // Set comment requirement based on action
    const isPauseAction = action.toLowerCase().includes('pause') || action.toLowerCase().includes('paused');
    const isStartAction = action.toLowerCase().includes('start') || action.toLowerCase().includes('play');

    if (commentField) {
        commentField.required = isPauseAction; // Only required for pause
        if (isPauseAction) {
            commentField.placeholder = 'Please explain why you are pausing this task...';
        } else if (isStartAction) {
            commentField.placeholder = 'Optional: Add any notes about starting this task...';
        } else {
            commentField.placeholder = 'What\'s the current progress? Any updates to share?';
        }
    }

    if (commentBadge) {
        commentBadge.textContent = isPauseAction ? 'Required' : 'Optional';
        commentBadge.className = isPauseAction ? 'required-badge' : 'optional-badge';
    }

    // Add hidden fields for status and time tracking
    const existingHiddenFields = form.querySelectorAll('input[type="hidden"]:not(#updateProjectId)');
    existingHiddenFields.forEach(field => field.remove());

    // Ensure project ID is set in the form
    if (!updateProjectId) {
        console.log('🔧 Creating updateProjectId hidden input');
        const projectIdInput = document.createElement('input');
        projectIdInput.type = 'hidden';
        projectIdInput.id = 'updateProjectId';
        projectIdInput.name = 'project_id';
        projectIdInput.value = String(projectId);
        form.appendChild(projectIdInput);
    }

    const hiddenFields = `
        <input type="hidden" name="status" value="${newStatus}">
        <input type="hidden" name="action_time" value="${currentTime.toISOString()}">
        <input type="hidden" name="action_type" value="assignee_update">
    `;

    form.insertAdjacentHTML('afterbegin', hiddenFields);

    // Clear form fields
    if (commentField) commentField.value = '';
    const fileField = document.getElementById('statusFile');
    if (fileField) fileField.value = '';

    console.log('✅ Modal setup complete, project ID set to:', projectId);

    // Show modal
    new bootstrap.Modal(modal).show();
}

// Legacy dropdown function removed - now using enhanced task status modal button

function showStatusUpdateModal(projectId, newStatus) {
    const statusNames = {
        'sent_for_review': 'Sent to client for review',
        'revision_needed': 'Revision needed',
        'client_accepted': 'Client accepted',
        'task_completed': 'Task completed'
    };

    console.log('🔧 showStatusUpdateModal called:', { projectId, newStatus });

    const modal = document.getElementById('statusUpdateModal');
    const form = document.getElementById('statusUpdateForm');

    if (!modal || !form) {
        console.error('❌ Modal or form not found');
        return;
    }

    // Update modal content using the redesigned modal structure
    const statusUpdateText = document.getElementById('statusUpdateText');
    const statusTimestamp = document.getElementById('statusTimestamp');
    const updateProjectId = document.getElementById('updateProjectId');
    const commentField = document.getElementById('statusComment');
    const commentBadge = document.getElementById('commentRequiredBadge');

    // Set current time
    const currentTime = new Date();
    const timeString = currentTime.toLocaleString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });

    // Update modal content
    if (statusUpdateText) statusUpdateText.textContent = statusNames[newStatus] || newStatus;
    if (statusTimestamp) statusTimestamp.textContent = timeString;
    if (updateProjectId) updateProjectId.value = projectId;

    // Set comment requirement (only for corrections)
    const requiresComment = newStatus === 'revision_needed';
    if (commentField) {
        commentField.required = requiresComment;
        commentField.placeholder = requiresComment ?
            'Please explain what needs to be corrected...' :
            'Add any additional details...';
    }

    if (commentBadge) {
        commentBadge.textContent = requiresComment ? 'Required' : 'Optional';
        commentBadge.className = requiresComment ? 'required-badge' : 'optional-badge';
    }

    // Add hidden fields for admin update
    const existingHiddenFields = form.querySelectorAll('input[type="hidden"]:not(#updateProjectId)');
    existingHiddenFields.forEach(field => field.remove());

    const hiddenFields = `
        <input type="hidden" name="status" value="${newStatus}">
        <input type="hidden" name="update_time" value="${currentTime.toISOString()}">
        <input type="hidden" name="action_type" value="manager_update">
    `;

    form.insertAdjacentHTML('afterbegin', hiddenFields);

    // Clear form fields
    if (commentField) commentField.value = '';
    const fileField = document.getElementById('statusFile');
    if (fileField) fileField.value = '';

    console.log('✅ Admin modal setup complete, project ID set to:', projectId);

    // Show modal
    new bootstrap.Modal(modal).show();
}

// Force border styles on all project cards - IMMEDIATE EXECUTION
(function() {
    console.log('🔧 Forcing border styles...');

    // Add CSS to force borders
    const forceStyle = document.createElement('style');
    forceStyle.innerHTML = `
        .project-card-final {
            border: 1px solid #e0e0e0 !important;
            border-width: 1px !important;
            border-style: solid !important;
            border-color: #e0e0e0 !important;
        }
        .project-card-final:hover {
            border: 2px solid #d0d0d0 !important;
            border-width: 2px !important;
            border-style: solid !important;
            border-color: #d0d0d0 !important;
        }
    `;
    document.head.appendChild(forceStyle);

    // Apply styles to existing cards
    function applyBorderStyles() {
        const cards = document.querySelectorAll('.project-card-final');
        cards.forEach(card => {
            card.style.border = '1px solid #e0e0e0';
            card.style.borderWidth = '1px';
            card.style.borderStyle = 'solid';
            card.style.borderColor = '#e0e0e0';
        });
        console.log('✅ Applied border styles to', cards.length, 'cards');
    }

    // Apply immediately and on DOM changes

    // Session and cache management for user switching
    function clearUserSessionData() {
        // Clear localStorage
        localStorage.clear();

        // Clear sessionStorage
        sessionStorage.clear();

        // Clear any cached project data
        if (window.projectsCache) {
            window.projectsCache = null;
        }

        // Clear any cached user data
        if (window.currentUser) {
            window.currentUser = null;
        }

        // Clear any cached stats
        if (window.statsCache) {
            window.statsCache = null;
        }

        // Clear any cached filters
        if (window.filtersCache) {
            window.filtersCache = null;
        }

        console.log('User session data cleared');
    }

    // Check for user session changes
    function checkUserSessionChange() {
        const currentUserId = <?= $user['id'] ?>;
        const storedUserId = localStorage.getItem('smartflo_user_id');

        if (storedUserId && storedUserId !== currentUserId.toString()) {
            console.log('User session changed, clearing cached data');
            clearUserSessionData();

            // Force reload projects
            if (typeof loadProjects === 'function') {
                loadProjects(true); // Force refresh
            }
        }

        // Store current user ID
        localStorage.setItem('smartflo_user_id', currentUserId.toString());
    }

    // Initialize session management
    checkUserSessionChange();

    // Cache invalidation system
    function initializeCacheInvalidation() {
        // Register service worker message handler
        if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
            console.log('Cache invalidation system initialized');
        }
    }

    // Invalidate cache for specific patterns
    function invalidateCache(pattern = 'projects') {
        if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
            navigator.serviceWorker.controller.postMessage({
                type: 'INVALIDATE_CACHE',
                pattern: pattern
            });
            console.log('Cache invalidation requested for:', pattern);
        }

        // Also clear browser cache headers
        if (window.fetch) {
            // Add cache-busting timestamp to future requests
            window.cacheBustTimestamp = Date.now();
        }
    }

    // Clear all application cache
    function clearAllCache() {
        if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
            navigator.serviceWorker.controller.postMessage({
                type: 'CLEAR_ALL_CACHE'
            });
        }

        // Clear browser storage
        clearUserSessionData();

        console.log('All cache cleared');
    }

    // Enhanced refresh with cache invalidation
    function forceRefreshWithCacheInvalidation() {
        console.log('Force refresh with cache invalidation');

        // Invalidate cache first
        invalidateCache();

        // Clear application cache
        window.projectsCache = null;
        window.statsCache = null;

        // Force reload with cache busting
        setTimeout(() => {
            loadProjects(true);
            loadStats();
        }, 100);
    }

    // Update task completion display in real-time
    function updateTaskCompletionDisplay(projectId, newStatus) {
        const projectCard = document.querySelector(`[data-project-id="${projectId}"]`);
        if (!projectCard) return;

        // If task is completed, update the task display with strikethrough
        if (newStatus === 'completed') {
            // Find the specific task and add strikethrough
            const taskElements = projectCard.querySelectorAll('.task-name-small');
            taskElements.forEach(taskEl => {
                if (!taskEl.classList.contains('completed-task')) {
                    taskEl.classList.add('completed-task', 'force-strikethrough');
                    taskEl.style.textDecoration = 'line-through';
                    taskEl.style.textDecorationColor = 'rgba(0, 0, 0, 0.3)';
                    taskEl.style.textDecorationThickness = '1px';
                    taskEl.style.opacity = '0.7';
                }
            });
        }

        console.log('Task completion display updated for project:', projectId, 'status:', newStatus);
    }

    // Expose cache management functions globally
    window.invalidateCache = invalidateCache;
    window.clearAllCache = clearAllCache;
    window.forceRefreshWithCacheInvalidation = forceRefreshWithCacheInvalidation;
    window.updateTaskCompletionDisplay = updateTaskCompletionDisplay;
    applyBorderStyles();

    // Watch for new cards being added
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length > 0) {
                applyBorderStyles();
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    console.log('🔧 Border style enforcement active');
})();

// Start revision for a project
function startRevision(projectId, buttonElement) {
    console.log('🔄 Starting revision for project:', projectId);

    // Show loading state
    const originalText = buttonElement.innerHTML;
    buttonElement.disabled = true;
    buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Starting...';

    fetch(`/projects/startRevision/${projectId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message || 'Revision started successfully');

            // Reload projects to show updated status
            loadProjects();

            console.log('✅ Revision started successfully');
        } else {
            showAlert('error', data.message || 'Failed to start revision');
            console.error('❌ Revision start failed:', data);
        }
    })
    .catch(error => {
        console.error('❌ Error starting revision:', error);
        showAlert('error', 'Error starting revision. Please try again.');
    })
    .finally(() => {
        // Restore button state
        buttonElement.disabled = false;
        buttonElement.innerHTML = originalText;
    });
}

// Manager Task Status Modal Functions
function openManagerTaskStatusModal(projectId, projectName) {
    console.log('🎯 Opening Manager Task Status Modal for project:', projectId, projectName);

    try {
        // Check if modal element exists
        const modalElement = document.getElementById('managerTaskStatusModal');
        if (!modalElement) {
            console.error('❌ Modal element not found!');
            alert('Modal element not found. Please refresh the page.');
            return;
        }

        // Check if Bootstrap is available
        if (typeof bootstrap === 'undefined') {
            console.error('❌ Bootstrap is not loaded!');
            alert('Bootstrap is not loaded. Please refresh the page.');
            return;
        }

        // Set project info
        const projectNameElement = document.getElementById('managerProjectName');
        const projectIdElement = document.getElementById('managerProjectId');

        if (projectNameElement) projectNameElement.textContent = projectName;
        if (projectIdElement) projectIdElement.value = projectId;

        // Reset form
        resetManagerTaskStatusForm();

        // Load tasks from project card data
        loadTasksFromProjectCard(projectId, projectName);

        // Show modal
        console.log('🎯 Attempting to show modal...');
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
        console.log('✅ Modal show() called successfully');

    } catch (error) {
        console.error('❌ Error opening modal:', error);
        alert('Error opening modal: ' + error.message);
    }
}

function resetManagerTaskStatusForm() {
    document.getElementById('managerTaskStatusForm').reset();
    document.getElementById('managerTaskSelect').disabled = false;
    document.getElementById('managerNewStatus').disabled = true;
    document.getElementById('managerTaskSubmitBtn').disabled = true;
    document.getElementById('managerTaskInfo').style.display = 'none';

    // Clear task selection
    const taskSelect = document.getElementById('managerTaskSelect');
    taskSelect.innerHTML = '<option value="">Loading tasks...</option>';
}

function loadTasksFromProjectCard(projectId, projectName) {
    console.log('📋 Loading tasks for selected project:', projectId, projectName);

    const taskSelect = document.getElementById('managerTaskSelect');
    taskSelect.innerHTML = '<option value="">Loading tasks...</option>';

    // Get tasks for the specific selected project
    let projectTasks = [];
    let selectedProject = null;

    // Look for the selected project in the global projects array
    if (window.currentProjects && Array.isArray(window.currentProjects)) {
        selectedProject = window.currentProjects.find(project => project.id == projectId);

        // If it's an admin consolidated project, get all tasks for this project
        if (selectedProject && selectedProject.tasks && Array.isArray(selectedProject.tasks)) {
            // Admin view: get completed tasks from the consolidated project
            projectTasks = selectedProject.tasks.filter(task => {
                const isCompleted = task.status === 'completed' || task.task_status === 'completed';
                return isCompleted;
            });
        } else if (selectedProject) {
            // Single task project: check if the project itself is completed
            const isCompleted = selectedProject.status === 'completed' || selectedProject.status === 'Completed' || selectedProject.status === 'COMPLETED';
            if (isCompleted) {
                projectTasks = [selectedProject]; // Treat the project as a single task
            }
        }
    }

    // If no tasks found in global array, try to find from project card
    if (projectTasks.length === 0) {
        const projectCard = document.querySelector(`[data-project-id="${projectId}"]`);
        if (projectCard && projectCard.dataset.projectData) {
            try {
                const projectData = JSON.parse(projectCard.dataset.projectData);
                const isCompleted = projectData.status === 'completed' || projectData.status === 'Completed' || projectData.status === 'COMPLETED';
                if (isCompleted) {
                    projectTasks = [projectData];
                }
            } catch (e) {
                console.error('❌ Error parsing project data from card:', e);
            }
        }
    }

    console.log('📊 Found completed tasks for project:', projectTasks.length, projectTasks);

    taskSelect.innerHTML = '';

    if (projectTasks.length === 0) {
        // No completed tasks found for this project
        const option = document.createElement('option');
        option.value = '';
        option.disabled = true;
        option.textContent = '❌ No completed tasks found for this project';
        taskSelect.appendChild(option);
        console.log('⚠️ No completed tasks available for this project');
        return;
    }

    // Add default option
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = 'Select a completed task...';
    taskSelect.appendChild(defaultOption);

    // Add completed tasks for this project to dropdown
    projectTasks.forEach(taskData => {
        const option = document.createElement('option');
        option.value = taskData.id || taskData.task_id || projectId;

        // Use task_name if available, otherwise use project_name
        const taskName = taskData.name || taskData.task_name || taskData.project_name || 'Unnamed Task';
        option.textContent = `✅ ${taskName} (Completed Task)`;
        option.dataset.projectData = JSON.stringify(taskData);
        taskSelect.appendChild(option);
    });

    // Auto-select the first completed task
    if (projectTasks.length > 0) {
        taskSelect.value = projectTasks[0].id || projectTasks[0].task_id || projectId;
        updateManagerTaskInfo();
        console.log('✅ Auto-selected first completed task for project');
    }

    console.log('✅ Project tasks loaded in dropdown:', projectTasks.length, 'tasks');
}

function updateManagerTaskInfo() {
    const taskSelect = document.getElementById('managerTaskSelect');
    const taskInfo = document.getElementById('managerTaskInfo');
    const statusSelect = document.getElementById('managerNewStatus');
    const submitBtn = document.getElementById('managerTaskSubmitBtn');
    const revisionInfo = document.getElementById('managerRevisionInfo');

    if (!taskSelect.value || taskSelect.value === '') {
        taskInfo.style.display = 'none';
        statusSelect.disabled = true;
        submitBtn.disabled = true;
        console.log('📋 No task selected, hiding task info');
        return;
    }

    // Get selected project data
    const selectedOption = taskSelect.options[taskSelect.selectedIndex];
    const projectData = JSON.parse(selectedOption.dataset.projectData);

    // Show project info
    document.getElementById('managerCurrentStatus').textContent = projectData.status || 'Unknown';
    document.getElementById('managerTaskAssignee').textContent = projectData.assigned_username || projectData.username || 'Not assigned';

    // Use task_name for description, or fall back to project description
    const taskDescription = projectData.task_name || projectData.description || projectData.project_name || 'No task description available';
    document.getElementById('managerTaskDescription').textContent = taskDescription;

    // Show revision info if available
    if (projectData.revision_count && projectData.revision_count > 0) {
        document.getElementById('managerRevisionCount').textContent = projectData.revision_count;
        revisionInfo.style.display = 'block';
    } else {
        revisionInfo.style.display = 'none';
    }

    taskInfo.style.display = 'block';
    statusSelect.disabled = false;

    // Enable submit button when status is selected
    statusSelect.addEventListener('change', function() {
        submitBtn.disabled = !this.value;
    });

    const taskName = projectData.task_name || projectData.project_name || projectData.name || 'Unnamed Task';
    console.log('✅ Manager completed task selected:', taskName, 'Current status:', projectData.status);
}

function submitManagerTaskStatus(event) {
    event.preventDefault();

    console.log('🚀 Submitting manager task status update');

    const form = document.getElementById('managerTaskStatusForm');
    const formData = new FormData(form);
    const submitBtn = document.getElementById('managerTaskSubmitBtn');

    // Debug: Log all form data
    console.log('📋 Form data being sent:');
    for (let [key, value] of formData.entries()) {
        console.log(`  ${key}: ${value}`);
    }

    // Validate required fields
    const projectId = formData.get('project_id');
    const status = formData.get('status');
    const notes = formData.get('notes') || '';

    console.log('🔍 Validation check:', {
        projectId: projectId,
        status: status,
        notes: notes,
        hasProjectId: !!projectId,
        hasStatus: !!status
    });

    if (!projectId) {
        showAlert('error', 'Project ID is missing. Please try again.');
        return;
    }

    if (!status) {
        showAlert('error', 'Please select a status.');
        return;
    }

    // Show loading state
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';

    fetch(`/projects/updateManagerStatus/${projectId}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message || 'Task status updated successfully');

            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('managerTaskStatusModal')).hide();

            // Reload projects to show updated status
            loadProjects();

            console.log('✅ Manager task status updated successfully');
        } else {
            showAlert('error', data.message || 'Failed to update task status');
            console.error('❌ Manager task status update failed:', data);
        }
    })
    .catch(error => {
        console.error('❌ Error updating manager task status:', error);
        showAlert('error', 'Error updating task status. Please try again.');
    })
    .finally(() => {
        // Restore button state
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
}

// Test function to manually open the modal (for debugging)
window.testModal = function() {
    console.log('🧪 Testing modal manually...');
    openManagerTaskStatusModal(1, 'Test Project');
};

// Test function to check if elements exist
window.checkModalElements = function() {
    console.log('🔍 Checking modal elements...');
    console.log('Modal element:', document.getElementById('managerTaskStatusModal'));
    console.log('Bootstrap available:', typeof bootstrap !== 'undefined');
    console.log('Project name element:', document.getElementById('managerProjectName'));
    console.log('Project ID element:', document.getElementById('managerProjectId'));
    console.log('Task select element:', document.getElementById('managerTaskSelect'));
    console.log('Status select element:', document.getElementById('managerNewStatus'));
    console.log('Notes textarea:', document.getElementById('managerTaskNotes'));
    console.log('File input:', document.getElementById('managerTaskFile'));
};

// Test function to check form data
window.testFormData = function() {
    console.log('🧪 Testing form data collection...');
    const form = document.getElementById('managerTaskStatusForm');
    if (!form) {
        console.error('❌ Form not found!');
        return;
    }

    const formData = new FormData(form);
    console.log('📋 Current form data:');
    for (let [key, value] of formData.entries()) {
        console.log(`  ${key}: ${value}`);
    }

    // Test individual field values
    console.log('🔍 Individual field values:');
    console.log('  project_id:', document.getElementById('managerProjectId')?.value);
    console.log('  status:', document.getElementById('managerNewStatus')?.value);
    console.log('  notes:', document.getElementById('managerTaskNotes')?.value);
    console.log('  file:', document.getElementById('managerTaskFile')?.files[0]?.name || 'No file');
};

// Show manager status modal for project
function showManagerStatusModal(projectId) {
    console.log('🎯 Opening manager status modal for project:', projectId);

    // Find project data
    const projectCard = document.querySelector(`[data-project-id="${projectId}"]`);
    if (!projectCard) {
        console.error('❌ Project card not found for ID:', projectId);
        showAlert('error', 'Project not found');
        return;
    }

    const projectData = JSON.parse(projectCard.getAttribute('data-project-data'));
    console.log('📋 Project data:', projectData);

    openManagerTaskStatusModal(projectId, projectData.project_name);
}

// Update task manager status directly
function updateTaskManagerStatus(taskId, status) {
    console.log('🔄 Updating task manager status:', { taskId, status });

    // Prevent double-clicking by disabling buttons temporarily
    const buttons = document.querySelectorAll(`[onclick*="updateTaskManagerStatus(${taskId}"]`);
    buttons.forEach(btn => {
        btn.disabled = true;
        btn.style.opacity = '0.6';
    });

    // Show payment modal for "sent_for_review" status
    if (status === 'sent_for_review') {
        showPaymentDetailsModal(taskId);
        // Re-enable buttons after modal
        setTimeout(() => {
            buttons.forEach(btn => {
                btn.disabled = false;
                btn.style.opacity = '1';
            });
        }, 1000);
        return;
    }

    // Use task-level manager status endpoint
    const endpoint = `/projects/updateTaskManagerStatus/${taskId}`;
    const formData = new FormData();
    formData.append('status', status);

    if (status === 'need_revision') {
        formData.append('notes', 'Task sent back for revision by manager');
    } else if (status === 'sent_for_review') {
        formData.append('notes', 'Task sent for client review');
    } else {
        formData.append('notes', `Task ${status.replace('_', ' ')} by manager`);
    }

    fetch(endpoint, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message || 'Task status updated successfully');
            // Refresh the project cards to show updated status
            loadProjects();
        } else {
            showAlert('error', data.message || 'Failed to update task status');
        }
    })
    .catch(error => {
        console.error('❌ Error updating task status:', error);
        showAlert('error', 'Error updating task status');
    })
    .finally(() => {
        // Re-enable buttons after request completes
        setTimeout(() => {
            buttons.forEach(btn => {
                btn.disabled = false;
                btn.style.opacity = '1';
            });
        }, 1000);
    });
}

// Show payment details modal for sent_for_review status
function showPaymentDetailsModal(taskId) {
    // Create a simple payment details modal
    const modalHtml = `
        <div class="modal fade" id="paymentDetailsModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Send for Client Review</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="paymentDetailsForm">
                            <div class="mb-3">
                                <label class="form-label">Payment Amount <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="advanceAmount" step="0.01" min="0" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Payment Status</label>
                                <select class="form-control" id="paymentStatus">
                                    <option value="unpaid">Unpaid</option>
                                    <option value="paid">Paid</option>
                                    <option value="partial">Partial</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Paid to Account</label>
                                <select class="form-control" id="paymentAccount">
                                    <option value="">Select Account</option>
                                    <option value="bank_account_1">Main Bank Account</option>
                                    <option value="bank_account_2">Secondary Account</option>
                                    <option value="cash">Cash</option>
                                    <option value="online">Online Payment</option>
                                </select>
                                <small class="form-text text-muted">Account can be configured in billing settings</small>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Payment Notes</label>
                                <textarea class="form-control" id="paymentNotes" rows="3" placeholder="Enter payment details..."></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Attach File (Optional)</label>
                                <input type="file" class="form-control" id="paymentFile" accept="image/*,.pdf,.doc,.docx">
                                <small class="form-text text-muted">Supported formats: Images, PDF, Word documents</small>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fab fa-google-drive me-2 text-success"></i>
                                    Google Drive Link (Optional)
                                </label>
                                <input type="url" class="form-control" id="googleDriveLink"
                                       placeholder="https://drive.google.com/..."
                                       title="Share Google Drive folder or file link for client access">
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Share a Google Drive link for easy file access by the client
                                </small>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="submitPaymentDetails(${taskId})">
                            Send for Review
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('paymentDetailsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('paymentDetailsModal'));
    modal.show();
}

// Submit payment details and update task status
function submitPaymentDetails(taskId) {
    const advanceAmount = document.getElementById('advanceAmount').value;
    const paymentStatus = document.getElementById('paymentStatus').value;
    const paymentAccount = document.getElementById('paymentAccount').value;
    const paymentNotes = document.getElementById('paymentNotes').value;
    const paymentFile = document.getElementById('paymentFile').files[0];
    const googleDriveLink = document.getElementById('googleDriveLink').value;

    // Validate required fields
    if (!advanceAmount || advanceAmount <= 0) {
        showAlert('error', 'Please enter a valid payment amount');
        return;
    }

    const formData = new FormData();
    formData.append('status', 'sent_for_review');
    formData.append('payment_amount', advanceAmount);
    formData.append('payment_status', paymentStatus);
    formData.append('payment_account', paymentAccount);
    formData.append('notes', paymentNotes || 'Task sent for review');

    // Get CSRF token from multiple possible sources
    let csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                   document.querySelector('input[name="csrf_token"]')?.value ||
                   document.querySelector('input[name="<?= csrf_token() ?>"]')?.value;

    // If no token found, try to get it from a hidden form
    if (!csrfToken) {
        const hiddenForm = document.querySelector('form input[name="csrf_token"]');
        if (hiddenForm) {
            csrfToken = hiddenForm.value;
        }
    }

    if (csrfToken) {
        formData.append('csrf_token', csrfToken);
    }

    // Add Google Drive link if provided
    if (googleDriveLink) {
        formData.append('google_drive_link', googleDriveLink);
    }

    // Add file if selected
    if (paymentFile) {
        formData.append('status_file', paymentFile);
    }

    // Debug: Log what we're sending
    console.log('🔄 Sending payment data:', {
        taskId: taskId,
        status: 'sent_for_review',
        payment_amount: advanceAmount,
        payment_status: paymentStatus,
        payment_account: paymentAccount,
        google_drive_link: googleDriveLink,
        notes: paymentNotes
    });

    // Submit with CSRF retry logic
    function submitPaymentForm(retryCount = 0) {
        const headers = {
            'X-Requested-With': 'XMLHttpRequest'
        };

        // Add CSRF token to header as well
        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken;
        }

        return fetch(`/projects/updateTaskManagerStatus/${taskId}`, {
            method: 'POST',
            body: formData,
            headers: headers
        })
        .then(response => {
            if (response.status === 403 && retryCount < 2) {
                // CSRF token expired, refresh and retry
                return fetch('/auth/csrf-token', {
                    method: 'GET',
                    headers: { 'X-Requested-With': 'XMLHttpRequest' }
                })
                .then(res => res.json())
                .then(data => {
                    if (data.csrf_token) {
                        formData.set('csrf_token', data.csrf_token);
                        return submitPaymentForm(retryCount + 1);
                    }
                    throw new Error('Failed to refresh CSRF token');
                });
            }
            return response;
        });
    }

    submitPaymentForm()
    .then(response => {
        console.log('📥 Response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('📥 Response data:', data);
        if (data.success) {
            showAlert('success', 'Task sent for review with payment details');
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('paymentDetailsModal'));
            modal.hide();
            // Refresh projects
            loadProjects();
        } else {
            showAlert('error', data.message || 'Failed to send task for review');
        }
    })
    .catch(error => {
        console.error('❌ Error submitting payment details:', error);
        showAlert('error', 'Error submitting payment details');
    });
}

// Show task status modal for individual task
function showTaskStatusModal(projectId, taskId) {
    console.log('🎯 Opening task status modal for task:', taskId, 'in project:', projectId);

    // For now, redirect to the manager status modal
    // This can be enhanced later for individual task management
    showManagerStatusModal(projectId);
}

console.log('🎯 Debug functions available: testModal(), checkModalElements(), testFormData()');

// Test function to check review menu
function testReviewMenu() {
    console.log('=== Testing Review Menu ===');
    console.log('User role:', '<?= $user['roles'] ?>');
    console.log('Is admin/manager:', <?= ($user['roles'] === 'admin' || $user['roles'] === 'manager') ? 'true' : 'false' ?>);

    // Test with sample task data
    const testTask = {
        id: 999,
        project_id: 1,
        task_name: 'Test Task',
        status: 'completed',
        task_manager_status: 'sent_for_review',
        assigned_to: 1
    };

    console.log('Test task:', testTask);
    const buttons = getTaskActionButtons(testTask);
    console.log('Generated buttons:', buttons);

    // Test with not_reviewed status
    const testTask2 = {...testTask, task_manager_status: 'not_reviewed'};
    const buttons2 = getTaskActionButtons(testTask2);
    console.log('Buttons for not_reviewed:', buttons2);
}

window.testReviewMenu = testReviewMenu;
console.log('🎯 Test function available: testReviewMenu()');

// Sidebar mini functionality is now handled by the universal sidebar-mini.js script
</script>
<?= $this->endSection() ?>
