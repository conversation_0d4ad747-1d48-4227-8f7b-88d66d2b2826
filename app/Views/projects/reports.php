<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Project Reports</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="/projects">Projects</a></li>
                    <li class="breadcrumb-item active">Reports</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="/projects" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Projects
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?= $stats['total'] ?></h4>
                            <p class="mb-0">Total Projects</p>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-project-diagram fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?= $stats['in_progress'] ?></h4>
                            <p class="mb-0">In Progress</p>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-play fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?= $stats['completed'] ?></h4>
                            <p class="mb-0">Completed</p>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?= $stats['on_hold'] ?></h4>
                            <p class="mb-0">On Hold</p>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-pause fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?= $stats['overdue'] ?></h4>
                            <p class="mb-0">Overdue</p>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h4 class="mb-0"><?= $stats['completion_rate'] ?>%</h4>
                            <p class="mb-0">Completion Rate</p>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-chart-pie fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Project Status Distribution
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="statusChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Monthly Progress
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="progressChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Projects Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>Recent Projects
            </h5>
        </div>
        <div class="card-body">
            <?php if (!empty($recentProjects)): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Project ID</th>
                            <th>Project Name</th>
                            <th>Client</th>
                            <th>Status</th>
                            <th>Progress</th>
                            <th>Target Date</th>
                            <th>Assigned To</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recentProjects as $project): ?>
                        <tr>
                            <td>
                                <a href="/projects/view/<?= $project['id'] ?>" class="text-decoration-none">
                                    <?= esc($project['project_id']) ?>
                                </a>
                            </td>
                            <td><?= esc($project['project_name']) ?></td>
                            <td><?= esc($project['client_name']) ?></td>
                            <td>
                                <span class="badge bg-<?= getStatusColor($project['status']) ?>">
                                    <?= ucwords(str_replace('_', ' ', $project['status'])) ?>
                                </span>
                            </td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: <?= $project['progress_percentage'] ?>%"
                                         aria-valuenow="<?= $project['progress_percentage'] ?>" 
                                         aria-valuemin="0" aria-valuemax="100">
                                        <?= $project['progress_percentage'] ?>%
                                    </div>
                                </div>
                            </td>
                            <td><?= date('M d, Y', strtotime($project['target_completion'])) ?></td>
                            <td><?= esc($project['assigned_username'] ?? 'Unassigned') ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php else: ?>
            <div class="text-center py-4">
                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                <h6>No Projects Found</h6>
                <p class="text-muted">Create your first project to see reports</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
function getStatusColor($status) {
    switch ($status) {
        case 'not_started': return 'secondary';
        case 'planning': return 'info';
        case 'in_progress': return 'primary';
        case 'on_hold': return 'warning';
        case 'completed': return 'success';
        case 'review': return 'warning';
        default: return 'secondary';
    }
}
?>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Status Distribution Chart
const statusCtx = document.getElementById('statusChart').getContext('2d');
const statusChart = new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: ['Not Started', 'In Progress', 'On Hold', 'Completed'],
        datasets: [{
            data: [<?= $stats['not_started'] ?>, <?= $stats['in_progress'] ?>, <?= $stats['on_hold'] ?>, <?= $stats['completed'] ?>],
            backgroundColor: ['#6c757d', '#007bff', '#ffc107', '#28a745'],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Progress Chart (placeholder data)
const progressCtx = document.getElementById('progressChart').getContext('2d');
const progressChart = new Chart(progressCtx, {
    type: 'bar',
    data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: 'Projects Completed',
            data: [2, 4, 3, 5, 6, 4],
            backgroundColor: '#28a745',
            borderColor: '#1e7e34',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>
<?= $this->endSection() ?>
