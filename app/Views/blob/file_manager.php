<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-folder-open me-2"></i>
                    File Manager
                </h1>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                    <i class="fas fa-upload me-2"></i>
                    Upload Image
                </button>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4" id="statsCards">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Files</h6>
                                    <h3 class="mb-0" id="totalFiles">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-file fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Size</h6>
                                    <h3 class="mb-0" id="totalSize">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-hdd fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Images</h6>
                                    <h3 class="mb-0" id="imageCount">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-image fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Documents</h6>
                                    <h3 class="mb-0" id="documentCount">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-file-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- File List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        Your Files
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="filesTable">
                            <thead>
                                <tr>
                                    <th>Preview</th>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Size</th>
                                    <th>Uploaded</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="filesTableBody">
                                <tr>
                                    <td colspan="6" class="text-center">
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadModalLabel">Upload Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="uploadForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="imageFile" class="form-label">Select Image</label>
                        <input type="file" class="form-control" id="imageFile" name="image" accept="image/*" required>
                        <div class="form-text">Supported formats: JPG, PNG, GIF, WebP. Max size: 10MB</div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="isPublic" name="is_public">
                            <label class="form-check-label" for="isPublic">
                                Make this image publicly accessible
                            </label>
                        </div>
                    </div>
                    <input type="hidden" name="csrf_token" value="<?= csrf_hash() ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="uploadBtn">
                        <i class="fas fa-upload me-2"></i>
                        Upload
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Image Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">Image Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="previewImage" src="" alt="Preview" class="img-fluid">
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadStats();
    loadFiles();

    // Upload form submission
    document.getElementById('uploadForm').addEventListener('submit', function(e) {
        e.preventDefault();
        uploadFile();
    });
});

function loadStats() {
    fetch('/blob/stats', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const stats = data.data;
            document.getElementById('totalFiles').textContent = stats.general.total_files;
            document.getElementById('totalSize').textContent = stats.general.total_size_formatted;
            
            // Update type counts
            let imageCount = 0, documentCount = 0;
            stats.by_type.forEach(type => {
                if (type.file_type === 'image') imageCount = type.count;
                if (type.file_type === 'document') documentCount = type.count;
            });
            
            document.getElementById('imageCount').textContent = imageCount;
            document.getElementById('documentCount').textContent = documentCount;
        }
    })
    .catch(error => {
        console.error('Error loading stats:', error);
    });
}

function loadFiles() {
    fetch('/blob/files', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayFiles(data.data);
        } else {
            showError('Failed to load files');
        }
    })
    .catch(error => {
        console.error('Error loading files:', error);
        showError('Error loading files');
    });
}

function displayFiles(files) {
    const tbody = document.getElementById('filesTableBody');
    
    if (files.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center">No files uploaded yet</td></tr>';
        return;
    }
    
    tbody.innerHTML = files.map(file => `
        <tr>
            <td>
                ${file.type.startsWith('image/') ? 
                    `<img src="${file.thumbnail_url}" alt="Thumbnail" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover; cursor: pointer;" onclick="previewImage('${file.url}', '${file.original_name}')">` :
                    '<i class="fas fa-file fa-2x text-muted"></i>'
                }
            </td>
            <td>${file.original_name}</td>
            <td><span class="badge bg-secondary">${file.type}</span></td>
            <td>${formatBytes(file.size)}</td>
            <td>${new Date(file.uploaded_at).toLocaleDateString()}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <a href="${file.url}" class="btn btn-outline-primary" target="_blank" title="View">
                        <i class="fas fa-eye"></i>
                    </a>
                    <button class="btn btn-outline-danger" onclick="deleteFile(${file.id})" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function uploadFile() {
    const form = document.getElementById('uploadForm');
    const formData = new FormData(form);
    const uploadBtn = document.getElementById('uploadBtn');
    
    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Uploading...';
    
    fetch('/blob/upload/image', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('Image uploaded successfully');
            bootstrap.Modal.getInstance(document.getElementById('uploadModal')).hide();
            form.reset();
            loadStats();
            loadFiles();
        } else {
            showError(data.message || 'Upload failed');
        }
    })
    .catch(error => {
        console.error('Upload error:', error);
        showError('Upload failed');
    })
    .finally(() => {
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<i class="fas fa-upload me-2"></i>Upload';
    });
}

function deleteFile(fileId) {
    if (!confirm('Are you sure you want to delete this file?')) {
        return;
    }
    
    fetch(`/blob/image/${fileId}`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('File deleted successfully');
            loadStats();
            loadFiles();
        } else {
            showError(data.message || 'Delete failed');
        }
    })
    .catch(error => {
        console.error('Delete error:', error);
        showError('Delete failed');
    });
}

function previewImage(url, name) {
    document.getElementById('previewImage').src = url;
    document.getElementById('previewModalLabel').textContent = name;
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showSuccess(message) {
    // You can implement a toast notification system here
    alert(message);
}

function showError(message) {
    // You can implement a toast notification system here
    alert(message);
}
</script>
<?= $this->endSection() ?>
