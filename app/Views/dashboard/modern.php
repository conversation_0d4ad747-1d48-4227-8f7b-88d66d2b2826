<?= $this->extend('layouts/app') ?>

<?= $this->section('styles') ?>
<style>
        /* ===== MODERN CSS VARIABLES ===== */
        :root {
            /* Colors - Modern Palette */
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #a5b4fc;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --info: #3b82f6;
            
            /* Neutrals - Refined Grays */
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            
            /* Layout */
            --sidebar-width: 280px;
            --header-height: 72px;
            --bottom-nav-height: 80px;
            
            /* Spacing */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;
            
            /* Border Radius */
            --radius-sm: 6px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 24px;
            
            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            
            /* Transitions */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-base: 250ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        /* ===== GLOBAL RESET ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html {
            font-size: 16px;
            scroll-behavior: smooth;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-weight: 400;
            line-height: 1.6;
            color: var(--gray-800);
            background: var(--gray-50);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            overflow-x: hidden;
        }
        
        /* ===== LAYOUT STRUCTURE ===== */
        .app-container {
            display: flex;
            min-height: 100vh;
            background: var(--gray-50);
        }
        
        /* ===== SIDEBAR ===== */
        .sidebar {
            width: var(--sidebar-width);
            background: white;
            border-right: 1px solid var(--gray-200);
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 1000;
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: var(--space-xl);
            border-bottom: 1px solid var(--gray-100);
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: var(--space-md);
            text-decoration: none;
            color: var(--gray-900);
        }
        
        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.25rem;
        }
        
        .logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-900);
        }
        
        .sidebar-nav {
            padding: var(--space-lg) 0;
        }
        
        .nav-section {
            margin-bottom: var(--space-xl);
        }
        
        .nav-section-title {
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--gray-500);
            padding: 0 var(--space-xl);
            margin-bottom: var(--space-md);
        }
        
        .nav-item {
            margin: var(--space-xs) var(--space-lg);
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            gap: var(--space-md);
            padding: var(--space-md) var(--space-lg);
            border-radius: var(--radius-md);
            text-decoration: none;
            color: var(--gray-600);
            font-weight: 500;
            transition: all var(--transition-fast);
            position: relative;
        }
        
        .nav-link:hover {
            background: var(--gray-50);
            color: var(--gray-900);
        }
        
        .nav-link.active {
            background: var(--primary);
            color: white;
            box-shadow: var(--shadow-md);
        }
        
        .nav-link i {
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }
        
        /* ===== MAIN CONTENT ===== */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            background: var(--gray-50);
        }
        
        /* ===== HEADER ===== */
        .header {
            background: white;
            border-bottom: 1px solid var(--gray-200);
            padding: 0 var(--space-xl);
            height: var(--header-height);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-left h1 {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0;
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: var(--space-lg);
        }
        
        .header-btn {
            width: 44px;
            height: 44px;
            border: none;
            background: var(--gray-100);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-600);
            cursor: pointer;
            transition: all var(--transition-fast);
            position: relative;
        }
        
        .header-btn:hover {
            background: var(--gray-200);
            color: var(--gray-800);
        }
        
        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            width: 18px;
            height: 18px;
            background: var(--error);
            color: white;
            border-radius: 50%;
            font-size: 0.7rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: var(--space-md);
            padding: var(--space-sm) var(--space-md);
            border-radius: var(--radius-md);
            background: var(--gray-50);
            cursor: pointer;
            transition: all var(--transition-fast);
        }
        
        .user-menu:hover {
            background: var(--gray-100);
        }
        
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        
        .user-info {
            display: flex;
            flex-direction: column;
        }
        
        .user-name {
            font-weight: 600;
            color: var(--gray-900);
            font-size: 0.875rem;
        }
        
        .user-role {
            font-size: 0.75rem;
            color: var(--gray-500);
        }

        /* ===== USER DROPDOWN ===== */
        .user-dropdown {
            position: fixed;
            top: 70px;
            right: 20px;
            width: 280px;
            background: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            border: 1px solid var(--gray-200);
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all var(--transition-fast);
        }

        .user-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .user-dropdown-header {
            padding: var(--space-lg);
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            gap: var(--space-md);
        }

        .user-avatar.large {
            width: 48px;
            height: 48px;
            font-size: 1.125rem;
        }

        .user-dropdown-menu {
            padding: var(--space-sm);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: var(--space-md);
            padding: var(--space-md);
            color: var(--gray-700);
            text-decoration: none;
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .dropdown-item:hover {
            background: var(--gray-50);
            color: var(--primary);
        }

        .dropdown-item.logout:hover {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error);
        }

        .dropdown-divider {
            height: 1px;
            background: var(--gray-200);
            margin: var(--space-sm) 0;
        }
        
        /* ===== CONTENT AREA ===== */
        .content {
            padding: var(--space-xl);
        }
        
        /* ===== WELCOME SECTION ===== */
        .welcome-section {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: var(--radius-xl);
            padding: calc(var(--space-2xl) * 0.85);
            margin-bottom: var(--space-xl);
            color: white;
            position: relative;
            overflow: hidden;
        }
        
        .welcome-section::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(50%, -50%);
        }
        
        .welcome-content {
            position: relative;
            z-index: 1;
        }
        
        .welcome-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: var(--space-sm);
        }
        
        .welcome-subtitle {
            font-size: 1.125rem;
            opacity: 0.9;
            margin-bottom: var(--space-lg);
        }
        
        .welcome-meta {
            display: flex;
            gap: var(--space-xl);
            font-size: 0.875rem;
            opacity: 0.8;
        }
        
        .welcome-meta-item {
            display: flex;
            flex-direction: column;
            gap: var(--space-xs);
        }
        
        .welcome-meta-label {
            opacity: 0.7;
        }
        
        .welcome-meta-value {
            font-weight: 600;
        }

        /* ===== PETTY CASH BLUR EFFECT ===== */
        .petty-cash-blur {
            filter: blur(8px);
            transition: filter var(--transition-base);
            cursor: pointer;
            user-select: none;
        }

        .petty-cash-blur.revealed {
            filter: blur(0);
        }

        .petty-cash-blur:hover {
            filter: blur(4px);
        }

        .petty-cash-blur.revealed:hover {
            filter: blur(0);
        }
        
        /* ===== STATS GRID ===== */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--space-lg);
            margin-bottom: var(--space-xl);
        }
        
        .stat-card {
            background: white;
            border-radius: var(--radius-lg);
            padding: calc(var(--space-xl) * 0.6); /* Reduced by 25% more (was 0.8, now 0.6) */
            border: 1px solid var(--gray-200);
            transition: all var(--transition-base);
            position: relative;
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary), var(--secondary));
        }
        
        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-lg);
        }
        
        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-md);
            background: var(--gray-100);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
            font-size: 1.25rem;
        }
        
        .stat-trend {
            display: flex;
            align-items: center;
            gap: var(--space-xs);
            font-size: 0.875rem;
            font-weight: 600;
            padding: var(--space-xs) var(--space-sm);
            border-radius: var(--radius-sm);
        }
        
        .stat-trend.positive {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }
        
        .stat-trend.negative {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error);
        }
        
        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: var(--space-xs);
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: var(--gray-600);
            font-weight: 500;
        }

        /* ===== QUICK ACTIONS ===== */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: calc(var(--space-lg) * 0.9); /* Reduced by 10% */
            margin-bottom: var(--space-xl);
        }

        .action-card {
            background: transparent;
            border: none;
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
            text-decoration: none;
            color: inherit;
            transition: all var(--transition-base);
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            min-height: 140px;
            justify-content: flex-start;
            position: relative;
            overflow: hidden;
        }

        .action-card:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
            text-decoration: none;
            color: var(--primary);
        }

        .action-card:active {
            transform: translateY(0) scale(0.98);
        }

        .action-icon {
            width: 80px;
            height: 80px;
            border-radius: var(--radius-md);
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.2rem;
            margin-bottom: var(--space-md);
            transition: all var(--transition-base);
            flex-shrink: 0;
        }

        .action-card:hover .action-icon {
            transform: scale(1.1);
        }

        .action-title {
            font-size: 1rem;
            font-weight: 500;
            color: var(--gray-900);
            margin-bottom: 0;
            margin-top: var(--space-sm);
            line-height: 1.2;
            text-align: center;
            height: 2.4em;
            display: flex;
            align-items: flex-start;
            justify-content: center;
        }



        /* ===== CONTENT GRID ===== */
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--space-xl);
            margin-bottom: var(--space-xl);
        }

        /* ===== ACTIVITY FEED ===== */
        .activity-section {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            overflow: hidden;
        }

        .section-header {
            padding: var(--space-xl);
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .activity-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: flex-start;
            gap: var(--space-md);
            padding: calc(var(--space-lg) * 0.75) var(--space-xl); /* Reduced vertical padding by 25% */
            border-bottom: 1px solid var(--gray-100);
            transition: all var(--transition-fast);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-item:hover {
            background: var(--gray-50);
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            flex-shrink: 0;
        }

        .activity-icon.success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }

        .activity-icon.warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning);
        }

        .activity-icon.info {
            background: rgba(59, 130, 246, 0.1);
            color: var(--info);
        }

        .activity-content {
            flex: 1;
            min-width: 0;
        }

        .activity-message {
            font-size: 0.875rem;
            color: var(--gray-900);
            margin-bottom: var(--space-xs);
            line-height: 1.4;
        }

        .activity-time {
            font-size: 0.75rem;
            color: var(--gray-500);
        }

        /* ===== NOTIFICATIONS PANEL ===== */
        .notifications-section {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            overflow: hidden;
        }

        .notification-item {
            padding: var(--space-lg) var(--space-xl);
            border-bottom: 1px solid var(--gray-100);
            transition: all var(--transition-fast);
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-item:hover {
            background: var(--gray-50);
        }

        .notification-header {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            margin-bottom: var(--space-sm);
        }

        .notification-type {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .notification-type.info {
            background: var(--info);
        }

        .notification-type.warning {
            background: var(--warning);
        }

        .notification-type.success {
            background: var(--success);
        }

        .notification-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--gray-900);
        }

        .notification-message {
            font-size: 0.875rem;
            color: var(--gray-600);
            line-height: 1.4;
            margin-bottom: var(--space-sm);
        }

        .notification-action {
            font-size: 0.75rem;
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
        }

        .notification-action:hover {
            color: var(--primary-dark);
        }

        /* ===== NOTIFICATION TRAY ===== */
        .notification-tray {
            position: fixed;
            top: var(--header-height);
            right: var(--space-lg);
            width: 320px;
            max-height: 400px;
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            z-index: 10000;
            transform: translateY(-10px);
            opacity: 0;
            visibility: hidden;
            transition: all var(--transition-base);
            overflow: hidden;
        }

        .notification-tray.show {
            transform: translateY(0);
            opacity: 1;
            visibility: visible;
        }

        .notification-tray-header {
            padding: var(--space-lg);
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .notification-tray-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }

        .notification-tray-close {
            background: none;
            border: none;
            color: var(--gray-500);
            cursor: pointer;
            padding: var(--space-xs);
            border-radius: var(--radius-sm);
            transition: all var(--transition-fast);
        }

        .notification-tray-close:hover {
            background: var(--gray-200);
            color: var(--gray-700);
        }

        .notification-tray-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .notification-tray-item {
            padding: calc(var(--space-lg) * 0.7); /* Reduced by 30% */
            border-bottom: 1px solid var(--gray-100);
            transition: all var(--transition-fast);
            cursor: pointer;
            position: relative;
        }

        .notification-tray-item:last-child {
            border-bottom: none;
        }

        .notification-tray-item:hover {
            background: var(--gray-50);
        }

        .notification-tray-item.unread {
            background: rgba(99, 102, 241, 0.05);
            border-left: 3px solid var(--primary);
        }

        .notification-close {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: none;
            border: none;
            color: var(--gray-400);
            cursor: pointer;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-fast);
        }

        .notification-close:hover {
            background: var(--gray-200);
            color: var(--gray-600);
        }

        .notification-mark-read {
            position: absolute;
            bottom: 0.5rem;
            right: 0.5rem;
            background: var(--primary);
            border: none;
            color: white;
            cursor: pointer;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-fast);
            font-size: 0.75rem;
        }

        .notification-mark-read:hover {
            background: var(--primary-dark);
            transform: scale(1.1);
        }

        .notification-tray-footer {
            padding: var(--space-md);
            border-top: 1px solid var(--gray-200);
            background: var(--gray-50);
            display: flex;
            gap: var(--space-sm);
            justify-content: space-between;
        }

        /* ===== MOBILE HEADER ===== */
        .mobile-header {
            display: none;
            background: white;
            border-bottom: 1px solid var(--gray-200);
            padding: var(--space-md) var(--space-lg);
            height: var(--header-height);
            align-items: center;
            justify-content: space-between;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1001;
        }

        .mobile-header .logo {
            gap: var(--space-sm);
        }

        .mobile-header .logo-icon {
            width: 32px;
            height: 32px;
            font-size: 1rem;
        }

        .mobile-header .logo-text {
            font-size: 1.25rem;
        }

        .mobile-actions {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
        }

        .mobile-user-menu {
            position: relative;
            cursor: pointer;
        }

        .mobile-user-menu .user-avatar {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all var(--transition-fast);
        }

        .mobile-user-menu:hover .user-avatar {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        /* ===== BOTTOM NAVIGATION ===== */
        .bottom-nav {
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            width: 100%;
            background: white;
            background-color: white;
            border-top: 1px solid var(--gray-200);
            height: var(--bottom-nav-height);
            z-index: 1000;
            padding: var(--space-xs) 0 calc(var(--space-xs) + env(safe-area-inset-bottom));
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            -webkit-backdrop-filter: blur(20px);
            backdrop-filter: blur(20px);
        }

        .bottom-nav-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            height: 100%;
            align-items: center;
            background: white;
            width: 100%;
        }

        .bottom-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: var(--gray-500);
            font-size: 0.75rem;
            font-weight: 500;
            transition: all var(--transition-fast);
            min-height: 44px;
            padding: var(--space-xs) var(--space-sm);
            position: relative;
            background: white;
        }

        .bottom-nav-item:hover {
            color: var(--primary);
            text-decoration: none;
        }

        .bottom-nav-item.active {
            color: var(--primary);
        }

        .bottom-nav-item.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 32px;
            height: 3px;
            background: var(--primary);
            border-radius: 0 0 var(--radius-sm) var(--radius-sm);
        }

        .bottom-nav-item i {
            font-size: 1.25rem;
            margin-bottom: var(--space-xs);
        }

        /* ===== RESPONSIVE DESIGN ===== */
        @media (max-width: 1400px) {
            .quick-actions {
                grid-template-columns: repeat(6, 1fr);
                gap: var(--space-md);
            }
        }

        @media (max-width: 1024px) {
            .content-grid {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                grid-template-columns: repeat(4, 1fr);
                gap: var(--space-md);
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding-top: var(--header-height);
                padding-bottom: var(--bottom-nav-height);
            }

            .mobile-header {
                display: flex;
            }

            .header {
                display: none;
            }

            .bottom-nav {
                display: block;
                position: fixed !important;
                bottom: 0 !important;
                left: 0 !important;
                right: 0 !important;
                background: white !important;
                z-index: 1000 !important;
            }

            .content {
                padding: var(--space-md); /* Reduced from lg */
            }

            .welcome-section {
                padding: calc(var(--space-lg) * 0.85); /* Reduced by 15% */
            }

            .welcome-title {
                font-size: 1.25rem; /* Reduced from 1.5rem (20% less) */
            }

            .welcome-meta {
                gap: var(--space-md); /* Reduced spacing */
            }

            .welcome-meta-item {
                gap: var(--space-xs); /* Tighter spacing */
            }

            .welcome-meta-label {
                font-size: 0.75rem; /* Smaller labels */
            }

            .welcome-meta-value {
                font-size: 0.875rem; /* Smaller values */
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: var(--space-md); /* Reduced from lg */
            }

            .stat-card {
                padding: calc(var(--space-md) * 0.8); /* Reduced by 20% */
            }

            .stat-icon {
                width: 38px; /* Reduced from 48px (20% less) */
                height: 38px;
                font-size: 1rem; /* Reduced font size */
            }

            .stat-value {
                font-size: 2rem; /* Reduced from 2.5rem (20% less) */
            }

            .stat-label {
                font-size: 0.75rem; /* Reduced font size */
            }

            .quick-actions {
                grid-template-columns: repeat(4, 1fr);
                gap: var(--space-sm); /* Reduced from md */
            }

            .action-card {
                padding: var(--space-md); /* Reduced from lg (20% less) */
                min-height: 110px; /* Fixed height for consistent alignment */
                justify-content: flex-start;
            }

            .action-icon {
                width: 46px; /* 20% larger than before */
                height: 46px;
                font-size: 1.2rem; /* 20% larger font */
                margin-bottom: var(--space-sm);
                flex-shrink: 0;
                margin-top: var(--space-xs);
            }

            .action-title {
                font-size: 0.8rem;
                font-weight: 500;
                margin-top: var(--space-xs);
                line-height: 1.1;
                text-align: center;
                height: 2.4em;
                display: flex;
                align-items: flex-start;
                justify-content: center;
                word-break: break-word;
                hyphens: auto;
            }



            /* User menu compact */
            .user-avatar {
                width: 28px; /* Reduced from 36px (20% less) */
                height: 28px;
                font-size: 0.75rem; /* Smaller font */
            }

            .user-name {
                font-size: 0.75rem; /* Reduced from 0.875rem */
            }

            .user-role {
                font-size: 0.65rem; /* Reduced from 0.75rem */
            }
        }

        @media (max-width: 480px) {
            .content {
                padding: var(--space-sm); /* Further reduced for small screens */
            }

            .welcome-section {
                padding: calc(var(--space-md) * 0.85); /* Reduced by 15% */
            }

            .welcome-title {
                font-size: 1.125rem; /* Further reduced */
            }

            .welcome-meta {
                gap: var(--space-sm); /* Tighter spacing */
            }

            .stats-grid {
                gap: var(--space-sm); /* Reduced from md */
            }

            .stat-card {
                padding: calc(var(--space-sm) * 0.8); /* Reduced by 20% */
            }

            .stat-icon {
                width: 32px; /* Further reduced (20% less) */
                height: 32px;
                font-size: 0.875rem;
            }

            .stat-value {
                font-size: 1.75rem; /* Reduced from 2rem (20% less) */
            }

            .stat-label {
                font-size: 0.7rem; /* Further reduced */
            }

            .quick-actions {
                grid-template-columns: repeat(4, 1fr);
                gap: var(--space-xs); /* Minimal gap */
            }

            .action-card {
                padding: var(--space-sm); /* Reduced from md (20% less) */
                min-height: 90px; /* Fixed height for consistent alignment */
                justify-content: flex-start;
            }

            .action-icon {
                width: 38px; /* 20% larger than before */
                height: 38px;
                font-size: 1.05rem; /* 20% larger font */
                margin-bottom: var(--space-xs);
                flex-shrink: 0;
                margin-top: var(--space-xs);
            }

            .action-title {
                font-size: 0.75rem;
                font-weight: 500;
                margin-top: var(--space-xs);
                line-height: 1.0;
                text-align: center;
                height: 2.2em;
                display: flex;
                align-items: flex-start;
                justify-content: center;
                word-break: break-word;
                hyphens: auto;
            }



            /* Ultra-compact user menu */
            .user-avatar {
                width: 24px; /* Further reduced */
                height: 24px;
                font-size: 0.7rem;
            }

            .user-name {
                font-size: 0.7rem;
            }

            .user-role {
                font-size: 0.6rem;
            }
        }

        /* ===== ANIMATIONS ===== */
        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        .fade-in-up.delay-1 { animation-delay: 0.1s; }
        .fade-in-up.delay-2 { animation-delay: 0.2s; }
        .fade-in-up.delay-3 { animation-delay: 0.3s; }
        .fade-in-up.delay-4 { animation-delay: 0.4s; }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* ===== LOADING STATES ===== */
        .loading-skeleton {
            background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: var(--radius-sm);
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-5px); }
            60% { transform: translateY(-3px); }
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* ===== UTILITIES ===== */
        .text-center { text-align: center; }
        .text-left { text-align: left; }
        .text-right { text-align: right; }

        .d-none { display: none; }
        .d-block { display: block; }
        .d-flex { display: flex; }
        .d-grid { display: grid; }

        .align-items-center { align-items: center; }
        .justify-content-center { justify-content: center; }
        .justify-content-between { justify-content: space-between; }

        .gap-sm { gap: var(--space-sm); }
        .gap-md { gap: var(--space-md); }
        .gap-lg { gap: var(--space-lg); }

        .mb-0 { margin-bottom: 0; }
        .mb-sm { margin-bottom: var(--space-sm); }
        .mb-md { margin-bottom: var(--space-md); }
        .mb-lg { margin-bottom: var(--space-lg); }
        .mb-xl { margin-bottom: var(--space-xl); }
    </style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
                <!-- Welcome Section -->
                <section class="welcome-section fade-in-up">
                    <div class="welcome-content">
                        <h2 class="welcome-title">Welcome back, <?= htmlspecialchars($user['username']) ?>!</h2>

                        <div class="welcome-meta">
                            <div class="welcome-meta-item">
                                <div class="welcome-meta-label">Last login</div>
                                <div class="welcome-meta-value">
                                    <?php
                                    if ($user['last_login']) {
                                        $lastLoginUTC = new DateTime($user['last_login'], new DateTimeZone('UTC'));
                                        $lastLoginUTC->setTimezone(new DateTimeZone('Asia/Kolkata'));
                                        echo $lastLoginUTC->format('M j, Y \a\t g:i A') . ' IST';
                                    } else {
                                        echo 'First time';
                                    }
                                    ?>
                                </div>
                            </div>
                            <div class="welcome-meta-item">
                                <div class="welcome-meta-label">Petty Cash Balance</div>
                                <div class="welcome-meta-value">
                                    <span id="pettyCashBalance" class="petty-cash-blur" onclick="togglePettyCash()">
                                        ₹<?= number_format($petty_cash_balance ?? 25000, 2) ?>
                                    </span>
                                    <i class="fas fa-eye" id="pettyCashIcon" onclick="togglePettyCash()" style="margin-left: 8px; cursor: pointer; opacity: 0.7;"></i>
                                </div>
                            </div>

                        </div>
                    </div>
                </section>

                <!-- Statistics Grid -->
                <section class="stats-grid fade-in-up delay-1">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-trend positive">
                                <i class="fas fa-arrow-up"></i>
                                +12%
                            </div>
                        </div>
                        <div class="stat-value"><?= number_format($stats['total_users']) ?></div>
                        <div class="stat-label">Total Users</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="stat-trend positive">
                                <i class="fas fa-arrow-up"></i>
                                +8%
                            </div>
                        </div>
                        <div class="stat-value"><?= number_format($stats['active_users']) ?></div>
                        <div class="stat-label">Active Users</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-trend positive">
                                <i class="fas fa-arrow-up"></i>
                                +24%
                            </div>
                        </div>
                        <div class="stat-value"><?= number_format($stats['recent_logins']) ?></div>
                        <div class="stat-label">Recent Logins</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon">
                                <i class="fas fa-heartbeat"></i>
                            </div>
                            <div class="stat-trend positive">
                                <i class="fas fa-check"></i>
                                Healthy
                            </div>
                        </div>
                        <div class="stat-value"><?= $stats['system_health']['score'] ?>%</div>
                        <div class="stat-label">System Health</div>
                    </div>
                </section>

                <!-- Quick Actions -->
                <section class="quick-actions fade-in-up delay-2">
                    <?php if (isset($quick_actions) && !empty($quick_actions)): ?>
                        <?php foreach ($quick_actions as $action): ?>
                        <a href="<?= $action['url'] ?>" class="action-card">
                            <div class="action-icon">
                                <i class="<?= $action['icon'] ?>"></i>
                            </div>
                            <div class="action-title"><?= $action['title'] ?></div>
                        </a>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <!-- Default quick actions if none provided -->
                        <a href="/admin/users" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="action-title">Manage Users</div>
                        </a>
                        <a href="/admin/roles" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-user-shield"></i>
                            </div>
                            <div class="action-title">Manage Roles</div>
                        </a>
                        <a href="/admin/system" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="action-title">System Settings</div>
                        </a>
                        <a href="/status" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="action-title">System Status</div>
                        </a>
                    <?php endif; ?>
                </section>

                <!-- Content Grid -->
                <div class="content-grid fade-in-up delay-3">
                    <!-- Recent Activity -->
                    <div class="activity-section">
                        <div class="section-header">
                            <h3 class="section-title">Recent Activity</h3>
                            <a href="/admin/activity" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-list me-1"></i>
                                See All
                            </a>
                        </div>

                        <div class="activity-list">
                            <?php if (!isset($recent_activity) || empty($recent_activity)): ?>
                            <div class="activity-item">
                                <div class="activity-icon info">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-message">No recent activity to display.</div>
                                    <div class="activity-time">System is running smoothly</div>
                                </div>
                            </div>
                            <?php else: ?>
                            <?php foreach ($recent_activity as $activity): ?>
                            <?php
                            // Convert activity timestamp to IST
                            $activityTimeIST = null;
                            if ($activity['timestamp']) {
                                $activityUTC = new DateTime($activity['timestamp'], new DateTimeZone('UTC'));
                                $activityUTC->setTimezone(new DateTimeZone('Asia/Kolkata'));
                                $activityTimeIST = $activityUTC->format('M j, Y \a\t g:i A') . ' IST';
                            }
                            ?>
                            <div class="activity-item">
                                <div class="activity-icon <?= $activity['color'] ?>">
                                    <i class="<?= $activity['icon'] ?>"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-message"><?= $activity['message'] ?></div>
                                    <div class="activity-time"><?= $activityTimeIST ?: 'Unknown time' ?></div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Notifications -->
                    <div class="notifications-section">
                        <div class="section-header">
                            <h3 class="section-title">Notifications</h3>
                        </div>

                        <?php if (!isset($notifications) || empty($notifications)): ?>
                        <div class="notification-item">
                            <div class="notification-header">
                                <div class="notification-type info"></div>
                                <div class="notification-title">All Clear</div>
                            </div>
                            <div class="notification-message">No notifications at this time.</div>
                        </div>
                        <?php else: ?>
                        <?php foreach ($notifications as $notification): ?>
                        <div class="notification-item">
                            <div class="notification-header">
                                <div class="notification-type <?= $notification['type'] ?>"></div>
                                <div class="notification-title"><?= $notification['title'] ?></div>
                            </div>
                            <div class="notification-message"><?= $notification['message'] ?></div>
                            <?php if (isset($notification['action_url'])): ?>
                            <a href="<?= $notification['action_url'] ?>" class="notification-action">
                                <?= $notification['action_text'] ?>
                            </a>
                            <?php endif; ?>
                        </div>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Bottom Navigation -->
    <nav class="bottom-nav">
        <div class="bottom-nav-grid">
            <a href="/dashboard" class="bottom-nav-item active">
                <i class="fas fa-home"></i>
                <span>Dashboard</span>
            </a>

            <a href="/admin/users" class="bottom-nav-item">
                <i class="fas fa-users"></i>
                <span>Users</span>
            </a>

            <a href="/admin/users/create" class="bottom-nav-item">
                <i class="fas fa-plus-circle"></i>
                <span>Add</span>
            </a>

            <a href="/admin/system" class="bottom-nav-item">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>

            <a href="/auth/logout" class="bottom-nav-item">
                <i class="fas fa-sign-out-alt"></i>
                <span>Logout</span>
            </a>
        </div>
    </nav>

    <!-- User Dropdown Menu -->
    <div class="user-dropdown" id="userDropdown">
        <div class="user-dropdown-header">
            <div class="user-avatar large">
                <?= strtoupper(substr($user['username'], 0, 1)) ?>
            </div>
            <div class="user-info">
                <div class="user-name"><?= htmlspecialchars($user['username']) ?></div>
                <div class="user-role">Administrator</div>
            </div>
        </div>
        <div class="user-dropdown-menu">
            <a href="/profile" class="dropdown-item">
                <i class="fas fa-user"></i>
                <span>Profile</span>
            </a>
            <a href="/admin/system" class="dropdown-item">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>
            <div class="dropdown-divider"></div>
            <a href="/auth/logout" class="dropdown-item logout">
                <i class="fas fa-sign-out-alt"></i>
                <span>Logout</span>
            </a>
        </div>
    </div>

    <!-- Notification Tray -->
    <div class="notification-tray" id="notificationTray">
        <div class="notification-tray-header">
            <h4 class="notification-tray-title">Notifications</h4>
            <div style="display: flex; gap: var(--space-sm);">
                <button class="notification-tray-close" onclick="sendTestNotification()" title="Test Notification" style="background: var(--primary); color: white;">
                    <i class="fas fa-bell"></i>
                </button>
                <button class="notification-tray-close" onclick="toggleNotifications()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="notification-tray-list" id="notificationList">
            <!-- Notifications will be loaded here -->
            <div id="notificationLoading" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Loading notifications...</p>
            </div>
        </div>
        <div class="notification-tray-footer">
            <button class="btn btn-sm btn-outline-primary" onclick="markAllAsRead()">
                <i class="fas fa-check-double me-1"></i>
                Mark All Read
            </button>
            <a href="/notifications" class="btn btn-sm btn-primary">
                <i class="fas fa-list me-1"></i>
                View All
            </a>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
        // ===== MODERN DASHBOARD JAVASCRIPT =====

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeAnimations();
            initializeInteractions();
            setupPWA();
        });

        // Initialize animations
        function initializeAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe all animated elements
            document.querySelectorAll('.fade-in-up').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                el.style.transition = 'all 0.6s ease-out';
                observer.observe(el);
            });
        }

        // Initialize interactions
        function initializeInteractions() {
            // Add click animations to cards
            document.querySelectorAll('.stat-card, .action-card').forEach(card => {
                card.addEventListener('mousedown', function() {
                    this.style.transform = 'translateY(-2px) scale(0.98)';
                });

                card.addEventListener('mouseup', function() {
                    this.style.transform = 'translateY(-2px) scale(1)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = '';
                });
            });

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        }

        // Toggle sidebar on mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }

        // Toggle notifications
        function toggleNotifications() {
            const tray = document.getElementById('notificationTray');
            tray.classList.toggle('show');

            // Load notifications when opening tray
            if (tray.classList.contains('show')) {
                loadNotifications();
                setTimeout(() => {
                    document.addEventListener('click', closeNotificationTray);
                }, 100);
            } else {
                document.removeEventListener('click', closeNotificationTray);
            }
        }

        // Load notifications
        function loadNotifications() {
            const loadingEl = document.getElementById('notificationLoading');
            const listEl = document.getElementById('notificationList');

            loadingEl.style.display = 'block';

            fetch('/notifications/')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        renderNotifications(data.data);
                        updateNotificationBadge(data.unread_count);
                    } else {
                        showNotificationError(data.message);
                    }
                })
                .catch(error => {
                    console.error('Error loading notifications:', error);
                    if (error.message.includes('Failed to fetch') || !navigator.onLine) {
                        showNotificationError('No internet connection. Please check your network.');
                    } else {
                        showNotificationError('Failed to load notifications.');
                    }
                })
                .finally(() => {
                    loadingEl.style.display = 'none';
                });
        }

        // Render notifications
        function renderNotifications(notifications) {
            const listEl = document.getElementById('notificationList');
            const loadingEl = document.getElementById('notificationLoading');

            if (notifications.length === 0) {
                listEl.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-bell-slash text-muted" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                        <p class="text-muted">No notifications</p>
                    </div>
                `;
                return;
            }

            const notificationHTML = notifications.map(notification => `
                <div class="notification-tray-item ${!notification.is_read ? 'unread' : ''}" data-id="${notification.id}">
                    <div class="notification-header">
                        <div class="notification-type ${notification.type}"></div>
                        <div class="notification-title">${notification.title}</div>
                        <button class="notification-close" onclick="deleteNotification(${notification.id})" title="Delete">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="notification-message">${notification.message}</div>
                    <div class="notification-time">
                        <i class="fas fa-clock me-1"></i>
                        ${notification.time_ago}
                        <small class="text-muted ms-2">${new Date(notification.created_at).toLocaleString()}</small>
                    </div>
                    ${!notification.is_read ? `<button class="notification-mark-read" onclick="markAsRead(${notification.id})" title="Mark as read"><i class="fas fa-check"></i></button>` : ''}
                </div>
            `).join('');

            listEl.innerHTML = notificationHTML;
        }

        // Show notification error
        function showNotificationError(message) {
            const listEl = document.getElementById('notificationList');
            listEl.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle text-warning" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <p class="text-muted">${message}</p>
                    <button class="btn btn-sm btn-outline-primary" onclick="loadNotifications()">
                        <i class="fas fa-refresh me-1"></i>
                        Try Again
                    </button>
                </div>
            `;
        }

        // Mark notification as read
        function markAsRead(notificationId) {
            fetch(`/notifications/mark-read/${notificationId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove unread class
                    const notificationEl = document.querySelector(`[data-id="${notificationId}"]`);
                    if (notificationEl) {
                        notificationEl.classList.remove('unread');
                        const markReadBtn = notificationEl.querySelector('.notification-mark-read');
                        if (markReadBtn) {
                            markReadBtn.remove();
                        }
                    }
                    updateNotificationBadge(data.unread_count);
                }
            })
            .catch(error => {
                console.error('Error marking notification as read:', error);
            });
        }

        // Mark all notifications as read
        function markAllAsRead() {
            fetch('/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove all unread classes
                    document.querySelectorAll('.notification-tray-item.unread').forEach(el => {
                        el.classList.remove('unread');
                        const markReadBtn = el.querySelector('.notification-mark-read');
                        if (markReadBtn) {
                            markReadBtn.remove();
                        }
                    });
                    updateNotificationBadge(0);
                }
            })
            .catch(error => {
                console.error('Error marking all notifications as read:', error);
            });
        }

        // Delete notification
        function deleteNotification(notificationId) {
            fetch(`/notifications/${notificationId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove notification element
                    const notificationEl = document.querySelector(`[data-id="${notificationId}"]`);
                    if (notificationEl) {
                        notificationEl.style.animation = 'slideOutRight 0.3s ease';
                        setTimeout(() => {
                            notificationEl.remove();
                            // Check if no notifications left
                            const remainingNotifications = document.querySelectorAll('.notification-tray-item');
                            if (remainingNotifications.length === 0) {
                                renderNotifications([]);
                            }
                        }, 300);
                    }
                    updateNotificationBadge(data.unread_count);
                }
            })
            .catch(error => {
                console.error('Error deleting notification:', error);
            });
        }

        // Update notification badge
        function updateNotificationBadge(count) {
            const badges = document.querySelectorAll('.notification-badge');
            badges.forEach(badge => {
                if (count > 0) {
                    badge.textContent = count;
                    badge.style.display = 'inline-block';
                } else {
                    badge.style.display = 'none';
                }
            });
        }

        function closeNotificationTray(e) {
            const tray = document.getElementById('notificationTray');
            const button = document.querySelector('[onclick="toggleNotifications()"]');

            if (!tray.contains(e.target) && !button.contains(e.target)) {
                tray.classList.remove('show');
                document.removeEventListener('click', closeNotificationTray);
            }
        }

        // Toggle user menu (desktop)
        function toggleUserMenu() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('show');

            // Close when clicking outside
            if (dropdown.classList.contains('show')) {
                setTimeout(() => {
                    document.addEventListener('click', closeUserMenuOnOutsideClick);
                }, 100);
            }
        }

        // Toggle mobile user menu
        function toggleMobileUserMenu() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('show');

            // Adjust position for mobile
            if (window.innerWidth <= 768) {
                dropdown.style.top = '60px';
                dropdown.style.right = '10px';
                dropdown.style.width = '260px';
            }

            // Close when clicking outside
            if (dropdown.classList.contains('show')) {
                setTimeout(() => {
                    document.addEventListener('click', closeUserMenuOnOutsideClick);
                }, 100);
            }
        }

        // Close user menu when clicking outside
        function closeUserMenuOnOutsideClick(e) {
            const dropdown = document.getElementById('userDropdown');
            const userMenu = document.querySelector('.user-menu');
            const mobileUserMenu = document.querySelector('.mobile-user-menu');

            if (!dropdown.contains(e.target) &&
                !userMenu?.contains(e.target) &&
                !mobileUserMenu?.contains(e.target)) {
                dropdown.classList.remove('show');
                document.removeEventListener('click', closeUserMenuOnOutsideClick);
            }
        }

        // Toggle petty cash visibility
        function togglePettyCash() {
            const balance = document.getElementById('pettyCashBalance');
            const icon = document.getElementById('pettyCashIcon');

            if (balance.classList.contains('revealed')) {
                balance.classList.remove('revealed');
                icon.className = 'fas fa-eye';
                icon.style.opacity = '0.7';
            } else {
                balance.classList.add('revealed');
                icon.className = 'fas fa-eye-slash';
                icon.style.opacity = '1';
            }
        }

        // Setup PWA functionality
        function setupPWA() {
            // Register service worker if available
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered:', registration);

                        // Request notification permission
                        if ('Notification' in window && 'serviceWorker' in navigator) {
                            requestNotificationPermission();
                        }
                    })
                    .catch(error => {
                        console.log('SW registration failed:', error);
                    });
            }

            // Handle viewport height for mobile
            function setVH() {
                const vh = window.innerHeight * 0.01;
                document.documentElement.style.setProperty('--vh', `${vh}px`);
            }

            setVH();
            window.addEventListener('resize', setVH);
            window.addEventListener('orientationchange', setVH);

            // Setup pull-to-refresh
            setupPullToRefresh();

            // Setup add-to-homescreen prompt
            setupInstallPrompt();
        }

        // Pull-to-refresh functionality
        function setupPullToRefresh() {
            let startY = 0;
            let currentY = 0;
            let pullDistance = 0;
            let isPulling = false;
            const refreshThreshold = 80;

            // Create pull-to-refresh indicator
            const pullIndicator = document.createElement('div');
            pullIndicator.innerHTML = `
                <div id="pull-refresh-indicator" style="
                    position: fixed;
                    top: -60px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: white;
                    padding: 12px 20px;
                    border-radius: 25px;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                    z-index: 9999;
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    font-size: 14px;
                    font-weight: 500;
                    color: var(--gray-700);
                    border: 1px solid var(--gray-200);
                    opacity: 0;
                ">
                    <i class="fas fa-arrow-down" style="font-size: 16px; animation: bounce 2s infinite;"></i>
                    <span>Pull down to refresh</span>
                </div>
            `;
            document.body.appendChild(pullIndicator);

            const indicator = document.getElementById('pull-refresh-indicator');

            // Touch events
            document.addEventListener('touchstart', function(e) {
                if (window.scrollY === 0) {
                    startY = e.touches[0].clientY;
                    isPulling = true;
                }
            });

            document.addEventListener('touchmove', function(e) {
                if (!isPulling || window.scrollY > 0) return;

                currentY = e.touches[0].clientY;
                pullDistance = Math.max(0, currentY - startY);

                if (pullDistance > 10) { // Start showing indicator after 10px pull
                    e.preventDefault();
                    const progress = Math.min(pullDistance / refreshThreshold, 1);

                    indicator.style.top = `${-60 + (progress * 80)}px`;
                    indicator.style.opacity = progress;

                    if (pullDistance >= refreshThreshold) {
                        indicator.querySelector('span').textContent = 'Release to refresh';
                        indicator.style.background = 'var(--primary)';
                        indicator.style.color = 'white';
                        indicator.querySelector('i').className = 'fas fa-sync-alt';
                        indicator.querySelector('i').style.animation = 'spin 1s linear infinite';
                    } else {
                        indicator.querySelector('span').textContent = 'Pull down to refresh';
                        indicator.style.background = 'white';
                        indicator.style.color = 'var(--gray-700)';
                        indicator.querySelector('i').className = 'fas fa-arrow-down';
                        indicator.querySelector('i').style.animation = 'bounce 2s infinite';
                    }
                }
            });

            document.addEventListener('touchend', function(e) {
                if (!isPulling) return;

                isPulling = false;

                if (pullDistance >= refreshThreshold) {
                    // Trigger refresh
                    indicator.querySelector('span').textContent = 'Refreshing...';
                    indicator.style.top = '20px';
                    indicator.style.background = 'var(--success)';
                    indicator.style.color = 'white';

                    setTimeout(() => {
                        window.location.reload();
                    }, 800);
                } else {
                    // Reset indicator
                    indicator.style.top = '-60px';
                    indicator.style.opacity = '0';
                    indicator.style.background = 'white';
                    indicator.style.color = 'var(--gray-700)';
                }

                pullDistance = 0;
            });
        }

        // Setup install prompt for PWA
        function setupInstallPrompt() {
            let deferredPrompt;

            window.addEventListener('beforeinstallprompt', (e) => {
                e.preventDefault();
                deferredPrompt = e;
                showInstallButton();
            });

            function showInstallButton() {
                // Check if already installed
                if (window.matchMedia('(display-mode: standalone)').matches) {
                    return; // Already installed
                }

                const installButton = document.createElement('div');
                installButton.innerHTML = `
                    <div id="install-prompt" style="
                        position: fixed;
                        bottom: 80px;
                        right: 20px;
                        background: var(--primary);
                        color: white;
                        padding: 12px 20px;
                        border-radius: 25px;
                        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                        z-index: 9998;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        font-size: 14px;
                        font-weight: 500;
                        cursor: pointer;
                        animation: slideInRight 0.5s ease;
                    ">
                        <i class="fas fa-download"></i>
                        <span>Install App</span>
                        <button onclick="dismissInstallPrompt()" style="
                            background: none;
                            border: none;
                            color: white;
                            margin-left: 10px;
                            cursor: pointer;
                            opacity: 0.7;
                        ">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                document.body.appendChild(installButton);

                document.getElementById('install-prompt').addEventListener('click', async () => {
                    if (deferredPrompt) {
                        deferredPrompt.prompt();
                        const { outcome } = await deferredPrompt.userChoice;
                        if (outcome === 'accepted') {
                            console.log('User accepted the install prompt');
                        }
                        deferredPrompt = null;
                        document.getElementById('install-prompt').remove();
                    }
                });
            }

            // Auto-hide install prompt after 10 seconds
            setTimeout(() => {
                const prompt = document.getElementById('install-prompt');
                if (prompt) {
                    prompt.style.animation = 'slideOutRight 0.5s ease';
                    setTimeout(() => prompt.remove(), 500);
                }
            }, 10000);
        }

        function dismissInstallPrompt() {
            const prompt = document.getElementById('install-prompt');
            if (prompt) {
                prompt.style.animation = 'slideOutRight 0.5s ease';
                setTimeout(() => prompt.remove(), 500);
            }
        }

        // Request notification permission
        async function requestNotificationPermission() {
            if (Notification.permission === 'default') {
                const permission = await Notification.requestPermission();
                if (permission === 'granted') {
                    console.log('Notification permission granted');
                    showWelcomeNotification();
                }
            } else if (Notification.permission === 'granted') {
                showWelcomeNotification();
            }
        }

        // Show welcome notification
        function showWelcomeNotification() {
            if ('serviceWorker' in navigator && 'Notification' in window) {
                navigator.serviceWorker.ready.then(registration => {
                    registration.showNotification('SmartFlo', {
                        body: 'Welcome to SmartFlo! You\'ll receive important system notifications here.',
                        icon: '/icons/icon-192x192.png',
                        badge: '/icons/icon-72x72.png',
                        vibrate: [200, 100, 200],
                        tag: 'welcome',
                        requireInteraction: false,
                        silent: false,
                        data: {
                            type: 'welcome',
                            url: '/dashboard'
                        }
                    });

                    // Play notification sound
                    playNotificationSound();
                });
            }
        }

        // Play notification sound
        function playNotificationSound() {
            try {
                // Create audio context for notification sound
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();

                // Create a simple notification beep
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.3);
            } catch (error) {
                console.log('Could not play notification sound:', error);
            }
        }

        // Send test notification (for demo purposes)
        function sendTestNotification() {
            if ('serviceWorker' in navigator && 'Notification' in window && Notification.permission === 'granted') {
                navigator.serviceWorker.ready.then(registration => {
                    registration.showNotification('SmartFlo System Alert', {
                        body: 'This is a test notification with sound. System is running smoothly.',
                        icon: '/icons/icon-192x192.png',
                        badge: '/icons/icon-72x72.png',
                        vibrate: [100, 50, 100, 50, 100],
                        tag: 'system-alert',
                        requireInteraction: true,
                        silent: false,
                        actions: [
                            {
                                action: 'view',
                                title: 'View Dashboard'
                            },
                            {
                                action: 'dismiss',
                                title: 'Dismiss'
                            }
                        ],
                        data: {
                            type: 'system-alert',
                            url: '/dashboard'
                        }
                    });

                    playNotificationSound();
                });
            }
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('sidebar');
            const mobileHeader = document.querySelector('.mobile-header');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(e.target) &&
                !mobileHeader.contains(e.target) &&
                sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
            }
        });

        // Auto-refresh dashboard data every 5 minutes
        setInterval(function() {
            refreshDashboardData();
        }, 300000);

        function refreshDashboardData() {
            // Add loading states
            document.querySelectorAll('.stat-value').forEach(el => {
                el.classList.add('loading-skeleton');
            });

            // Simulate data refresh (replace with actual API call)
            setTimeout(() => {
                document.querySelectorAll('.stat-value').forEach(el => {
                    el.classList.remove('loading-skeleton');
                });
            }, 1500);
        }
</script>
<?= $this->endSection() ?>
