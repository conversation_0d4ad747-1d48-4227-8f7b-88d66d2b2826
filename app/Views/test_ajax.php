<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?= csrf_hash() ?>">
    <title>AJAX Test - SmartFlo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>SmartFlo AJAX Debugging</h1>
    
    <div class="test-section">
        <h2>1. CSRF Token Test</h2>
        <p>Current CSRF Token: <code id="currentToken"><?= csrf_hash() ?></code></p>
        <button onclick="testCSRFToken()">Test CSRF Token Refresh</button>
        <div id="csrfResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. Authentication Test</h2>
        <button onclick="testAuth()">Test Auth Status</button>
        <div id="authResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. Projects API Test</h2>
        <button onclick="testProjectsAPI()">Test Get Projects</button>
        <button onclick="testProjectStats()">Test Get Stats</button>
        <button onclick="testGetUsers()">Test Get Users</button>
        <div id="projectsResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. Reports API Test</h2>
        <button onclick="testReportsAPI()">Test Get Staff List</button>
        <button onclick="testDailyReport()">Test Daily Report</button>
        <div id="reportsResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>5. Raw Fetch Test</h2>
        <button onclick="testRawFetch()">Test Raw Fetch</button>
        <div id="rawResult" class="result"></div>
    </div>

    <script>
        // Utility function to display results
        function displayResult(elementId, type, message, data = null) {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.innerHTML = `
                <strong>${type.toUpperCase()}:</strong> ${message}
                ${data ? `<pre>${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;
        }

        // Test CSRF token refresh
        function testCSRFToken() {
            console.log('Testing CSRF token refresh...');
            
            fetch('/auth/csrf-token', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('CSRF Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('CSRF Response data:', data);
                if (data.success) {
                    document.getElementById('currentToken').textContent = data.csrf_token;
                    displayResult('csrfResult', 'success', 'CSRF token refreshed successfully', data);
                } else {
                    displayResult('csrfResult', 'error', 'Failed to refresh CSRF token', data);
                }
            })
            .catch(error => {
                console.error('CSRF Error:', error);
                displayResult('csrfResult', 'error', `Error: ${error.message}`);
            });
        }

        // Test authentication
        function testAuth() {
            console.log('Testing authentication...');
            
            fetch('/auth/status', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Auth Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Auth Response data:', data);
                displayResult('authResult', data.success ? 'success' : 'error', 
                    data.success ? 'Authentication successful' : 'Authentication failed', data);
            })
            .catch(error => {
                console.error('Auth Error:', error);
                displayResult('authResult', 'error', `Error: ${error.message}`);
            });
        }

        // Test Projects API
        function testProjectsAPI() {
            console.log('Testing Projects API...');
            
            fetch('/projects/getProjects', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Projects Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Projects Response data:', data);
                displayResult('projectsResult', data.success ? 'success' : 'error', 
                    data.success ? `Loaded ${data.projects ? data.projects.length : 0} projects` : 'Failed to load projects', data);
            })
            .catch(error => {
                console.error('Projects Error:', error);
                displayResult('projectsResult', 'error', `Error: ${error.message}`);
            });
        }

        // Test Project Stats
        function testProjectStats() {
            console.log('Testing Project Stats...');
            
            fetch('/projects/getStats', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Stats Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Stats Response data:', data);
                displayResult('projectsResult', data.success ? 'success' : 'error', 
                    data.success ? 'Stats loaded successfully' : 'Failed to load stats', data);
            })
            .catch(error => {
                console.error('Stats Error:', error);
                displayResult('projectsResult', 'error', `Error: ${error.message}`);
            });
        }

        // Test Get Users
        function testGetUsers() {
            console.log('Testing Get Users...');
            
            fetch('/projects/getUsers', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Users Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Users Response data:', data);
                displayResult('projectsResult', data.success ? 'success' : 'error', 
                    data.success ? `Loaded ${data.users ? data.users.length : 0} users` : 'Failed to load users', data);
            })
            .catch(error => {
                console.error('Users Error:', error);
                displayResult('projectsResult', 'error', `Error: ${error.message}`);
            });
        }

        // Test Reports API
        function testReportsAPI() {
            console.log('Testing Reports API...');
            
            fetch('/reports/getStaffList', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Reports Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Reports Response data:', data);
                displayResult('reportsResult', data.success ? 'success' : 'error', 
                    data.success ? `Loaded ${data.staff ? data.staff.length : 0} staff members` : 'Failed to load staff', data);
            })
            .catch(error => {
                console.error('Reports Error:', error);
                displayResult('reportsResult', 'error', `Error: ${error.message}`);
            });
        }

        // Test Daily Report
        function testDailyReport() {
            console.log('Testing Daily Report...');
            
            const today = new Date().toISOString().split('T')[0];
            
            fetch(`/reports/getDailyReport?date=${today}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Daily Report Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Daily Report Response data:', data);
                displayResult('reportsResult', data.success ? 'success' : 'error', 
                    data.success ? 'Daily report generated successfully' : 'Failed to generate daily report', data);
            })
            .catch(error => {
                console.error('Daily Report Error:', error);
                displayResult('reportsResult', 'error', `Error: ${error.message}`);
            });
        }

        // Test raw fetch without any interceptors
        function testRawFetch() {
            console.log('Testing raw fetch...');
            
            // Store original fetch
            const originalFetch = window.fetch;
            
            // Use original fetch to bypass any interceptors
            originalFetch('/projects/getProjects', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => {
                console.log('Raw Response status:', response.status);
                console.log('Raw Response headers:', [...response.headers.entries()]);
                return response.text();
            })
            .then(text => {
                console.log('Raw Response text:', text);
                try {
                    const data = JSON.parse(text);
                    displayResult('rawResult', 'success', 'Raw fetch successful', data);
                } catch (e) {
                    displayResult('rawResult', 'error', 'Response is not JSON', { responseText: text });
                }
            })
            .catch(error => {
                console.error('Raw Fetch Error:', error);
                displayResult('rawResult', 'error', `Error: ${error.message}`);
            });
        }

        // Log all network requests
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            console.log('FETCH REQUEST:', args);
            return originalFetch.apply(this, args)
                .then(response => {
                    console.log('FETCH RESPONSE:', response.status, response.statusText);
                    return response;
                });
        };
    </script>
</body>
</html>
