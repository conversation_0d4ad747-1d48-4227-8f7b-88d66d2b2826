<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-chart-line me-2"></i>Daily Reports</h1>
                    <p class="text-muted">Comprehensive staff and project activity reports</p>
                </div>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="exportReport()">
                        <i class="fas fa-download me-2"></i>Export Report
                    </button>
                    <button class="btn btn-outline-primary" onclick="refreshReport()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="reportDate" class="form-label">Report Date</label>
                            <input type="date" class="form-control" id="reportDate" value="<?= date('Y-m-d') ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="staffFilter" class="form-label">Staff Member</label>
                            <select class="form-control" id="staffFilter">
                                <option value="">All Staff</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="reportType" class="form-label">Report Type</label>
                            <select class="form-control" id="reportType">
                                <option value="daily">Daily Summary</option>
                                <option value="detailed">Detailed Activities</option>
                                <option value="productivity">Staff Productivity</option>
                                <option value="projects">Project Status</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button class="btn btn-success w-100" onclick="generateReport()">
                                <i class="fas fa-chart-bar me-2"></i>Generate Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Summary Cards -->
            <div class="row mb-4" id="summaryCards">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-primary">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalActivities">-</h3>
                            <p>Total Activities</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-success">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="activeProjects">-</h3>
                            <p>Active Projects</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-info">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalWorkTime">-</h3>
                            <p>Total Work Time</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-warning">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="completedProjects">-</h3>
                            <p>Completed Today</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Content -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-alt me-2"></i>Report Details
                    </h5>
                </div>
                <div class="card-body">
                    <div id="reportContent">
                        <div class="text-center py-5">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Select filters and click "Generate Report" to view data</h5>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h6>Generating Report...</h6>
                <p class="text-muted mb-0">Please wait while we compile your data</p>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.page-title h1 {
    margin: 0;
    color: #2c3e50;
    font-weight: 600;
}

.page-title p {
    margin: 0;
    font-size: 0.9rem;
}

.page-actions {
    display: flex;
    gap: 0.5rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-content h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
}

.stat-content p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

.report-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.report-table th,
.report-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.report-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.report-table tbody tr:hover {
    background-color: #f8f9fa;
}

.activity-item {
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    background: white;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.activity-title {
    font-weight: 600;
    color: #2c3e50;
}

.activity-time {
    font-size: 0.85rem;
    color: #6c757d;
}

.activity-details {
    font-size: 0.9rem;
    color: #495057;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-in-progress {
    background-color: #e3f2fd;
    color: #1976d2;
}

.status-completed {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.status-on-hold {
    background-color: #fff3e0;
    color: #f57c00;
}

.status-not-started {
    background-color: #f5f5f5;
    color: #616161;
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .page-actions {
        width: 100%;
        justify-content: stretch;
    }

    .page-actions .btn {
        flex: 1;
    }

    .stat-card {
        margin-bottom: 1rem;
    }
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
let currentReportData = null;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadStaffList();
    
    // Auto-generate report for today
    setTimeout(() => {
        generateReport();
    }, 500);
});

// Load staff list for filter
function loadStaffList() {
    fetch('/reports/getStaffList', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const select = document.getElementById('staffFilter');
            select.innerHTML = '<option value="">All Staff</option>';
            
            data.staff.forEach(staff => {
                const option = document.createElement('option');
                option.value = staff.id;
                option.textContent = `${staff.name} (${staff.role})`;
                select.appendChild(option);
            });
        }
    })
    .catch(error => {
        console.error('Error loading staff list:', error);
    });
}

// Generate report
function generateReport() {
    const date = document.getElementById('reportDate').value;
    const staffId = document.getElementById('staffFilter').value;
    const reportType = document.getElementById('reportType').value;



    if (!date) {
        showAlert('error', 'Please select a date');
        return;
    }

    // Show loading
    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    loadingModal.show();

    // Set a timeout to hide modal if request takes too long
    const timeoutId = setTimeout(() => {
        try {
            loadingModal.hide();
            showAlert('error', 'Request timed out. Please try again.');
        } catch (e) {
            console.error('Error hiding modal on timeout:', e);
        }
    }, 30000); // 30 second timeout

    let url = `/reports/getDailyReport?date=${date}`;
    if (staffId) {
        url += `&staff_id=${staffId}`;
    }



    fetch(url, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {

        // Clear timeout
        clearTimeout(timeoutId);

        // Always hide the modal first
        try {
            loadingModal.hide();
        } catch (e) {
            console.error('Error hiding modal:', e);
        }

        if (data.success) {
            currentReportData = data.data;
            updateSummaryCards(data.data.summary);
            displayReport(data.data, reportType);
            showAlert('success', 'Report generated successfully');
        } else {
            showAlert('error', data.message || 'Error generating report');
        }
    })
    .catch(error => {
        console.error('Error generating report:', error);

        // Clear timeout
        clearTimeout(timeoutId);

        // Always hide the modal
        try {
            loadingModal.hide();
        } catch (e) {
            console.error('Error hiding modal:', e);
        }

        showAlert('error', 'Network error: ' + error.message);
    });
}

// Update summary cards
function updateSummaryCards(summary) {
    document.getElementById('totalActivities').textContent = summary.total_activities;
    document.getElementById('activeProjects').textContent = summary.unique_projects;
    document.getElementById('totalWorkTime').textContent = summary.total_work_time_formatted;
    document.getElementById('completedProjects').textContent = summary.projects_completed;
}

// Display report based on type
function displayReport(data, type) {
    const container = document.getElementById('reportContent');
    
    switch (type) {
        case 'daily':
            container.innerHTML = generateDailySummary(data);
            break;
        case 'detailed':
            container.innerHTML = generateDetailedActivities(data);
            break;
        case 'productivity':
            container.innerHTML = generateProductivityReport(data);
            break;
        case 'projects':
            container.innerHTML = generateProjectStatusReport(data);
            break;
        default:
            container.innerHTML = generateDailySummary(data);
    }
}

// Generate daily summary report
function generateDailySummary(data) {
    return `
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-chart-pie me-2"></i>Project Status Summary</h6>
                <table class="report-table">
                    <tbody>
                        <tr><td>Projects Started</td><td><strong>${data.project_stats.projects_started}</strong></td></tr>
                        <tr><td>Projects Completed</td><td><strong>${data.project_stats.projects_completed}</strong></td></tr>
                        <tr><td>Projects On Hold</td><td><strong>${data.project_stats.projects_on_hold}</strong></td></tr>
                        <tr><td>Projects Resumed</td><td><strong>${data.project_stats.projects_resumed}</strong></td></tr>
                        <tr><td>Total Active Projects</td><td><strong>${data.project_stats.total_active_projects}</strong></td></tr>
                    </tbody>
                </table>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-users me-2"></i>Staff Productivity</h6>
                <table class="report-table">
                    <thead>
                        <tr><th>Staff Member</th><th>Activities</th></tr>
                    </thead>
                    <tbody>
                        ${data.staff_productivity.map(staff => `
                            <tr>
                                <td>${staff.name}</td>
                                <td><strong>${staff.activity_count}</strong></td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        </div>
        <div class="mt-4">
            <h6><i class="fas fa-clock me-2"></i>Work Sessions Summary</h6>
            <p><strong>Total Work Time:</strong> ${data.summary.total_work_time_formatted}</p>
            <p><strong>Number of Work Sessions:</strong> ${data.work_durations.length}</p>
            <p><strong>Average Session Time:</strong> ${formatDuration(data.summary.average_session_time)}</p>
        </div>
    `;
}

// Generate detailed activities report
function generateDetailedActivities(data) {
    if (data.activities.length === 0) {
        return '<div class="text-center py-4"><h6 class="text-muted">No activities found for this date</h6></div>';
    }

    return `
        <h6><i class="fas fa-list me-2"></i>Detailed Activities (${data.activities.length} total)</h6>
        <div class="activities-list">
            ${data.activities.map(activity => `
                <div class="activity-item">
                    <div class="activity-header">
                        <div class="activity-title">${activity.title}</div>
                        <div class="activity-time">${formatDateTime(activity.created_at)}</div>
                    </div>
                    <div class="activity-details">
                        <strong>Project:</strong> ${activity.project_name || 'N/A'} |
                        <strong>User:</strong> ${activity.username} |
                        <strong>Status:</strong> <span class="status-badge status-${activity.new_status}">${formatStatus(activity.new_status)}</span>
                        ${activity.notes ? `<br><strong>Notes:</strong> ${activity.notes}` : ''}
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

// Generate productivity report
function generateProductivityReport(data) {
    return `
        <h6><i class="fas fa-chart-bar me-2"></i>Staff Productivity Analysis</h6>
        <table class="report-table">
            <thead>
                <tr>
                    <th>Staff Member</th>
                    <th>Total Activities</th>
                    <th>Productivity Score</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                ${data.staff_productivity.map(staff => {
                    const score = Math.min(100, staff.activity_count * 10);
                    const status = score >= 80 ? 'Excellent' : score >= 60 ? 'Good' : score >= 40 ? 'Average' : 'Low';
                    const statusClass = score >= 80 ? 'success' : score >= 60 ? 'info' : score >= 40 ? 'warning' : 'danger';

                    return `
                        <tr>
                            <td>${staff.name}</td>
                            <td><strong>${staff.activity_count}</strong></td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-${statusClass}" style="width: ${score}%">${score}%</div>
                                </div>
                            </td>
                            <td><span class="badge bg-${statusClass}">${status}</span></td>
                        </tr>
                    `;
                }).join('')}
            </tbody>
        </table>

        <div class="mt-4">
            <h6><i class="fas fa-stopwatch me-2"></i>Work Duration Analysis</h6>
            ${data.work_durations.length > 0 ? `
                <table class="report-table">
                    <thead>
                        <tr><th>Staff</th><th>Project</th><th>Duration</th><th>Session Time</th></tr>
                    </thead>
                    <tbody>
                        ${data.work_durations.map(session => `
                            <tr>
                                <td>${session.user}</td>
                                <td>${session.project}</td>
                                <td><strong>${session.duration_formatted}</strong></td>
                                <td>${session.start_time ? formatTime(session.start_time) : 'N/A'} - ${session.end_time ? formatTime(session.end_time) : 'N/A'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            ` : '<p class="text-muted">No work sessions recorded for this date.</p>'}
        </div>
    `;
}

// Generate project status report
function generateProjectStatusReport(data) {
    return `
        <h6><i class="fas fa-building me-2"></i>Project Status Overview</h6>
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="text-center p-3 border rounded">
                    <h3 class="text-success">${data.project_stats.projects_started}</h3>
                    <p class="mb-0">Started Today</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center p-3 border rounded">
                    <h3 class="text-primary">${data.project_stats.projects_completed}</h3>
                    <p class="mb-0">Completed Today</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center p-3 border rounded">
                    <h3 class="text-warning">${data.project_stats.projects_on_hold}</h3>
                    <p class="mb-0">Put On Hold</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center p-3 border rounded">
                    <h3 class="text-info">${data.project_stats.projects_resumed}</h3>
                    <p class="mb-0">Resumed Today</p>
                </div>
            </div>
        </div>

        <h6><i class="fas fa-tasks me-2"></i>Recent Project Activities</h6>
        ${data.activities.filter(a => a.project_name).slice(0, 10).map(activity => `
            <div class="activity-item">
                <div class="activity-header">
                    <div class="activity-title">${activity.project_name}</div>
                    <div class="activity-time">${formatDateTime(activity.created_at)}</div>
                </div>
                <div class="activity-details">
                    <strong>Action:</strong> ${activity.title} |
                    <strong>By:</strong> ${activity.username} |
                    <strong>Status:</strong> <span class="status-badge status-${activity.new_status}">${formatStatus(activity.new_status)}</span>
                </div>
            </div>
        `).join('')}
    `;
}

// Utility functions
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('en-IN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });
}

function formatTime(timeString) {
    const date = new Date(timeString);
    return date.toLocaleTimeString('en-IN', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });
}

function formatStatus(status) {
    const statusMap = {
        'not_started': 'Not Started',
        'in_progress': 'In Progress',
        'on_hold': 'On Hold',
        'completed': 'Completed',
        'planning': 'Planning'
    };
    return statusMap[status] || status.replace('_', ' ');
}

function formatDuration(seconds) {
    if (seconds < 60) {
        return Math.round(seconds) + 's';
    } else if (seconds < 3600) {
        return Math.round(seconds / 60) + 'm';
    } else {
        return Math.round(seconds / 3600 * 10) / 10 + 'h';
    }
}

// Export report
function exportReport() {
    if (!currentReportData) {
        showAlert('error', 'No report data to export. Please generate a report first.');
        return;
    }

    const date = document.getElementById('reportDate').value;
    const reportType = document.getElementById('reportType').value;

    // Create CSV content
    let csvContent = `SmartFlo Daily Report - ${date}\n\n`;
    csvContent += `Report Type: ${reportType.charAt(0).toUpperCase() + reportType.slice(1)}\n`;
    csvContent += `Generated: ${new Date().toLocaleString()}\n\n`;

    // Add summary
    csvContent += `SUMMARY\n`;
    csvContent += `Total Activities,${currentReportData.summary.total_activities}\n`;
    csvContent += `Active Projects,${currentReportData.summary.unique_projects}\n`;
    csvContent += `Total Work Time,${currentReportData.summary.total_work_time_formatted}\n`;
    csvContent += `Projects Completed,${currentReportData.summary.projects_completed}\n\n`;

    // Add detailed data based on report type
    if (reportType === 'detailed') {
        csvContent += `DETAILED ACTIVITIES\n`;
        csvContent += `Time,Project,User,Action,Status\n`;
        currentReportData.activities.forEach(activity => {
            csvContent += `"${formatDateTime(activity.created_at)}","${activity.project_name || 'N/A'}","${activity.username}","${activity.title}","${formatStatus(activity.new_status)}"\n`;
        });
    }

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `smartflo-report-${date}-${reportType}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    showAlert('success', 'Report exported successfully!');
}

// Refresh report
function refreshReport() {
    generateReport();
}

// Show alert
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
<?= $this->endSection() ?>
