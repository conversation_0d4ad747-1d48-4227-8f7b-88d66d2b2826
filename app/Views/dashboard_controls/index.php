<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-th-large me-2"></i>
                    Dashboard Controls
                </h1>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createControlModal">
                    <i class="fas fa-plus me-2"></i>
                    Add Control
                </button>
            </div>

            <!-- Controls Grid -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        Dashboard Quick Buttons
                    </h5>
                </div>
                <div class="card-body">
                    <div id="controlsList">
                        <div class="text-center py-4">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Control Modal -->
<div class="modal fade" id="createControlModal" tabindex="-1" aria-labelledby="createControlModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createControlModalLabel">Add Dashboard Control</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createControlForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="controlTitle" class="form-label">Title</label>
                                <input type="text" class="form-control" id="controlTitle" name="title" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="controlIcon" class="form-label">Icon</label>
                                <input type="text" class="form-control" id="controlIcon" name="icon" placeholder="fas fa-home" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="controlDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="controlDescription" name="description" rows="2"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="controlActionType" class="form-label">Action Type</label>
                                <select class="form-select" id="controlActionType" name="action_type" required>
                                    <option value="url">URL</option>
                                    <option value="function">JavaScript Function</option>
                                    <option value="modal">Modal</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="controlActionValue" class="form-label">Action Value</label>
                                <input type="text" class="form-control" id="controlActionValue" name="action_value" placeholder="/dashboard" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="controlColor" class="form-label">Color</label>
                                <select class="form-select" id="controlColor" name="color" required>
                                    <option value="primary">Primary (Blue)</option>
                                    <option value="secondary">Secondary (Gray)</option>
                                    <option value="success">Success (Green)</option>
                                    <option value="danger">Danger (Red)</option>
                                    <option value="warning">Warning (Yellow)</option>
                                    <option value="info">Info (Cyan)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="controlRoles" class="form-label">Roles</label>
                                <input type="text" class="form-control" id="controlRoles" name="roles" placeholder="user,admin" required>
                                <small class="form-text text-muted">Comma-separated list of roles</small>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="controlSortOrder" class="form-label">Sort Order</label>
                                <input type="number" class="form-control" id="controlSortOrder" name="sort_order" value="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3 d-flex align-items-end">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="controlIsActive" name="is_active" checked>
                                    <label class="form-check-label" for="controlIsActive">
                                        Active
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" name="csrf_token" value="<?= csrf_hash() ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="createControlBtn">
                        <i class="fas fa-save me-2"></i>
                        Create Control
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.control-item {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.control-item:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.control-icon {
    width: 60px;
    height: 60px;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1rem;
}

.control-actions {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.control-item:hover .control-actions {
    opacity: 1;
}

.sortable-placeholder {
    border: 2px dashed #dee2e6;
    background: #f8f9fa;
    height: 120px;
    border-radius: 0.5rem;
}
</style>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadControls();

    // Create control form
    document.getElementById('createControlForm').addEventListener('submit', function(e) {
        e.preventDefault();
        createControl();
    });
});

function loadControls() {
    fetch('/dashboard-controls/get-all-controls', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayControls(data.controls);
        } else {
            displaySampleControls();
        }
    })
    .catch(error => {
        console.error('Error loading controls:', error);
        displaySampleControls();
    });
}

function displaySampleControls() {
    const sampleControls = [
        {
            id: 1,
            title: 'File Manager',
            description: 'Manage uploaded files and documents',
            icon: 'fas fa-folder-open',
            action_type: 'url',
            action_value: '/blob/manager',
            color: 'primary',
            is_active: 1,
            sort_order: 1
        },
        {
            id: 2,
            title: 'Messages',
            description: 'View and send messages',
            icon: 'fas fa-envelope',
            action_type: 'url',
            action_value: '/messages',
            color: 'success',
            is_active: 1,
            sort_order: 2
        },
        {
            id: 3,
            title: 'Settings',
            description: 'Configure your account settings',
            icon: 'fas fa-cog',
            action_type: 'url',
            action_value: '/settings',
            color: 'secondary',
            is_active: 1,
            sort_order: 3
        }
    ];
    
    displayControls(sampleControls);
}

function displayControls(controls) {
    const container = document.getElementById('controlsList');
    
    if (controls.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">No controls found</p>';
        return;
    }
    
    container.innerHTML = `
        <div class="row" id="sortableControls">
            ${controls.map(control => `
                <div class="col-md-4 mb-3" data-control-id="${control.id}">
                    <div class="control-item">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div class="control-icon bg-${control.color}">
                                <i class="${control.icon}"></i>
                            </div>
                            <div class="control-actions">
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="editControl(${control.id})" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-${control.is_active ? 'warning' : 'success'}" onclick="toggleControl(${control.id})" title="${control.is_active ? 'Deactivate' : 'Activate'}">
                                        <i class="fas fa-${control.is_active ? 'pause' : 'play'}"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteControl(${control.id})" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <h6 class="mb-2">${control.title}</h6>
                        <p class="text-muted mb-2">${control.description || ''}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">${control.action_type}: ${control.action_value}</small>
                            <span class="badge bg-${control.is_active ? 'success' : 'secondary'}">${control.is_active ? 'Active' : 'Inactive'}</span>
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

function createControl() {
    const form = document.getElementById('createControlForm');
    const formData = new FormData(form);
    const createBtn = document.getElementById('createControlBtn');
    
    createBtn.disabled = true;
    createBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
    
    fetch('/dashboard-controls/create', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('createControlModal')).hide();
            form.reset();
            loadControls();
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error creating control:', error);
        showAlert('danger', 'Error creating control');
    })
    .finally(() => {
        createBtn.disabled = false;
        createBtn.innerHTML = '<i class="fas fa-save me-2"></i>Create Control';
    });
}

function toggleControl(controlId) {
    fetch(`/dashboard-controls/toggle-active/${controlId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            csrf_token: '<?= csrf_hash() ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            loadControls();
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error toggling control:', error);
        showAlert('danger', 'Error updating control');
    });
}

function deleteControl(controlId) {
    if (!confirm('Are you sure you want to delete this control?')) {
        return;
    }
    
    fetch(`/dashboard-controls/${controlId}`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            loadControls();
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error deleting control:', error);
        showAlert('danger', 'Error deleting control');
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
<?= $this->endSection() ?>
