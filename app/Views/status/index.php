<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #1e3a8a;
            --secondary: #3b82f6;
            --mobile-header-height: 60px;
            --bottom-nav-height: 70px;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .mobile-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: var(--mobile-header-height);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid #e2e8f0;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1rem;
        }

        .main-content {
            padding: 1rem;
            padding-top: calc(var(--mobile-header-height) + 1rem);
            padding-bottom: calc(var(--bottom-nav-height) + 1rem);
            min-height: 100vh;
        }

        .page-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: #64748b;
            font-size: 0.875rem;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            margin-bottom: 1rem;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: var(--bottom-nav-height);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid #e2e8f0;
            z-index: 1000;
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            align-items: center;
        }

        .bottom-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: #64748b;
            font-size: 0.75rem;
            font-weight: 500;
            min-height: 44px;
            padding: 0.5rem 0.25rem;
            border-radius: 8px;
            margin: 0 0.25rem;
        }

        .bottom-nav-item:hover {
            color: var(--primary);
            text-decoration: none;
        }

        .bottom-nav-item.active {
            color: var(--primary);
            background: rgba(30, 58, 138, 0.1);
        }

        .bottom-nav-item i {
            font-size: 1.125rem;
            margin-bottom: 0.25rem;
        }
    </style>
</head>
<body>
    <!-- Mobile Header -->
    <div class="mobile-header">
        <a href="/dashboard" class="btn btn-link p-0">
            <i class="fas fa-arrow-left text-primary"></i>
        </a>
        <h5 class="mb-0 text-primary fw-bold">Status</h5>
        <div></div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-chart-line me-2"></i>
                Status Overview
            </h1>
            <p class="page-subtitle">Monitor your project status and progress</p>
        </div>

        <div class="card">
            <h5 class="mb-3">
                <i class="fas fa-info-circle me-2 text-primary"></i>
                Status Information
            </h5>
            <p class="text-muted">This page will display your project status and progress information.</p>
            <div class="alert alert-info">
                <i class="fas fa-construction me-2"></i>
                This feature is under development and will be available soon.
            </div>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <a href="/dashboard" class="bottom-nav-item">
            <i class="fas fa-home"></i>
            <span>Dashboard</span>
        </a>
        <a href="/admin/users" class="bottom-nav-item">
            <i class="fas fa-users"></i>
            <span>Users</span>
        </a>
        <a href="/admin/add" class="bottom-nav-item">
            <i class="fas fa-plus"></i>
            <span>Add</span>
        </a>
        <a href="/admin/system" class="bottom-nav-item">
            <i class="fas fa-cog"></i>
            <span>Settings</span>
        </a>
        <a href="/auth/logout" class="bottom-nav-item">
            <i class="fas fa-sign-out-alt"></i>
            <span>Logout</span>
        </a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
