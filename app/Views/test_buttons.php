<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Test Page</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 12px;
            padding: 20px;
            margin: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .assignee-btn {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #495057;
            border: 1px solid #dee2e6;
            padding: 8px 14px;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 5px;
        }

        .assignee-btn.play-btn {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border-color: #b8dacc;
        }

        .assignee-btn.pause-btn {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            border-color: #ffd93d;
        }

        .assignee-btn.complete-btn {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            color: #0c5460;
            border-color: #abdde5;
        }
        
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>Button Visibility Test</h2>
        
        <div class="test-card">
            <h4>Test 1: Direct HTML Buttons</h4>
            <button class="assignee-btn play-btn" onclick="testClick('Direct Play')">
                <i class="fas fa-play"></i>
                <span>Play</span>
            </button>
            <button class="assignee-btn pause-btn" onclick="testClick('Direct Pause')">
                <i class="fas fa-pause"></i>
                <span>Pause</span>
            </button>
            <button class="assignee-btn complete-btn" onclick="testClick('Direct Complete')">
                <i class="fas fa-check"></i>
                <span>Complete</span>
            </button>
        </div>

        <div class="test-card">
            <h4>Test 2: JavaScript Generated Buttons</h4>
            <div id="jsButtons"></div>
        </div>

        <div class="test-card">
            <h4>Test 3: Function Test</h4>
            <div id="functionButtons"></div>
        </div>

        <div class="debug-info">
            <h5>Debug Information:</h5>
            <div id="debugInfo"></div>
        </div>
    </div>

    <script>
        // Test user data
        const testUser = {
            id: <?= $user['id'] ?? 1 ?>,
            roles: '<?= $user['roles'] ?? 'user' ?>'
        };

        const testProject = {
            id: 1,
            project_name: 'Test Project',
            status: 'in_progress',
            assigned_to: testUser.id,
            created_by: testUser.id
        };

        // Test click function
        function testClick(buttonName) {
            alert('Button clicked: ' + buttonName);
            console.log('✅ Button clicked:', buttonName);
        }

        // Test 2: JavaScript generated buttons
        document.getElementById('jsButtons').innerHTML = `
            <button class="assignee-btn play-btn" onclick="testClick('JS Play')">
                <i class="fas fa-play"></i>
                <span>JS Play</span>
            </button>
            <button class="assignee-btn pause-btn" onclick="testClick('JS Pause')">
                <i class="fas fa-pause"></i>
                <span>JS Pause</span>
            </button>
            <button class="assignee-btn complete-btn" onclick="testClick('JS Complete')">
                <i class="fas fa-check"></i>
                <span>JS Complete</span>
            </button>
        `;

        // Test 3: Function test
        function getTestAssigneeButtons(project) {
            const userId = testUser.id;
            const isAssignee = project.assigned_to == userId;
            
            console.log('🔍 TEST DEBUG:', {
                userId: userId,
                projectAssignedTo: project.assigned_to,
                isAssignee: isAssignee,
                status: project.status
            });

            if (!isAssignee) {
                return '<p class="text-danger">User is not assignee</p>';
            }

            let buttons = '';

            if (project.status === 'in_progress') {
                buttons = `
                    <button class="assignee-btn pause-btn" onclick="testClick('Function Pause')">
                        <i class="fas fa-pause"></i>
                        <span>Function Pause</span>
                    </button>
                    <button class="assignee-btn complete-btn" onclick="testClick('Function Complete')">
                        <i class="fas fa-check"></i>
                        <span>Function Complete</span>
                    </button>
                `;
            }

            return buttons;
        }

        // Execute test 3
        document.getElementById('functionButtons').innerHTML = getTestAssigneeButtons(testProject);

        // Debug information
        document.getElementById('debugInfo').innerHTML = `
            <strong>User ID:</strong> ${testUser.id}<br>
            <strong>User Role:</strong> ${testUser.roles}<br>
            <strong>Project Assigned To:</strong> ${testProject.assigned_to}<br>
            <strong>Project Status:</strong> ${testProject.status}<br>
            <strong>Is Assignee:</strong> ${testProject.assigned_to == testUser.id}<br>
            <strong>Type of User ID:</strong> ${typeof testUser.id}<br>
            <strong>Type of Assigned To:</strong> ${typeof testProject.assigned_to}
        `;
    </script>
</body>
</html>
