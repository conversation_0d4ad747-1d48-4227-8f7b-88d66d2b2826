<?= $this->extend('layouts/app') ?>

<?= $this->section('title') ?>
<?= $title ?? 'View Message - SmartFlo' ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Message Details</h1>
                    <p class="text-muted mb-0">View and reply to message</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="/messages" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Messages
                    </a>
                    <button class="btn btn-primary" onclick="replyToMessage()">
                        <i class="fas fa-reply me-2"></i>
                        Reply
                    </button>
                </div>
            </div>

            <!-- Message Card -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h5 class="card-title mb-1"><?= htmlspecialchars($message['subject']) ?></h5>
                            <div class="d-flex align-items-center gap-3 text-muted">
                                <span>
                                    <i class="fas fa-user me-1"></i>
                                    From: <?= htmlspecialchars($message['sender_username'] ?? 'Unknown') ?>
                                </span>
                                <span>
                                    <i class="fas fa-clock me-1"></i>
                                    <?= date('M j, Y \a\t g:i A', strtotime($message['created_at'])) ?>
                                </span>
                                <?php if ($message['priority'] !== 'normal'): ?>
                                <span class="badge bg-<?= $message['priority'] === 'urgent' ? 'danger' : ($message['priority'] === 'high' ? 'warning' : 'info') ?>">
                                    <?= ucfirst($message['priority']) ?> Priority
                                </span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="replyToMessage()">
                                    <i class="fas fa-reply me-2"></i>Reply
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="forwardMessage()">
                                    <i class="fas fa-share me-2"></i>Forward
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteMessage()">
                                    <i class="fas fa-trash me-2"></i>Delete
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="message-content">
                        <?= nl2br(htmlspecialchars($message['message'])) ?>
                    </div>
                    
                    <?php if (!empty($message['attachments'])): ?>
                    <div class="attachments mt-4">
                        <h6><i class="fas fa-paperclip me-2"></i>Attachments</h6>
                        <div class="attachment-list">
                            <?php foreach ($message['attachments'] as $attachment): ?>
                            <div class="attachment-item">
                                <i class="fas fa-file me-2"></i>
                                <a href="/messages/download/<?= $attachment['id'] ?>" target="_blank">
                                    <?= htmlspecialchars($attachment['filename']) ?>
                                </a>
                                <span class="text-muted ms-2">(<?= formatFileSize($attachment['filesize']) ?>)</span>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Reply Form (Hidden by default) -->
            <div class="card mt-4" id="replyForm" style="display: none;">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-reply me-2"></i>
                        Reply to Message
                    </h5>
                </div>
                <div class="card-body">
                    <form id="messageReplyForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="replySubject" class="form-label">Subject</label>
                            <input type="text" class="form-control" id="replySubject" name="subject" 
                                   value="Re: <?= htmlspecialchars($message['subject']) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="replyMessage" class="form-label">Message</label>
                            <textarea class="form-control" id="replyMessage" name="message" rows="6" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="replyAttachments" class="form-label">Attachments (Optional)</label>
                            <input type="file" class="form-control" id="replyAttachments" name="attachments[]" multiple>
                            <div class="form-text">Maximum file size: 10MB per file. Allowed types: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG</div>
                        </div>
                        
                        <input type="hidden" name="recipient_id" value="<?= $message['sender_id'] ?>">
                        <input type="hidden" name="message_type" value="individual">
                        <input type="hidden" name="csrf_token" value="<?= csrf_hash() ?>">
                        <input type="hidden" name="priority" value="normal">
                        <input type="hidden" name="csrf_token" value="<?= csrf_hash() ?>">
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary" id="sendReplyBtn">
                                <i class="fas fa-paper-plane me-2"></i>
                                Send Reply
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="cancelReply()">
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.message-content {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--gray-800);
    background: var(--gray-50);
    padding: var(--space-lg);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary);
}

.attachment-item {
    display: flex;
    align-items: center;
    padding: var(--space-sm);
    background: var(--gray-50);
    border-radius: var(--radius-sm);
    margin-bottom: var(--space-xs);
}

.attachment-item a {
    text-decoration: none;
    color: var(--primary);
}

.attachment-item a:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: var(--space-md);
    }
    
    .d-flex.gap-2 {
        width: 100%;
        justify-content: stretch;
    }
    
    .d-flex.gap-2 .btn {
        flex: 1;
    }
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function replyToMessage() {
    const replyForm = document.getElementById('replyForm');
    replyForm.style.display = 'block';
    replyForm.scrollIntoView({ behavior: 'smooth' });
    document.getElementById('replyMessage').focus();
}

function cancelReply() {
    const replyForm = document.getElementById('replyForm');
    replyForm.style.display = 'none';
    document.getElementById('messageReplyForm').reset();
}

function forwardMessage() {
    // Redirect to compose with pre-filled content
    const subject = encodeURIComponent('Fwd: <?= htmlspecialchars($message['subject']) ?>');
    const message = encodeURIComponent('--- Forwarded Message ---\nFrom: <?= htmlspecialchars($message['sender_username'] ?? 'Unknown') ?>\nDate: <?= date('M j, Y \a\t g:i A', strtotime($message['created_at'])) ?>\nSubject: <?= htmlspecialchars($message['subject']) ?>\n\n<?= htmlspecialchars($message['message']) ?>');
    window.location.href = `/messages?compose=1&subject=${subject}&message=${message}`;
}

function deleteMessage() {
    if (!confirm('Are you sure you want to delete this message? This action cannot be undone.')) {
        return;
    }
    
    fetch('/messages/<?= $message['id'] ?>', {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            csrf_token: '<?= csrf_hash() ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Message deleted successfully');
            setTimeout(() => {
                window.location.href = '/messages';
            }, 1500);
        } else {
            showAlert('danger', data.message || 'Failed to delete message');
        }
    })
    .catch(error => {
        console.error('Error deleting message:', error);
        showAlert('danger', 'Error deleting message');
    });
}

// Reply form submission
document.getElementById('messageReplyForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const sendBtn = document.getElementById('sendReplyBtn');
    const originalText = sendBtn.innerHTML;
    
    sendBtn.disabled = true;
    sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
    
    fetch('/messages/send', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            cancelReply();
        } else {
            showAlert('danger', data.message || 'Failed to send reply');
            if (data.errors) {
                console.error('Validation errors:', data.errors);
            }
        }
    })
    .catch(error => {
        console.error('Error sending reply:', error);
        showAlert('danger', 'Error sending reply');
    })
    .finally(() => {
        sendBtn.disabled = false;
        sendBtn.innerHTML = originalText;
    });
});

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Helper function for file size formatting
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>
<?= $this->endSection() ?>
