<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-envelope me-2"></i>
                    Messages
                </h1>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#composeModal">
                    <i class="fas fa-plus me-2"></i>
                    Compose Message
                </button>
            </div>

            <div class="row">
                <!-- Message Folders -->
                <div class="col-md-3 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-folder me-2"></i>
                                Folders
                            </h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                <a href="#" class="list-group-item list-group-item-action active" data-filter="inbox">
                                    <i class="fas fa-inbox me-2"></i>
                                    Inbox
                                    <span class="badge bg-primary float-end" id="inboxCount">0</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" data-filter="sent">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    Sent
                                    <span class="badge bg-secondary float-end" id="sentCount">0</span>
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" data-filter="drafts">
                                    <i class="fas fa-edit me-2"></i>
                                    Drafts
                                    <span class="badge bg-warning float-end" id="draftsCount">0</span>
                                </a>
                                <?php if ($user && (strpos($user['roles'] ?? '', 'admin') !== false || strpos($user['permissions'] ?? '', 'message.broadcast') !== false)): ?>
                                <a href="#" class="list-group-item list-group-item-action" data-filter="broadcast">
                                    <i class="fas fa-bullhorn me-2"></i>
                                    Broadcast
                                    <span class="badge bg-info float-end" id="broadcastCount">0</span>
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                Quick Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="markAllAsRead()">
                                    <i class="fas fa-check-double me-2"></i>
                                    Mark All Read
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="deleteSelected()">
                                    <i class="fas fa-trash me-2"></i>
                                    Delete Selected
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Messages List -->
                <div class="col-md-9">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-list me-2"></i>
                                <span id="currentFolderTitle">Inbox</span>
                            </h6>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="refreshMessages()">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button class="btn btn-outline-secondary" onclick="toggleSelectAll()">
                                    <i class="fas fa-check-square"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="messagesList">
                                <div class="text-center py-4">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Compose Message Modal -->
<div class="modal fade" id="composeModal" tabindex="-1" aria-labelledby="composeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="composeModalLabel">Compose Message</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="composeForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="messageType" class="form-label">Message Type</label>
                                <select class="form-select" id="messageType" name="message_type" onchange="toggleRecipientOptions()">
                                    <option value="individual">Individual Message</option>
                                    <?php if ($user && (strpos($user['roles'] ?? '', 'admin') !== false || strpos($user['permissions'] ?? '', 'message.broadcast') !== false)): ?>
                                    <option value="role">Send to Role</option>
                                    <option value="broadcast">Broadcast to All</option>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="messagePriority" class="form-label">Priority</label>
                                <select class="form-select" id="messagePriority" name="priority">
                                    <option value="normal">Normal</option>
                                    <option value="high">High</option>
                                    <option value="urgent">Urgent</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3" id="recipientSection">
                        <label for="messageRecipient" class="form-label">Recipient</label>
                        <select class="form-select" id="messageRecipient" name="recipient_id">
                            <option value="">Select recipient...</option>
                        </select>
                    </div>

                    <div class="mb-3" id="roleSection" style="display: none;">
                        <label for="messageRole" class="form-label">Send to Role</label>
                        <select class="form-select" id="messageRole" name="recipient_role">
                            <option value="">Select role...</option>
                            <option value="admin">Administrators</option>
                            <option value="manager">Managers</option>
                            <option value="staff">Staff</option>
                            <option value="user">Users</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="messageSubject" class="form-label">Subject</label>
                        <input type="text" class="form-control" id="messageSubject" name="subject" required>
                    </div>

                    <div class="mb-3">
                        <label for="messageContent" class="form-label">Message</label>
                        <textarea class="form-control" id="messageContent" name="message" rows="5" required></textarea>
                    </div>

                    <input type="hidden" name="csrf_token" value="<?= csrf_hash() ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-outline-primary" onclick="saveDraft()">
                        <i class="fas fa-save me-2"></i>
                        Save Draft
                    </button>
                    <button type="submit" class="btn btn-primary" id="sendMessageBtn">
                        <i class="fas fa-paper-plane me-2"></i>
                        Send Message
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.message-item {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.message-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.message-item.unread {
    background-color: #fff;
    border-left: 4px solid #0d6efd;
    font-weight: 600;
}

.message-item.selected {
    background-color: #e3f2fd;
    border-color: #2196f3;
}

.message-checkbox {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.list-group-item-action.active {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

.priority-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.priority-high {
    background-color: #ffc107;
    color: #000;
}

.priority-urgent {
    background-color: #dc3545;
    color: #fff;
}
</style>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
let currentFilter = 'inbox';
let selectedMessages = [];

document.addEventListener('DOMContentLoaded', function() {
    loadMessages();
    loadUsers();

    // Filter click handlers
    document.querySelectorAll('[data-filter]').forEach(filter => {
        filter.addEventListener('click', function(e) {
            e.preventDefault();

            // Update active filter
            document.querySelectorAll('[data-filter]').forEach(f => f.classList.remove('active'));
            this.classList.add('active');

            currentFilter = this.dataset.filter;
            document.getElementById('currentFolderTitle').textContent = this.textContent.trim().split('\n')[0];
            loadMessages();
        });
    });

    // Compose form
    document.getElementById('composeForm').addEventListener('submit', function(e) {
        e.preventDefault();
        sendMessage();
    });
});

function loadMessages() {
    fetch(`/messages/get-messages?type=${currentFilter}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayMessages(data.messages);
            updateCounts(data);
        } else {
            displaySampleMessages();
        }
    })
    .catch(error => {
        console.error('Error loading messages:', error);
        displaySampleMessages();
    });
}

function displaySampleMessages() {
    // Load real messages instead of sample data
    loadMessages();
        {
            id: 3,
            subject: 'Material Delivery Schedule',
            sender_username: 'Supply Manager',
            created_at: new Date(Date.now() - 172800000).toISOString(),
            is_read: false,
            priority: 'normal',
            message: 'The concrete delivery for Site Beta has been rescheduled to next Monday.'
        }
    ];

    displayMessages(sampleMessages);
}

function displayMessages(messages) {
    const container = document.getElementById('messagesList');

    if (messages.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">No messages found</p>';
        return;
    }

    container.innerHTML = messages.map(message => `
        <div class="message-item ${!message.is_read ? 'unread' : ''}" onclick="viewMessage(${message.id})" data-message-id="${message.id}">
            <div class="message-checkbox">
                <input type="checkbox" class="form-check-input" onclick="event.stopPropagation(); toggleMessageSelection(${message.id})">
            </div>
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <div class="d-flex align-items-center mb-2">
                        <h6 class="mb-0 me-2">${message.subject}</h6>
                        ${!message.is_read ? '<span class="badge bg-primary">New</span>' : ''}
                        ${message.priority === 'high' ? '<span class="badge priority-high ms-2">High Priority</span>' : ''}
                        ${message.priority === 'urgent' ? '<span class="badge priority-urgent ms-2">Urgent</span>' : ''}
                    </div>
                    <p class="mb-1 text-muted">From: ${message.sender_username || 'Unknown'}</p>
                    <p class="mb-1 text-truncate">${message.message || 'No preview available'}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">${new Date(message.created_at).toLocaleString()}</small>
                        ${getReadReceiptIcon(message)}
                    </div>
                </div>
                <div class="message-actions">
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="event.stopPropagation(); viewMessage(${message.id})" title="View">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="event.stopPropagation(); replyToMessage(${message.id})" title="Reply">
                            <i class="fas fa-reply"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="event.stopPropagation(); deleteMessage(${message.id})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

function loadUsers() {
    fetch('/messages/get-users', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const select = document.getElementById('messageRecipient');
            select.innerHTML = '<option value="">Select recipient...</option>';
            data.users.forEach(user => {
                const displayName = user.first_name && user.last_name ?
                    `${user.first_name} ${user.last_name} (${user.username})` :
                    user.username;
                const role = user.roles || 'User';
                select.innerHTML += `<option value="${user.id}">${displayName} - ${role}</option>`;
            });
        } else {
            console.error('Failed to load users:', data.message);
            showAlert('warning', 'Could not load users list');
        }
    })
    .catch(error => {
        console.error('Error loading users:', error);
        showAlert('warning', 'Error loading users list');
        // Add fallback users
        const select = document.getElementById('messageRecipient');
        select.innerHTML = `
            <option value="">Select recipient...</option>
            <option value="1">Admin (Administrator)</option>
            <option value="2">Manager (Project Manager)</option>
            <option value="3">Staff (Site Supervisor)</option>
        `;
    });
}

function toggleRecipientOptions() {
    const messageType = document.getElementById('messageType').value;
    const recipientSection = document.getElementById('recipientSection');
    const roleSection = document.getElementById('roleSection');

    if (messageType === 'role') {
        recipientSection.style.display = 'none';
        roleSection.style.display = 'block';
    } else if (messageType === 'broadcast') {
        recipientSection.style.display = 'none';
        roleSection.style.display = 'none';
    } else {
        recipientSection.style.display = 'block';
        roleSection.style.display = 'none';
    }
}

function sendMessage() {
    const form = document.getElementById('composeForm');
    const formData = new FormData(form);
    const sendBtn = document.getElementById('sendMessageBtn');

    sendBtn.disabled = true;
    sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';

    fetch('/messages/send', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('composeModal')).hide();
            form.reset();
            loadMessages();
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error sending message:', error);
        showAlert('danger', 'Error sending message');
    })
    .finally(() => {
        sendBtn.disabled = false;
        sendBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Send Message';
    });
}

function viewMessage(messageId) {
    window.location.href = `/messages/view/${messageId}`;
}

function replyToMessage(messageId) {
    // Load message details and pre-fill compose form
    fetch(`/messages/get/${messageId}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const message = data.message;

            // Set message type to individual
            document.getElementById('messageType').value = 'individual';
            toggleRecipientOptions();

            // Pre-fill form
            document.getElementById('messageSubject').value = 'Re: ' + message.subject;
            document.getElementById('messageRecipient').value = message.sender_id;
            document.getElementById('messageContent').value = `\n\n--- Original Message ---\nFrom: ${message.sender_username}\nDate: ${new Date(message.created_at).toLocaleString()}\nSubject: ${message.subject}\n\n${message.message}`;

            const modal = new bootstrap.Modal(document.getElementById('composeModal'));
            modal.show();
        } else {
            showAlert('danger', data.message || 'Error loading message');
        }
    })
    .catch(error => {
        console.error('Error loading message for reply:', error);
        showAlert('danger', 'Error loading message for reply');
    });
}

function deleteMessage(messageId) {
    if (!confirm('Are you sure you want to delete this message?')) {
        return;
    }

    fetch(`/messages/${messageId}`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadMessages();
            showAlert('success', 'Message deleted successfully');
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error deleting message:', error);
        showAlert('danger', 'Error deleting message');
    });
}

function markAllAsRead() {
    fetch('/messages/mark-all-read', {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            csrf_token: '<?= csrf_hash() ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadMessages();
            showAlert('success', 'All messages marked as read');
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error marking messages as read:', error);
        showAlert('danger', 'Error updating messages');
    });
}

function deleteSelected() {
    if (selectedMessages.length === 0) {
        showAlert('warning', 'Please select messages to delete');
        return;
    }

    if (!confirm(`Are you sure you want to delete ${selectedMessages.length} selected message(s)?`)) {
        return;
    }

    fetch('/messages/delete-multiple', {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            message_ids: selectedMessages,
            csrf_token: '<?= csrf_hash() ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            selectedMessages = [];
            loadMessages();
            showAlert('success', 'Selected messages deleted');
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error deleting messages:', error);
        showAlert('danger', 'Error deleting messages');
    });
}

function toggleMessageSelection(messageId) {
    const index = selectedMessages.indexOf(messageId);
    if (index > -1) {
        selectedMessages.splice(index, 1);
    } else {
        selectedMessages.push(messageId);
    }

    // Update visual selection
    const messageItem = document.querySelector(`[data-message-id="${messageId}"]`);
    if (messageItem) {
        messageItem.classList.toggle('selected');
    }
}

function toggleSelectAll() {
    const checkboxes = document.querySelectorAll('.message-item input[type="checkbox"]');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);

    checkboxes.forEach(cb => {
        cb.checked = !allChecked;
        const messageId = parseInt(cb.closest('.message-item').dataset.messageId);

        if (!allChecked && !selectedMessages.includes(messageId)) {
            selectedMessages.push(messageId);
            cb.closest('.message-item').classList.add('selected');
        } else if (allChecked) {
            const index = selectedMessages.indexOf(messageId);
            if (index > -1) selectedMessages.splice(index, 1);
            cb.closest('.message-item').classList.remove('selected');
        }
    });
}

function refreshMessages() {
    loadMessages();
    showAlert('info', 'Messages refreshed');
}

function saveDraft() {
    const form = document.getElementById('composeForm');
    const formData = new FormData(form);
    formData.append('is_draft', '1');

    fetch('/messages/save-draft', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Draft saved successfully');
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error saving draft:', error);
        showAlert('danger', 'Error saving draft');
    });
}

function updateCounts(data) {
    // Update folder counts
    document.getElementById('inboxCount').textContent = data.inbox_count || 0;
    document.getElementById('sentCount').textContent = data.sent_count || 0;
    document.getElementById('draftsCount').textContent = data.drafts_count || 0;
    if (document.getElementById('broadcastCount')) {
        document.getElementById('broadcastCount').textContent = data.broadcast_count || 0;
    }
}

function getReadReceiptIcon(message) {
    // Only show read receipts for sent messages
    if (currentFolder !== 'sent') {
        return '';
    }

    if (message.is_read && message.read_at) {
        // Double blue tick for read
        return '<span class="read-receipt read" title="Read"><i class="fas fa-check-double text-primary"></i></span>';
    } else {
        // Single gray tick for sent but not read
        return '<span class="read-receipt sent" title="Sent"><i class="fas fa-check text-muted"></i></span>';
    }
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
<?= $this->endSection() ?>
