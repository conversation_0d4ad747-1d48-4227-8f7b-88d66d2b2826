<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class AdminSeeder extends Seeder
{
    public function run()
    {
        // Create admin user
        $userData = [
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password_hash' => password_hash('Admin@123', PASSWORD_DEFAULT),
            'is_active' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ];

        $this->db->table('users')->insert($userData);
        $userId = $this->db->insertID();

        // Get admin role ID
        $adminRole = $this->db->table('roles')
            ->where('role_name', 'admin')
            ->get()
            ->getRow();

        if ($adminRole) {
            // Assign admin role to admin user
            $userRoleData = [
                'user_id' => $userId,
                'role_id' => $adminRole->id,
                'created_at' => date('Y-m-d H:i:s'),
            ];

            $this->db->table('user_roles')->insert($userRoleData);
        }

        echo "Admin user created successfully!\n";
        echo "Username: admin\n";
        echo "Password: Admin@123\n";
        echo "Email: <EMAIL>\n";
    }
}
