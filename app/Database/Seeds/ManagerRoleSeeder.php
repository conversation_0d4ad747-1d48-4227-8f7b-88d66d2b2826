<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class ManagerRoleSeeder extends Seeder
{
    public function run()
    {
        // Check if manager role already exists
        $roleModel = new \App\Models\RoleModel();
        $existingRole = $roleModel->where('role_name', 'manager')->first();
        
        if ($existingRole) {
            echo "Manager role already exists. Updating permissions...\n";
            $roleId = $existingRole['id'];
        } else {
            echo "Creating manager role...\n";
            
            // Manager role permissions - comprehensive project and team management
            $managerPermissions = [
                // User Management (limited)
                'user.read',
                'user.update',  // Can edit user details but not create/delete
                
                // Project Management (full access)
                'project.create',
                'project.read',
                'project.update',
                'project.delete',
                'project.assign',
                'project.status',
                'project.timeline',
                
                // Task Management (full access)
                'task.create',
                'task.read',
                'task.update',
                'task.delete',
                'task.assign',
                'task.status',
                
                // Reports & Analytics
                'report.view',
                'report.export',
                'analytics.view',
                
                // Team Management
                'team.manage',
                'team.assign',
                
                // Dashboard Controls
                'dashboard.manage',
            ];
            
            $roleData = [
                'role_name' => 'manager',
                'description' => 'Project Manager - Can manage projects, tasks, and team assignments with comprehensive oversight capabilities',
                'permissions' => $managerPermissions,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $roleId = $roleModel->insert($roleData);
        }
        
        if ($roleId) {
            echo "Manager role created/updated successfully with ID: {$roleId}\n";
            
            // Update existing role if it was found
            if ($existingRole) {
                $managerPermissions = [
                    'user.read',
                    'user.update',
                    'project.create',
                    'project.read',
                    'project.update',
                    'project.delete',
                    'project.assign',
                    'project.status',
                    'project.timeline',
                    'task.create',
                    'task.read',
                    'task.update',
                    'task.delete',
                    'task.assign',
                    'task.status',
                    'report.view',
                    'report.export',
                    'analytics.view',
                    'team.manage',
                    'team.assign',
                    'dashboard.manage',
                ];
                
                $roleModel->update($roleId, [
                    'permissions' => $managerPermissions,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                
                echo "Manager role permissions updated.\n";
            }
            
            echo "Manager role setup completed successfully!\n";
            echo "\nManager Role Capabilities:\n";
            echo "✓ Full project management (create, edit, delete, assign)\n";
            echo "✓ Complete task management and assignment\n";
            echo "✓ Team member management and assignment\n";
            echo "✓ Project status updates and timeline access\n";
            echo "✓ Reports and analytics viewing/export\n";
            echo "✓ Dashboard controls management\n";
            echo "✓ User profile editing (limited user management)\n";
            echo "✗ Cannot create/delete users (admin only)\n";
            echo "✗ Cannot manage roles (admin only)\n";
            echo "✗ No admin panel access\n";
            
        } else {
            echo "Failed to create manager role.\n";
        }
    }
}
