<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class EnhanceProjectsTable extends Migration
{
    public function up()
    {
        // Check if projects table exists, if not create it
        if (!$this->db->tableExists('projects')) {
            $this->forge->addField([
                'id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'auto_increment' => true,
                ],
                'client_name' => [
                    'type' => 'VARCHAR',
                    'constraint' => 100,
                    'null' => false,
                ],
                'project_name' => [
                    'type' => 'VARCHAR',
                    'constraint' => 150,
                    'null' => false,
                ],
                'location' => [
                    'type' => 'VARCHAR',
                    'constraint' => 255,
                    'null' => false,
                ],
                'description' => [
                    'type' => 'TEXT',
                    'null' => true,
                ],
                'start_date' => [
                    'type' => 'DATE',
                    'null' => false,
                ],
                'target_completion' => [
                    'type' => 'DATE',
                    'null' => false,
                ],
                'estimated_completion' => [
                    'type' => 'DATE',
                    'null' => true,
                ],
                'actual_completion' => [
                    'type' => 'DATETIME',
                    'null' => true,
                ],
                'budget' => [
                    'type' => 'DECIMAL',
                    'constraint' => '15,2',
                    'null' => true,
                ],
                'actual_cost' => [
                    'type' => 'DECIMAL',
                    'constraint' => '15,2',
                    'default' => 0.00,
                ],
                'status' => [
                    'type' => 'ENUM',
                    'constraint' => ['not_started', 'in_progress', 'on_hold', 'completed'],
                    'default' => 'not_started',
                ],
                'progress_percentage' => [
                    'type' => 'INT',
                    'constraint' => 3,
                    'default' => 0,
                ],
                'last_update_notes' => [
                    'type' => 'TEXT',
                    'null' => true,
                ],
                'last_updated' => [
                    'type' => 'DATETIME',
                    'null' => true,
                ],
                'created_by' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'null' => false,
                ],
                'assigned_to' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'null' => false,
                ],
                'assigned_manager' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'null' => true,
                ],
                'assigned_supervisor' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'null' => true,
                ],
                // Status change timestamps
                'not_started_at' => [
                    'type' => 'DATETIME',
                    'null' => true,
                ],
                'in_progress_at' => [
                    'type' => 'DATETIME',
                    'null' => true,
                ],
                'on_hold_at' => [
                    'type' => 'DATETIME',
                    'null' => true,
                ],
                'completed_at' => [
                    'type' => 'DATETIME',
                    'null' => true,
                ],
                'created_at' => [
                    'type' => 'DATETIME',
                    'null' => false,
                ],
                'updated_at' => [
                    'type' => 'DATETIME',
                    'null' => false,
                ],
                'deleted_at' => [
                    'type' => 'DATETIME',
                    'null' => true,
                ],
            ]);

            $this->forge->addKey('id', true);
            $this->forge->addKey('created_by');
            $this->forge->addKey('assigned_to');
            $this->forge->addKey('assigned_manager');
            $this->forge->addKey('assigned_supervisor');
            $this->forge->addKey('status');
            $this->forge->addKey('target_completion');
            $this->forge->addKey('created_at');
            $this->forge->addKey(['status', 'assigned_to']);
            $this->forge->addKey(['target_completion', 'status']);

            // Foreign key constraints
            $this->forge->addForeignKey('created_by', 'users', 'id', 'CASCADE', 'CASCADE');
            $this->forge->addForeignKey('assigned_to', 'users', 'id', 'CASCADE', 'CASCADE');
            $this->forge->addForeignKey('assigned_manager', 'users', 'id', 'SET NULL', 'CASCADE');
            $this->forge->addForeignKey('assigned_supervisor', 'users', 'id', 'SET NULL', 'CASCADE');

            $this->forge->createTable('projects');
        } else {
            // Table exists, add new columns if they don't exist
            $fields = [];

            if (!$this->db->fieldExists('target_completion', 'projects')) {
                $fields['target_completion'] = [
                    'type' => 'DATE',
                    'null' => false,
                    'after' => 'start_date'
                ];
            }

            if (!$this->db->fieldExists('assigned_to', 'projects')) {
                $fields['assigned_to'] = [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'null' => false,
                    'after' => 'created_by'
                ];
            }

            // Status timestamp fields
            $statusFields = ['not_started_at', 'in_progress_at', 'on_hold_at', 'completed_at'];
            foreach ($statusFields as $field) {
                if (!$this->db->fieldExists($field, 'projects')) {
                    $fields[$field] = [
                        'type' => 'DATETIME',
                        'null' => true,
                        'after' => 'assigned_supervisor'
                    ];
                }
            }

            if (!empty($fields)) {
                $this->forge->addColumn('projects', $fields);
            }

            // Add foreign key for assigned_to if it doesn't exist
            if ($this->db->fieldExists('assigned_to', 'projects')) {
                try {
                    $this->forge->addForeignKey('assigned_to', 'users', 'id', 'CASCADE', 'CASCADE');
                } catch (\Exception $e) {
                    // Foreign key might already exist
                    log_message('info', 'Foreign key for assigned_to might already exist: ' . $e->getMessage());
                }
            }
        }

        // Insert sample projects for testing
        $sampleProjects = [
            [
                'client_name' => 'ABC Construction Ltd',
                'project_name' => 'Residential Complex - Phase 1',
                'location' => 'Downtown District, City Center',
                'description' => '50-unit residential complex with modern amenities',
                'start_date' => '2024-01-15',
                'target_completion' => '2024-12-31',
                'budget' => 2500000.00,
                'actual_cost' => 1800000.00,
                'status' => 'in_progress',
                'progress_percentage' => 65,
                'last_update_notes' => 'Foundation work completed, starting structural work',
                'created_by' => 1,
                'assigned_to' => 1,
                'in_progress_at' => '2024-01-20 09:00:00',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'client_name' => 'XYZ Developers',
                'project_name' => 'Commercial Plaza',
                'location' => 'Business District, Main Street',
                'description' => 'Multi-story commercial plaza with retail and office spaces',
                'start_date' => '2024-03-01',
                'target_completion' => '2025-06-30',
                'budget' => 5000000.00,
                'actual_cost' => 1200000.00,
                'status' => 'not_started',
                'progress_percentage' => 0,
                'last_update_notes' => 'Waiting for permits approval',
                'created_by' => 1,
                'assigned_to' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'client_name' => 'Green Homes Inc',
                'project_name' => 'Eco-Friendly Villas',
                'location' => 'Suburban Area, Green Valley',
                'description' => 'Sustainable housing project with solar panels and rainwater harvesting',
                'start_date' => '2023-09-01',
                'target_completion' => '2024-08-31',
                'budget' => 3200000.00,
                'actual_cost' => 3100000.00,
                'status' => 'completed',
                'progress_percentage' => 100,
                'last_update_notes' => 'Project completed successfully, handover done',
                'created_by' => 1,
                'assigned_to' => 1,
                'completed_at' => '2024-08-25 16:30:00',
                'created_at' => date('Y-m-d H:i:s', strtotime('-6 months')),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        // Only insert if table is empty
        if ($this->db->table('projects')->countAll() == 0) {
            $this->db->table('projects')->insertBatch($sampleProjects);
        }
    }

    public function down()
    {
        $this->forge->dropTable('projects');
    }
}
