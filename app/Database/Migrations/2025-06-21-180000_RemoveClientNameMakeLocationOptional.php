<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class RemoveClientNameMakeLocationOptional extends Migration
{
    public function up()
    {
        // Check if client_name column exists and remove it
        if ($this->db->fieldExists('client_name', 'projects')) {
            $this->forge->dropColumn('projects', 'client_name');
        }

        // Make location column nullable
        if ($this->db->fieldExists('location', 'projects')) {
            $this->forge->modifyColumn('projects', [
                'location' => [
                    'type' => 'VARCHAR',
                    'constraint' => 255,
                    'null' => true,
                ]
            ]);
        }
    }

    public function down()
    {
        // Add client_name column back
        if (!$this->db->fieldExists('client_name', 'projects')) {
            $this->forge->addColumn('projects', [
                'client_name' => [
                    'type' => 'VARCHAR',
                    'constraint' => 100,
                    'null' => false,
                    'after' => 'project_id'
                ]
            ]);
        }

        // Make location column required again
        if ($this->db->fieldExists('location', 'projects')) {
            $this->forge->modifyColumn('projects', [
                'location' => [
                    'type' => 'VARCHAR',
                    'constraint' => 255,
                    'null' => false,
                ]
            ]);
        }
    }
}
