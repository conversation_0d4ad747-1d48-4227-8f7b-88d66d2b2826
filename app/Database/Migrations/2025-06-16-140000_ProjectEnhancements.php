<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class ProjectEnhancements extends Migration
{
    public function up()
    {
        // Add project_id field and remove budget fields
        if ($this->db->tableExists('projects')) {
            // Add project_id field if it doesn't exist
            if (!$this->db->fieldExists('project_id', 'projects')) {
                $this->forge->addColumn('projects', [
                    'project_id' => [
                        'type' => 'VARCHAR',
                        'constraint' => 50,
                        'null' => false,
                        'unique' => true,
                        'after' => 'id'
                    ]
                ]);
                
                // Add index for project_id
                $this->forge->addKey('project_id', false, false, 'projects');
            }

            // Remove budget and actual_cost fields if they exist
            if ($this->db->fieldExists('budget', 'projects')) {
                $this->forge->dropColumn('projects', 'budget');
            }
            
            if ($this->db->fieldExists('actual_cost', 'projects')) {
                $this->forge->dropColumn('projects', 'actual_cost');
            }

            // Update existing projects with generated project IDs if they don't have them
            $projects = $this->db->table('projects')
                                ->select('id')
                                ->where('project_id IS NULL OR project_id = ""')
                                ->get()
                                ->getResultArray();

            foreach ($projects as $project) {
                $year = date('Y');
                $projectId = 'PRJ-' . $year . '-' . str_pad($project['id'], 3, '0', STR_PAD_LEFT);
                
                $this->db->table('projects')
                        ->where('id', $project['id'])
                        ->update(['project_id' => $projectId]);
            }
        }

        // Create project_tasks table for multi-task workflow system
        if (!$this->db->tableExists('project_tasks')) {
            $this->forge->addField([
                'id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'auto_increment' => true,
                ],
                'project_id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'null' => false,
                ],
                'task_type_id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'null' => false,
                ],
                'task_name' => [
                    'type' => 'VARCHAR',
                    'constraint' => 150,
                    'null' => false,
                ],
                'description' => [
                    'type' => 'TEXT',
                    'null' => true,
                ],
                'assigned_to' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'null' => false,
                ],
                'status' => [
                    'type' => 'ENUM',
                    'constraint' => ['not_started', 'in_progress', 'on_hold', 'completed'],
                    'default' => 'not_started',
                ],
                'priority' => [
                    'type' => 'ENUM',
                    'constraint' => ['low', 'medium', 'high', 'urgent'],
                    'default' => 'medium',
                ],
                'depends_on' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'null' => true,
                ],
                'estimated_hours' => [
                    'type' => 'DECIMAL',
                    'constraint' => '8,2',
                    'null' => true,
                ],
                'actual_hours' => [
                    'type' => 'DECIMAL',
                    'constraint' => '8,2',
                    'default' => 0.00,
                ],
                'start_date' => [
                    'type' => 'DATE',
                    'null' => true,
                ],
                'due_date' => [
                    'type' => 'DATE',
                    'null' => true,
                ],
                'completed_date' => [
                    'type' => 'DATETIME',
                    'null' => true,
                ],
                'notes' => [
                    'type' => 'TEXT',
                    'null' => true,
                ],
                'created_by' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'null' => false,
                ],
                'created_at' => [
                    'type' => 'DATETIME',
                    'null' => false,
                ],
                'updated_at' => [
                    'type' => 'DATETIME',
                    'null' => false,
                ],
                'deleted_at' => [
                    'type' => 'DATETIME',
                    'null' => true,
                ],
            ]);

            $this->forge->addKey('id', true);
            $this->forge->addKey('project_id');
            $this->forge->addKey('task_type_id');
            $this->forge->addKey('assigned_to');
            $this->forge->addKey('status');
            $this->forge->addKey(['project_id', 'status']);

            // Foreign key constraints
            $this->forge->addForeignKey('project_id', 'projects', 'id', 'CASCADE', 'CASCADE');
            $this->forge->addForeignKey('assigned_to', 'users', 'id', 'CASCADE', 'CASCADE');
            $this->forge->addForeignKey('created_by', 'users', 'id', 'CASCADE', 'CASCADE');
            $this->forge->addForeignKey('depends_on', 'project_tasks', 'id', 'SET NULL', 'CASCADE');

            $this->forge->createTable('project_tasks');
        }

        // Create task_types table for customizable task types
        if (!$this->db->tableExists('task_types')) {
            $this->forge->addField([
                'id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'auto_increment' => true,
                ],
                'name' => [
                    'type' => 'VARCHAR',
                    'constraint' => 100,
                    'null' => false,
                ],
                'description' => [
                    'type' => 'TEXT',
                    'null' => true,
                ],
                'color' => [
                    'type' => 'VARCHAR',
                    'constraint' => 7,
                    'default' => '#0d6efd',
                ],
                'icon' => [
                    'type' => 'VARCHAR',
                    'constraint' => 50,
                    'default' => 'fas fa-tasks',
                ],
                'is_default' => [
                    'type' => 'TINYINT',
                    'constraint' => 1,
                    'default' => 0,
                ],
                'sort_order' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'default' => 0,
                ],
                'is_active' => [
                    'type' => 'TINYINT',
                    'constraint' => 1,
                    'default' => 1,
                ],
                'created_by' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'null' => false,
                ],
                'created_at' => [
                    'type' => 'DATETIME',
                    'null' => false,
                ],
                'updated_at' => [
                    'type' => 'DATETIME',
                    'null' => false,
                ],
            ]);

            $this->forge->addKey('id', true);
            $this->forge->addKey('name');
            $this->forge->addKey('is_active');
            $this->forge->addKey('sort_order');

            $this->forge->addForeignKey('created_by', 'users', 'id', 'CASCADE', 'CASCADE');

            $this->forge->createTable('task_types');

            // Insert default task types
            $defaultTaskTypes = [
                [
                    'name' => 'Planning',
                    'description' => 'Initial project planning and design phase',
                    'color' => '#6c757d',
                    'icon' => 'fas fa-drafting-compass',
                    'is_default' => 1,
                    'sort_order' => 1,
                    'created_by' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'name' => '3D Design',
                    'description' => '3D modeling and architectural design',
                    'color' => '#0d6efd',
                    'icon' => 'fas fa-cube',
                    'is_default' => 1,
                    'sort_order' => 2,
                    'created_by' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'name' => 'Permit Approval',
                    'description' => 'Government permits and regulatory approvals',
                    'color' => '#fd7e14',
                    'icon' => 'fas fa-stamp',
                    'is_default' => 1,
                    'sort_order' => 3,
                    'created_by' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'name' => 'Construction',
                    'description' => 'Main construction and building phase',
                    'color' => '#dc3545',
                    'icon' => 'fas fa-hard-hat',
                    'is_default' => 1,
                    'sort_order' => 4,
                    'created_by' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'name' => 'Completion',
                    'description' => 'Final inspection and project handover',
                    'color' => '#198754',
                    'icon' => 'fas fa-check-circle',
                    'is_default' => 1,
                    'sort_order' => 5,
                    'created_by' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]
            ];

            $this->db->table('task_types')->insertBatch($defaultTaskTypes);
        }
    }

    public function down()
    {
        // Drop the new tables
        $this->forge->dropTable('project_tasks', true);
        $this->forge->dropTable('task_types', true);
        
        // Remove project_id field and restore budget fields
        if ($this->db->tableExists('projects')) {
            if ($this->db->fieldExists('project_id', 'projects')) {
                $this->forge->dropColumn('projects', 'project_id');
            }
            
            // Restore budget fields
            $this->forge->addColumn('projects', [
                'budget' => [
                    'type' => 'DECIMAL',
                    'constraint' => '15,2',
                    'null' => true,
                    'after' => 'actual_completion'
                ],
                'actual_cost' => [
                    'type' => 'DECIMAL',
                    'constraint' => '15,2',
                    'default' => 0.00,
                    'after' => 'budget'
                ]
            ]);
        }
    }
}
