<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddTaskRevisions extends Migration
{
    public function up()
    {
        // Add revision tracking fields to project_tasks table
        $fields = [
            'revision_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
                'comment' => 'Number of revisions for this task'
            ],
            'current_revision' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
                'comment' => 'Current revision number'
            ],
            'revision_requested_at' => [
                'type' => 'DATETIME',
                'null' => true,
                'comment' => 'When revision was last requested'
            ],
            'revision_requested_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'comment' => 'User who requested the revision'
            ],
            'revision_notes' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Notes about the revision request'
            ]
        ];

        $this->forge->addColumn('project_tasks', $fields);

        // Create task_revisions table for detailed revision history
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'task_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'revision_number' => [
                'type' => 'INT',
                'constraint' => 11,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['revision_needed', 'in_revision', 'revision_completed'],
                'default' => 'revision_needed',
            ],
            'requested_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'assigned_to' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'notes' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'requested_at' => [
                'type' => 'DATETIME',
            ],
            'started_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'completed_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('task_id');
        $this->forge->addKey('revision_number');
        $this->forge->createTable('task_revisions');

        // Add foreign key constraints
        $this->forge->addForeignKey('task_id', 'project_tasks', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('requested_by', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('assigned_to', 'users', 'id', 'CASCADE', 'CASCADE');
    }

    public function down()
    {
        // Drop foreign keys first
        $this->forge->dropForeignKey('task_revisions', 'task_revisions_task_id_foreign');
        $this->forge->dropForeignKey('task_revisions', 'task_revisions_requested_by_foreign');
        $this->forge->dropForeignKey('task_revisions', 'task_revisions_assigned_to_foreign');
        
        // Drop table
        $this->forge->dropTable('task_revisions');

        // Remove columns from project_tasks
        $this->forge->dropColumn('project_tasks', [
            'revision_count',
            'current_revision',
            'revision_requested_at',
            'revision_requested_by',
            'revision_notes'
        ]);
    }
}
