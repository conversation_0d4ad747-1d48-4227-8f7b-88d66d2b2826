<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddFilePathToProjectTasks extends Migration
{
    public function up()
    {
        // Add file_path column to project_tasks table
        if ($this->db->tableExists('project_tasks')) {
            if (!$this->db->fieldExists('file_path', 'project_tasks')) {
                $this->forge->addColumn('project_tasks', [
                    'file_path' => [
                        'type' => 'VARCHAR',
                        'constraint' => 500,
                        'null' => true,
                        'comment' => 'Path to uploaded task files',
                        'after' => 'google_drive_link'
                    ]
                ]);
                
                log_message('info', 'Added file_path column to project_tasks table');
            } else {
                log_message('info', 'file_path column already exists in project_tasks table');
            }
        }
    }

    public function down()
    {
        // Remove file_path column from project_tasks table
        if ($this->db->tableExists('project_tasks')) {
            if ($this->db->fieldExists('file_path', 'project_tasks')) {
                $this->forge->dropColumn('project_tasks', 'file_path');
                log_message('info', 'Removed file_path column from project_tasks table');
            }
        }
    }
}
