<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateNotificationsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'title' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'message' => [
                'type' => 'TEXT',
            ],
            'type' => [
                'type' => 'ENUM',
                'constraint' => ['info', 'success', 'warning', 'error', 'user', 'system', 'security'],
                'default' => 'info',
            ],
            'is_read' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 0,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('user_id');
        $this->forge->addKey(['user_id', 'is_read']);
        $this->forge->addKey('created_at');
        
        // Add foreign key constraint
        $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        
        $this->forge->createTable('notifications');

        // Insert some sample notifications for testing
        $data = [
            [
                'user_id' => 1,
                'title' => 'Welcome to SmartFlo',
                'message' => 'Welcome to SmartFlo! Your account has been successfully created.',
                'type' => 'success',
                'is_read' => 0,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'user_id' => 1,
                'title' => 'System Update',
                'message' => 'SmartFlo has been updated to version 3.0 with new features and improvements.',
                'type' => 'info',
                'is_read' => 0,
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 hour')),
            ],
            [
                'user_id' => 1,
                'title' => 'Security Alert',
                'message' => 'Your password was changed successfully. If this wasn\'t you, please contact support.',
                'type' => 'security',
                'is_read' => 1,
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours')),
            ],
            [
                'user_id' => 1,
                'title' => 'Profile Updated',
                'message' => 'Your profile information has been updated successfully.',
                'type' => 'success',
                'is_read' => 1,
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
            ],
            [
                'user_id' => 1,
                'title' => 'Maintenance Notice',
                'message' => 'Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM IST.',
                'type' => 'warning',
                'is_read' => 0,
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
            ],
        ];

        $this->db->table('notifications')->insertBatch($data);
    }

    public function down()
    {
        $this->forge->dropTable('notifications');
    }
}
