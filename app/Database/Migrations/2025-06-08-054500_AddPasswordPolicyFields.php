<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddPasswordPolicyFields extends Migration
{
    public function up()
    {
        $fields = [
            'password_changed_at' => [
                'type' => 'DATETIME',
                'null' => true,
                'comment' => 'When password was last changed'
            ],
            'force_password_change' => [
                'type' => 'BOOLEAN',
                'default' => false,
                'comment' => 'Force user to change password on next login'
            ],
            'first_login' => [
                'type' => 'BOOLEAN',
                'default' => true,
                'comment' => 'Is this the users first login'
            ]
        ];

        $this->forge->addColumn('users', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('users', ['password_changed_at', 'force_password_change', 'first_login']);
    }
}
