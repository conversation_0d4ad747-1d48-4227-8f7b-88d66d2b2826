<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\AuthLibrary;
use App\Models\UserModel;

class Settings extends BaseController
{
    protected $authLib;
    protected $userModel;

    public function __construct()
    {
        $this->authLib = new AuthLibrary();
        $this->userModel = new UserModel();
    }

    /**
     * Settings main page
     */
    public function index()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        $user = $this->authLib->user();

        $data = [
            'title' => 'Settings - SmartFlo',
            'user' => $user,
            'page' => 'settings'
        ];

        return view('settings/index', $data);
    }

    /**
     * Account settings
     */
    public function account()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        $user = $this->authLib->user();

        $data = [
            'title' => 'Account Settings - SmartFlo',
            'user' => $user,
            'page' => 'account-settings'
        ];

        return view('settings/account', $data);
    }

    /**
     * Notification settings
     */
    public function notifications()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        $user = $this->authLib->user();

        $data = [
            'title' => 'Notification Settings - SmartFlo',
            'user' => $user,
            'page' => 'notification-settings'
        ];

        return view('settings/notifications', $data);
    }

    /**
     * Security settings
     */
    public function security()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        $user = $this->authLib->user();

        $data = [
            'title' => 'Security Settings - SmartFlo',
            'user' => $user,
            'page' => 'security-settings'
        ];

        return view('settings/security', $data);
    }

    /**
     * System preferences
     */
    public function preferences()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        $user = $this->authLib->user();

        $data = [
            'title' => 'Preferences - SmartFlo',
            'user' => $user,
            'page' => 'preferences'
        ];

        return view('settings/preferences', $data);
    }

    /**
     * Update notification settings
     */
    public function updateNotifications()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        // Validate CSRF
        if (!$this->validate(['csrf_token' => 'required'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid security token'
            ])->setStatusCode(403);
        }

        $user = $this->authLib->user();
        $settings = [
            'email_notifications' => $this->request->getPost('email_notifications') ? 1 : 0,
            'push_notifications' => $this->request->getPost('push_notifications') ? 1 : 0,
            'sms_notifications' => $this->request->getPost('sms_notifications') ? 1 : 0,
            'notification_frequency' => $this->request->getPost('notification_frequency') ?: 'immediate'
        ];

        // For now, we'll store these in session or a separate settings table
        // In a full implementation, you'd create a user_settings table
        session()->set('notification_settings_' . $user['id'], $settings);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Notification settings updated successfully'
        ]);
    }

    /**
     * Get notification settings
     */
    public function getNotificationSettings()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        $settings = session()->get('notification_settings_' . $user['id']) ?: [
            'email_notifications' => 1,
            'push_notifications' => 1,
            'sms_notifications' => 0,
            'notification_frequency' => 'immediate'
        ];

        return $this->response->setJSON([
            'success' => true,
            'data' => $settings
        ]);
    }

    /**
     * Update preferences
     */
    public function updatePreferences()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        // Validate CSRF
        if (!$this->validate(['csrf_token' => 'required'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid security token'
            ])->setStatusCode(403);
        }

        $user = $this->authLib->user();
        $preferences = [
            'theme' => $this->request->getPost('theme') ?: 'light',
            'language' => $this->request->getPost('language') ?: 'en',
            'timezone' => $this->request->getPost('timezone') ?: 'UTC',
            'date_format' => $this->request->getPost('date_format') ?: 'Y-m-d',
            'items_per_page' => (int)$this->request->getPost('items_per_page') ?: 20
        ];

        // Store in session for now
        session()->set('user_preferences_' . $user['id'], $preferences);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Preferences updated successfully'
        ]);
    }

    /**
     * Get user preferences
     */
    public function getPreferences()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        $preferences = session()->get('user_preferences_' . $user['id']) ?: [
            'theme' => 'light',
            'language' => 'en',
            'timezone' => 'UTC',
            'date_format' => 'Y-m-d',
            'items_per_page' => 20
        ];

        return $this->response->setJSON([
            'success' => true,
            'data' => $preferences
        ]);
    }
}
