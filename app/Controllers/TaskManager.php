<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Libraries\AuthLibrary;
use App\Models\TaskTypeModel;

class TaskManager extends Controller
{
    protected $authLib;
    protected $taskTypeModel;

    public function __construct()
    {
        $this->authLib = new AuthLibrary();
        $this->taskTypeModel = new TaskTypeModel();
    }

    /**
     * Task management page
     */
    public function index()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        $user = $this->authLib->user();

        // Check if user has permission to manage tasks
        if (!in_array($user['roles'], ['admin', 'manager'])) {
            return redirect()->to('/projects')->with('error', 'Access denied');
        }

        $data = [
            'title' => 'Task Management',
            'user' => $user,
            'page' => 'task-manager'
        ];

        return view('task_manager/index', $data);
    }

    /**
     * Get task categories and tasks
     */
    public function getTaskData()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        if (!in_array($user['roles'], ['admin', 'manager'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        // Get task types from database
        $taskTypes = $this->taskTypeModel->getActiveTaskTypes();

        // Group by category (for now we'll use a simple categorization)
        $taskData = [
            'office' => [],
            'site' => [],
            'design' => [],
            'management' => []
        ];

        foreach ($taskTypes as $task) {
            // Simple categorization based on task name
            $category = 'office'; // default
            $taskName = strtolower($task['name']);

            if (strpos($taskName, 'construction') !== false ||
                strpos($taskName, 'supervision') !== false ||
                strpos($taskName, 'safety') !== false) {
                $category = 'site';
            } elseif (strpos($taskName, 'design') !== false ||
                      strpos($taskName, '3d') !== false ||
                      strpos($taskName, 'modeling') !== false) {
                $category = 'design';
            } elseif (strpos($taskName, 'management') !== false ||
                      strpos($taskName, 'budget') !== false ||
                      strpos($taskName, 'client') !== false) {
                $category = 'management';
            }

            $taskData[$category][] = [
                'id' => $task['id'],
                'name' => $task['name'],
                'default_days' => $task['default_target_days'] ?? 7,
                'description' => $task['description'],
                'color' => $task['color'],
                'icon' => $task['icon']
            ];
        }

        return $this->response->setJSON([
            'success' => true,
            'categories' => array_keys($taskData),
            'tasks' => $taskData
        ]);
    }

    /**
     * Add new task category
     */
    public function addCategory()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        if (!in_array($user['roles'], ['admin', 'manager'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        $categoryName = $this->request->getPost('category_name');
        $categoryId = $this->request->getPost('category_id');

        if (empty($categoryName) || empty($categoryId)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Category name and ID are required'
            ]);
        }

        try {
            // Create a new task type for this category
            $taskTypeData = [
                'name' => $categoryName,
                'description' => 'Custom category: ' . $categoryName,
                'color' => '#6c757d',
                'icon' => 'fas fa-folder',
                'is_default' => 0,
                'sort_order' => 999,
                'is_active' => 1,
                'created_by' => $user['id'],
                'default_target_days' => 7
            ];

            $newTaskTypeId = $this->taskTypeModel->insert($taskTypeData);

            if ($newTaskTypeId) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Category added successfully',
                    'category' => [
                        'id' => $newTaskTypeId,
                        'name' => $categoryName
                    ]
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to create category'
                ]);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error creating category: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Add new task to category
     */
    public function addTask()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        if (!in_array($user['roles'], ['admin', 'manager'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        $categoryId = $this->request->getPost('category_id');
        $taskName = $this->request->getPost('task_name');
        $taskId = $this->request->getPost('task_id');
        $defaultDays = $this->request->getPost('default_days');

        if (empty($categoryId) || empty($taskName) || empty($taskId)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Category ID, task name, and task ID are required'
            ]);
        }

        try {
            // Create a new task type
            $taskTypeData = [
                'name' => $taskName,
                'description' => 'Task: ' . $taskName,
                'color' => '#0d6efd',
                'icon' => 'fas fa-tasks',
                'is_default' => 0,
                'sort_order' => 999,
                'is_active' => 1,
                'created_by' => $user['id'],
                'default_target_days' => (int)$defaultDays ?: 7
            ];

            $newTaskTypeId = $this->taskTypeModel->insert($taskTypeData);

            if ($newTaskTypeId) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Task added successfully',
                    'task' => [
                        'id' => $newTaskTypeId,
                        'name' => $taskName,
                        'category_id' => $categoryId,
                        'default_days' => (int)$defaultDays ?: 7
                    ]
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to create task'
                ]);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error creating task: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update task
     */
    public function updateTask()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        if (!in_array($user['roles'], ['admin', 'manager'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        $taskId = $this->request->getPost('task_id');
        $taskName = $this->request->getPost('task_name');
        $defaultDays = $this->request->getPost('default_days');

        if (empty($taskId) || empty($taskName)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Task ID and name are required'
            ]);
        }

        try {
            // Update the task type
            $updateData = [
                'name' => $taskName,
                'default_target_days' => (int)$defaultDays ?: 7,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $result = $this->taskTypeModel->update($taskId, $updateData);

            if ($result) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Task updated successfully',
                    'task' => [
                        'id' => $taskId,
                        'name' => $taskName,
                        'default_days' => (int)$defaultDays ?: 7
                    ]
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update task'
                ]);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating task: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Delete task
     */
    public function deleteTask()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        if (!in_array($user['roles'], ['admin', 'manager'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        $taskId = $this->request->getPost('task_id');

        if (empty($taskId)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Task ID is required'
            ]);
        }

        try {
            // Check if task can be deleted
            if (!$this->taskTypeModel->canDelete($taskId)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Cannot delete this task as it is being used in projects or is a default task'
                ]);
            }

            // Soft delete the task type
            $result = $this->taskTypeModel->softDelete($taskId);

            if ($result) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Task deleted successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to delete task'
                ]);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error deleting task: ' . $e->getMessage()
            ]);
        }
    }
}
