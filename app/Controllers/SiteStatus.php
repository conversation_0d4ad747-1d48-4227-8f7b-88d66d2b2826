<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\AuthLibrary;
use App\Models\SiteStatusModel;

class SiteStatus extends BaseController
{
    protected $authLib;
    protected $statusModel;

    public function __construct()
    {
        $this->authLib = new AuthLibrary();
        $this->statusModel = new SiteStatusModel();
    }

    /**
     * Site status management page
     */
    public function index()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        $user = $this->authLib->user();

        $data = [
            'title' => 'Site Status Management - SmartFlo',
            'user' => $user,
            'page' => 'site-status'
        ];

        return view('site_status/index', $data);
    }

    /**
     * Get current site status
     */
    public function getStatus()
    {
        $status = $this->statusModel->getCurrentStatus();
        
        return $this->response->setJSON([
            'success' => true,
            'status' => $status
        ]);
    }

    /**
     * Update site status
     */
    public function updateStatus()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        // Check if user has permission to update site status
        if (!$this->authLib->hasPermission('site.manage')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Insufficient permissions'
            ])->setStatusCode(403);
        }

        // Validate CSRF
        if (!$this->validate(['csrf_token' => 'required'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid security token'
            ])->setStatusCode(403);
        }

        $rules = [
            'status' => 'required|in_list[online,maintenance,construction,offline]',
            'message' => 'max_length[500]',
            'estimated_completion' => 'valid_date'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ])->setStatusCode(400);
        }

        $user = $this->authLib->user();
        $statusData = [
            'status' => $this->request->getPost('status'),
            'message' => $this->request->getPost('message') ?: null,
            'estimated_completion' => $this->request->getPost('estimated_completion') ?: null,
            'updated_by' => $user['id'],
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($this->statusModel->updateStatus($statusData)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Site status updated successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update site status'
            ])->setStatusCode(500);
        }
    }

    /**
     * Get status history
     */
    public function getHistory()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $page = (int) $this->request->getGet('page') ?: 1;
        $limit = 20;

        $history = $this->statusModel->getStatusHistory($limit, ($page - 1) * $limit);
        $totalRecords = $this->statusModel->countStatusHistory();

        return $this->response->setJSON([
            'success' => true,
            'history' => $history,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($totalRecords / $limit),
                'total_records' => $totalRecords
            ]
        ]);
    }

    /**
     * Get status statistics
     */
    public function getStats()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $stats = $this->statusModel->getStatusStats();

        return $this->response->setJSON([
            'success' => true,
            'stats' => $stats
        ]);
    }

    /**
     * Public status page (for visitors)
     */
    public function publicStatus()
    {
        $status = $this->statusModel->getCurrentStatus();

        $data = [
            'title' => 'Site Status - SmartFlo',
            'status' => $status,
            'page' => 'public-status'
        ];

        return view('site_status/public', $data);
    }

    /**
     * API endpoint for status (for external monitoring)
     */
    public function apiStatus()
    {
        $status = $this->statusModel->getCurrentStatus();
        
        $response = [
            'status' => $status['status'],
            'message' => $status['message'],
            'last_updated' => $status['updated_at'],
            'estimated_completion' => $status['estimated_completion']
        ];

        return $this->response->setJSON($response);
    }

    /**
     * Schedule maintenance
     */
    public function scheduleMaintenance()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        // Check permissions
        if (!$this->authLib->hasPermission('site.manage')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Insufficient permissions'
            ])->setStatusCode(403);
        }

        // Validate CSRF
        if (!$this->validate(['csrf_token' => 'required'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid security token'
            ])->setStatusCode(403);
        }

        $rules = [
            'start_time' => 'required|valid_date',
            'end_time' => 'required|valid_date',
            'message' => 'required|max_length[500]'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ])->setStatusCode(400);
        }

        $user = $this->authLib->user();
        $maintenanceData = [
            'start_time' => $this->request->getPost('start_time'),
            'end_time' => $this->request->getPost('end_time'),
            'message' => $this->request->getPost('message'),
            'scheduled_by' => $user['id']
        ];

        if ($this->statusModel->scheduleMaintenance($maintenanceData)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Maintenance scheduled successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to schedule maintenance'
            ])->setStatusCode(500);
        }
    }

    /**
     * Get scheduled maintenance
     */
    public function getScheduledMaintenance()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $maintenance = $this->statusModel->getScheduledMaintenance();

        return $this->response->setJSON([
            'success' => true,
            'maintenance' => $maintenance
        ]);
    }

    /**
     * Cancel scheduled maintenance
     */
    public function cancelMaintenance($maintenanceId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        // Check permissions
        if (!$this->authLib->hasPermission('site.manage')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Insufficient permissions'
            ])->setStatusCode(403);
        }

        if ($this->statusModel->cancelMaintenance($maintenanceId)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Maintenance cancelled successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to cancel maintenance'
            ])->setStatusCode(500);
        }
    }
}
