<?php

namespace App\Controllers;

use CodeIgniter\RESTful\ResourceController;

class Api extends ResourceController
{
    protected $format = 'json';
    protected $session;

    public function __construct()
    {
        $this->session = \Config\Services::session();
    }

    /**
     * Get unread message and notification counts
     */
    public function unreadCounts()
    {
        // Check if user is authenticated
        if (!$this->session->get('user_id')) {
            return $this->respond([
                'success' => false,
                'message' => 'Unauthorized'
            ], 401);
        }

        $userId = $this->session->get('user_id');

        try {
            // Get unread message count
            $messageModel = new \App\Models\MessageModel();
            $unreadMessages = $messageModel->where('recipient_id', $userId)
                                          ->where('is_read', 0)
                                          ->countAllResults();

            // Get unread notification count
            $notificationModel = new \App\Models\NotificationModel();
            $unreadNotifications = $notificationModel->getUnreadCount($userId);

            return $this->respond([
                'success' => true,
                'data' => [
                    'messages' => $unreadMessages,
                    'notifications' => $unreadNotifications
                ]
            ]);

        } catch (\Exception $e) {
            return $this->respond([
                'success' => false,
                'message' => 'Error fetching counts: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get recent messages for dropdown
     */
    public function recentMessages()
    {
        if (!$this->session->get('user_id')) {
            return $this->respond([
                'success' => false,
                'message' => 'Unauthorized'
            ], 401);
        }

        $userId = $this->session->get('user_id');

        try {
            $messageModel = new \App\Models\MessageModel();
            $messages = $messageModel->select('messages.*, users.username as sender_username')
                                   ->join('users', 'users.id = messages.sender_id', 'left')
                                   ->where('messages.recipient_id', $userId)
                                   ->orderBy('messages.created_at', 'DESC')
                                   ->limit(5)
                                   ->findAll();

            return $this->respond([
                'success' => true,
                'data' => $messages
            ]);

        } catch (\Exception $e) {
            return $this->respond([
                'success' => false,
                'message' => 'Error fetching messages: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get recent notifications for dropdown
     */
    public function recentNotifications()
    {
        if (!$this->session->get('user_id')) {
            return $this->respond([
                'success' => false,
                'message' => 'Unauthorized'
            ], 401);
        }

        $userId = $this->session->get('user_id');

        try {
            $notificationModel = new \App\Models\NotificationModel();
            $notifications = $notificationModel->getRecentNotifications($userId, 5);

            return $this->respond([
                'success' => true,
                'data' => $notifications
            ]);

        } catch (\Exception $e) {
            return $this->respond([
                'success' => false,
                'message' => 'Error fetching notifications: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark notification as read
     */
    public function markNotificationRead($id)
    {
        if (!$this->session->get('user_id')) {
            return $this->respond([
                'success' => false,
                'message' => 'Unauthorized'
            ], 401);
        }

        $userId = $this->session->get('user_id');

        try {
            $notificationModel = new \App\Models\NotificationModel();
            $result = $notificationModel->markAsRead($id, $userId);

            if ($result) {
                return $this->respond([
                    'success' => true,
                    'message' => 'Notification marked as read'
                ]);
            } else {
                return $this->respond([
                    'success' => false,
                    'message' => 'Failed to mark notification as read'
                ], 500);
            }

        } catch (\Exception $e) {
            return $this->respond([
                'success' => false,
                'message' => 'Error marking notification as read: ' . $e->getMessage()
            ], 500);
        }
    }
}
