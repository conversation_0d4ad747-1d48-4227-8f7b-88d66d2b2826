<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Models\ProjectModel;
use App\Models\ProjectTaskModel;
use App\Models\ProjectTimelineModel;

class TestWorkflow extends Controller
{
    protected $projectModel;
    protected $projectTaskModel;
    protected $timelineModel;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->projectTaskModel = new ProjectTaskModel();
        $this->timelineModel = new ProjectTimelineModel();
    }

    public function index()
    {
        echo "<h1>SmartFlo Workflow Test</h1>";

        // Fix 1: Update NULL values
        echo "<h2>0. Fix NULL Values</h2>";
        $this->fixNullValues();

        // Test 1: Check task manager status field
        echo "<h2>1. Task Manager Status Field Test</h2>";
        $this->testTaskManagerStatus();

        // Test 2: Check project data structure
        echo "<h2>2. Project Data Structure Test</h2>";
        $this->testProjectDataStructure();

        // Test 3: Check timeline functionality
        echo "<h2>3. Timeline Functionality Test</h2>";
        $this->testTimelineFunctionality();

        // Test 4: Check billing integration
        echo "<h2>4. Billing Integration Test</h2>";
        $this->testBillingIntegration();
    }

    private function testTaskManagerStatus()
    {
        try {
            // Get a sample of tasks with their manager status
            $tasks = $this->projectTaskModel
                ->select('id, task_name, status, task_manager_status, payment_status, payment_amount')
                ->limit(10)
                ->findAll();
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Task Name</th><th>Status</th><th>Manager Status</th><th>Payment Status</th><th>Payment Amount</th></tr>";
            
            foreach ($tasks as $task) {
                echo "<tr>";
                echo "<td>{$task['id']}</td>";
                echo "<td>{$task['task_name']}</td>";
                echo "<td>{$task['status']}</td>";
                echo "<td>" . ($task['task_manager_status'] ?? 'NULL') . "</td>";
                echo "<td>" . ($task['payment_status'] ?? 'NULL') . "</td>";
                echo "<td>" . ($task['payment_amount'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Check for tasks that should show "Waiting to submit to client"
            $completedTasks = $this->projectTaskModel
                ->where('status', 'completed')
                ->where('task_manager_status IS NULL OR task_manager_status = "not_reviewed"')
                ->findAll();
            
            echo "<p><strong>Tasks that should show 'Waiting to submit to client': " . count($completedTasks) . "</strong></p>";
            
        } catch (\Exception $e) {
            echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
        }
    }

    private function testProjectDataStructure()
    {
        try {
            // Test the project data structure used by the frontend
            $projects = $this->projectModel->getProjectsForUser(3, 'user'); // User ID 3
            
            echo "<p><strong>Total projects for user 3: " . count($projects) . "</strong></p>";
            
            if (!empty($projects)) {
                $project = $projects[0];
                echo "<h3>Sample Project Data Structure:</h3>";
                echo "<pre>";
                print_r([
                    'id' => $project['id'] ?? 'missing',
                    'task_id' => $project['task_id'] ?? 'missing',
                    'task_name' => $project['task_name'] ?? 'missing',
                    'task_manager_status' => $project['task_manager_status'] ?? 'missing',
                    'status' => $project['status'] ?? 'missing',
                    'task_assigned_to' => $project['task_assigned_to'] ?? 'missing',
                    'task_due_date' => $project['task_due_date'] ?? 'missing',
                    'payment_amount' => $project['payment_amount'] ?? 'missing',
                    'payment_status' => $project['payment_status'] ?? 'missing',
                    'payment_account' => $project['payment_account'] ?? 'missing'
                ]);
                echo "</pre>";

                echo "<h3>All Project Fields:</h3>";
                echo "<pre>";
                print_r(array_keys($project));
                echo "</pre>";
            }
            
        } catch (\Exception $e) {
            echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
        }
    }

    private function testTimelineFunctionality()
    {
        try {
            // Test timeline functionality
            $timeline = $this->timelineModel->getProjectTimeline(2); // Project ID 2
            
            echo "<p><strong>Timeline entries for project 2: " . count($timeline) . "</strong></p>";
            
            if (!empty($timeline)) {
                echo "<h3>Sample Timeline Entry:</h3>";
                echo "<pre>";
                print_r($timeline[0]);
                echo "</pre>";
            }
            
        } catch (\Exception $e) {
            echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
        }
    }

    private function testBillingIntegration()
    {
        try {
            // Test billing data
            $billingTasks = $this->projectTaskModel
                ->select('id, task_name, payment_amount, payment_status, task_manager_status')
                ->where('payment_amount >', 0)
                ->findAll();
            
            echo "<p><strong>Tasks with payment amounts: " . count($billingTasks) . "</strong></p>";
            
            if (!empty($billingTasks)) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>ID</th><th>Task Name</th><th>Amount</th><th>Payment Status</th><th>Manager Status</th></tr>";
                
                foreach (array_slice($billingTasks, 0, 5) as $task) {
                    echo "<tr>";
                    echo "<td>{$task['id']}</td>";
                    echo "<td>{$task['task_name']}</td>";
                    echo "<td>{$task['payment_amount']}</td>";
                    echo "<td>" . ($task['payment_status'] ?? 'NULL') . "</td>";
                    echo "<td>" . ($task['task_manager_status'] ?? 'NULL') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            
        } catch (\Exception $e) {
            echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
        }
    }

    private function fixNullValues()
    {
        try {
            // Fix NULL task_manager_status
            $updated1 = $this->projectTaskModel
                ->where('task_manager_status IS NULL')
                ->set(['task_manager_status' => 'not_reviewed'])
                ->update();

            // Fix empty payment_status
            $updated2 = $this->projectTaskModel
                ->where('payment_status IS NULL OR payment_status = ""')
                ->set(['payment_status' => 'unpaid'])
                ->update();

            // Fix NULL revision_count
            $updated3 = $this->projectTaskModel
                ->where('revision_count IS NULL')
                ->set(['revision_count' => 0])
                ->update();

            echo "<p><strong>Fixed NULL values:</strong></p>";
            echo "<ul>";
            echo "<li>Task manager status: {$updated1} tasks updated</li>";
            echo "<li>Payment status: {$updated2} tasks updated</li>";
            echo "<li>Revision count: {$updated3} tasks updated</li>";
            echo "</ul>";

        } catch (\Exception $e) {
            echo "<p style='color: red;'>Error fixing NULL values: " . $e->getMessage() . "</p>";
        }
    }
}
