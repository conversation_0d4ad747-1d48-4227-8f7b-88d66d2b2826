<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\AuthLibrary;
use App\Models\ProjectModel;

class ProjectStatus extends BaseController
{
    protected $authLib;
    protected $projectModel;

    public function __construct()
    {
        $this->authLib = new AuthLibrary();
        $this->projectModel = new ProjectModel();
    }

    /**
     * Project status dashboard
     */
    public function index()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        $user = $this->authLib->user();

        $data = [
            'title' => 'Project Status',
            'user' => $user,
            'page' => 'project-status'
        ];

        return view('projects/status', $data);
    }

    /**
     * Get all projects
     */
    public function getProjects()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        $projects = $this->projectModel->getProjectsForUser($user['id'], $user['roles']);

        return $this->response->setJSON([
            'success' => true,
            'projects' => $projects
        ]);
    }

    /**
     * Get project details
     */
    public function getProject($projectId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $project = $this->projectModel->find($projectId);
        if (!$project) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Project not found'
            ])->setStatusCode(404);
        }

        return $this->response->setJSON([
            'success' => true,
            'project' => $project
        ]);
    }

    /**
     * Create new project
     */
    public function create()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        // Allow all authenticated users to create projects for now
        // TODO: Add proper permission checks later

        // Validate CSRF
        if (!$this->validate(['csrf_token' => 'required'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid security token'
            ])->setStatusCode(403);
        }

        $rules = [
            'client_name' => 'required|max_length[100]',
            'project_name' => 'required|max_length[150]',
            'location' => 'required|max_length[255]',
            'start_date' => 'required|valid_date',
            'estimated_completion' => 'valid_date',
            'budget' => 'decimal',
            'status' => 'required|in_list[not_started,planning,in_progress,on_hold,completed,cancelled,review,sent_for_review,revision_needed,client_accepted,task_completed]'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ])->setStatusCode(400);
        }

        $user = $this->authLib->user();
        $projectData = [
            'client_name' => $this->request->getPost('client_name'),
            'project_name' => $this->request->getPost('project_name'),
            'location' => $this->request->getPost('location'),
            'description' => $this->request->getPost('description'),
            'start_date' => $this->request->getPost('start_date'),
            'estimated_completion' => $this->request->getPost('estimated_completion'),
            'budget' => $this->request->getPost('budget'),
            'status' => $this->request->getPost('status'),
            'progress_percentage' => 0,
            'created_by' => $user['id']
        ];

        $projectId = $this->projectModel->insert($projectData);

        if ($projectId) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Project created successfully',
                'project_id' => $projectId
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to create project'
            ])->setStatusCode(500);
        }
    }

    /**
     * Update project
     */
    public function update($projectId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        // Allow all authenticated users to update projects for now
        // TODO: Add proper permission checks later

        $project = $this->projectModel->find($projectId);
        if (!$project) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Project not found'
            ])->setStatusCode(404);
        }

        $rules = [
            'client_name' => 'required|max_length[100]',
            'project_name' => 'required|max_length[150]',
            'location' => 'required|max_length[255]',
            'start_date' => 'required|valid_date',
            'estimated_completion' => 'valid_date',
            'budget' => 'decimal',
            'status' => 'required|in_list[not_started,planning,in_progress,on_hold,completed,cancelled,review,sent_for_review,revision_needed,client_accepted,task_completed]',
            'progress_percentage' => 'integer|greater_than_equal_to[0]|less_than_equal_to[100]'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ])->setStatusCode(400);
        }

        $updateData = [
            'client_name' => $this->request->getPost('client_name'),
            'project_name' => $this->request->getPost('project_name'),
            'location' => $this->request->getPost('location'),
            'description' => $this->request->getPost('description'),
            'start_date' => $this->request->getPost('start_date'),
            'estimated_completion' => $this->request->getPost('estimated_completion'),
            'budget' => $this->request->getPost('budget'),
            'status' => $this->request->getPost('status'),
            'progress_percentage' => $this->request->getPost('progress_percentage') ?: 0
        ];

        if ($this->projectModel->update($projectId, $updateData)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Project updated successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update project'
            ])->setStatusCode(500);
        }
    }

    /**
     * Delete project
     */
    public function delete($projectId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        // Allow all authenticated users to delete projects for now
        // TODO: Add proper permission checks later

        $project = $this->projectModel->find($projectId);
        if (!$project) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Project not found'
            ])->setStatusCode(404);
        }

        // Load ProjectTaskModel to check for running tasks
        $projectTaskModel = new \App\Models\ProjectTaskModel();

        // Check if any tasks are currently running (in_progress status)
        $runningTasks = $projectTaskModel->where('project_id', $projectId)
                                        ->where('status', 'in_progress')
                                        ->findAll();

        if (!empty($runningTasks)) {
            $taskNames = array_column($runningTasks, 'task_name');
            $taskList = implode(', ', $taskNames);

            return $this->response->setJSON([
                'success' => false,
                'message' => "Cannot delete project. The following tasks are currently running: {$taskList}. Please complete or pause these tasks before deleting the project."
            ])->setStatusCode(400);
        }

        // Start transaction to delete project and all its tasks
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            // Delete related project tasks first
            $projectTaskModel->where('project_id', $projectId)->delete();

            // Delete the project
            $result = $this->projectModel->delete($projectId);

            $db->transComplete();

            if ($db->transStatus() === false || !$result) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to delete project'
                ])->setStatusCode(500);
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Project deleted successfully'
            ]);
        } catch (\Exception $e) {
            $db->transRollback();
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error deleting project: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Update project progress
     */
    public function updateProgress($projectId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $project = $this->projectModel->find($projectId);
        if (!$project) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Project not found'
            ])->setStatusCode(404);
        }

        $progress = $this->request->getPost('progress_percentage');
        $notes = $this->request->getPost('notes');

        if ($progress < 0 || $progress > 100) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Progress must be between 0 and 100'
            ])->setStatusCode(400);
        }

        $updateData = [
            'progress_percentage' => $progress,
            'last_update_notes' => $notes,
            'last_updated' => date('Y-m-d H:i:s')
        ];

        if ($this->projectModel->update($projectId, $updateData)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Project progress updated successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update project progress'
            ])->setStatusCode(500);
        }
    }

    /**
     * Get project statistics
     */
    public function getStats()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        $stats = $this->projectModel->getProjectStats($user['id'], $user['roles']);

        return $this->response->setJSON([
            'success' => true,
            'stats' => $stats
        ]);
    }
}
