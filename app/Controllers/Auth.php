<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\AuthLibrary;
use App\Libraries\SecurityLibrary;
use App\Models\UserModel;
use App\Models\PasswordResetModel;
use CodeIgniter\HTTP\ResponseInterface;

class Auth extends BaseController
{
    protected $authLib;
    protected $securityLib;
    protected $userModel;
    protected $failedLoginModel;
    protected $passwordResetModel;

    public function __construct()
    {
        $this->authLib = new AuthLibrary();
        $this->securityLib = new SecurityLibrary();
        $this->userModel = new UserModel();
        $this->failedLoginModel = new \App\Models\FailedLoginAttemptModel();
        $this->passwordResetModel = new PasswordResetModel();
    }

    /**
     * Default index - redirect to dashboard if logged in, otherwise login
     */
    public function index()
    {
        // Check if already logged in
        if ($this->authLib->isLoggedIn()) {
            return redirect()->to('/dashboard');
        }

        // Check remember token
        $this->authLib->checkRememberToken();
        if ($this->authLib->isLoggedIn()) {
            return redirect()->to('/dashboard');
        }

        return redirect()->to('/auth/login');
    }

    /**
     * Show login form
     */
    public function login()
    {
        // Check if already logged in
        if ($this->authLib->isLoggedIn()) {
            return redirect()->to('/dashboard');
        }

        // Check remember token
        $this->authLib->checkRememberToken();
        if ($this->authLib->isLoggedIn()) {
            return redirect()->to('/dashboard');
        }

        $data = [
            'title' => 'Login - SmartFlo',
            'csrf_token' => csrf_token(),
            'csrf_hash' => csrf_hash()
        ];

        return view('auth/login', $data);
    }

    /**
     * Process login
     */
    public function processLogin()
    {
        // Validate CSRF
        if (!$this->validate(['csrf_token' => 'required'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid security token.'
            ]);
        }

        // Validate input
        $rules = [
            'username' => 'required|min_length[3]|max_length[50]',
            'password' => 'required|min_length[8]'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Please check your input.',
                'errors' => $this->validator->getErrors()
            ]);
        }

        $username = $this->securityLib->sanitizeInput($this->request->getPost('username'));
        $password = $this->request->getPost('password');
        $remember = $this->request->getPost('remember') === '1';
        $ipAddress = $this->request->getIPAddress();

        // Check if IP is blocked
        if ($this->failedLoginModel->isIPBlocked($ipAddress)) {
            $this->failedLoginModel->logFailedAttempt($username, null, 'ip_blocked');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Too many failed attempts. Your IP is temporarily blocked.'
            ]);
        }

        // Check recent failed attempts for this username/IP
        $recentAttempts = $this->failedLoginModel->getRecentAttempts($username, $ipAddress, 15);
        if ($recentAttempts >= 5) {
            // Block IP for 30 minutes after 5 failed attempts
            $this->failedLoginModel->blockIP($ipAddress, 30);
            $this->failedLoginModel->logFailedAttempt($username, null, 'too_many_attempts');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Too many failed attempts. Your IP has been blocked for 30 minutes.'
            ]);
        }

        // Attempt login
        $result = $this->authLib->attempt($username, $password, $remember);

        if ($result['success']) {
            // Log successful login
            $this->securityLib->logSecurityEvent('successful_login', [
                'username' => $username
            ]);

            $response = [
                'success' => true,
                'message' => $result['message']
            ];

            // Check if password change is required
            if (isset($result['requires_password_change']) && $result['requires_password_change']) {
                $response['redirect'] = '/auth/change-password';
                $response['message'] = 'Please change your password to continue.';
            } else {
                $response['redirect'] = '/dashboard';
            }

            return $this->response->setJSON($response);
        } else {
            // Log failed attempt in database
            $user = $this->userModel->where('username', $username)->first();
            $email = $user ? $user['email'] : null;

            $this->failedLoginModel->logFailedAttempt($username, $email, 'invalid_credentials');

            // Log failed login in security log
            $this->securityLib->logSecurityEvent('failed_login', [
                'username' => $username,
                'reason' => $result['message']
            ]);

            return $this->response->setJSON([
                'success' => false,
                'message' => $result['message']
            ]);
        }
    }

    /**
     * Check user status (for real-time updates)
     */
    public function status()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Not authenticated'
            ]);
        }

        $user = $this->authLib->user();

        // Check if user is still active
        $currentUser = $this->userModel->find($user['id']);

        if (!$currentUser || !$currentUser['is_active']) {
            // User has been deactivated
            $this->authLib->logout();

            return $this->response->setJSON([
                'success' => false,
                'message' => 'Account deactivated',
                'data' => ['status' => 'inactive']
            ]);
        }

        return $this->response->setJSON([
            'success' => true,
            'message' => 'User is active',
            'data' => [
                'status' => 'active',
                'user' => $currentUser
            ]
        ]);
    }

    /**
     * Logout
     */
    public function logout()
    {
        $user = $this->authLib->user();

        if ($user) {
            $this->securityLib->logSecurityEvent('logout', [
                'username' => $user['username']
            ]);
        }

        // Clear all session data
        $this->authLib->logout();

        // Clear any cached data
        $cache = \Config\Services::cache();
        $cache->clean();

        // Set headers to prevent caching
        $response = service('response');
        $response->setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        $response->setHeader('Pragma', 'no-cache');
        $response->setHeader('Expires', '0');

        return redirect()->to('/auth/login')
                        ->with('message', 'You have been logged out successfully.')
                        ->withHeaders([
                            'Cache-Control' => 'no-cache, no-store, must-revalidate',
                            'Pragma' => 'no-cache',
                            'Expires' => '0'
                        ]);
    }

    /**
     * Get fresh CSRF token
     */
    public function csrfToken()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(404);
        }

        // Generate new CSRF token
        $token = csrf_hash();
        
        // Update session with new token
        session()->set('csrf_token', $token);

        return $this->response->setJSON([
            'success' => true,
            'csrf_token' => $token,
            'timestamp' => time()
        ]);
    }

    /**
     * Show forgot password form
     */
    public function forgotPassword()
    {
        $data = [
            'title' => 'Forgot Password - SmartFlo',
            'csrf_token' => csrf_token(),
            'csrf_hash' => csrf_hash()
        ];

        return view('auth/forgot_password', $data);
    }

    /**
     * Process forgot password
     */
    public function processForgotPassword()
    {
        // Validate CSRF
        if (!$this->validate(['csrf_token' => 'required'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid security token.'
            ]);
        }

        // Validate email
        $rules = [
            'email' => 'required|valid_email'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Please enter a valid email address.',
                'errors' => $this->validator->getErrors()
            ]);
        }

        $email = $this->securityLib->sanitizeInput($this->request->getPost('email'), 'email');

        // Check rate limiting
        $rateLimitKey = 'password_reset_' . md5($email);
        $rateLimit = $this->securityLib->checkRateLimit($rateLimitKey, 3, 3600);

        if (!$rateLimit['allowed']) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Too many password reset requests. Please try again later.'
            ]);
        }

        // Check if user exists
        $user = $this->userModel->getUserByEmail($email);

        if (!$user) {
            // Don't reveal if email exists, but still record the attempt
            $this->securityLib->recordRateLimitAttempt($rateLimitKey, 3600);
            
            return $this->response->setJSON([
                'success' => true,
                'message' => 'If an account with that email exists, a password reset link has been sent.'
            ]);
        }

        // Check for recent reset request
        if ($this->passwordResetModel->hasRecentRequest($email, 5)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'A password reset email was already sent recently. Please check your email.'
            ]);
        }

        // Generate reset token
        $token = $this->passwordResetModel->generateToken();
        
        if ($this->passwordResetModel->createResetToken($email, $token)) {
            // Send reset email
            $this->sendPasswordResetEmail($email, $token);
            
            // Record rate limit attempt
            $this->securityLib->recordRateLimitAttempt($rateLimitKey, 3600);
            
            // Log password reset request
            $this->securityLib->logSecurityEvent('password_reset_requested', [
                'email' => $email
            ]);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'If an account with that email exists, a password reset link has been sent.'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'An error occurred. Please try again.'
        ]);
    }

    /**
     * Show reset password form
     */
    public function resetPassword($token = null)
    {
        if (!$token) {
            return redirect()->to('/auth/forgot-password')->with('error', 'Invalid reset token.');
        }

        // Verify token
        $tokenData = $this->passwordResetModel->verifyResetToken($token);

        if (!$tokenData) {
            return redirect()->to('/auth/forgot-password')->with('error', 'Invalid or expired reset token.');
        }

        $data = [
            'title' => 'Reset Password - SmartFlo',
            'token' => $token,
            'email' => $tokenData['email'],
            'csrf_token' => csrf_token(),
            'csrf_hash' => csrf_hash()
        ];

        return view('auth/reset_password', $data);
    }

    /**
     * Process reset password
     */
    public function processResetPassword()
    {
        // Validate CSRF
        if (!$this->validate(['csrf_token' => 'required'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid security token.'
            ]);
        }

        // Validate input
        $rules = [
            'token' => 'required',
            'password' => 'required|min_length[8]',
            'password_confirm' => 'required|matches[password]'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Please check your input.',
                'errors' => $this->validator->getErrors()
            ]);
        }

        $token = $this->request->getPost('token');
        $password = $this->request->getPost('password');

        // Validate password strength
        $passwordValidation = $this->securityLib->validatePassword($password);
        if (!$passwordValidation['valid']) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Password does not meet requirements.',
                'errors' => ['password' => $passwordValidation['errors']]
            ]);
        }

        // Verify token
        $tokenData = $this->passwordResetModel->verifyResetToken($token);

        if (!$tokenData) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid or expired reset token.'
            ]);
        }

        // Update password
        $user = $this->userModel->getUserByEmail($tokenData['email']);
        
        if ($user) {
            $this->userModel->update($user['id'], [
                'password_hash' => password_hash($password, PASSWORD_DEFAULT)
            ]);

            // Delete reset token
            $this->passwordResetModel->deleteResetToken($token);

            // Log password reset
            $this->securityLib->logSecurityEvent('password_reset_completed', [
                'email' => $tokenData['email']
            ]);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Password has been reset successfully. You can now log in.',
                'redirect' => '/auth/login'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'An error occurred. Please try again.'
        ]);
    }

    /**
     * Send password reset email
     */
    protected function sendPasswordResetEmail($email, $token)
    {
        $emailService = \Config\Services::email();
        
        $resetLink = base_url("auth/reset-password/{$token}");
        
        $message = "
        <h2>Password Reset Request</h2>
        <p>You have requested to reset your password for SmartFlo.</p>
        <p>Click the link below to reset your password:</p>
        <p><a href='{$resetLink}' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Reset Password</a></p>
        <p>This link will expire in 1 hour.</p>
        <p>If you did not request this reset, please ignore this email.</p>
        ";

        $emailService->setTo($email);
        $emailService->setSubject('Password Reset - SmartFlo');
        $emailService->setMessage($message);

        return $emailService->send();
    }

    /**
     * Show change password form
     */
    public function changePassword()
    {
        // Check if user is logged in
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login');
        }

        $user = $this->authLib->user();
        $needsPasswordChange = $this->userModel->needsPasswordChange($user['id']);

        $data = [
            'title' => 'Change Password - SmartFlo',
            'user' => $user,
            'required' => $needsPasswordChange,
            'csrf_token' => csrf_token(),
            'csrf_hash' => csrf_hash()
        ];

        return view('auth/change_password', $data);
    }

    /**
     * Process change password
     */
    public function processChangePassword()
    {
        // Check if user is logged in
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'You must be logged in to change your password.'
            ]);
        }

        // Validate CSRF
        if (!$this->validate(['csrf_token' => 'required'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid security token.'
            ]);
        }

        // Validate input
        $rules = [
            'current_password' => 'required',
            'new_password' => 'required|min_length[8]',
            'confirm_password' => 'required|matches[new_password]'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Please check your input.',
                'errors' => $this->validator->getErrors()
            ]);
        }

        $user = $this->authLib->user();
        $currentPassword = $this->request->getPost('current_password');
        $newPassword = $this->request->getPost('new_password');

        // Verify current password
        if (!password_verify($currentPassword, $user['password_hash'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Current password is incorrect.'
            ]);
        }

        // Validate new password strength
        $passwordValidation = $this->securityLib->validatePassword($newPassword);
        if (!$passwordValidation['valid']) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'New password does not meet requirements.',
                'errors' => ['new_password' => $passwordValidation['errors']]
            ]);
        }

        // Check if new password is different from current
        if (password_verify($newPassword, $user['password_hash'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'New password must be different from your current password.'
            ]);
        }

        // Update password
        if ($this->userModel->updatePassword($user['id'], $newPassword)) {
            // Log password change
            $this->securityLib->logSecurityEvent('password_changed', [
                'user_id' => $user['id'],
                'username' => $user['username']
            ]);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Password changed successfully.',
                'redirect' => '/dashboard'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'An error occurred while changing your password. Please try again.'
        ]);
    }

    /**
     * Show user profile
     */
    public function profile()
    {
        // Check if user is logged in
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login');
        }

        $user = $this->authLib->user();

        // Get user roles
        $userRoleModel = new \App\Models\UserRoleModel();
        $userRoles = $userRoleModel->getUserRoles($user['id']);

        $data = [
            'title' => 'Profile - SmartFlo',
            'user' => $user,
            'userRoles' => $userRoles,
            'csrf_token' => csrf_token(),
            'csrf_hash' => csrf_hash()
        ];

        return view('auth/profile', $data);
    }

    /**
     * Update user profile
     */
    public function updateProfile()
    {
        // Check if user is logged in
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'You must be logged in to update your profile.'
            ]);
        }

        // CSRF validation is handled automatically by CodeIgniter's CSRF filter
        // No need for manual validation here

        $user = $this->authLib->user();
        $userId = $user['id'];

        // Validate input - username and email cannot be changed
        $rules = [
            'first_name' => 'permit_empty|max_length[50]',
            'last_name' => 'permit_empty|max_length[50]',
            'phone' => 'permit_empty|max_length[20]',
            'bio' => 'permit_empty|max_length[500]'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Please check your input.',
                'errors' => $this->validator->getErrors()
            ]);
        }

        // Prepare update data (excluding username, email, and id)
        $updateData = [
            'first_name' => $this->securityLib->sanitizeInput($this->request->getPost('first_name')),
            'last_name' => $this->securityLib->sanitizeInput($this->request->getPost('last_name')),
            'phone' => $this->securityLib->sanitizeInput($this->request->getPost('phone')),
            'bio' => $this->securityLib->sanitizeInput($this->request->getPost('bio'))
        ];

        // Remove empty values
        $updateData = array_filter($updateData, function($value) {
            return $value !== null && $value !== '';
        });

        // Update profile
        if ($this->userModel->update($userId, $updateData)) {
            // Log profile update
            $this->securityLib->logSecurityEvent('profile_updated', [
                'user_id' => $userId,
                'username' => $user['username'],
                'updated_fields' => array_keys($updateData)
            ]);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Profile updated successfully.'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'An error occurred while updating your profile. Please try again.'
        ]);
    }
}
