<?php

namespace App\Controllers;

class Expenses extends BaseController
{
    public function addMaterial()
    {
        $data = [
            'title' => 'Add Material Expense - SmartFlo',
            'page' => 'add-material'
        ];

        return view('expenses/add_material', $data);
    }

    public function storeMaterial()
    {
        // Handle material expense storage
        return redirect()->to('/dashboard')->with('success', 'Material expense added successfully');
    }

    public function addLabour()
    {
        $data = [
            'title' => 'Add Labour Expense - SmartFlo',
            'page' => 'add-labour'
        ];

        return view('expenses/add_labour', $data);
    }

    public function storeLabour()
    {
        // Handle labour expense storage
        return redirect()->to('/dashboard')->with('success', 'Labour expense added successfully');
    }

    public function addOffice()
    {
        $data = [
            'title' => 'Add Office Expense - SmartFlo',
            'page' => 'add-office'
        ];

        return view('expenses/add_office', $data);
    }

    public function storeOffice()
    {
        // Handle office expense storage
        return redirect()->to('/dashboard')->with('success', 'Office expense added successfully');
    }
}
