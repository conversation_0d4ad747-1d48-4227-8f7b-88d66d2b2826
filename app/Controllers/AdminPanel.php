<?php

namespace App\Controllers;

use CodeIgniter\Controller;

class AdminPanel extends Controller
{
    protected $authLib;
    protected $session;

    public function __construct()
    {
        $this->authLib = service('auth');
        $this->session = session();
        
        // Check if user is logged in
        if (!$this->authLib->isLoggedIn()) {
            redirect()->to('/auth/login')->send();
            exit;
        }
        
        // Check if user is admin
        $user = $this->authLib->getCurrentUser();
        if ($user['role'] !== 'admin') {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Access denied');
        }
    }

    public function index()
    {
        $user = $this->authLib->getCurrentUser();
        
        // Admin panel specific actions
        $adminActions = [
            [
                'title' => 'User Management',
                'icon' => 'fas fa-users-cog',
                'url' => '/admin/users',
                'description' => 'Manage user accounts, roles and permissions'
            ],
            [
                'title' => 'System Status',
                'icon' => 'fas fa-server',
                'url' => '/admin/system/status',
                'description' => 'Monitor system health and performance'
            ],
            [
                'title' => 'Role Manager',
                'icon' => 'fas fa-user-shield',
                'url' => '/admin/roles',
                'description' => 'Configure user roles and permissions'
            ],
            [
                'title' => 'System Settings',
                'icon' => 'fas fa-cogs',
                'url' => '/admin/system',
                'description' => 'Configure system preferences and settings'
            ],
            [
                'title' => 'Database Manager',
                'icon' => 'fas fa-database',
                'url' => '/admin/database',
                'description' => 'Database backup, restore and maintenance'
            ],
            [
                'title' => 'Security Logs',
                'icon' => 'fas fa-shield-alt',
                'url' => '/admin/security',
                'description' => 'View security logs and audit trails'
            ],
            [
                'title' => 'File Manager',
                'icon' => 'fas fa-folder-open',
                'url' => '/blob/manager',
                'description' => 'Manage uploaded files and blob storage'
            ]
        ];

        $data = [
            'title' => 'Admin Panel - SmartFlo',
            'user' => $user,
            'adminActions' => $adminActions
        ];

        return view('admin/panel', $data);
    }
}
