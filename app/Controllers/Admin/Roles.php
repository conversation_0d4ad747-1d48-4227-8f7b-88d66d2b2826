<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Libraries\AuthLibrary;
use App\Libraries\SecurityLibrary;
use App\Models\RoleModel;
use App\Models\UserRoleModel;

class Roles extends BaseController
{
    protected $authLib;
    protected $securityLib;
    protected $roleModel;
    protected $userRoleModel;

    public function __construct()
    {
        $this->authLib = new AuthLibrary();
        $this->securityLib = new SecurityLibrary();
        $this->roleModel = new RoleModel();
        $this->userRoleModel = new UserRoleModel();
    }

    /**
     * List roles
     */
    public function index()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Access denied.');
        }

        $data = [
            'title' => 'Role Management - SmartFlo',
            'user' => $this->authLib->user()
        ];

        return view('admin/roles', $data);
    }

    /**
     * Get roles data for both DataTable and simple AJAX requests
     */
    public function getData()
    {
        try {

        $request = $this->request;

        // Check if this is a DataTable request or simple AJAX request
        $isDataTable = $request->getGet('draw') !== null;

        if ($isDataTable) {
            // DataTable request
            $search = $request->getGet('search')['value'] ?? '';
            $start = (int) $request->getGet('start') ?? 0;
            $length = (int) $request->getGet('length') ?? 10;

            // Get roles with user counts
            $roles = $this->roleModel->getRolesWithUserCounts($search, $length, $start);
            $totalRoles = $this->roleModel->countRoles($search);
            $totalRecords = $this->roleModel->countAllResults();

            // Format data for DataTable
            $data = [];
            foreach ($roles as $role) {
                $data[] = [
                    'id' => $role['id'],
                    'role_name' => htmlspecialchars($role['role_name']),
                    'description' => htmlspecialchars($role['description'] ?: 'No description'),
                    'user_count' => $role['user_count'],
                    'created_at' => date('M j, Y', strtotime($role['created_at'])),
                    'actions' => $this->generateActionButtons($role)
                ];
            }

            return $this->response->setJSON([
                'draw' => (int) $request->getGet('draw'),
                'recordsTotal' => $totalRecords,
                'recordsFiltered' => $totalRoles,
                'data' => $data
            ]);
        } else {
            // Simple AJAX request for card view
            $search = $request->getGet('search') ?? '';

            // Get all roles with user counts (limit to reasonable number for performance)
            $roles = $this->roleModel->getRolesWithUserCounts($search, 100, 0);

            // Format data for card view
            $data = [];
            foreach ($roles as $role) {
                $data[] = [
                    'id' => $role['id'],
                    'role_name' => htmlspecialchars($role['role_name']),
                    'description' => htmlspecialchars($role['description'] ?: ''),
                    'user_count' => $role['user_count'],
                    'created_at' => date('M j, Y', strtotime($role['created_at'])),
                    'actions' => $this->generateActionButtons($role)
                ];
            }

            return $this->response->setJSON([
                'success' => true,
                'data' => $data
            ]);
        }

        } catch (\Exception $e) {
            log_message('error', 'Roles getData error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to load roles: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Show create role form
     */
    public function create()
    {
        if (!$this->authLib->isLoggedIn() || !$this->authLib->hasPermission('role.create')) {
            return redirect()->to('/admin/roles')->with('error', 'Access denied.');
        }

        $availablePermissions = $this->roleModel->getAvailablePermissions();

        $data = [
            'title' => 'Create Role - SmartFlo',
            'user' => $this->authLib->user(),
            'availablePermissions' => $availablePermissions,
            'csrf_token' => csrf_token(),
            'csrf_hash' => csrf_hash()
        ];

        return view('admin/roles/create', $data);
    }

    /**
     * Store new role
     */
    public function store()
    {
        if (!$this->authLib->isLoggedIn() || !$this->authLib->hasPermission('role.create')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied.'
            ]);
        }

        // CSRF validation is handled automatically by CodeIgniter's CSRF filter
        // No need for manual validation here

        // Validate input
        $rules = [
            'role_name' => 'required|min_length[2]|max_length[50]|is_unique[roles.role_name]',
            'description' => 'permit_empty|max_length[255]',
            'permissions' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Please check your input.',
                'errors' => $this->validator->getErrors()
            ]);
        }

        $roleName = $this->securityLib->sanitizeInput($this->request->getPost('role_name'));
        $description = $this->securityLib->sanitizeInput($this->request->getPost('description'));
        $permissions = $this->request->getPost('permissions') ?: [];

        // Validate permissions
        $availablePermissions = array_keys($this->roleModel->getAvailablePermissions());
        $invalidPermissions = array_diff($permissions, $availablePermissions);
        
        if (!empty($invalidPermissions)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid permissions selected.'
            ]);
        }

        // Create role
        $roleData = [
            'role_name' => $roleName,
            'description' => $description,
            'permissions' => $permissions
        ];

        $roleId = $this->roleModel->insert($roleData);

        if ($roleId) {
            // Log role creation
            $this->securityLib->logSecurityEvent('role_created', [
                'role_id' => $roleId,
                'role_name' => $roleName,
                'created_by' => $this->authLib->user()['username']
            ]);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Role created successfully.',
                'redirect' => '/admin/roles'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to create role.'
        ]);
    }

    /**
     * Show edit role form
     */
    public function edit($id)
    {
        if (!$this->authLib->isLoggedIn() || !$this->authLib->hasPermission('role.update')) {
            return redirect()->to('/admin/roles')->with('error', 'Access denied.');
        }

        $role = $this->roleModel->find($id);
        if (!$role) {
            return redirect()->to('/admin/roles')->with('error', 'Role not found.');
        }

        $availablePermissions = $this->roleModel->getAvailablePermissions();

        // Get current role permissions
        $rolePermissions = $role['permissions'] ?? [];
        if (is_string($rolePermissions)) {
            $rolePermissions = json_decode($rolePermissions, true) ?? [];
        }

        $data = [
            'title' => 'Edit Role - SmartFlo',
            'user' => $this->authLib->user(),
            'editRole' => $role,  // Changed from 'role' to 'editRole'
            'rolePermissions' => $rolePermissions,  // Added missing variable
            'availablePermissions' => $availablePermissions,
            'csrf_token' => csrf_token(),
            'csrf_hash' => csrf_hash()
        ];

        return view('admin/roles/edit', $data);
    }

    /**
     * Update role
     */
    public function update($id)
    {
        if (!$this->authLib->isLoggedIn() || !$this->authLib->hasPermission('role.update')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied.'
            ]);
        }

        $role = $this->roleModel->find($id);
        if (!$role) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Role not found.'
            ]);
        }

        // CSRF validation is handled automatically by CodeIgniter's CSRF filter
        // No need for manual validation here

        // Validate input
        $rules = [
            'role_name' => "required|min_length[2]|max_length[50]|is_unique[roles.role_name,id,{$id}]",
            'description' => 'permit_empty|max_length[255]',
            'permissions' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Please check your input.',
                'errors' => $this->validator->getErrors()
            ]);
        }

        $roleName = $this->securityLib->sanitizeInput($this->request->getPost('role_name'));
        $description = $this->securityLib->sanitizeInput($this->request->getPost('description'));
        $permissions = $this->request->getPost('permissions') ?: [];

        // Validate permissions
        $availablePermissions = array_keys($this->roleModel->getAvailablePermissions());
        $invalidPermissions = array_diff($permissions, $availablePermissions);
        
        if (!empty($invalidPermissions)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid permissions selected.'
            ]);
        }

        // Update role
        $updateData = [
            'role_name' => $roleName,
            'description' => $description,
            'permissions' => $permissions
        ];

        if ($this->roleModel->update($id, $updateData)) {
            // Log role update
            $this->securityLib->logSecurityEvent('role_updated', [
                'role_id' => $id,
                'role_name' => $roleName,
                'updated_by' => $this->authLib->user()['username']
            ]);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Role updated successfully.',
                'redirect' => '/admin/roles'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to update role.'
        ]);
    }

    /**
     * Delete role
     */
    public function delete($id)
    {
        if (!$this->authLib->isLoggedIn() || !$this->authLib->hasPermission('role.delete')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied.'
            ]);
        }

        $role = $this->roleModel->find($id);
        if (!$role) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Role not found.'
            ]);
        }

        // Check if role can be deleted
        if (!$this->roleModel->canDelete($id)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Cannot delete role that is assigned to users.'
            ]);
        }

        // Prevent deleting admin role
        if ($role['role_name'] === 'admin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Cannot delete the admin role.'
            ]);
        }

        // Delete role
        if ($this->roleModel->delete($id)) {
            // Log role deletion
            $this->securityLib->logSecurityEvent('role_deleted', [
                'role_id' => $id,
                'role_name' => $role['role_name'],
                'deleted_by' => $this->authLib->user()['username']
            ]);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Role deleted successfully.'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to delete role.'
        ]);
    }

    /**
     * View role details
     */
    public function view($id)
    {
        if (!$this->authLib->isLoggedIn() || !$this->authLib->hasPermission('role.read')) {
            return redirect()->to('/admin/roles')->with('error', 'Access denied.');
        }

        $role = $this->roleModel->getRoleWithUserCount($id);
        if (!$role) {
            return redirect()->to('/admin/roles')->with('error', 'Role not found.');
        }

        $roleUsers = $this->userRoleModel->getRoleUsers($id);
        $availablePermissions = $this->roleModel->getAvailablePermissions();

        $data = [
            'title' => 'Role Details - SmartFlo',
            'user' => $this->authLib->user(),
            'role' => $role,
            'roleUsers' => $roleUsers,
            'availablePermissions' => $availablePermissions
        ];

        return view('admin/roles/view', $data);
    }

    /**
     * Generate action buttons for DataTable
     */
    protected function generateActionButtons($role)
    {
        $buttons = '';

        if ($this->authLib->hasPermission('role.read')) {
            $buttons .= '<a href="/admin/roles/view/' . $role['id'] . '" class="btn btn-sm btn-info me-1" title="View">
                <i class="fas fa-eye"></i>
            </a>';
        }

        if ($this->authLib->hasPermission('role.update')) {
            $buttons .= '<a href="/admin/roles/edit/' . $role['id'] . '" class="btn btn-sm btn-primary me-1" title="Edit">
                <i class="fas fa-edit"></i>
            </a>';
        }

        if ($this->authLib->hasPermission('role.delete') && $role['role_name'] !== 'admin') {
            $canDelete = $role['user_count'] == 0;
            $deleteClass = $canDelete ? 'btn-danger delete-role' : 'btn-secondary';
            $deleteTitle = $canDelete ? 'Delete' : 'Cannot delete (users assigned)';
            $deleteDisabled = $canDelete ? '' : 'disabled';

            $buttons .= '<button class="btn btn-sm ' . $deleteClass . '"
                data-id="' . $role['id'] . '" data-name="' . htmlspecialchars($role['role_name']) . '"
                title="' . $deleteTitle . '" ' . $deleteDisabled . '>
                <i class="fas fa-trash"></i>
            </button>';
        }

        return $buttons ?: '<span class="text-muted">No actions</span>';
    }

    /**
     * Get available permissions
     */
    public function getPermissions()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied.'
            ]);
        }

        try {
            $permissions = $this->roleModel->getAvailablePermissions();

            return $this->response->setJSON([
                'success' => true,
                'data' => $permissions
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Roles getPermissions error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to load permissions: ' . $e->getMessage()
            ]);
        }
    }
}
