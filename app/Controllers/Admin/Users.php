<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Libraries\AuthLibrary;
use App\Libraries\SecurityLibrary;
use App\Models\UserModel;
use App\Models\RoleModel;
use App\Models\UserRoleModel;

class Users extends BaseController
{
    protected $authLib;
    protected $securityLib;
    protected $userModel;
    protected $roleModel;
    protected $userRoleModel;

    public function __construct()
    {
        $this->authLib = new AuthLibrary();
        $this->securityLib = new SecurityLibrary();
        $this->userModel = new UserModel();
        $this->roleModel = new RoleModel();
        $this->userRoleModel = new UserRoleModel();
    }

    /**
     * List users
     */
    public function index()
    {
        if (!$this->authLib->isLoggedIn() || !$this->authLib->hasPermission('user.read')) {
            return redirect()->to('/auth/login')->with('error', 'Access denied.');
        }

        $data = [
            'title' => 'User Management - SmartFlo',
            'user' => $this->authLib->user()
        ];

        return view('admin/users', $data);
    }

    /**
     * Get users data for both DataTable and simple AJAX requests
     */
    public function getData()
    {
        try {

        $request = $this->request;

        // Check if this is a DataTable request or simple AJAX request
        $isDataTable = $request->getGet('draw') !== null;

        if ($isDataTable) {
            // DataTable request
            $search = $request->getGet('search')['value'] ?? '';
            $start = (int) $request->getGet('start') ?? 0;
            $length = (int) $request->getGet('length') ?? 10;
            $orderColumn = (int) $request->getGet('order')[0]['column'] ?? 0;
            $orderDir = $request->getGet('order')[0]['dir'] ?? 'asc';

            $columns = ['id', 'username', 'email', 'roles', 'is_active', 'created_at'];
            $orderBy = $columns[$orderColumn] ?? 'id';

            // Get users with roles
            $users = $this->userModel->getUsersWithRoles($search, $length, $start);
            $totalUsers = $this->userModel->countUsers($search);
            $totalRecords = $this->userModel->countAllResults();

            // Format data for DataTable
            $data = [];
            foreach ($users as $user) {
                $data[] = [
                    'id' => $user['id'],
                    'username' => htmlspecialchars($user['username']),
                    'email' => htmlspecialchars($user['email']),
                    'roles' => $user['roles'] ? htmlspecialchars($user['roles']) : 'No roles',
                    'is_active' => $user['is_active'] ? 'Active' : 'Inactive',
                    'created_at' => date('M j, Y', strtotime($user['created_at'])),
                    'actions' => $this->generateActionButtons($user)
                ];
            }

            return $this->response->setJSON([
                'draw' => (int) $request->getGet('draw'),
                'recordsTotal' => $totalRecords,
                'recordsFiltered' => $totalUsers,
                'data' => $data
            ]);
        } else {
            // Simple AJAX request for card view
            $search = $request->getGet('search') ?? '';

            // Get all users with roles (limit to reasonable number for performance)
            $users = $this->userModel->getUsersWithRoles($search, 100, 0);

            // Format data for card view
            $data = [];
            foreach ($users as $user) {
                $data[] = [
                    'id' => $user['id'],
                    'username' => htmlspecialchars($user['username']),
                    'email' => htmlspecialchars($user['email']),
                    'roles' => $user['roles'] ? htmlspecialchars($user['roles']) : '',
                    'is_active' => $user['is_active'] ? 'Active' : 'Inactive',
                    'created_at' => date('M j, Y', strtotime($user['created_at'])),
                    'actions' => $this->generateActionButtons($user)
                ];
            }

            return $this->response->setJSON([
                'success' => true,
                'data' => $data
            ]);
        }

        } catch (\Exception $e) {
            log_message('error', 'Users getData error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to load users: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Show create user form
     */
    public function create()
    {
        if (!$this->authLib->isLoggedIn() || !$this->authLib->hasPermission('user.create')) {
            return redirect()->to('/admin/users')->with('error', 'Access denied.');
        }

        $roles = $this->roleModel->findAll();

        $data = [
            'title' => 'Create User - SmartFlo',
            'user' => $this->authLib->user(),
            'roles' => $roles,
            'csrf_token' => csrf_token(),
            'csrf_hash' => csrf_hash()
        ];

        return view('admin/users/create', $data);
    }

    /**
     * Store new user
     */
    public function store()
    {
        if (!$this->authLib->isLoggedIn() || !$this->authLib->hasPermission('user.create')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied.'
            ]);
        }

        // CSRF validation is handled automatically by CodeIgniter's CSRF filter
        // No need for manual validation here

        // Validate input
        $rules = [
            'username' => 'required|min_length[3]|max_length[50]|is_unique[users.username]',
            'email' => 'required|valid_email|is_unique[users.email]',
            'password' => 'required|min_length[8]',
            'password_confirm' => 'required|matches[password]',
            'is_active' => 'permit_empty|in_list[0,1]', // Allow empty (will default to 1)
            'roles' => 'required|numeric' // Single role required
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Please check your input.',
                'errors' => $this->validator->getErrors()
            ]);
        }

        $username = $this->securityLib->sanitizeInput($this->request->getPost('username'));
        $email = $this->securityLib->sanitizeInput($this->request->getPost('email'), 'email');
        $password = $this->request->getPost('password');
        $isActive = $this->request->getPost('is_active') === '1' ? 1 : 0; // Allow both active and inactive
        $roleId = $this->request->getPost('roles'); // Single role selection

        // Validate password strength
        $passwordValidation = $this->securityLib->validatePassword($password);
        if (!$passwordValidation['valid']) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Password does not meet requirements.',
                'errors' => ['password' => $passwordValidation['errors']]
            ]);
        }

        // Validate username
        $usernameValidation = $this->securityLib->validateUsername($username);
        if (!$usernameValidation['valid']) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Username is invalid.',
                'errors' => ['username' => $usernameValidation['errors']]
            ]);
        }

        // Create user
        $userData = [
            'username' => $username,
            'email' => $email,
            'password_hash' => password_hash($password, PASSWORD_DEFAULT),
            'is_active' => $isActive
        ];

        $userId = $this->userModel->insert($userData);

        if ($userId) {
            // Assign role (single role)
            if (!empty($roleId)) {
                $this->userRoleModel->syncUserRoles($userId, [$roleId]);
            }

            // Log user creation
            $this->securityLib->logSecurityEvent('user_created', [
                'created_user_id' => $userId,
                'created_username' => $username,
                'created_by' => $this->authLib->user()['username']
            ]);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'User created successfully.',
                'redirect' => '/admin/users'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to create user.'
        ]);
    }

    /**
     * Show edit user form
     */
    public function edit($id)
    {
        if (!$this->authLib->isLoggedIn() || !$this->authLib->hasPermission('user.update')) {
            return redirect()->to('/admin/users')->with('error', 'Access denied.');
        }

        $user = $this->userModel->find($id);
        if (!$user) {
            return redirect()->to('/admin/users')->with('error', 'User not found.');
        }

        $roles = $this->roleModel->findAll();
        $userRoles = $this->userRoleModel->getUserRoles($id);
        $userRoleIds = array_column($userRoles, 'id');

        $data = [
            'title' => 'Edit User - SmartFlo',
            'user' => $this->authLib->user(),
            'editUser' => $user,
            'roles' => $roles,
            'userRoleIds' => $userRoleIds,
            'csrf_token' => csrf_token(),
            'csrf_hash' => csrf_hash()
        ];

        return view('admin/users/edit', $data);
    }

    /**
     * Update user
     */
    public function update($id)
    {
        if (!$this->authLib->isLoggedIn() || !$this->authLib->hasPermission('user.update')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied.'
            ]);
        }

        $user = $this->userModel->find($id);
        if (!$user) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not found.'
            ]);
        }

        // CSRF validation is handled automatically by CodeIgniter's CSRF filter
        // No need for manual validation here

        // Validate input
        $rules = [
            'username' => "required|min_length[3]|max_length[50]|is_unique[users.username,id,{$id}]",
            'email' => "required|valid_email|is_unique[users.email,id,{$id}]",
            'is_active' => 'permit_empty|in_list[0,1]', // Allow empty (will default to 0)
            'roles' => 'required|numeric' // Single role required
        ];

        // Add password validation if password is provided
        if ($this->request->getPost('password')) {
            $rules['password'] = 'min_length[8]';
            $rules['password_confirm'] = 'matches[password]';
        }

        if (!$this->validate($rules)) {
            $errors = $this->validator->getErrors();
            log_message('error', 'User update validation failed: ' . json_encode($errors));
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Please check your input.',
                'errors' => $errors,
                'debug' => 'Validation failed'
            ]);
        }

        $username = $this->securityLib->sanitizeInput($this->request->getPost('username'));
        $email = $this->securityLib->sanitizeInput($this->request->getPost('email'), 'email');
        $password = $this->request->getPost('password');
        $isActive = $this->request->getPost('is_active') === '1' ? 1 : 0; // Allow both active and inactive
        $roleId = $this->request->getPost('roles'); // Single role selection

        // Validate username
        $usernameValidation = $this->securityLib->validateUsername($username);
        if (!$usernameValidation['valid']) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Username is invalid.',
                'errors' => ['username' => $usernameValidation['errors']]
            ]);
        }

        // Prepare update data
        $updateData = [
            'username' => $username,
            'email' => $email,
            'is_active' => $isActive
        ];

        // Add password if provided
        if ($password) {
            $passwordValidation = $this->securityLib->validatePassword($password);
            if (!$passwordValidation['valid']) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Password does not meet requirements.',
                    'errors' => ['password' => $passwordValidation['errors']]
                ]);
            }
            $updateData['password_hash'] = password_hash($password, PASSWORD_DEFAULT);
        }

        // Update user
        try {
            log_message('debug', 'Attempting to update user ID: ' . $id . ' with data: ' . json_encode($updateData));
            $updateResult = $this->userModel->updateUser($id, $updateData);
            log_message('debug', 'Update result: ' . ($updateResult ? 'true' : 'false'));

            // Check for validation errors
            if (!$updateResult) {
                $validationErrors = $this->userModel->errors();
                if (!empty($validationErrors)) {
                    log_message('error', 'User update validation errors: ' . json_encode($validationErrors));
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Validation failed.',
                        'errors' => $validationErrors,
                        'debug' => 'Model validation failed'
                    ]);
                }
            }

            if ($updateResult) {
                // Update role (single role)
                if (!empty($roleId)) {
                    $this->userRoleModel->syncUserRoles($id, [$roleId]);
                }

                // Log user update
                $this->securityLib->logSecurityEvent('user_updated', [
                    'updated_user_id' => $id,
                    'updated_username' => $username,
                    'updated_by' => $this->authLib->user()['username']
                ]);

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'User updated successfully.',
                    'redirect' => '/admin/users'
                ]);
            } else {
                log_message('error', 'User update failed for ID: ' . $id . ', Data: ' . json_encode($updateData));
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update user in database.',
                    'debug' => 'Database update returned false'
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'User update exception: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An error occurred while updating the user.',
                'debug' => 'Exception: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Delete user
     */
    public function delete($id)
    {
        if (!$this->authLib->isLoggedIn() || !$this->authLib->hasPermission('user.delete')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied.'
            ]);
        }

        $user = $this->userModel->find($id);
        if (!$user) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not found.'
            ]);
        }

        // Prevent deleting own account
        if ($id == $this->authLib->user()['id']) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'You cannot delete your own account.'
            ]);
        }

        // Delete user roles first
        $this->userRoleModel->removeAllUserRoles($id);

        // Delete user
        if ($this->userModel->delete($id)) {
            // Log user deletion
            $this->securityLib->logSecurityEvent('user_deleted', [
                'deleted_user_id' => $id,
                'deleted_username' => $user['username'],
                'deleted_by' => $this->authLib->user()['username']
            ]);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'User deleted successfully.'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to delete user.'
        ]);
    }

    /**
     * Toggle user status
     */
    public function toggleStatus($id)
    {
        if (!$this->authLib->isLoggedIn() || !$this->authLib->hasPermission('user.update')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied.'
            ]);
        }

        $user = $this->userModel->find($id);
        if (!$user) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not found.'
            ]);
        }

        // Allow deactivating own account (instant deactivation)

        $newStatus = !$user['is_active'];

        if ($this->userModel->update($id, ['is_active' => $newStatus])) {
            // Log status change
            $this->securityLib->logSecurityEvent('user_status_changed', [
                'user_id' => $id,
                'username' => $user['username'],
                'new_status' => $newStatus ? 'active' : 'inactive',
                'changed_by' => $this->authLib->user()['username']
            ]);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'User status updated successfully.',
                'new_status' => $newStatus
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to update user status.'
        ]);
    }

    /**
     * Generate action buttons for DataTable
     */
    protected function generateActionButtons($user)
    {
        $buttons = '';
        $currentUser = $this->authLib->user();

        if ($this->authLib->hasPermission('user.update')) {
            $buttons .= '<a href="/admin/users/edit/' . $user['id'] . '" class="btn btn-sm btn-primary me-1" title="Edit">
                <i class="fas fa-edit"></i>
            </a>';

            // Show toggle for all accounts (including own account for instant deactivation)
            $statusClass = $user['is_active'] ? 'btn-warning' : 'btn-success';
            $statusTitle = $user['is_active'] ? 'Deactivate' : 'Activate';
            $statusIcon = $user['is_active'] ? 'fa-ban' : 'fa-check';

            $buttons .= '<button class="btn btn-sm ' . $statusClass . ' me-1 toggle-status"
                data-id="' . $user['id'] . '" title="' . $statusTitle . '">
                <i class="fas ' . $statusIcon . '"></i>
            </button>';
        }

        // Removed delete functionality - users can only be deactivated

        return $buttons ?: '<span class="text-muted">No actions</span>';
    }
}
