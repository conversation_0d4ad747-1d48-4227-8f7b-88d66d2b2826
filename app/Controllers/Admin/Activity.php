<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Libraries\AuthLibrary;
use App\Models\UserModel;
use App\Models\LoginAttemptModel;

class Activity extends BaseController
{
    protected $authLib;
    protected $userModel;
    protected $loginAttemptModel;

    public function __construct()
    {
        $this->authLib = new AuthLibrary();
        $this->userModel = new UserModel();
        $this->loginAttemptModel = new LoginAttemptModel();
    }

    /**
     * Show activity log
     */
    public function index()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login');
        }

        $activities = $this->getAllActivity();

        $data = [
            'title' => 'Activity Log - SmartFlo',
            'user' => $this->authLib->user(),
            'activities' => $activities
        ];

        return view('admin/activity', $data);
    }

    /**
     * Get all activity
     */
    protected function getAllActivity()
    {
        $activities = [];

        // Recent user registrations
        $recentUsers = $this->userModel
            ->select('username, created_at')
            ->orderBy('created_at', 'DESC')
            ->limit(20)
            ->findAll();

        foreach ($recentUsers as $user) {
            $activities[] = [
                'type' => 'user_created',
                'message' => "New user '{$user['username']}' was created",
                'timestamp' => $user['created_at'],
                'icon' => 'fas fa-user-plus',
                'color' => 'success'
            ];
        }

        // Recent login attempts (failed)
        $failedAttempts = $this->loginAttemptModel
            ->select('username, ip_address, last_attempt')
            ->where('attempts >', 0)
            ->orderBy('last_attempt', 'DESC')
            ->limit(20)
            ->findAll();

        foreach ($failedAttempts as $attempt) {
            $ipAddress = !empty($attempt['ip_address']) ? $attempt['ip_address'] : 'unknown IP';
            $activities[] = [
                'type' => 'failed_login',
                'message' => "Failed login attempt for '{$attempt['username']}' from {$ipAddress}",
                'timestamp' => $attempt['last_attempt'],
                'icon' => 'fas fa-exclamation-triangle',
                'color' => 'warning'
            ];
        }

        // Sort by timestamp
        usort($activities, function($a, $b) {
            return strtotime($b['timestamp']) - strtotime($a['timestamp']);
        });

        return array_slice($activities, 0, 50);
    }
}
