<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Libraries\AuthLibrary;

class Add extends BaseController
{
    protected $authLib;

    public function __construct()
    {
        $this->authLib = new AuthLibrary();
    }

    /**
     * Show add new item page
     */
    public function index()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        $data = [
            'title' => 'Add New Item - SmartFlo',
            'user' => $this->authLib->user()
        ];

        return view('admin/add', $data);
    }
}
