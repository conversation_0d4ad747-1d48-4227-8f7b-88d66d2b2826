<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\FailedLoginAttemptModel;
use App\Models\UserModel;

class Security extends BaseController
{
    protected $failedLoginModel;
    protected $userModel;
    protected $authLib;

    public function __construct()
    {
        $this->failedLoginModel = new FailedLoginAttemptModel();
        $this->userModel = new UserModel();
        $this->authLib = new \App\Libraries\AuthLibrary();
    }

    /**
     * Show failed login attempts
     */
    public function failedLogins()
    {
        // Check authentication and permissions
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login');
        }

        $user = $this->authLib->user();
        if (!$this->authLib->hasPermission('admin.access')) {
            return redirect()->to('/dashboard')->with('error', 'Access denied.');
        }

        $data = [
            'title' => 'Failed Login Attempts - SmartFlo',
            'user' => $user
        ];

        return view('admin/security/failed_logins', $data);
    }

    /**
     * Get failed login attempts data (AJAX)
     */
    public function getFailedLoginsData()
    {
        // Check authentication and permissions
        if (!$this->authLib->isLoggedIn() || !$this->authLib->hasPermission('admin.access')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        $limit = $this->request->getGet('limit') ?? 100;
        $offset = $this->request->getGet('offset') ?? 0;

        $attempts = $this->failedLoginModel->getFailedAttemptsForAdmin($limit, $offset);
        $statistics = $this->failedLoginModel->getStatistics();

        // Format data for display
        $formattedAttempts = array_map(function($attempt) {
            return [
                'id' => $attempt['id'],
                'username' => $attempt['username'],
                'email' => $attempt['email'] ?? 'N/A',
                'ip_address' => $attempt['ip_address'],
                'user_agent' => $this->truncateUserAgent($attempt['user_agent']),
                'attempted_at' => date('M j, Y g:i A', strtotime($attempt['attempted_at'])),
                'reason' => ucfirst(str_replace('_', ' ', $attempt['reason'])),
                'blocked_until' => $attempt['blocked_until'] ? date('M j, Y g:i A', strtotime($attempt['blocked_until'])) : null,
                'is_blocked' => $attempt['blocked_until'] && strtotime($attempt['blocked_until']) > time()
            ];
        }, $attempts);

        return $this->response->setJSON([
            'success' => true,
            'data' => $formattedAttempts,
            'statistics' => $statistics
        ]);
    }

    /**
     * Get failed login statistics
     */
    public function getStatistics()
    {
        // Check authentication and permissions
        if (!$this->authLib->isLoggedIn() || !$this->authLib->hasPermission('admin.access')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        $statistics = $this->failedLoginModel->getStatistics();

        return $this->response->setJSON([
            'success' => true,
            'data' => $statistics
        ]);
    }

    /**
     * Block IP address
     */
    public function blockIP()
    {
        // Check authentication and permissions
        if (!$this->authLib->isLoggedIn() || !$this->authLib->hasPermission('admin.access')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        $ipAddress = $this->request->getPost('ip_address');
        $minutes = $this->request->getPost('minutes') ?? 60;

        if (!filter_var($ipAddress, FILTER_VALIDATE_IP)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid IP address'
            ]);
        }

        if ($this->failedLoginModel->blockIP($ipAddress, $minutes)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => "IP {$ipAddress} has been blocked for {$minutes} minutes"
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to block IP address'
        ]);
    }

    /**
     * Unblock IP address
     */
    public function unblockIP()
    {
        // Check authentication and permissions
        if (!$this->authLib->isLoggedIn() || !$this->authLib->hasPermission('admin.access')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        $ipAddress = $this->request->getPost('ip_address');

        if (!filter_var($ipAddress, FILTER_VALIDATE_IP)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid IP address'
            ]);
        }

        // Set blocked_until to current time to unblock
        $result = $this->failedLoginModel->where('ip_address', $ipAddress)
                                        ->set('blocked_until', date('Y-m-d H:i:s'))
                                        ->update();

        if ($result) {
            return $this->response->setJSON([
                'success' => true,
                'message' => "IP {$ipAddress} has been unblocked"
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to unblock IP address'
        ]);
    }

    /**
     * Clean old failed login attempts
     */
    public function cleanOldAttempts()
    {
        // Check authentication and permissions
        if (!$this->authLib->isLoggedIn() || !$this->authLib->hasPermission('admin.access')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        $deleted = $this->failedLoginModel->cleanOldAttempts();

        return $this->response->setJSON([
            'success' => true,
            'message' => "Cleaned {$deleted} old failed login attempts"
        ]);
    }

    /**
     * Truncate user agent string for display
     */
    private function truncateUserAgent($userAgent, $length = 50)
    {
        if (strlen($userAgent) <= $length) {
            return $userAgent;
        }

        return substr($userAgent, 0, $length) . '...';
    }
}
