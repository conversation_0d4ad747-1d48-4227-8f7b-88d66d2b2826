<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Libraries\AuthLibrary;
use Config\Version;

class System extends BaseController
{
    protected $authLib;
    protected $versionConfig;

    public function __construct()
    {
        $this->authLib = new AuthLibrary();
        $this->versionConfig = new Version();
    }

    /**
     * System information page
     */
    public function index()
    {
        // Check authentication and admin access
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login');
        }

        $user = $this->authLib->user();
        if (!$this->authLib->hasPermission('admin.access')) {
            return redirect()->to('/admin/dashboard')->with('error', 'Access denied.');
        }

        $data = [
            'title' => 'System Information - SmartFlo',
            'user' => $user,
            'system_info' => $this->versionConfig->getSystemInfo(),
            'version_history' => $this->versionConfig->getVersionHistory(),
            'server_info' => $this->getServerInfo(),
            'database_info' => $this->getDatabaseInfo()
        ];

        return view('admin/system/index', $data);
    }

    /**
     * Get server information
     */
    private function getServerInfo()
    {
        return [
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
            'server_name' => $_SERVER['SERVER_NAME'] ?? 'Unknown',
            'server_port' => $_SERVER['SERVER_PORT'] ?? 'Unknown',
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'timezone' => date_default_timezone_get(),
            'current_time' => date('Y-m-d H:i:s'),
            'disk_free_space' => $this->formatBytes(disk_free_space('.')),
            'disk_total_space' => $this->formatBytes(disk_total_space('.'))
        ];
    }

    /**
     * Get database information
     */
    private function getDatabaseInfo()
    {
        try {
            $db = \Config\Database::connect();
            
            // Get database version
            $versionQuery = $db->query('SELECT VERSION() as version');
            $version = $versionQuery->getRowArray()['version'] ?? 'Unknown';
            
            // Get database size
            $sizeQuery = $db->query("
                SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
            ");
            $size = $sizeQuery->getRowArray()['size_mb'] ?? 0;
            
            // Get table count
            $tableQuery = $db->query("
                SELECT COUNT(*) as table_count 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
            ");
            $tableCount = $tableQuery->getRowArray()['table_count'] ?? 0;
            
            return [
                'version' => $version,
                'size' => $size . ' MB',
                'table_count' => $tableCount,
                'connection_status' => 'Connected',
                'charset' => $db->getCharset(),
                'database_name' => $db->getDatabase()
            ];
            
        } catch (\Exception $e) {
            return [
                'version' => 'Unknown',
                'size' => 'Unknown',
                'table_count' => 'Unknown',
                'connection_status' => 'Error: ' . $e->getMessage(),
                'charset' => 'Unknown',
                'database_name' => 'Unknown'
            ];
        }
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Update system version (for future use)
     */
    public function updateVersion()
    {
        // This would be used for system updates
        // For now, just return current version info
        
        if (!$this->authLib->hasPermission('admin.access')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied.'
            ]);
        }

        return $this->response->setJSON([
            'success' => true,
            'current_version' => $this->versionConfig->getCurrentVersion(),
            'message' => 'System is up to date.'
        ]);
    }
}
