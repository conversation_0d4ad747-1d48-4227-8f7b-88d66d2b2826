<?php

namespace App\Controllers;

use CodeIgniter\Controller;

class DatabaseInit extends Controller
{
    public function createProjectTimelineTable()
    {
        $db = \Config\Database::connect();
        
        // Check if table exists
        if ($db->tableExists('project_timeline')) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'project_timeline table already exists'
            ]);
        }
        
        // Create the table
        $sql = "
        CREATE TABLE `project_timeline` (
            `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
            `project_id` int(11) unsigned NOT NULL,
            `user_id` int(11) unsigned NOT NULL,
            `action_type` enum('status_change','comment_added','file_uploaded','project_created','project_updated','assignment_changed') NOT NULL DEFAULT 'status_change',
            `old_status` varchar(50) DEFAULT NULL,
            `new_status` varchar(50) DEFAULT NULL,
            `title` varchar(255) NOT NULL,
            `description` text DEFAULT NULL,
            `notes` text DEFAULT NULL,
            `file_path` varchar(500) DEFAULT NULL,
            `metadata` json DEFAULT NULL,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_project_timeline_project_id` (`project_id`),
            KEY `idx_project_timeline_user_id` (`user_id`),
            KEY `idx_project_timeline_action_type` (`action_type`),
            KEY `idx_project_timeline_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        try {
            $db->query($sql);
            
            return $this->response->setJSON([
                'success' => true,
                'message' => 'project_timeline table created successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Failed to create project_timeline table: ' . $e->getMessage());
            
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to create project_timeline table: ' . $e->getMessage()
            ]);
        }
    }
    
    public function checkTables()
    {
        $db = \Config\Database::connect();

        $tables = [
            'project_timeline',
            'projects',
            'users',
            'notifications'
        ];

        $results = [];

        foreach ($tables as $table) {
            $results[$table] = $db->tableExists($table);
        }

        return $this->response->setJSON([
            'success' => true,
            'tables' => $results
        ]);
    }

    public function addSampleTimelineData()
    {
        $db = \Config\Database::connect();

        // Check if table exists
        if (!$db->tableExists('project_timeline')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'project_timeline table does not exist'
            ]);
        }

        // Get first project and user for sample data
        $project = $db->table('projects')->limit(1)->get()->getRowArray();
        $user = $db->table('users')->limit(1)->get()->getRowArray();

        if (!$project || !$user) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No projects or users found for sample data'
            ]);
        }

        // Sample timeline entries
        $sampleData = [
            [
                'project_id' => $project['id'],
                'user_id' => $user['id'],
                'action_type' => 'project_created',
                'title' => 'Project Created',
                'description' => 'Project was successfully created',
                'metadata' => json_encode(['project_name' => $project['project_name']]),
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 days'))
            ],
            [
                'project_id' => $project['id'],
                'user_id' => $user['id'],
                'action_type' => 'status_change',
                'old_status' => 'not_started',
                'new_status' => 'in_progress',
                'title' => 'Status Changed',
                'description' => 'Project status changed from Not Started to In Progress',
                'notes' => 'Starting work on the project',
                'metadata' => json_encode(['duration' => '2 days']),
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
            ],
            [
                'project_id' => $project['id'],
                'user_id' => $user['id'],
                'action_type' => 'comment_added',
                'title' => 'Comment Added',
                'description' => 'Progress update added',
                'notes' => 'Made good progress on the initial planning phase',
                'metadata' => json_encode(['comment_type' => 'progress_update']),
                'created_at' => date('Y-m-d H:i:s', strtotime('-6 hours'))
            ]
        ];

        try {
            foreach ($sampleData as $data) {
                $db->table('project_timeline')->insert($data);
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Sample timeline data added successfully',
                'project_id' => $project['id']
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to add sample data: ' . $e->getMessage()
            ]);
        }
    }
}
