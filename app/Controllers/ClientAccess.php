<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\ProjectModel;
use App\Models\ProjectTaskModel;

class ClientAccess extends BaseController
{
    protected $projectModel;
    protected $projectTaskModel;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->projectTaskModel = new ProjectTaskModel();
    }

    /**
     * Generate QR code for project access
     */
    public function generateQR($projectId)
    {
        $project = $this->projectModel->find($projectId);

        if (!$project) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Project not found'
            ])->setStatusCode(404);
        }

        // Generate unique access token
        $accessToken = bin2hex(random_bytes(32));
        
        // Store access token in database (you might want to create a separate table for this)
        $this->projectModel->update($projectId, [
            'client_access_token' => $accessToken,
            'client_access_expires' => date('Y-m-d H:i:s', strtotime('+30 days'))
        ]);

        // Generate QR code URL
        $baseUrl = base_url();
        $qrUrl = $baseUrl . "/client/project/{$accessToken}";

        return $this->response->setJSON([
            'success' => true,
            'qr_url' => $qrUrl,
            'access_token' => $accessToken,
            'expires_at' => date('Y-m-d H:i:s', strtotime('+30 days'))
        ]);
    }

    /**
     * Client project view (no authentication required)
     */
    public function viewProject($accessToken)
    {
        // Find project by access token
        $project = $this->projectModel->where('client_access_token', $accessToken)
                                    ->where('client_access_expires >', date('Y-m-d H:i:s'))
                                    ->first();

        if (!$project) {
            return view('client/access_expired', [
                'title' => 'Access Expired - SmartFlo'
            ]);
        }

        // Get project tasks with payment information
        $tasks = $this->projectTaskModel->select('
            project_tasks.*,
            task_types.name as task_type_name,
            task_types.color as task_type_color,
            task_types.icon as task_type_icon,
            assigned_user.username as assigned_username,
            assigned_user.email as assigned_email,
            project_tasks.payment_amount,
            project_tasks.payment_status,
            project_tasks.payment_account,
            project_tasks.task_manager_status,
            project_tasks.file_path,
            project_tasks.google_drive_link
        ')
        ->join('task_types', 'task_types.id = project_tasks.task_type_id', 'left')
        ->join('users as assigned_user', 'assigned_user.id = project_tasks.assigned_to', 'left')
        ->where('project_tasks.project_id', $project['id'])
        ->orderBy('project_tasks.task_order', 'ASC')
        ->findAll();

        $taskStats = $this->projectTaskModel->getProjectTaskStats($project['id']);

        $data = [
            'title' => 'Project Status - ' . $project['project_name'],
            'project' => $project,
            'tasks' => $tasks,
            'stats' => $taskStats
        ];

        return view('client/project_status', $data);
    }

    /**
     * Get project status API for client (no auth required)
     */
    public function getProjectStatus($accessToken)
    {
        $project = $this->projectModel->where('client_access_token', $accessToken)
                                    ->where('client_access_expires >', date('Y-m-d H:i:s'))
                                    ->first();

        if (!$project) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access token expired or invalid'
            ])->setStatusCode(404);
        }

        // Get project tasks with payment and file information (same as view method)
        $tasks = $this->projectTaskModel->select('
            project_tasks.*,
            task_types.name as task_type_name,
            task_types.color as task_type_color,
            task_types.icon as task_type_icon,
            assigned_user.username as assigned_username,
            assigned_user.email as assigned_email,
            project_tasks.payment_amount,
            project_tasks.payment_status,
            project_tasks.payment_account,
            project_tasks.task_manager_status,
            project_tasks.file_path,
            project_tasks.google_drive_link
        ')
        ->join('task_types', 'task_types.id = project_tasks.task_type_id', 'left')
        ->join('users as assigned_user', 'assigned_user.id = project_tasks.assigned_to', 'left')
        ->where('project_tasks.project_id', $project['id'])
        ->orderBy('project_tasks.task_order', 'ASC')
        ->findAll();

        $taskStats = $this->projectTaskModel->getProjectTaskStats($project['id']);

        return $this->response->setJSON([
            'success' => true,
            'project' => [
                'id' => $project['project_id'],
                'name' => $project['project_name'],
                'client_name' => $project['client_name'],
                'location' => $project['location'],
                'description' => $project['description'],
                'start_date' => $project['start_date'],
                'target_completion' => $project['target_completion'],
                'status' => $project['status'],
                'progress_percentage' => $project['progress_percentage'],
                'last_update_notes' => $project['last_update_notes'],
                'last_updated' => $project['last_updated']
            ],
            'tasks' => array_map(function($task) {
                return [
                    'id' => $task['id'],
                    'name' => $task['task_name'],
                    'type' => $task['task_type_name'],
                    'status' => $task['status'],
                    'description' => $task['description'],
                    'start_date' => $task['start_date'],
                    'due_date' => $task['due_date'],
                    'completed_date' => $task['completed_date'],
                    'payment_amount' => $task['payment_amount'],
                    'payment_status' => $task['payment_status'],
                    'payment_account' => $task['payment_account'],
                    'task_manager_status' => $task['task_manager_status'],
                    'file_path' => $task['file_path'],
                    'google_drive_link' => $task['google_drive_link']
                ];
            }, $tasks),
            'stats' => $taskStats
        ]);
    }



    /**
     * Revoke client access
     */
    public function revokeAccess($projectId)
    {
        $project = $this->projectModel->find($projectId);

        if (!$project) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Project not found'
            ])->setStatusCode(404);
        }

        // Clear access token
        $this->projectModel->update($projectId, [
            'client_access_token' => null,
            'client_access_expires' => null
        ]);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Client access revoked successfully'
        ]);
    }

    /**
     * Get QR code image (using a simple QR code generator)
     */
    public function getQRImage($accessToken)
    {
        $baseUrl = base_url();
        $qrUrl = $baseUrl . "/client/project/{$accessToken}";
        
        // Use Google Charts API for QR code generation
        $qrImageUrl = "https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=" . urlencode($qrUrl);
        
        // Redirect to the QR code image
        return redirect()->to($qrImageUrl);
    }

    /**
     * Download QR code as image
     */
    public function downloadQR($projectId)
    {
        $project = $this->projectModel->find($projectId);

        if (!$project || !$project['client_access_token']) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No active client access found'
            ])->setStatusCode(404);
        }

        $baseUrl = base_url();
        $qrUrl = $baseUrl . "/client/project/{$project['client_access_token']}";
        
        // Generate QR code image URL
        $qrImageUrl = "https://chart.googleapis.com/chart?chs=400x400&cht=qr&chl=" . urlencode($qrUrl);
        
        // Get the image content
        $imageContent = file_get_contents($qrImageUrl);
        
        if ($imageContent === false) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to generate QR code'
            ])->setStatusCode(500);
        }

        // Set headers for download
        $this->response->setHeader('Content-Type', 'image/png');
        $this->response->setHeader('Content-Disposition', 'attachment; filename="project-qr-' . $project['project_id'] . '.png"');
        $this->response->setHeader('Content-Length', strlen($imageContent));

        return $this->response->setBody($imageContent);
    }

    /**
     * Download task files for clients (with payment check)
     */
    public function downloadTaskFiles($taskId)
    {
        try {
            // Get task with payment information
            $task = $this->projectTaskModel->select('
                project_tasks.*,
                projects.client_access_token,
                projects.client_access_expires
            ')
            ->join('projects', 'projects.id = project_tasks.project_id')
            ->where('project_tasks.id', $taskId)
            ->first();

            if (!$task) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Task not found'
                ])->setStatusCode(404);
            }

            // Check if client access is valid
            if (!$task['client_access_token'] ||
                !$task['client_access_expires'] ||
                strtotime($task['client_access_expires']) < time()) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Access expired'
                ])->setStatusCode(403);
            }

            // Check payment status
            if ($task['payment_status'] !== 'paid') {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Payment required to download files',
                    'payment_required' => true,
                    'payment_amount' => $task['payment_amount']
                ])->setStatusCode(402);
            }

            // Check if task is completed and sent for review
            if ($task['status'] !== 'completed' || $task['task_manager_status'] !== 'sent_for_review') {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Files not available for download'
                ])->setStatusCode(400);
            }

            // For now, create a simple text file as placeholder
            // In a real implementation, you would zip actual project files
            $fileContent = "Project Files for Task: " . $task['task_name'] . "\n";
            $fileContent .= "Task ID: " . $task['id'] . "\n";
            $fileContent .= "Status: " . $task['status'] . "\n";
            $fileContent .= "Payment Status: " . $task['payment_status'] . "\n";
            $fileContent .= "Download Date: " . date('Y-m-d H:i:s') . "\n\n";
            $fileContent .= "This is a placeholder file. In production, this would contain actual project files.\n";

            // Set headers for download
            $this->response->setHeader('Content-Type', 'application/octet-stream');
            $this->response->setHeader('Content-Disposition', 'attachment; filename="task-' . $taskId . '-files.txt"');
            $this->response->setHeader('Content-Length', strlen($fileContent));

            return $this->response->setBody($fileContent);

        } catch (\Exception $e) {
            log_message('error', 'Error downloading task files: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error downloading files'
            ])->setStatusCode(500);
        }
    }
}
