<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\AuthLibrary;
use App\Models\UserModel;
use App\Models\RoleModel;
use App\Models\LoginAttemptModel;
use App\Models\NotificationModel;
use App\Models\ProjectModel;
use App\Models\ProjectTaskModel;
use Config\Version;

class Dashboard extends BaseController
{
    protected $authLib;
    protected $userModel;
    protected $roleModel;
    protected $loginAttemptModel;
    protected $notificationModel;
    protected $projectModel;
    protected $projectTaskModel;
    protected $versionConfig;

    public function __construct()
    {
        try {
            $this->authLib = new AuthLibrary();
            $this->userModel = new UserModel();
            $this->roleModel = new RoleModel();
            $this->loginAttemptModel = new LoginAttemptModel();
            $this->notificationModel = new NotificationModel();
            $this->projectModel = new ProjectModel();
            $this->projectTaskModel = new ProjectTaskModel();
            $this->versionConfig = new Version();
        } catch (\Exception $e) {
            log_message('error', 'Dashboard constructor error: ' . $e->getMessage());
            // Initialize with minimal setup
            $this->authLib = new AuthLibrary();
            $this->userModel = new UserModel();
        }
    }

    /**
     * Main dashboard - app-style interface
     */
    public function index()
    {
        // Check authentication
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login');
        }

        $user = $this->authLib->user();
        
        // Get dashboard data with error handling
        try {
            $stats = $this->getDashboardStats();
            $quickActions = $this->getQuickActions($user);
            $recentActivity = $this->getRecentActivity();
            $notifications = $this->getNotifications($user);
            $projectUpdates = $this->getProjectUpdates($user);
            $unreadCount = isset($this->notificationModel) ? $this->notificationModel->getUnreadCount($user['id']) : 0;
        } catch (\Exception $e) {
            log_message('error', 'Dashboard data error: ' . $e->getMessage());
            // Fallback data
            $stats = ['total_users' => 0, 'active_users' => 0, 'recent_logins' => 0, 'system_health' => ['status' => 'unknown', 'score' => 0]];
            $quickActions = $this->getQuickActions($user);
            $recentActivity = [];
            $notifications = [];
            $projectUpdates = [];
            $unreadCount = 0;
        }

        $data = [
            'title' => 'SmartFlo',
            'user' => $user,
            'stats' => $stats,
            'quick_actions' => $quickActions,
            'recent_activity' => $recentActivity,
            'notifications' => $notifications,
            'project_updates' => $projectUpdates,
            'unread_count' => $unreadCount,
            'petty_cash_balance' => 25000.00, // Default petty cash balance
            'version' => $this->versionConfig->version
        ];

        return view('dashboard/modern', $data);
    }

    /**
     * Get dashboard statistics
     */
    protected function getDashboardStats()
    {
        try {
            $totalUsers = $this->userModel->countAllResults();
            $activeUsers = $this->userModel->where('is_active', 1)->countAllResults();
            $recentLogins = $this->userModel
                ->where('last_login >', date('Y-m-d H:i:s', strtotime('-24 hours')))
                ->countAllResults();

            // System health check
            $systemHealth = $this->getSystemHealth();

            return [
                'total_users' => $totalUsers,
                'active_users' => $activeUsers,
                'recent_logins' => $recentLogins,
                'system_health' => $systemHealth
            ];
        } catch (\Exception $e) {
            log_message('error', 'Dashboard stats error: ' . $e->getMessage());
            return [
                'total_users' => 0,
                'active_users' => 0,
                'recent_logins' => 0,
                'system_health' => ['status' => 'unknown', 'score' => 0]
            ];
        }
    }

    /**
     * Get quick actions based on user permissions
     */
    protected function getQuickActions($user)
    {
        $actions = [];
        $userRole = $user['roles'] ?? 'user';

        // Define role-based button permissions
        $buttonPermissions = [
            'admin_panel' => ['admin'],
            'project_status' => ['admin', 'manager', 'supervisor'],
            'messages' => ['admin', 'manager', 'supervisor', 'staff', 'user'],
            'task_manager' => ['admin', 'manager', 'supervisor'],
            'add_material_expense' => ['admin', 'manager', 'supervisor'],
            'add_labour_expense' => ['admin', 'manager', 'supervisor'],
            'add_office_expense' => ['admin', 'manager'],
            'my_entry' => ['admin', 'manager', 'supervisor', 'staff', 'user'],
            'credit_purchases' => ['admin', 'manager'],
            'client_pay' => ['admin', 'manager'],
            'my_pettycash' => ['admin', 'manager', 'supervisor'],
            'purchases' => ['admin', 'manager', 'supervisor']
        ];

        // Define all available buttons
        $allButtons = [
            'admin_panel' => [
                'title' => 'Admin Panel',
                'icon' => 'fas fa-tachometer-alt',
                'url' => '/admin/panel'
            ],
            'project_status' => [
                'title' => 'Project Status',
                'icon' => 'fas fa-building',
                'url' => '/projects/status'
            ],
            'messages' => [
                'title' => 'Messages',
                'icon' => 'fas fa-envelope',
                'url' => '/messages'
            ],
            'task_manager' => [
                'title' => 'Task Manager',
                'icon' => 'fas fa-tasks',
                'url' => '/tasks'
            ],
            'add_material_expense' => [
                'title' => 'Add Material Expense',
                'icon' => 'fas fa-cubes',
                'url' => '/add-material'
            ],
            'add_labour_expense' => [
                'title' => 'Add Labour Expense',
                'icon' => 'fas fa-hard-hat',
                'url' => '/add-labour'
            ],
            'add_office_expense' => [
                'title' => 'Add Office Expense',
                'icon' => 'fas fa-briefcase',
                'url' => '/add-office'
            ],
            'my_entry' => [
                'title' => 'My Entry',
                'icon' => 'fas fa-clock',
                'url' => '/my-entry'
            ],
            'credit_purchases' => [
                'title' => 'Credit Purchases',
                'icon' => 'fas fa-credit-card',
                'url' => '/credit-purchases'
            ],
            'client_pay' => [
                'title' => 'Client Pay',
                'icon' => 'fas fa-money-bill-wave',
                'url' => '/client-pay'
            ],
            'my_pettycash' => [
                'title' => 'My Pettycash',
                'icon' => 'fas fa-wallet',
                'url' => '/petty-cash'
            ],
            'purchases' => [
                'title' => 'Purchases',
                'icon' => 'fas fa-shopping-cart',
                'url' => '/purchases'
            ]
        ];

        // Add buttons based on user role
        foreach ($allButtons as $buttonKey => $buttonData) {
            $allowedRoles = $buttonPermissions[$buttonKey] ?? [];

            // Check if user role is in allowed roles
            if (in_array($userRole, $allowedRoles)) {
                $actions[] = $buttonData;
            }
        }

        return $actions;
    }

    /**
     * Get recent activity
     */
    protected function getRecentActivity()
    {
        try {
            $activities = [];

            // Recent user registrations
            $recentUsers = $this->userModel
                ->select('username, created_at')
                ->orderBy('created_at', 'DESC')
                ->limit(5)
                ->findAll();

            foreach ($recentUsers as $user) {
                $activities[] = [
                    'type' => 'user_created',
                    'message' => "New user '{$user['username']}' was created",
                    'timestamp' => $user['created_at'],
                    'icon' => 'fas fa-user-plus',
                    'color' => 'success'
                ];
            }

            // Recent login attempts (failed) with error handling
            if (isset($this->loginAttemptModel)) {
                try {
                    $failedAttempts = $this->loginAttemptModel
                        ->select('username, ip_address, last_attempt')
                        ->where('attempts >', 0)
                        ->orderBy('last_attempt', 'DESC')
                        ->limit(3)
                        ->findAll();

                    foreach ($failedAttempts as $attempt) {
                        $ipAddress = !empty($attempt['ip_address']) ? $attempt['ip_address'] : 'unknown IP';
                        $activities[] = [
                            'type' => 'failed_login',
                            'message' => "Failed login attempt for '{$attempt['username']}' from {$ipAddress}",
                            'timestamp' => $attempt['last_attempt'],
                            'icon' => 'fas fa-exclamation-triangle',
                            'color' => 'warning'
                        ];
                    }
                } catch (\Exception $e) {
                    log_message('debug', 'Failed to get login attempts: ' . $e->getMessage());
                }
            }

            // Sort by timestamp
            usort($activities, function($a, $b) {
                return strtotime($b['timestamp']) - strtotime($a['timestamp']);
            });

            return array_slice($activities, 0, 5);
        } catch (\Exception $e) {
            log_message('error', 'Recent activity error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get notifications for user
     */
    protected function getNotifications($user)
    {
        try {
            // Get real notifications from database with error handling
            $notifications = [];
            if (isset($this->notificationModel)) {
                try {
                    $notifications = $this->notificationModel->getRecentNotifications($user['id'], 5);
                } catch (\Exception $e) {
                    log_message('debug', 'Failed to get notifications: ' . $e->getMessage());
                    $notifications = [];
                }
            }

            // Format notifications for dashboard
            $formattedNotifications = [];
            foreach ($notifications as $notification) {
                $formattedNotifications[] = [
                    'id' => $notification['id'],
                    'type' => $notification['type'],
                    'title' => $notification['title'],
                    'message' => $notification['message'],
                    'is_read' => (bool) $notification['is_read'],
                    'created_at' => $notification['created_at'],
                    'time_ago' => $this->timeAgo($notification['created_at']),
                    'icon' => $this->getNotificationIcon($notification['type'])
                ];
            }

            // Add system notifications if needed
            try {
                if ($this->userModel->needsPasswordChange($user['id'])) {
                    array_unshift($formattedNotifications, [
                        'id' => 0,
                        'type' => 'warning',
                        'title' => 'Password Change Required',
                        'message' => 'Your password needs to be changed for security reasons.',
                        'is_read' => false,
                        'created_at' => date('Y-m-d H:i:s'),
                        'time_ago' => 'Now',
                        'icon' => 'fas fa-exclamation-triangle',
                        'action_url' => '/auth/change-password',
                        'action_text' => 'Change Password'
                    ]);
                }
            } catch (\Exception $e) {
                log_message('debug', 'Failed to check password change: ' . $e->getMessage());
            }

            return $formattedNotifications;
        } catch (\Exception $e) {
            log_message('error', 'Get notifications error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get notification icon based on type
     */
    private function getNotificationIcon($type)
    {
        $icons = [
            'info' => 'fas fa-info-circle',
            'success' => 'fas fa-check-circle',
            'warning' => 'fas fa-exclamation-triangle',
            'error' => 'fas fa-times-circle',
            'user' => 'fas fa-user',
            'system' => 'fas fa-cog',
            'security' => 'fas fa-shield-alt'
        ];

        return $icons[$type] ?? 'fas fa-bell';
    }

    /**
     * Calculate time ago
     */
    private function timeAgo($datetime)
    {
        $time = time() - strtotime($datetime);

        if ($time < 60) {
            return 'Just now';
        } elseif ($time < 3600) {
            $minutes = floor($time / 60);
            return $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
        } elseif ($time < 86400) {
            $hours = floor($time / 3600);
            return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
        } elseif ($time < 2592000) {
            $days = floor($time / 86400);
            return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
        } else {
            return date('M j, Y', strtotime($datetime));
        }
    }

    /**
     * Get system health status
     */
    protected function getSystemHealth()
    {
        $health = [
            'status' => 'healthy',
            'score' => 100
        ];

        try {
            // Check database
            $db = \Config\Database::connect();
            $db->query('SELECT 1')->getResult();
        } catch (\Exception $e) {
            $health['status'] = 'warning';
            $health['score'] -= 30;
        }

        // Check disk space
        $freeSpace = disk_free_space('.');
        $totalSpace = disk_total_space('.');
        $usedPercent = (($totalSpace - $freeSpace) / $totalSpace) * 100;

        if ($usedPercent > 90) {
            $health['status'] = 'critical';
            $health['score'] -= 40;
        } elseif ($usedPercent > 80) {
            $health['status'] = 'warning';
            $health['score'] -= 20;
        }

        return $health;
    }

    /**
     * Get project updates for dashboard
     */
    protected function getProjectUpdates($user)
    {
        try {
            $updates = [];

            // Get recent project status changes
            $recentProjects = $this->projectModel
                ->select('projects.*, users.username as updated_by_username')
                ->join('users', 'users.id = projects.created_by', 'left')
                ->where('projects.last_updated >', date('Y-m-d H:i:s', strtotime('-7 days')))
                ->orderBy('projects.last_updated', 'DESC')
                ->limit(10)
                ->findAll();

            foreach ($recentProjects as $project) {
                $updates[] = [
                    'type' => 'project_status',
                    'project_id' => $project['id'],
                    'project_name' => $project['project_name'],
                    'client_name' => $project['client_name'],
                    'status' => $project['status'],
                    'progress' => $project['progress_percentage'],
                    'last_update' => $project['last_update_notes'],
                    'updated_by' => $project['updated_by_username'],
                    'timestamp' => $project['last_updated'],
                    'time_ago' => $this->timeAgo($project['last_updated']),
                    'icon' => $this->getProjectStatusIcon($project['status']),
                    'color' => $this->getProjectStatusColor($project['status'])
                ];
            }

            // Get recent task completions
            $recentTasks = $this->projectTaskModel
                ->select('project_tasks.*, projects.project_name, projects.client_name,
                         creator.username as updated_by_username')
                ->join('projects', 'projects.id = project_tasks.project_id', 'left')
                ->join('users as creator', 'creator.id = project_tasks.created_by', 'left')
                ->where('project_tasks.updated_at >', date('Y-m-d H:i:s', strtotime('-7 days')))
                ->where('project_tasks.status', 'completed')
                ->orderBy('project_tasks.updated_at', 'DESC')
                ->limit(5)
                ->findAll();

            foreach ($recentTasks as $task) {
                $updates[] = [
                    'type' => 'task_completed',
                    'project_id' => $task['project_id'],
                    'project_name' => $task['project_name'],
                    'client_name' => $task['client_name'],
                    'task_name' => $task['task_name'],
                    'updated_by' => $task['updated_by_username'],
                    'timestamp' => $task['updated_at'],
                    'time_ago' => $this->timeAgo($task['updated_at']),
                    'icon' => 'fas fa-check-circle',
                    'color' => 'success'
                ];
            }

            // Sort by timestamp
            usort($updates, function($a, $b) {
                return strtotime($b['timestamp']) - strtotime($a['timestamp']);
            });

            return array_slice($updates, 0, 15);
        } catch (\Exception $e) {
            log_message('error', 'Project updates error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get project status icon
     */
    private function getProjectStatusIcon($status)
    {
        $icons = [
            'not_started' => 'fas fa-clock',
            'in_progress' => 'fas fa-play-circle',
            'on_hold' => 'fas fa-pause-circle',
            'completed' => 'fas fa-check-circle'
        ];
        return $icons[$status] ?? 'fas fa-project-diagram';
    }

    /**
     * Get project status color
     */
    private function getProjectStatusColor($status)
    {
        $colors = [
            'not_started' => 'secondary',
            'in_progress' => 'primary',
            'on_hold' => 'warning',
            'completed' => 'success'
        ];
        return $colors[$status] ?? 'info';
    }



    /**
     * Get dashboard data via AJAX
     */
    public function getData()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required.'
            ]);
        }

        $user = $this->authLib->user();
        $stats = $this->getDashboardStats();
        $recentActivity = $this->getRecentActivity();
        $projectUpdates = $this->getProjectUpdates($user);

        return $this->response->setJSON([
            'success' => true,
            'data' => [
                'stats' => $stats,
                'recent_activity' => $recentActivity,
                'project_updates' => $projectUpdates
            ]
        ]);
    }

    /**
     * Get project updates via AJAX
     */
    public function getProjectUpdatesAjax()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required.'
            ]);
        }

        $user = $this->authLib->user();
        $updates = $this->getProjectUpdates($user);

        return $this->response->setJSON([
            'success' => true,
            'updates' => $updates
        ]);
    }
}
