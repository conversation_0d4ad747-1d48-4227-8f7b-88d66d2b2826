<?php

namespace App\Controllers;

use App\Models\ProjectModel;
use App\Models\ProjectTaskModel;
use App\Models\ProjectTimelineModel;
use App\Models\UserModel;
use App\Libraries\AuthLib;

class Client extends BaseController
{
    protected $projectModel;
    protected $projectTaskModel;
    protected $projectTimelineModel;
    protected $userModel;
    protected $authLib;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->projectTaskModel = new ProjectTaskModel();
        $this->projectTimelineModel = new ProjectTimelineModel();
        $this->userModel = new UserModel();
        $this->authLib = new AuthLib();
    }

    /**
     * Show project status page for clients
     */
    public function project($accessToken)
    {
        // Find project by access token
        $project = $this->projectModel->where('client_access_token', $accessToken)->first();
        
        if (!$project) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }

        // Get project tasks with files
        $tasks = $this->projectTaskModel->getTasksWithFiles($project['id']);
        
        // Get project timeline
        $timeline = $this->projectTimelineModel->getProjectTimeline($project['id']);
        
        // Get client information
        $client = $this->userModel->find($project['client_id']);

        $data = [
            'title' => 'Project Status - ' . $project['project_name'],
            'project' => $project,
            'tasks' => $tasks,
            'timeline' => $timeline,
            'client' => $client
        ];

        return view('client/project_status', $data);
    }

    /**
     * Submit client review for a task
     */
    public function submitReview($taskId)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(404);
        }

        try {
            $input = $this->request->getJSON(true);
            $reviewStatus = $input['review_status'] ?? '';

            if (!in_array($reviewStatus, ['client_accepted', 'need_revision'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid review status'
                ])->setStatusCode(400);
            }

            // Get task details
            $task = $this->projectTaskModel->find($taskId);
            if (!$task) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Task not found'
                ])->setStatusCode(404);
            }

            // Verify task is in sent_for_review status
            if ($task['task_manager_status'] !== 'sent_for_review') {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Task is not available for review'
                ])->setStatusCode(400);
            }

            // Update task manager status
            $updateData = [
                'task_manager_status' => $reviewStatus,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // If revision needed, reset task status to in_progress
            if ($reviewStatus === 'need_revision') {
                $updateData['status'] = 'in_progress';
            }

            $result = $this->projectTaskModel->update($taskId, $updateData);

            if ($result) {
                // Add timeline entry
                $project = $this->projectModel->find($task['project_id']);
                $timelineNotes = $reviewStatus === 'client_accepted' 
                    ? 'Client accepted the work' 
                    : 'Client requested revision';

                $this->projectTimelineModel->insert([
                    'project_id' => $task['project_id'],
                    'user_id' => $project['client_id'], // Use client as user
                    'action_type' => 'client_review',
                    'title' => 'Client Review',
                    'description' => $timelineNotes,
                    'metadata' => json_encode([
                        'task_id' => $taskId,
                        'task_name' => $task['task_name'],
                        'review_status' => $reviewStatus
                    ]),
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                $message = $reviewStatus === 'client_accepted' 
                    ? 'Work accepted successfully!' 
                    : 'Revision request submitted successfully!';

                return $this->response->setJSON([
                    'success' => true,
                    'message' => $message
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to submit review'
                ])->setStatusCode(500);
            }

        } catch (\Exception $e) {
            log_message('error', 'Client review submission error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error submitting review: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }
}
