<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\AuthLibrary;
use App\Models\ProjectModel;
use App\Models\UserModel;
use App\Models\NotificationModel;
use App\Models\ProjectTaskModel;
use App\Models\ProjectPaymentModel;
use App\Models\TaskTypeModel;

class Projects extends BaseController
{
    protected $authLib;
    protected $projectModel;
    protected $userModel;
    protected $notificationModel;
    protected $projectTaskModel;
    protected $projectPaymentModel;
    protected $projectTimelineModel;
    protected $taskTypeModel;
    protected $validation;

    public function __construct()
    {
        $this->authLib = new AuthLibrary();
        $this->projectModel = new ProjectModel();
        $this->userModel = new UserModel();
        $this->notificationModel = new NotificationModel();
        $this->projectTaskModel = new ProjectTaskModel();
        $this->projectPaymentModel = new ProjectPaymentModel();
        $this->projectTimelineModel = new \App\Models\ProjectTimelineModel();
        $this->taskTypeModel = new TaskTypeModel();
        $this->validation = \Config\Services::validation();
    }

    /**
     * Projects dashboard
     */
    public function index()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        $user = $this->authLib->user();

        $data = [
            'title' => 'Projects',
            'user' => $user,
            'page' => 'projects'
        ];

        return view('projects/index', $data);
    }

    /**
     * Show create project page
     */
    public function createPage()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        $user = $this->authLib->user();

        // Check if user has permission to create projects
        if (!in_array($user['roles'], ['admin', 'manager', 'projects'])) {
            return redirect()->to('/projects')->with('error', 'Access denied');
        }

        $data = [
            'title' => 'Create Project',
            'user' => $user,
            'page' => 'projects'
        ];

        return view('projects/create', $data);
    }

    /**
     * Get projects for current user
     */
    public function getProjects()
    {
        // Set no-cache headers for real-time data
        $this->response->setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        $this->response->setHeader('Pragma', 'no-cache');
        $this->response->setHeader('Expires', '0');

        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        $filter = $this->request->getGet('filter') ?: 'assigned';
        $filterType = $this->request->getGet('filterType') ?: 'project';
        $status = $this->request->getGet('status') ?: '';

        try {
            // Handle admin assignee filtering
            if ($user['roles'] === 'admin' && $filterType === 'assignee') {
                if ($filter === 'all') {
                    $projects = $this->projectModel->getProjectsForUser($user['id'], $user['roles'], 'all');
                } else {
                    // Filter by specific assignee
                    $projects = $this->projectModel->getProjectsByAssignee($filter);
                }
            } else {
                $projects = $this->projectModel->getProjectsForUser($user['id'], $user['roles'], $filter);
            }

            // Apply status filter if provided
            if (!empty($status)) {
                $projects = array_filter($projects, function($project) use ($status) {
                    return $project['status'] === $status;
                });
                $projects = array_values($projects); // Re-index array
            }

            // Get stats
            $stats = $this->getProjectStatsForUser($user['id'], $user['roles'], $projects);

            return $this->response->setJSON([
                'success' => true,
                'projects' => $projects,
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error loading projects: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Check if project ID already exists (for real-time validation)
     */
    public function checkProjectId()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $projectId = $this->request->getGet('project_id');

        if (empty($projectId)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Project ID is required'
            ])->setStatusCode(400);
        }

        try {
            // Check if base project ID already exists
            $exists = $this->checkBaseProjectIdExists($projectId);

            return $this->response->setJSON([
                'success' => true,
                'exists' => $exists,
                'message' => $exists ? 'Project ID already exists or conflicts with existing projects' : 'Project ID is available'
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error checking project ID: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Get assignees for admin filter
     */
    public function getAssignees()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        // Only admins can access this
        if ($user['roles'] !== 'admin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        try {
            // Get all users who have been assigned to projects
            $assignees = $this->userModel->select('users.id, users.username as name')
                                        ->join('projects', 'projects.assigned_to = users.id')
                                        ->groupBy('users.id')
                                        ->findAll();

            return $this->response->setJSON([
                'success' => true,
                'assignees' => $assignees
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error loading assignees: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Check for project updates (for real-time refresh)
     */
    public function checkUpdates()
    {
        // Set no-cache headers for real-time data
        $this->response->setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        $this->response->setHeader('Pragma', 'no-cache');
        $this->response->setHeader('Expires', '0');

        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        try {
            $user = $this->authLib->user();
            $lastCheck = $this->request->getGet('last_check') ?: date('Y-m-d H:i:s', strtotime('-1 minute'));

            // Check for recent project updates
            $recentUpdates = $this->projectModel->where('updated_at >', $lastCheck)
                                               ->findAll();

            return $this->response->setJSON([
                'success' => true,
                'has_updates' => !empty($recentUpdates),
                'update_count' => count($recentUpdates),
                'last_check' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error checking updates: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Get project statistics for user
     */
    private function getProjectStatsForUser($userId, $userRoles, $projects = null)
    {
        if ($projects === null) {
            $projects = $this->projectModel->getProjectsForUser($userId, $userRoles, 'all');
        }

        $stats = [
            'total_projects' => count($projects),
            'active_projects' => 0,
            'completed_projects' => 0,
            'on_hold_projects' => 0,
            'my_total_projects' => 0,
            'my_active_projects' => 0,
            'my_completed_projects' => 0,
            'my_efficiency' => 0
        ];

        foreach ($projects as $project) {
            switch ($project['status']) {
                case 'in_progress':
                    $stats['active_projects']++;
                    break;
                case 'completed':
                    $stats['completed_projects']++;
                    break;
                case 'on_hold':
                    $stats['on_hold_projects']++;
                    break;
            }

            // Personal stats for assignees
            if ($project['assigned_to'] == $userId) {
                $stats['my_total_projects']++;
                if ($project['status'] === 'in_progress') {
                    $stats['my_active_projects']++;
                } elseif ($project['status'] === 'completed') {
                    $stats['my_completed_projects']++;
                }
            }
        }

        // Calculate efficiency for assignees
        if ($stats['my_total_projects'] > 0) {
            $stats['my_efficiency'] = round(($stats['my_completed_projects'] / $stats['my_total_projects']) * 100);
        }

        return $stats;
    }

    /**
     * Create new project (admin/manager only)
     */
    public function create()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        
        // Check if user can create projects
        if (!$this->authLib->canManageProjects() && !in_array($user['roles'], ['projects'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied. Only admins, managers, and project managers can create projects.'
            ])->setStatusCode(403);
        }

        $rules = [
            'project_id' => 'required|max_length[20]|regex_match[/^[0-9]+$/]',
            'project_name' => 'required|max_length[150]',
            'client_mobile' => 'permit_empty|max_length[20]',
            'location' => 'permit_empty|max_length[255]',
            'description' => 'permit_empty|max_length[1000]',
            'start_date' => 'required|valid_date'
        ];

        // Custom validation messages
        $messages = [
            'project_id' => [
                'is_unique' => 'This Project ID already exists. Please choose a different ID.',
                'regex_match' => 'Project ID can only contain numbers (e.g., 1001, 1002, etc.).'
            ]
        ];

        $this->validation->setRules($rules, $messages);

        // Log the POST data for debugging
        $postData = $this->request->getPost();
        log_message('info', 'Project creation POST data: ' . json_encode($postData));

        // Log specific required fields
        log_message('info', 'Required fields check: ' . json_encode([
            'project_id' => $postData['project_id'] ?? 'MISSING',
            'project_name' => $postData['project_name'] ?? 'MISSING',
            'start_date' => $postData['start_date'] ?? 'MISSING',
            'assigned_to' => $postData['assigned_to'] ?? 'MISSING',
            'task_category' => $postData['task_category'] ?? 'MISSING',
            'task_type' => $postData['task_type'] ?? 'MISSING'
        ]));

        if (!$this->validation->run($postData)) {
            $errors = $this->validation->getErrors();
            log_message('error', 'Project creation validation failed: ' . json_encode($errors));
            log_message('error', 'Received POST data: ' . json_encode($postData));
            log_message('error', 'Validation rules: ' . json_encode($rules));

            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', array_values($errors)),
                'errors' => $errors,
                'debug_info' => [
                    'received_fields' => array_keys($postData),
                    'validation_rules' => array_keys($rules),
                    'post_data_sample' => array_slice($postData, 0, 10, true) // First 10 fields for debugging
                ]
            ])->setStatusCode(400);
        }

        // Custom validation: Check if project ID exists in active projects only
        $projectId = $this->request->getPost('project_id');
        $existingProject = $this->projectModel->where('project_id', $projectId)
                                             ->where('status !=', 'deleted')
                                             ->first();

        if ($existingProject) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'This Project ID already exists in active projects. Please choose a different ID.',
                'errors' => ['project_id' => 'Project ID already exists in active projects.']
            ])->setStatusCode(400);
        }

        // Additional validation for mobile number
        $clientMobile = $this->sanitizeMobileNumber($this->request->getPost('client_mobile'));
        if (!empty($clientMobile)) {
            // Check if it's a valid mobile format
            if (!preg_match('/^\+91\s[0-9]{5}\s[0-9]{5}$/', $clientMobile) &&
                !preg_match('/^[\+]?[0-9\s\-\(\)]{10,20}$/', $clientMobile)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => ['client_mobile' => 'Please enter a valid mobile number (e.g., +91 98765 43210)']
                ])->setStatusCode(400);
            }
        }

        try {
            log_message('info', 'Starting project creation process');

            // Check for duplicate base project ID
            $baseProjectId = $this->request->getPost('project_id');
            if ($this->checkBaseProjectIdExists($baseProjectId)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'A project with this ID or similar ID already exists. Please choose a different Project ID.',
                    'errors' => ['project_id' => 'Project ID already exists or conflicts with existing projects.']
                ])->setStatusCode(400);
            }

            // Get assignment data
            $taskCategories = $this->request->getPost('task_category') ?: [];
            $taskTypes = $this->request->getPost('task_type') ?: [];
            $assignedUsers = $this->request->getPost('assigned_to') ?: [];

            log_message('info', 'Assignment data: ' . json_encode([
                'task_categories' => $taskCategories,
                'task_types' => $taskTypes,
                'assigned_users' => $assignedUsers
            ]));

            // Validate that we have at least one assignment
            if (empty($assignedUsers) || empty($assignedUsers[0])) {
                log_message('error', 'No assignees provided. Received assigned_users: ' . json_encode($assignedUsers));
                log_message('error', 'Task categories: ' . json_encode($taskCategories));
                log_message('error', 'Task types: ' . json_encode($taskTypes));
                log_message('error', 'All POST data: ' . json_encode($this->request->getPost()));

                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'At least one task assignment is required. Please assign tasks to team members.',
                    'debug_info' => [
                        'assigned_users' => $assignedUsers,
                        'task_categories' => $taskCategories,
                        'task_types' => $taskTypes,
                        'all_post_keys' => array_keys($this->request->getPost())
                    ]
                ])->setStatusCode(400);
            }

            // Create a single main project with multiple tasks
            $baseProjectId = $this->request->getPost('project_id');

            // Create the main project (assigned to the first user for now)
            $mainProjectData = [
                'project_id' => $baseProjectId,
                'project_name' => $this->request->getPost('project_name'),
                'client_mobile' => $this->sanitizeMobileNumber($this->request->getPost('client_mobile')),
                'location' => $this->request->getPost('location') ?: '',
                'description' => $this->request->getPost('description'),
                'start_date' => $this->request->getPost('start_date'),
                'target_completion' => $this->calculateTargetCompletion($this->request->getPost('start_date'), $this->calculateDurationFromTasks()),
                'assigned_to' => $assignedUsers[0], // Main assignee
                'status' => 'planning',
                'progress_percentage' => 0,
                'created_by' => $user['id'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            log_message('info', 'Creating main project: ' . json_encode($mainProjectData));

            $mainProjectId = $this->projectModel->skipValidation(true)->insert($mainProjectData);

            if (!$mainProjectId) {
                $errors = $this->projectModel->errors();
                log_message('error', 'Main project creation failed: ' . json_encode($errors));
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to create main project',
                    'errors' => $errors
                ])->setStatusCode(500);
            }

            log_message('info', "Main project created successfully with ID: $mainProjectId");

            // Record project creation in timeline
            try {
                $this->projectTimelineModel->addProjectCreated(
                    $mainProjectId,
                    $user['id'],
                    $this->request->getPost('project_name')
                );
            } catch (\Exception $e) {
                log_message('error', 'Timeline creation failed: ' . $e->getMessage());
            }

            // Create individual tasks with dependencies
            $createdTasks = $this->createProjectTasksWithDependencies(
                $mainProjectId,
                $taskCategories,
                $taskTypes,
                $assignedUsers,
                $this->request->getPost('target_days') ?: [],
                $this->request->getPost('depends_on') ?: [],
                $this->request->getPost('task_priority') ?: [],
                $user['id']
            );

            if (empty($createdTasks)) {
                log_message('error', 'No tasks were created');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to create project tasks'
                ])->setStatusCode(500);
            }

            // Create notifications for all assignees
            $this->createAssignmentNotifications($mainProjectId, $this->request->getPost('project_name'), $assignedUsers);

            $createdProjects = [$mainProjectId];

            if (!empty($createdProjects)) {
                log_message('info', "All projects created successfully. IDs: " . implode(', ', $createdProjects));

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Project(s) created successfully for ' . count($createdProjects) . ' assignee(s)',
                    'project_ids' => $createdProjects,
                    'total_created' => count($createdProjects)
                ]);
            } else {
                log_message('error', 'No projects were created');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'No projects were created'
                ])->setStatusCode(500);
            }

        } catch (\Exception $e) {
            log_message('error', 'Project creation error: ' . $e->getMessage());
            log_message('error', 'Project creation stack trace: ' . $e->getTraceAsString());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error creating project: ' . $e->getMessage(),
                'debug_info' => [
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            ])->setStatusCode(500);
        }
    }

    /**
     * Create project tasks with dependencies
     */
    private function createProjectTasksWithDependencies($projectId, $taskCategories, $taskTypes, $assignedUsers, $targetDays, $dependencies, $priorities, $createdBy)
    {
        $createdTasks = [];
        $taskIdMapping = []; // Map array index to actual task ID

        for ($i = 0; $i < count($assignedUsers); $i++) {
            if (!empty($assignedUsers[$i])) {
                $category = $taskCategories[$i] ?? 'general';
                $taskTypeId = $taskTypes[$i] ?? 1; // Use the actual task_type_id from form
                $assigneeId = $assignedUsers[$i];
                $targetDay = $targetDays[$i] ?? 7;
                $priority = $priorities[$i] ?? 'medium';

                // Convert to integer to ensure it's a valid task_type_id
                $taskTypeId = (int)$taskTypeId;

                // Get the actual task name from the task type
                $taskTypeModel = new \App\Models\TaskTypeModel();

                // Ensure default task types exist
                $this->ensureDefaultTaskTypes($taskTypeModel);

                $taskTypeData = $taskTypeModel->find($taskTypeId);
                $taskName = $taskTypeData ? $taskTypeData['name'] : $this->getTaskName($category, $taskType);

                // Create a task for this assignment
                $taskData = [
                    'project_id' => $projectId,
                    'task_type_id' => $taskTypeId,
                    'task_name' => $taskName,
                    'description' => "Task: " . $taskName . " (Category: " . ucfirst($category) . ")",
                    'assigned_to' => $assigneeId,
                    'status' => 'not_started',
                    'priority' => $priority,
                    'target_days' => $targetDay,
                    'task_order' => $i + 1, // Add task order
                    'created_by' => $createdBy,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                log_message('info', "Attempting to create task: " . json_encode($taskData));

                $taskId = $this->projectTaskModel->skipValidation(true)->insert($taskData);

                if ($taskId) {
                    $createdTasks[] = $taskId;
                    $taskIdMapping[$i] = $taskId;
                    log_message('info', "Task created with ID: $taskId for assignee: $assigneeId");
                } else {
                    $errors = $this->projectTaskModel->errors();
                    log_message('error', "Task creation failed: " . json_encode($errors));
                    log_message('error', "Task data that failed: " . json_encode($taskData));
                }
            }
        }

        // Now handle dependencies after all tasks are created
        log_message('info', 'Dependencies received: ' . json_encode($dependencies));
        log_message('info', 'Task ID mapping: ' . json_encode($taskIdMapping));

        if (!empty($dependencies)) {
            $this->createTaskDependencies($taskIdMapping, $dependencies);
        } else {
            log_message('info', 'No dependencies to create');
        }

        return $createdTasks;
    }

    /**
     * Create task dependencies
     */
    private function createTaskDependencies($taskIdMapping, $dependencies)
    {
        log_message('info', 'Creating task dependencies. Task mapping: ' . json_encode($taskIdMapping));
        log_message('info', 'Dependencies array: ' . json_encode($dependencies));

        foreach ($dependencies as $taskIndex => $dependsOnArray) {
            log_message('info', "Processing dependency for task index $taskIndex: " . json_encode($dependsOnArray));

            if (!empty($dependsOnArray) && isset($taskIdMapping[$taskIndex])) {
                $currentTaskId = $taskIdMapping[$taskIndex];
                log_message('info', "Current task ID: $currentTaskId");

                // Handle multiple dependencies
                if (is_array($dependsOnArray)) {
                    log_message('info', "Processing multiple dependencies: " . json_encode($dependsOnArray));
                    foreach ($dependsOnArray as $dependsOnIndex) {
                        if (!empty($dependsOnIndex) && isset($taskIdMapping[$dependsOnIndex])) {
                            $dependsOnTaskId = $taskIdMapping[$dependsOnIndex];
                            log_message('info', "Creating dependency: Task $currentTaskId depends on Task $dependsOnTaskId");
                            $this->createSingleTaskDependency($currentTaskId, $dependsOnTaskId);
                        } else {
                            log_message('warning', "Dependency index $dependsOnIndex not found in task mapping");
                        }
                    }
                } else {
                    // Single dependency
                    log_message('info', "Processing single dependency: $dependsOnArray");
                    if (!empty($dependsOnArray) && isset($taskIdMapping[$dependsOnArray])) {
                        $dependsOnTaskId = $taskIdMapping[$dependsOnArray];
                        log_message('info', "Creating dependency: Task $currentTaskId depends on Task $dependsOnTaskId");
                        $this->createSingleTaskDependency($currentTaskId, $dependsOnTaskId);
                    } else {
                        log_message('warning', "Dependency index $dependsOnArray not found in task mapping");
                    }
                }
            } else {
                log_message('warning', "Task index $taskIndex not found in task mapping or dependency is empty");
            }
        }
    }

    /**
     * Create a single task dependency
     */
    private function createSingleTaskDependency($taskId, $dependsOnTaskId)
    {
        try {
            // Update the depends_on field directly in the project_tasks table
            $updateResult = $this->projectTaskModel->update($taskId, [
                'depends_on' => $dependsOnTaskId,
                'is_locked' => true, // Lock the task initially
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            if ($updateResult) {
                log_message('info', "Dependency created: Task $taskId depends on Task $dependsOnTaskId");

                // Update task locking based on dependencies
                $this->projectTaskModel->lockTasksBasedOnDependencies($this->getProjectIdFromTask($taskId));
            } else {
                log_message('error', "Failed to update depends_on field for task $taskId");
            }
        } catch (\Exception $e) {
            log_message('error', 'Failed to create task dependency: ' . $e->getMessage());
        }
    }

    /**
     * Get project ID from task ID
     */
    private function getProjectIdFromTask($taskId)
    {
        $task = $this->projectTaskModel->find($taskId);
        return $task ? $task['project_id'] : null;
    }

    /**
     * Create project tasks based on assignments (legacy method)
     */
    private function createProjectTasks($projectId, $taskCategories, $taskTypes, $assignedUsers, $createdBy)
    {
        for ($i = 0; $i < count($assignedUsers); $i++) {
            if (!empty($assignedUsers[$i])) {
                $category = $taskCategories[$i] ?? 'general';
                $taskTypeId = $taskTypes[$i] ?? 1; // Use the actual task_type_id from form
                $assigneeId = $assignedUsers[$i];

                // Convert to integer to ensure it's a valid task_type_id
                $taskTypeId = (int)$taskTypeId;

                // Get the actual task name from the task type
                $taskTypeModel = new \App\Models\TaskTypeModel();
                $taskTypeData = $taskTypeModel->find($taskTypeId);
                $taskName = $taskTypeData ? $taskTypeData['name'] : $this->getTaskName($category, $taskType);

                // Create a task for this assignment
                $taskData = [
                    'project_id' => $projectId,
                    'task_type_id' => $taskTypeId,
                    'task_name' => $taskName,
                    'description' => "Task: " . $taskName . " (Category: " . ucfirst($category) . ")",
                    'assigned_to' => $assigneeId,
                    'status' => 'not_started',
                    'priority' => 'medium',
                    'created_by' => $createdBy,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $this->projectTaskModel->insert($taskData);
            }
        }
    }

    /**
     * Get task name based on category and type
     */
    private function getTaskName($category, $taskType)
    {
        $taskNames = [
            'office' => [
                'planning' => 'Project Planning',
                'documentation' => 'Documentation',
                'permits' => 'Permit Processing',
                'coordination' => 'Team Coordination'
            ],
            'site' => [
                'supervision' => 'Site Supervision',
                'quality_control' => 'Quality Control',
                'safety' => 'Safety Management',
                'material_management' => 'Material Management'
            ],
            'design' => [
                '3d_design' => '3D Design',
                'architectural' => 'Architectural Design',
                'structural' => 'Structural Design',
                'electrical' => 'Electrical Design'
            ],
            'management' => [
                'project_management' => 'Project Management',
                'client_relations' => 'Client Relations',
                'budget_control' => 'Budget Control',
                'timeline_management' => 'Timeline Management'
            ]
        ];

        return $taskNames[$category][$taskType] ?? ucwords(str_replace('_', ' ', $taskType));
    }

    /**
     * Get task type ID based on category and type
     */
    private function getTaskTypeId($category, $taskType)
    {
        // Map categories and types to default task type IDs
        $taskTypeMapping = [
            'office' => [
                'planning' => 1,        // Planning
                'documentation' => 1,   // Planning
                'permits' => 3,         // Permit Approval
                'coordination' => 1     // Planning
            ],
            'site' => [
                'supervision' => 4,     // Construction
                'quality_control' => 4, // Construction
                'safety' => 4,          // Construction
                'material_management' => 4 // Construction
            ],
            'design' => [
                '3d_design' => 2,       // 3D Design
                'architectural' => 2,   // 3D Design
                'structural' => 2,      // 3D Design
                'electrical' => 2       // 3D Design
            ],
            'management' => [
                'project_management' => 1, // Planning
                'client_relations' => 5,   // Completion
                'budget_control' => 1,     // Planning
                'timeline_management' => 1 // Planning
            ]
        ];

        // Get the task type ID from mapping, default to 1 (Planning)
        return $taskTypeMapping[$category][$taskType] ?? 1;
    }

    /**
     * Ensure default task types exist in the database
     */
    private function ensureDefaultTaskTypes($taskTypeModel)
    {
        $existingTypes = $taskTypeModel->findAll();
        if (empty($existingTypes)) {
            // Create default task types
            $taskTypeModel->createDefaultTaskTypes(1);
        }
    }

    /**
     * Calculate target completion date from start date and duration
     */
    private function calculateTargetCompletion($startDate, $duration)
    {
        $start = new \DateTime($startDate);
        $start->add(new \DateInterval('P' . $duration . 'D'));
        return $start->format('Y-m-d');
    }

    /**
     * Calculate project duration from task assignments
     */
    private function calculateDurationFromTasks()
    {
        $targetDays = $this->request->getPost('target_days') ?: [];

        if (empty($targetDays)) {
            return 7; // Default duration if no tasks
        }

        // Sum all target days for total duration
        $totalDuration = 0;
        foreach ($targetDays as $days) {
            $totalDuration += (int)$days;
        }

        return max($totalDuration, 1); // Minimum 1 day
    }

    /**
     * Sanitize mobile number - return null if only prefix
     */
    private function sanitizeMobileNumber($mobile)
    {
        if (empty($mobile) || trim($mobile) === '+91' || trim($mobile) === '+91 ') {
            return null;
        }
        return trim($mobile);
    }

    /**
     * Check if base project ID already exists (including variations with suffixes)
     */
    private function checkBaseProjectIdExists($baseProjectId)
    {
        // Check for exact match
        $exactMatch = $this->projectModel->where('project_id', $baseProjectId)->first();
        if ($exactMatch) {
            return true;
        }

        // Check for variations with suffixes (e.g., PRJ001-1, PRJ001-2)
        $variations = $this->projectModel->like('project_id', $baseProjectId . '-', 'after')->findAll();
        if (!empty($variations)) {
            return true;
        }

        // Check if this looks like a variation of an existing base ID
        $basePattern = preg_replace('/-\d+$/', '', $baseProjectId);
        if ($basePattern !== $baseProjectId) {
            $baseExists = $this->projectModel->where('project_id', $basePattern)->first();
            if ($baseExists) {
                return true;
            }
        }

        return false;
    }

    /**
     * Test page for debugging buttons
     */
    public function testButtons()
    {
        $data = [
            'title' => 'Button Test Page',
            'user' => session()->get('user')
        ];

        return view('test_buttons', $data);
    }

    /**
     * Create notifications for assigned users
     */
    private function createAssignmentNotifications($projectId, $projectName, $assignedUsers)
    {
        foreach ($assignedUsers as $userId) {
            if (!empty($userId)) {
                $assignedUser = $this->userModel->find($userId);
                if ($assignedUser) {
                    $result = $this->notificationModel->createNotification(
                        $assignedUser['id'],
                        'info',
                        'New Project Assignment',
                        "You have been assigned to project: {$projectName}. Please check your tasks.",
                        ['project_id' => $projectId, 'project_name' => $projectName]
                    );

                    if ($result) {
                        log_message('info', "Notification sent to user {$assignedUser['id']} for project {$projectName}");
                    } else {
                        log_message('error', "Failed to send notification to user {$assignedUser['id']} for project {$projectName}");
                    }
                }
            }
        }
    }

    /**
     * Debug endpoint to test project creation
     */
    public function debugCreate()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        return $this->response->setJSON([
            'success' => true,
            'debug_info' => [
                'user' => $user,
                'post_data' => $this->request->getPost(),
                'method' => $this->request->getMethod(),
                'headers' => $this->request->getHeaders(),
                'database_connection' => $this->projectModel->db->getDatabase()
            ]
        ]);
    }

    /**
     * Test project deletion functionality
     */
    public function testDelete()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        try {
            // Create a test project with tasks
            $testData = [
                'project_id' => 'DELETE-TEST-' . time(),
                'project_name' => 'Delete Test Project',
                'location' => 'Test Location',
                'start_date' => date('Y-m-d'),
                'status' => 'planning',
                'progress_percentage' => 0,
                'created_by' => $user['id'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $projectId = $this->projectModel->skipValidation(true)->insert($testData);

            if ($projectId) {
                // Create test tasks - one not started, one in progress
                $tasks = [
                    [
                        'project_id' => $projectId,
                        'task_type_id' => 1,
                        'task_name' => 'Test Task 1',
                        'description' => 'Not started task',
                        'assigned_to' => $user['id'],
                        'status' => 'not_started',
                        'priority' => 'medium',
                        'target_days' => 5,
                        'task_order' => 1,
                        'created_by' => $user['id']
                    ],
                    [
                        'project_id' => $projectId,
                        'task_type_id' => 1,
                        'task_name' => 'Test Task 2',
                        'description' => 'Running task',
                        'assigned_to' => $user['id'],
                        'status' => 'in_progress',
                        'priority' => 'medium',
                        'target_days' => 3,
                        'task_order' => 2,
                        'created_by' => $user['id']
                    ]
                ];

                foreach ($tasks as $taskData) {
                    $this->projectTaskModel->skipValidation(true)->insert($taskData);
                }

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Test project created with tasks',
                    'project_id' => $projectId,
                    'note' => 'Try to delete this project - it should fail because Task 2 is in_progress'
                ]);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Simple test endpoint to test basic project insertion
     */
    public function testInsert()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        try {
            // Simple test data
            $testData = [
                'project_id' => 'TEST-' . time(),
                'project_name' => 'Test Project',
                'location' => 'Test Location',
                'start_date' => date('Y-m-d'),
                'status' => 'planning',
                'progress_percentage' => 0,
                'created_by' => $user['id'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            log_message('info', 'Test insert data: ' . json_encode($testData));

            $projectId = $this->projectModel->skipValidation(true)->insert($testData);

            if ($projectId) {
                log_message('info', 'Test insert successful: ' . $projectId);

                // Test task creation
                $taskData = [
                    'project_id' => $projectId,
                    'task_type_id' => 1,
                    'task_name' => 'Test Task',
                    'description' => 'Test task description',
                    'assigned_to' => $user['id'],
                    'status' => 'not_started',
                    'priority' => 'medium',
                    'target_days' => 5,
                    'task_order' => 1,
                    'created_by' => $user['id'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                log_message('info', 'Test task data: ' . json_encode($taskData));

                $taskId = $this->projectTaskModel->skipValidation(true)->insert($taskData);

                if ($taskId) {
                    log_message('info', 'Test task created: ' . $taskId);
                } else {
                    $taskErrors = $this->projectTaskModel->errors();
                    log_message('error', 'Test task creation failed: ' . json_encode($taskErrors));
                }

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Test project created successfully',
                    'project_id' => $projectId,
                    'task_id' => $taskId ?? null,
                    'task_errors' => $taskErrors ?? null,
                    'data' => $testData
                ]);
            } else {
                $errors = $this->projectModel->errors();
                log_message('error', 'Test insert failed: ' . json_encode($errors));
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Test project creation failed',
                    'errors' => $errors
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Test insert exception: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Update project status with comment
     */
    public function updateStatus($projectId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        $project = $this->projectModel->find($projectId);

        if (!$project) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Project not found'
            ])->setStatusCode(404);
        }

        // Check if user can update this project
        if ($project['assigned_to'] !== $user['id'] && !$this->authLib->canManageProjects()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        $rules = [
            'status' => 'required|in_list[not_started,planning,in_progress,on_hold,completed,review,sent_for_review,revision_needed,client_accepted,task_completed]',
            'notes' => 'permit_empty|max_length[500]'
        ];

        $this->validation->setRules($rules);

        if (!$this->validation->run($this->request->getPost())) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validation->getErrors()
            ])->setStatusCode(400);
        }

        try {
            $newStatus = $this->request->getPost('status');
            $notes = $this->request->getPost('notes') ?: '';
            $currentTime = date('Y-m-d H:i:s');

            // Handle file upload if present
            $filePath = null;
            $file = $this->request->getFile('status_file');

            if ($file && $file->isValid() && !$file->hasMoved()) {
                // Load upload library
                $uploadLib = new \App\Libraries\UploadLib();
                $uploadResult = $uploadLib->uploadFile($file, 'project_updates');

                if ($uploadResult['success']) {
                    $filePath = $uploadResult['file_path'];
                } else {
                    log_message('error', 'File upload failed during status update: ' . json_encode($uploadResult['errors']));
                    // Continue without file - don't fail the status update
                }
            }

            // Check payment completion before allowing project completion
            if ($newStatus === 'completed') {
                $paymentSummary = $this->projectPaymentModel->getProjectPaymentSummary($projectId);
                if ($paymentSummary['payment_status'] !== 'completed' && $paymentSummary['total_amount'] > 0) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Project cannot be completed until all payments are processed. Please complete payment details first.',
                        'requires_payment' => true,
                        'payment_summary' => $paymentSummary
                    ])->setStatusCode(400);
                }
            }

            $updateData = [
                'status' => $newStatus,
                'last_update_notes' => $notes,
                'last_updated' => $currentTime,
                'updated_at' => $currentTime
            ];

            // Set completion date if completed
            if ($newStatus === 'completed') {
                $updateData['actual_completion'] = $currentTime;
                $updateData['progress_percentage'] = 100;
            }

            // Record status change timestamp
            $statusField = $newStatus . '_at';
            $updateData[$statusField] = $currentTime;

            if ($this->projectModel->update($projectId, $updateData)) {
                // Record status change in timeline with file attachment (with error handling)
                try {
                    $this->projectTimelineModel->addStatusChange(
                        $projectId,
                        $user['id'],
                        $project['status'],
                        $newStatus,
                        $notes,
                        $filePath  // Include file path in timeline
                    );
                } catch (\Exception $e) {
                    log_message('error', 'Timeline update failed: ' . $e->getMessage());
                    // Continue execution - timeline is not critical for status update
                }

                // Create notification for project creator and managers
                $this->createStatusChangeNotification($project, $newStatus, $notes, $user);

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Project status updated successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update project status'
                ])->setStatusCode(500);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error updating project status: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating project status: ' . $e->getMessage(),
                'debug_info' => [
                    'project_id' => $projectId,
                    'new_status' => $newStatus ?? 'unknown',
                    'user_id' => $user['id'] ?? 'unknown',
                    'error' => $e->getMessage()
                ]
            ])->setStatusCode(500);
        }
    }

    /**
     * Get project timeline with status history
     */
    public function getTimeline($projectId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        $project = $this->projectModel->find($projectId);

        if (!$project) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Project not found'
            ])->setStatusCode(404);
        }

        // Check if user can view this project
        if ($project['assigned_to'] !== $user['id'] && !$this->authLib->canViewAllProjects()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        try {
            $timeline = $this->projectTimelineModel->getProjectTimeline($projectId);

            return $this->response->setJSON([
                'success' => true,
                'timeline' => $timeline
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error loading project timeline: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error loading project timeline'
            ])->setStatusCode(500);
        }
    }

    /**
     * Get users for assignment dropdown
     */
    public function getUsers()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $search = $this->request->getGet('search') ?: '';

        try {
            $builder = $this->userModel->select('id, username, email, roles')
                                     ->where('is_active', 1);

            // Add search functionality
            if (!empty($search)) {
                $builder->groupStart()
                       ->like('username', $search)
                       ->orLike('email', $search)
                       ->orLike('roles', $search)
                       ->groupEnd();
            }

            $users = $builder->orderBy('username', 'ASC')
                           ->findAll();

            // Format users for dropdown display
            $formattedUsers = array_map(function($user) {
                return [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'roles' => $user['roles'],
                    'display_name' => $user['username'] . ' (' . ucfirst($user['roles']) . ')',
                    'full_display' => $user['username'] . ' - ' . $user['email'] . ' (' . ucfirst($user['roles']) . ')'
                ];
            }, $users);

            return $this->response->setJSON([
                'success' => true,
                'users' => $formattedUsers,
                'count' => count($formattedUsers)
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error loading users for project assignment: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error loading users: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Bulk import projects from Excel (admin/manager only)
     */
    public function bulkImport()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        // Check if user can import projects
        if (!in_array($user['roles'], ['admin', 'manager'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied. Only admins and managers can import projects.'
            ])->setStatusCode(403);
        }

        $file = $this->request->getFile('excel_file');

        if (!$file || !$file->isValid()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Please select a valid Excel file'
            ])->setStatusCode(400);
        }

        // Validate file type
        $allowedTypes = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
        if (!in_array($file->getMimeType(), $allowedTypes)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Only Excel files (.xls, .xlsx) are allowed'
            ])->setStatusCode(400);
        }

        try {
            // For now, return success message - actual Excel parsing would require PhpSpreadsheet
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Excel import feature will be implemented with PhpSpreadsheet library'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Project import error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error importing projects: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * View project details
     */
    public function view($projectId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login');
        }

        $user = $this->authLib->user();

        try {
            // Get project details
            $project = $this->projectModel->find($projectId);

            if (!$project) {
                return redirect()->to('/projects')->with('error', 'Project not found');
            }

            // Check if user has permission to view this project
            $canView = $this->authLib->canViewAllProjects() ||
                      ($project['created_by'] == $user['id']) ||
                      ($project['assigned_to'] == $user['id']);

            // Also check if user is assigned to any task in this project
            if (!$canView && isset($this->projectTaskModel)) {
                $userTask = $this->projectTaskModel
                    ->where('project_id', $projectId)
                    ->where('assigned_to', $user['id'])
                    ->first();

                if ($userTask) {
                    $canView = true;
                }
            }

            if (!$canView) {
                return redirect()->to('/projects')->with('error', 'Access denied');
            }

            // Get project creator and assignee details
            $creator = $this->userModel->find($project['created_by']);
            $assignee = $this->userModel->find($project['assigned_to']);

            // Get all team members for multi-task projects
            $teamMembers = $this->getProjectTeamMembers($projectId);

            // Get project timeline/history
            $timeline = $this->getProjectTimelineForView($projectId);

            // Get project tasks if any
            $tasks = [];
            if (isset($this->projectTaskModel)) {
                $tasks = $this->projectTaskModel->where('project_id', $projectId)->findAll();
            }

            $data = [
                'title' => 'Project Details - ' . $project['project_name'],
                'user' => $user,
                'project' => $project,
                'creator' => $creator,
                'assignee' => $assignee,
                'teamMembers' => $teamMembers,
                'timeline' => $timeline,
                'tasks' => $tasks
            ];

            return view('projects/view', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error viewing project: ' . $e->getMessage());
            return redirect()->to('/projects')->with('error', 'Error loading project details');
        }
    }

    /**
     * Get project timeline for view (private helper)
     */
    private function getProjectTimelineForView($projectId)
    {
        try {
            // First try to get timeline from project_timeline table
            $timelineData = [];
            if (isset($this->projectTimelineModel)) {
                $timelineData = $this->projectTimelineModel
                    ->select('project_timeline.*, users.username, users.first_name, users.last_name')
                    ->join('users', 'users.id = project_timeline.user_id', 'left')
                    ->where('project_timeline.project_id', $projectId)
                    ->orderBy('project_timeline.created_at', 'DESC')
                    ->findAll();
            }

            // If no timeline data, create basic timeline from project status changes
            if (empty($timelineData)) {
                return $this->generateBasicTimeline($projectId);
            }

            $timeline = [];
            $taskDurations = []; // Track total time per task

            foreach ($timelineData as $item) {
                // Get user details
                $user = $this->userModel->find($item['user_id']);
                $userName = $user ? $user['username'] : 'Unknown User';

                // Parse metadata for additional info
                $metadata = json_decode($item['metadata'], true) ?? [];
                $duration = isset($metadata['duration_formatted']) ? $metadata['duration_formatted'] : null;
                $durationSeconds = isset($metadata['duration_seconds']) ? $metadata['duration_seconds'] : 0;

                // Extract assignee and task information from metadata
                $assigneeName = $metadata['assignee_name'] ?? 'N/A';
                $taskName = $metadata['task_name'] ?? 'General Task';
                $assignerName = $metadata['assigner_name'] ?? $userName;
                $taskId = $metadata['task_id'] ?? 'general';

                // Try to get real task name from project_tasks table if task_id exists
                if ($taskId !== 'general' && isset($this->projectTaskModel)) {
                    $realTask = $this->projectTaskModel
                        ->select('project_tasks.task_name, task_types.name as task_type_name')
                        ->join('task_types', 'task_types.id = project_tasks.task_type_id', 'left')
                        ->find($taskId);

                    if ($realTask && !empty($realTask['task_name'])) {
                        $taskName = $realTask['task_name'];
                    } elseif ($realTask && !empty($realTask['task_type_name'])) {
                        $taskName = $realTask['task_type_name'];
                    }
                }

                // Calculate total time per task
                if (!isset($taskDurations[$taskId])) {
                    $taskDurations[$taskId] = [
                        'total_seconds' => 0,
                        'task_name' => $taskName,
                        'sessions' => []
                    ];
                }

                if ($durationSeconds > 0) {
                    $taskDurations[$taskId]['total_seconds'] += $durationSeconds;
                    $taskDurations[$taskId]['sessions'][] = [
                        'duration' => $duration,
                        'timestamp' => $item['created_at'],
                        'action' => $item['title']
                    ];
                }

                // Build timeline entry
                $timelineEntry = [
                    'id' => $item['id'],
                    'action' => $item['title'],
                    'status' => $item['new_status'] ?? 'unknown',
                    'user' => $userName,
                    'assignee' => $assigneeName,
                    'task_name' => $taskName,
                    'task_id' => $taskId,
                    'assigner' => $assignerName,
                    'timestamp' => $item['created_at'],
                    'notes' => $item['notes'] ?? $item['description'],
                    'duration' => $duration,
                    'duration_seconds' => $durationSeconds,
                    'file_path' => $item['file_path'] ?? null,
                    'has_attachment' => !empty($item['file_path'])
                ];

                // Add session duration info if available
                if ($duration) {
                    $timelineEntry['notes'] = ($timelineEntry['notes'] ? $timelineEntry['notes'] . ' ' : '') .
                                            "(Session Duration: {$duration})";
                }

                $timeline[] = $timelineEntry;
            }

            // Add total task time information to timeline entries
            foreach ($timeline as &$entry) {
                $taskId = $entry['task_id'];
                if (isset($taskDurations[$taskId]) && $taskDurations[$taskId]['total_seconds'] > 0) {
                    $totalTime = $this->formatDuration($taskDurations[$taskId]['total_seconds']);
                    $entry['total_task_time'] = $totalTime;
                    $entry['total_task_seconds'] = $taskDurations[$taskId]['total_seconds'];
                    $entry['task_sessions_count'] = count($taskDurations[$taskId]['sessions']);
                }
            }

            return $timeline;

        } catch (\Exception $e) {
            log_message('error', 'Error loading project timeline: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get all team members for a project (including multi-task assignments)
     */
    private function getProjectTeamMembers($projectId)
    {
        try {
            log_message('info', "Getting team members for project ID: $projectId");

            // Get the main project ID (remove task suffixes like -1, -2, etc.)
            $mainProjectId = preg_replace('/-\d+$/', '', $projectId);
            log_message('info', "Main project ID: $mainProjectId");

            $teamMembers = [];
            $seenUsers = []; // Track users we've already added

            // Method 1: Check project_tasks table for modern multi-task projects
            if (isset($this->projectTaskModel)) {
                log_message('info', "Checking project_tasks table for project ID: $projectId");

                $projectTasks = $this->projectTaskModel
                    ->select('project_tasks.*, users.username, users.email, users.first_name, users.last_name, task_types.name as task_type_name')
                    ->join('users', 'users.id = project_tasks.assigned_to', 'left')
                    ->join('task_types', 'task_types.id = project_tasks.task_type_id', 'left')
                    ->where('project_tasks.project_id', $projectId)
                    ->findAll();

                log_message('info', "Found " . count($projectTasks) . " project tasks");
                log_message('info', "Project tasks data: " . json_encode($projectTasks));

                foreach ($projectTasks as $task) {
                    if ($task['assigned_to']) {
                        $teamMembers[] = [
                            'id' => $task['assigned_to'],
                            'username' => $task['username'],
                            'email' => $task['email'],
                            'first_name' => $task['first_name'],
                            'last_name' => $task['last_name'],
                            'task_name' => $task['task_name'] ?? ($task['task_type_name'] ?? 'General Task'),
                            'task_status' => $task['status'] ?? 'not_started',
                            'status' => $task['status'] ?? 'not_started',
                            'task_id' => $task['id'],
                            'task_type_name' => $task['task_type_name']
                        ];
                        $seenUsers[] = $task['assigned_to'];
                    }
                }

                log_message('info', "Team members from project_tasks: " . json_encode($teamMembers));
            } else {
                log_message('error', "projectTaskModel is not initialized");
            }

            // Method 2: Check projects table for legacy multi-task projects (project IDs like 34-1, 34-2, etc.)
            $allProjectTasks = $this->projectModel
                ->select('projects.*, users.username, users.email, users.first_name, users.last_name')
                ->join('users', 'users.id = projects.assigned_to', 'left')
                ->like('projects.project_id', $mainProjectId)
                ->where('projects.assigned_to IS NOT NULL')
                ->findAll();

            foreach ($allProjectTasks as $task) {
                if ($task['assigned_to']) {
                    $teamMembers[] = [
                        'id' => $task['assigned_to'],
                        'username' => $task['username'],
                        'email' => $task['email'],
                        'first_name' => $task['first_name'],
                        'last_name' => $task['last_name'],
                        'task_name' => $task['task_name'] ?? 'General Task',
                        'task_status' => $task['task_status'] ?? $task['status'] ?? 'not_started',
                        'status' => $task['status'] ?? 'not_started',
                        'project_task_id' => $task['id'],
                        'project_id_full' => $task['project_id']
                    ];
                    $seenUsers[] = $task['assigned_to'];
                }
            }

            // Method 3: Include main project assignee if not already included
            $project = $this->projectModel->find($projectId);
            if ($project && $project['assigned_to'] && !in_array($project['assigned_to'], $seenUsers)) {
                $mainAssignee = $this->userModel->find($project['assigned_to']);
                if ($mainAssignee) {
                    $teamMembers[] = [
                        'id' => $mainAssignee['id'],
                        'username' => $mainAssignee['username'],
                        'email' => $mainAssignee['email'],
                        'first_name' => $mainAssignee['first_name'],
                        'last_name' => $mainAssignee['last_name'],
                        'task_name' => 'Project Lead',
                        'task_status' => $project['status'] ?? 'not_started',
                        'status' => $project['status'] ?? 'not_started',
                        'task_id' => null
                    ];
                }
            }

            // Method 4: Check for additional assignees in project fields (assigned_manager, assigned_supervisor)
            if ($project) {
                // Add manager if exists and not already included
                if (!empty($project['assigned_manager']) && !in_array($project['assigned_manager'], $seenUsers)) {
                    $manager = $this->userModel->find($project['assigned_manager']);
                    if ($manager) {
                        $teamMembers[] = [
                            'id' => $manager['id'],
                            'username' => $manager['username'],
                            'email' => $manager['email'],
                            'first_name' => $manager['first_name'],
                            'last_name' => $manager['last_name'],
                            'task_name' => 'Project Manager',
                            'task_status' => $project['status'] ?? 'not_started',
                            'status' => $project['status'] ?? 'not_started',
                            'task_id' => null
                        ];
                        $seenUsers[] = $project['assigned_manager'];
                    }
                }

                // Add supervisor if exists and not already included
                if (!empty($project['assigned_supervisor']) && !in_array($project['assigned_supervisor'], $seenUsers)) {
                    $supervisor = $this->userModel->find($project['assigned_supervisor']);
                    if ($supervisor) {
                        $teamMembers[] = [
                            'id' => $supervisor['id'],
                            'username' => $supervisor['username'],
                            'email' => $supervisor['email'],
                            'first_name' => $supervisor['first_name'],
                            'last_name' => $supervisor['last_name'],
                            'task_name' => 'Project Supervisor',
                            'task_status' => $project['status'] ?? 'not_started',
                            'status' => $project['status'] ?? 'not_started',
                            'task_id' => null
                        ];
                        $seenUsers[] = $project['assigned_supervisor'];
                    }
                }
            }

            return $teamMembers;

        } catch (\Exception $e) {
            log_message('error', 'Error loading project team members: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Generate basic timeline from project status changes
     */
    private function generateBasicTimeline($projectId)
    {
        try {
            $project = $this->projectModel->find($projectId);
            if (!$project) {
                return [];
            }

            $timeline = [];

            // Project creation
            $creator = $this->userModel->find($project['created_by']);
            $timeline[] = [
                'id' => 'created',
                'action' => 'Project Created',
                'status' => 'created',
                'user' => $creator ? $creator['username'] : 'System',
                'assignee' => 'N/A',
                'task_name' => 'Project Setup',
                'task_id' => 'general',
                'assigner' => $creator ? $creator['username'] : 'System',
                'timestamp' => $project['created_at'],
                'notes' => 'Project was created and initialized',
                'duration' => null,
                'duration_seconds' => 0
            ];

            // Current status
            if ($project['status'] !== 'not_started') {
                $assignee = $this->userModel->find($project['assigned_to']);
                $timeline[] = [
                    'id' => 'status_' . $project['status'],
                    'action' => 'Status Updated to ' . ucwords(str_replace('_', ' ', $project['status'])),
                    'status' => $project['status'],
                    'user' => $assignee ? $assignee['username'] : 'System',
                    'assignee' => $assignee ? $assignee['username'] : 'N/A',
                    'task_name' => 'General Task',
                    'task_id' => 'general',
                    'assigner' => $assignee ? $assignee['username'] : 'System',
                    'timestamp' => $project['updated_at'] ?? $project['created_at'],
                    'notes' => $project['last_update_notes'] ?? 'Status changed to ' . ucwords(str_replace('_', ' ', $project['status'])),
                    'duration' => null,
                    'duration_seconds' => 0
                ];
            }

            return array_reverse($timeline); // Most recent first

        } catch (\Exception $e) {
            log_message('error', 'Error generating basic timeline: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Format duration in seconds to human readable format
     */
    private function formatDuration($seconds)
    {
        if ($seconds < 60) {
            return $seconds . 's';
        } elseif ($seconds < 3600) {
            $minutes = floor($seconds / 60);
            $remainingSeconds = $seconds % 60;
            return $minutes . 'm' . ($remainingSeconds > 0 ? ' ' . $remainingSeconds . 's' : '');
        } else {
            $hours = floor($seconds / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            $remainingSeconds = $seconds % 60;
            $result = $hours . 'h';
            if ($minutes > 0) $result .= ' ' . $minutes . 'm';
            if ($remainingSeconds > 0) $result .= ' ' . $remainingSeconds . 's';
            return $result;
        }
    }

    /**
     * Edit project page
     */
    public function edit($projectId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login');
        }

        $user = $this->authLib->user();

        try {
            // Get project details
            $project = $this->projectModel->find($projectId);

            if (!$project) {
                return redirect()->to('/projects')->with('error', 'Project not found');
            }

            // Check if user has permission to edit this project
            $canEdit = $this->authLib->canManageProjects() || ($project['created_by'] == $user['id']);

            if (!$canEdit) {
                return redirect()->to('/projects')->with('error', 'Access denied');
            }

            // Get users for assignment dropdown
            $users = $this->userModel->where('is_active', 1)->findAll();

            $data = [
                'title' => 'Edit Project - ' . $project['project_name'],
                'user' => $user,
                'project' => $project,
                'users' => $users
            ];

            return view('projects/edit', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error loading edit page: ' . $e->getMessage());
            return redirect()->to('/projects')->with('error', 'Error loading project for editing');
        }
    }

    /**
     * Update project
     */
    public function update($projectId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login');
        }

        $user = $this->authLib->user();

        // Validation rules
        $rules = [
            'project_name' => 'required|min_length[3]|max_length[255]',
            'location' => 'permit_empty|max_length[255]',
            'start_date' => 'required|valid_date',
            'target_completion' => 'required|valid_date',
            'assigned_to' => 'required|integer',
            'description' => 'permit_empty|max_length[1000]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        try {
            // Get project to check permissions
            $project = $this->projectModel->find($projectId);
            if (!$project) {
                return redirect()->to('/projects')->with('error', 'Project not found');
            }

            // Check permissions
            $canEdit = $this->authLib->canManageProjects() || ($project['created_by'] == $user['id']);
            if (!$canEdit) {
                return redirect()->to('/projects')->with('error', 'Access denied');
            }

            // Update project data
            $updateData = [
                'project_name' => $this->request->getPost('project_name'),
                'client_name' => $this->request->getPost('client_name'),
                'location' => $this->request->getPost('location'),
                'start_date' => $this->request->getPost('start_date'),
                'target_completion' => $this->request->getPost('target_completion'),
                'assigned_to' => $this->request->getPost('assigned_to'),
                'description' => $this->request->getPost('description'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            if ($this->projectModel->update($projectId, $updateData)) {
                return redirect()->to('/projects')->with('success', 'Project updated successfully');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to update project');
            }

        } catch (\Exception $e) {
            log_message('error', 'Error updating project: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Error updating project');
        }
    }

    /**
     * Test manager permissions
     */
    public function testManagerPermissions()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        $permissions = [
            'canManageProjects' => $this->authLib->canManageProjects(),
            'canManageTasks' => $this->authLib->canManageTasks(),
            'canViewAllProjects' => $this->authLib->canViewAllProjects(),
            'canAssignWork' => $this->authLib->canAssignWork(),
            'hasAdminRole' => $this->authLib->hasRole('admin'),
            'hasManagerRole' => $this->authLib->hasRole('manager'),
            'hasUserRole' => $this->authLib->hasRole('user'),
            'userRoles' => $user['roles'] ?? 'none'
        ];

        return $this->response->setJSON([
            'success' => true,
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'email' => $user['email']
            ],
            'permissions' => $permissions
        ]);
    }

    /**
     * Test status validation
     */
    public function testStatusValidation()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $testStatuses = [
            'not_started',
            'planning',
            'in_progress',
            'on_hold',
            'completed',
            'review',
            'sent_for_review',
            'revision_needed',
            'client_accepted',
            'task_completed'
        ];

        $validationResults = [];

        foreach ($testStatuses as $status) {
            $rules = [
                'status' => 'required|in_list[not_started,planning,in_progress,on_hold,completed,review,sent_for_review,revision_needed,client_accepted,task_completed]',
                'notes' => 'permit_empty|max_length[500]'
            ];

            $this->validation->setRules($rules);
            $testData = ['status' => $status, 'notes' => 'Test note'];

            $isValid = $this->validation->run($testData);
            $validationResults[$status] = [
                'valid' => $isValid,
                'errors' => $isValid ? [] : $this->validation->getErrors()
            ];
        }

        return $this->response->setJSON([
            'success' => true,
            'validation_results' => $validationResults,
            'allowed_statuses' => $testStatuses
        ]);
    }



    /**
     * Get project statistics for dashboard
     */
    public function getStats()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        try {
            $stats = $this->projectModel->getProjectStats($user['id'], $user['roles']);

            return $this->response->setJSON([
                'success' => true,
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error loading statistics: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Get project details
     */
    public function getProject($projectId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        try {
            $project = $this->projectModel->getProjectWithDetails($projectId);

            if (!$project) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Project not found'
                ])->setStatusCode(404);
            }

            // Check access permissions
            if ($project['assigned_to'] !== $user['id'] && !in_array($user['roles'], ['admin', 'manager'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Access denied'
                ])->setStatusCode(403);
            }

            return $this->response->setJSON([
                'success' => true,
                'project' => $project
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error loading project: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Get project tasks
     */
    public function getProjectTasks($projectId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        try {
            $project = $this->projectModel->find($projectId);

            if (!$project) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Project not found'
                ])->setStatusCode(404);
            }

            // Check access permissions
            if ($project['assigned_to'] !== $user['id'] && !in_array($user['roles'], ['admin', 'manager'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Access denied'
                ])->setStatusCode(403);
            }

            $tasks = $this->projectTaskModel->getProjectTasks($projectId);
            $taskStats = $this->projectTaskModel->getProjectTaskStats($projectId);

            return $this->response->setJSON([
                'success' => true,
                'tasks' => $tasks,
                'stats' => $taskStats
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error loading project tasks: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Get tasks for editing (alias for getProjectTasks)
     */
    public function getTasks($projectId)
    {
        return $this->getProjectTasks($projectId);
    }

    /**
     * Debug endpoint to check database tables and data
     */
    public function debugDatabase()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        try {
            $debug = [];

            // Check if project_tasks table exists
            $debug['project_tasks_table_exists'] = $this->projectModel->db->tableExists('project_tasks');

            // Get all projects
            $projects = $this->projectModel->findAll();
            $debug['total_projects'] = count($projects);
            $debug['projects'] = array_slice($projects, 0, 5); // First 5 projects

            // Get all project tasks if table exists
            if ($debug['project_tasks_table_exists']) {
                $projectTasks = $this->projectTaskModel->findAll();
                $debug['total_project_tasks'] = count($projectTasks);
                $debug['project_tasks'] = array_slice($projectTasks, 0, 10); // First 10 tasks
            } else {
                $debug['total_project_tasks'] = 'Table does not exist';
                $debug['project_tasks'] = [];
            }

            // Check task_types table
            $debug['task_types_table_exists'] = $this->projectModel->db->tableExists('task_types');
            if ($debug['task_types_table_exists']) {
                $taskTypes = $this->projectModel->db->table('task_types')->get()->getResultArray();
                $debug['task_types'] = $taskTypes;
            }

            // Test a specific project's team members
            if (!empty($projects)) {
                $testProject = $projects[0];
                $debug['test_project'] = $testProject;
                $debug['test_project_team_members'] = $this->getProjectTeamMembers($testProject['id']);
            }

            return $this->response->setJSON([
                'success' => true,
                'debug' => $debug
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error debugging database: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ])->setStatusCode(500);
        }
    }

    /**
     * Get project timeline
     */
    public function getProjectTimeline($projectId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        try {
            $project = $this->projectModel->find($projectId);

            if (!$project) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Project not found'
                ])->setStatusCode(404);
            }

            // Check access permissions
            if ($project['assigned_to'] !== $user['id'] && !in_array($user['roles'], ['admin', 'manager'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Access denied'
                ])->setStatusCode(403);
            }

            // Use the correct ProjectTimelineModel
            $timeline = $this->projectTimelineModel->getProjectTimeline($projectId);

            return $this->response->setJSON([
                'success' => true,
                'timeline' => $timeline,
                'project' => $project
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error loading project timeline: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error loading project timeline: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Update task status
     */
    public function updateTaskStatus($taskId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        $task = $this->projectTaskModel->find($taskId);

        if (!$task) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Task not found'
            ])->setStatusCode(404);
        }

        // Check if user can update this task
        if ($task['assigned_to'] !== $user['id'] && !$this->authLib->canManageTasks()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        $rules = [
            'status' => 'required|in_list[not_started,in_progress,on_hold,completed,review,revision_needed,client_accepted,sent_for_review,rejected]',
            'notes' => 'max_length[1000]'
        ];

        $this->validation->setRules($rules);

        if (!$this->validation->run($this->request->getPost())) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validation->getErrors()
            ])->setStatusCode(400);
        }

        try {
            $newStatus = $this->request->getPost('status');
            $notes = $this->request->getPost('notes');

            // Handle file upload if present
            $filePath = null;
            $file = $this->request->getFile('status_file');

            if ($file && $file->isValid() && !$file->hasMoved()) {
                // Load upload library
                $uploadLib = new \App\Libraries\UploadLib();
                $uploadResult = $uploadLib->uploadFile($file, 'task_updates');

                if ($uploadResult['success']) {
                    $filePath = $uploadResult['file_path'];
                } else {
                    log_message('error', 'File upload failed during task status update: ' . json_encode($uploadResult['errors']));
                    // Continue without file - don't fail the status update
                }
            }

            // Check if task can be started (dependencies)
            if ($newStatus === 'in_progress' && !$this->projectTaskModel->canStartTask($taskId)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Cannot start this task. Dependent tasks must be completed first.'
                ])->setStatusCode(400);
            }

            // Update task status with file attachment
            if ($this->projectTaskModel->updateTaskStatusWithAttachment($taskId, $newStatus, $notes, $user['id'], $filePath)) {
                // Record status change in timeline with time tracking
                try {
                    $this->projectTimelineModel->addStatusChange(
                        $task['project_id'],
                        $user['id'],
                        $task['status'],
                        $newStatus,
                        $notes,
                        $filePath
                    );
                } catch (\Exception $e) {
                    log_message('error', 'Timeline update failed for task status: ' . $e->getMessage());
                    // Continue execution - timeline is not critical for status update
                }

                // Update dependency locks for all tasks in this project
                // This is critical for the dependency system to work properly
                $this->projectTaskModel->lockTasksBasedOnDependencies($task['project_id']);

                // Create notification for project manager
                $project = $this->projectModel->find($task['project_id']);
                if ($project) {
                    $this->createTaskStatusNotification($task, $project, $newStatus, $notes, $user, $filePath);
                }

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Task status updated successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update task status'
                ])->setStatusCode(500);
            }

        } catch (\Exception $e) {
            log_message('error', 'Task status update error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating task status: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Create task status change notification
     */
    private function createTaskStatusNotification($task, $project, $newStatus, $notes, $updatedBy, $filePath = null)
    {
        $statusText = ucwords(str_replace('_', ' ', $newStatus));
        $message = "Task '{$task['task_name']}' in project '{$project['project_name']}' status changed to {$statusText}.";

        if ($notes) {
            $message .= " Notes: {$notes}";
        }

        if ($filePath) {
            $message .= " (File attachment included)";
        }

        // Notify project creator if different from task updater
        if ($project['created_by'] !== $updatedBy['id']) {
            $this->notificationModel->createNotification(
                $project['created_by'],
                'Task Status Updated',
                $message,
                'info'
            );
        }

        // Notify project assignee if different from task updater
        if ($project['assigned_to'] !== $updatedBy['id'] && $project['assigned_to'] !== $project['created_by']) {
            $this->notificationModel->createNotification(
                $project['assigned_to'],
                'Task Status Updated',
                $message,
                'info'
            );
        }
    }

    /**
     * Delete project (admin only)
     */
    public function delete($projectId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        // Only admins can delete projects
        if ($user['roles'] !== 'admin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied. Only administrators can delete projects.'
            ])->setStatusCode(403);
        }

        try {
            $project = $this->projectModel->find($projectId);

            if (!$project) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Project not found'
                ])->setStatusCode(404);
            }

            // Check if any tasks are currently running (in_progress status)
            $runningTasks = $this->projectTaskModel->where('project_id', $projectId)
                                                  ->where('status', 'in_progress')
                                                  ->findAll();

            if (!empty($runningTasks)) {
                $taskNames = array_column($runningTasks, 'task_name');
                $taskList = implode(', ', $taskNames);

                return $this->response->setJSON([
                    'success' => false,
                    'message' => "Cannot delete project. The following tasks are currently running: {$taskList}. Please complete or pause these tasks before deleting the project."
                ])->setStatusCode(400);
            }

            // Start transaction for complete deletion
            $this->projectModel->db->transStart();

            // 1. Delete project tasks (hard delete)
            $this->projectTaskModel->where('project_id', $projectId)->purgeDeleted();
            $this->projectModel->db->query("DELETE FROM project_tasks WHERE project_id = ?", [$projectId]);

            // 2. Delete project timeline entries (hard delete)
            if (isset($this->projectTimelineModel)) {
                $this->projectModel->db->query("DELETE FROM project_timeline WHERE project_id = ?", [$projectId]);
            }

            // 3. Delete project-related notifications (hard delete)
            if (isset($this->notificationModel)) {
                // Delete notifications that mention this project
                $this->projectModel->db->query("DELETE FROM notifications WHERE message LIKE ? OR title LIKE ?",
                    ['%' . $project['project_name'] . '%', '%' . $project['project_name'] . '%']);
            }

            // 4. Delete project-related files (if any file system exists)
            // Note: This would need to be implemented based on your file storage system

            // 5. Hard delete the project itself (bypass soft delete)
            $this->projectModel->db->query("DELETE FROM projects WHERE id = ?", [$projectId]);

            $this->projectModel->db->transComplete();

            if ($this->projectModel->db->transStatus() === false) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to completely delete project'
                ])->setStatusCode(500);
            }

            // Create notification for project team
            $this->createProjectDeletionNotification($project, $user);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Project deleted successfully'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Project deletion error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error deleting project: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Permanently delete all soft-deleted projects (admin only)
     */
    public function purgeDeletedProjects()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        // Only admins can purge deleted projects
        if ($user['roles'] !== 'admin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied. Only administrators can purge deleted projects.'
            ])->setStatusCode(403);
        }

        try {
            // Get all soft-deleted projects
            $deletedProjects = $this->projectModel->onlyDeleted()->findAll();

            if (empty($deletedProjects)) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'No deleted projects found to purge',
                    'purged_count' => 0
                ]);
            }

            $purgedCount = 0;
            $this->projectModel->db->transStart();

            foreach ($deletedProjects as $project) {
                $projectId = $project['id'];

                // 1. Delete project tasks (hard delete)
                $this->projectModel->db->query("DELETE FROM project_tasks WHERE project_id = ?", [$projectId]);

                // 2. Delete project timeline entries (hard delete)
                $this->projectModel->db->query("DELETE FROM project_timeline WHERE project_id = ?", [$projectId]);

                // 3. Delete project-related notifications (hard delete)
                $this->projectModel->db->query("DELETE FROM notifications WHERE message LIKE ? OR title LIKE ?",
                    ['%' . $project['project_name'] . '%', '%' . $project['project_name'] . '%']);

                // 4. Hard delete the project itself
                $this->projectModel->db->query("DELETE FROM projects WHERE id = ?", [$projectId]);

                $purgedCount++;
                log_message('info', "Permanently deleted project: {$project['project_name']} (ID: {$projectId})");
            }

            $this->projectModel->db->transComplete();

            if ($this->projectModel->db->transStatus() === false) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to purge deleted projects'
                ])->setStatusCode(500);
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => "Successfully purged {$purgedCount} deleted projects",
                'purged_count' => $purgedCount
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Project purge error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error purging deleted projects: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Create project deletion notification
     */
    private function createProjectDeletionNotification($project, $deletedBy)
    {
        $message = "Project '{$project['project_name']}' has been deleted by administrator.";

        // Notify assigned user if different from deleter
        if ($project['assigned_to'] && $project['assigned_to'] !== $deletedBy['id']) {
            $this->notificationModel->createNotification(
                $project['assigned_to'],
                'Project Deleted',
                $message,
                'warning'
            );
        }

        // Notify project creator if different from deleter and assignee
        if ($project['created_by'] !== $deletedBy['id'] && $project['created_by'] !== $project['assigned_to']) {
            $this->notificationModel->createNotification(
                $project['created_by'],
                'Project Deleted',
                $message,
                'warning'
            );
        }
    }

    /**
     * Project Reports page
     */
    public function reports()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login');
        }

        $user = $this->authLib->user();

        // Get project statistics
        $stats = $this->getProjectStatistics();

        // Get recent projects
        $recentProjects = $this->projectModel->getProjectsWithDetails($user['id'], $user['roles'], 10);

        $data = [
            'title' => 'Project Reports - SmartFlo',
            'user' => $user,
            'stats' => $stats,
            'recentProjects' => $recentProjects
        ];

        return view('projects/reports', $data);
    }

    /**
     * Get project statistics for reports
     */
    private function getProjectStatistics()
    {
        $user = $this->authLib->user();

        // Base query conditions based on user role
        $baseConditions = [];
        if ($user['roles'] !== 'admin') {
            $baseConditions = [
                'OR' => [
                    'created_by' => $user['id'],
                    'assigned_to' => $user['id']
                ]
            ];
        }

        // Get total projects
        $totalProjects = $this->projectModel->where($baseConditions)->countAllResults(false);

        // Get projects by status
        $notStarted = $this->projectModel->where($baseConditions)->where('status', 'not_started')->countAllResults(false);
        $inProgress = $this->projectModel->where($baseConditions)->where('status', 'in_progress')->countAllResults(false);
        $onHold = $this->projectModel->where($baseConditions)->where('status', 'on_hold')->countAllResults(false);
        $completed = $this->projectModel->where($baseConditions)->where('status', 'completed')->countAllResults(false);

        // Get overdue projects
        $overdue = $this->projectModel->where($baseConditions)
                                     ->where('target_completion <', date('Y-m-d'))
                                     ->where('status !=', 'completed')
                                     ->countAllResults(false);

        return [
            'total' => $totalProjects,
            'not_started' => $notStarted,
            'in_progress' => $inProgress,
            'on_hold' => $onHold,
            'completed' => $completed,
            'overdue' => $overdue,
            'completion_rate' => $totalProjects > 0 ? round(($completed / $totalProjects) * 100, 1) : 0
        ];
    }

    /**
     * Request revision for a task (increments revision number)
     */
    public function requestRevision($taskId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        // Check if user can manage tasks (admin or manager)
        if (!$this->authLib->canManageTasks()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied. Manager privileges required.'
            ])->setStatusCode(403);
        }

        try {
            $task = $this->projectTaskModel->find($taskId);
            if (!$task) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Task not found'
                ])->setStatusCode(404);
            }

            $notes = $this->request->getPost('notes') ?: 'Task sent back for revision';

            // Increment revision and reset status to in_progress
            $result = $this->projectTaskModel->incrementRevision($taskId, $user['id'], $notes);

            if ($result) {
                // Record revision request in timeline
                try {
                    $this->projectTimelineModel->addStatusChange(
                        $task['project_id'],
                        $user['id'],
                        'completed',
                        'in_progress',
                        $notes . ' (Revision requested)',
                        null
                    );
                } catch (\Exception $e) {
                    log_message('error', 'Timeline update failed for revision request: ' . $e->getMessage());
                }

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Task sent back for revision successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to request revision'
                ])->setStatusCode(500);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error requesting revision: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error requesting revision: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Update task-level manager status
     */
    public function updateTaskManagerStatus($taskId)
    {
        // Add detailed logging
        log_message('info', "updateTaskManagerStatus called with taskId: {$taskId}");
        log_message('info', "Request method: " . $this->request->getMethod());
        log_message('info', "Request data: " . json_encode($this->request->getPost()));

        if (!$this->authLib->isLoggedIn()) {
            log_message('error', "Authentication failed for updateTaskManagerStatus");
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        log_message('info', "User authenticated: " . json_encode(['id' => $user['id'], 'username' => $user['username']]));

        // Check if user can manage tasks (admin or manager)
        if (!$this->authLib->canManageTasks()) {
            log_message('error', "Access denied for user {$user['id']} - insufficient privileges");
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied. Manager privileges required.'
            ])->setStatusCode(403);
        }

        // Load TaskFileModel for file management
        $taskFileModel = new \App\Models\TaskFileModel();

        // Validate CSRF token (CodeIgniter 4 handles this automatically if enabled)
        // The CSRF token validation is handled by the framework's security filters
        // if CSRF protection is enabled in the configuration

        try {
            log_message('info', "Looking for task with ID: {$taskId}");
            $task = $this->projectTaskModel->find($taskId);

            if (!$task) {
                log_message('error', "Task not found with ID: {$taskId}");
                // Try to find with soft deletes included
                $taskWithDeleted = $this->projectTaskModel->withDeleted()->find($taskId);
                if ($taskWithDeleted) {
                    log_message('error', "Task {$taskId} was found but is deleted");
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Task has been deleted'
                    ])->setStatusCode(404);
                }
                log_message('error', "Task {$taskId} not found in database");
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Task not found'
                ])->setStatusCode(404);
            }

            log_message('info', "Task found: " . json_encode(['id' => $task['id'], 'task_name' => $task['task_name'], 'status' => $task['status']]));

            log_message('info', "Task found: " . json_encode($task));

            $newStatus = $this->request->getPost('status');
            $notes = $this->request->getPost('notes') ?: '';
            $paymentAmount = $this->request->getPost('payment_amount');
            $paymentType = $this->request->getPost('payment_type') ?: 'advance';
            $paymentStatus = $this->request->getPost('payment_status') ?: 'unpaid';
            $paymentAccount = $this->request->getPost('payment_account');
            $googleDriveLink = $this->request->getPost('google_drive_link');



            // Validate status
            $validStatuses = ['not_reviewed', 'sent_for_review', 'need_revision', 'client_accepted', 'rejected', 'review_not_required'];
            if (!in_array($newStatus, $validStatuses)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid status'
                ])->setStatusCode(400);
            }

            // Handle revision logic
            if ($newStatus === 'need_revision') {
                // Increment revision and reset task status
                $result = $this->projectTaskModel->incrementRevision($taskId, $user['id'], $notes);

                // Also update task manager status
                $this->projectTaskModel->update($taskId, [
                    'task_manager_status' => $newStatus,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            } else {
                // Update task manager status
                $updateData = [
                    'task_manager_status' => $newStatus,
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                // Add payment fields if provided
                if ($paymentAmount) {
                    $updateData['payment_amount'] = $paymentAmount;
                    $updateData['payment_type'] = $paymentType;
                    $updateData['payment_status'] = $paymentStatus;
                    $updateData['payment_account'] = $paymentAccount;
                }

                // Add Google Drive link if provided
                if ($googleDriveLink) {
                    $updateData['google_drive_link'] = $googleDriveLink;
                }

                $result = $this->projectTaskModel->update($taskId, $updateData);
            }

            // Handle file uploads for sent_for_review status
            if ($result && $newStatus === 'sent_for_review') {
                $files = $this->request->getFiles();
                if (isset($files['status_file']) && is_array($files['status_file'])) {
                    // Multiple files
                    foreach ($files['status_file'] as $file) {
                        if ($file->isValid() && !$file->hasMoved()) {
                            $this->saveTaskFile($file, $taskId, $user['id'], $taskFileModel);
                        }
                    }
                } elseif (isset($files['status_file']) && $files['status_file']->isValid()) {
                    // Single file
                    $this->saveTaskFile($files['status_file'], $taskId, $user['id'], $taskFileModel);
                }
            }

            if ($result) {
                // Prepare enhanced notes with payment information
                $timelineNotes = $notes;
                if ($paymentAmount && $newStatus === 'sent_for_review') {
                    $timelineNotes = ($notes ? $notes . ' ' : '') . "| Payment: ₹" . number_format($paymentAmount, 2) . " (" . ucfirst($paymentStatus) . ")";
                    if ($paymentAccount) {
                        $timelineNotes .= " - Account: " . $paymentAccount;
                    }
                    if ($googleDriveLink) {
                        $timelineNotes .= " | Google Drive: " . $googleDriveLink;
                    }
                }

                // Record status change in timeline
                try {
                    $this->projectTimelineModel->addStatusChange(
                        $task['project_id'],
                        $user['id'],
                        $task['task_manager_status'] ?? 'not_reviewed',
                        $newStatus,
                        $timelineNotes,
                        null
                    );
                } catch (\Exception $e) {
                    log_message('error', 'Timeline update failed for task manager status: ' . $e->getMessage());
                }

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Task status updated successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update task status'
                ])->setStatusCode(500);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error updating task manager status: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating task status: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }



    /**
     * Generate QR code for client access
     */
    public function generateQR($projectId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        // Check if user can manage projects
        if (!$this->authLib->canManageProjects()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied. Manager privileges required.'
            ])->setStatusCode(403);
        }

        try {
            $project = $this->projectModel->find($projectId);
            if (!$project) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Project not found'
                ])->setStatusCode(404);
            }

            // Generate or get existing access token
            $accessToken = $project['client_access_token'];
            if (!$accessToken || strtotime($project['client_access_expires']) < time()) {
                // Generate new access token
                $accessToken = bin2hex(random_bytes(32));
                $expiresAt = date('Y-m-d H:i:s', strtotime('+30 days'));

                $this->projectModel->update($projectId, [
                    'client_access_token' => $accessToken,
                    'client_access_expires' => $expiresAt,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            // Generate QR code URL using a simple QR code service
            $clientUrl = base_url("client/project/{$accessToken}");
            $qrCodeUrl = "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" . urlencode($clientUrl);

            return $this->response->setJSON([
                'success' => true,
                'access_token' => $accessToken,
                'client_url' => $clientUrl,
                'qr_code_url' => $qrCodeUrl,
                'expires_at' => $project['client_access_expires'] ?? date('Y-m-d H:i:s', strtotime('+30 days'))
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error generating QR code: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error generating QR code: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }



    /**
     * Update manager status for completed projects
     */
    public function updateManagerStatus($projectId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        // Check if user can manage projects
        if (!in_array($user['roles'], ['admin', 'manager'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        $project = $this->projectModel->find($projectId);

        if (!$project) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Project not found'
            ])->setStatusCode(404);
        }

        // Allow status updates for projects with completed tasks or completed projects
        $hasCompletedTasks = $this->projectTaskModel->where('project_id', $projectId)
                                                   ->where('status', 'completed')
                                                   ->countAllResults() > 0;

        // Debug logging
        log_message('info', 'Manager status validation: ' . json_encode([
            'project_id' => $projectId,
            'project_status' => $project['status'],
            'has_completed_tasks' => $hasCompletedTasks,
            'validation_passed' => ($project['status'] === 'completed' || $hasCompletedTasks)
        ]));

        if ($project['status'] !== 'completed' && !$hasCompletedTasks) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Only projects with completed tasks can be reviewed'
            ])->setStatusCode(400);
        }

        $rules = [
            'status' => 'required|in_list[sent_for_review,revision_needed,client_accepted,rejected]',
            'notes' => 'max_length[1000]'
        ];

        $this->validation->setRules($rules);

        if (!$this->validation->run($this->request->getPost())) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validation->getErrors()
            ])->setStatusCode(400);
        }

        try {
            $newStatus = $this->request->getPost('status');
            $notes = $this->request->getPost('notes');
            $advanceAmount = $this->request->getPost('advance_amount');

            // Debug logging
            log_message('info', 'Manager status update request: ' . json_encode([
                'project_id' => $projectId,
                'new_status' => $newStatus,
                'notes' => $notes,
                'advance_amount' => $advanceAmount,
                'user_id' => $user['id'],
                'post_data' => $this->request->getPost()
            ]));

            // Handle file upload if present
            $filePath = null;
            $file = $this->request->getFile('status_file');

            if ($file && $file->isValid() && !$file->hasMoved()) {
                // Load upload library
                $uploadLib = new \App\Libraries\UploadLib();
                $uploadResult = $uploadLib->uploadFile($file, 'project_reviews');

                if ($uploadResult['success']) {
                    $filePath = $uploadResult['file_path'];
                } else {
                    log_message('error', 'File upload failed during manager status update: ' . json_encode($uploadResult['errors']));
                }
            }

            // Update manager status fields, NOT the main project status
            $updateData = [
                'manager_status' => $newStatus,
                'manager_notes' => $notes,
                'manager_reviewed_by' => $user['id'],
                'manager_reviewed_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Handle revision logic - only change main status if revision needed
            if ($newStatus === 'revision_needed') {
                $revisionCount = ($project['revision_count'] ?? 0) + 1;
                $updateData['revision_count'] = $revisionCount;
                $updateData['current_revision'] = $revisionCount;
                $updateData['revision_requested_at'] = date('Y-m-d H:i:s');
                $updateData['revision_requested_by'] = $user['id'];
                $updateData['revision_notes'] = $notes;

                // Change main status back to in_progress for assignee to work on
                $updateData['status'] = 'in_progress';
            }
            // For other statuses (sent_for_review, client_accepted, rejected),
            // keep the main status as 'completed' and only update manager_status

            // Log the update data being sent
            log_message('info', 'Update data for project ' . $projectId . ': ' . json_encode($updateData));

            $updateResult = $this->projectModel->update($projectId, $updateData);

            if ($updateResult) {
                // Record manager status change in timeline with payment details
                try {
                    // Prepare enhanced notes with payment information
                    $timelineNotes = $notes;
                    if ($advanceAmount && $newStatus === 'sent_for_review') {
                        $timelineNotes = ($notes ? $notes . ' ' : '') . "| Advance Payment: ₹" . number_format($advanceAmount, 2);
                    }

                    $this->projectTimelineModel->addStatusChange(
                        $projectId,
                        $user['id'],
                        $project['manager_status'] ?? 'not_reviewed',
                        $newStatus,
                        $timelineNotes,
                        $filePath
                    );
                } catch (\Exception $e) {
                    log_message('error', 'Timeline update failed for manager status: ' . $e->getMessage());
                    // Continue execution - timeline is not critical
                }

                // Create notification
                $this->createManagerStatusNotification($project, $newStatus, $notes, $user, $filePath);

                $message = $newStatus === 'revision_needed' ?
                    'Revision requested. Project is now active for updates.' :
                    'Manager review status updated successfully';

                log_message('info', 'Manager status update successful for project ' . $projectId);

                return $this->response->setJSON([
                    'success' => true,
                    'message' => $message
                ]);
            } else {
                // Get more specific error information
                $errors = $this->projectModel->errors();
                $errorMessage = 'Failed to update manager status';

                if (!empty($errors)) {
                    $errorMessage .= ': ' . implode(', ', $errors);
                }

                log_message('error', 'Manager status update failed for project ' . $projectId . ': ' . $errorMessage);

                return $this->response->setJSON([
                    'success' => false,
                    'message' => $errorMessage
                ])->setStatusCode(500);
            }

        } catch (\Exception $e) {
            log_message('error', 'Manager status update error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating project status: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Start revision for a project
     */
    public function startRevision($projectId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        $project = $this->projectModel->find($projectId);

        if (!$project) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Project not found'
            ])->setStatusCode(404);
        }

        // Check if user is assigned to this project
        if ($project['user_id'] !== $user['id']) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied. You are not assigned to this project.'
            ])->setStatusCode(403);
        }

        // Check if project has revision requested
        if (!$project['revision_count'] || $project['revision_count'] == 0) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No revision requested for this project'
            ])->setStatusCode(400);
        }

        try {
            $updateData = [
                'status' => 'in_progress',
                'status_start_time' => date('Y-m-d H:i:s'),
                'current_work_start' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            if ($this->projectModel->update($projectId, $updateData)) {
                // Create notification for manager
                $this->createRevisionStartNotification($project, $user);

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Revision started successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to start revision'
                ])->setStatusCode(500);
            }

        } catch (\Exception $e) {
            log_message('error', 'Revision start error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error starting revision: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Create revision start notification
     */
    private function createRevisionStartNotification($project, $user)
    {
        $userName = $user['username'] ?? ($user['first_name'] . ' ' . $user['last_name']) ?? 'Unknown User';
        $message = "Revision work started on project '{$project['project_name']}' by {$userName}.";

        // Notify the manager who requested the revision
        if ($project['revision_requested_by'] && $project['revision_requested_by'] !== $user['id']) {
            $this->notificationModel->createNotification(
                $project['revision_requested_by'],
                'Revision Started',
                $message,
                'info'
            );
        }
    }

    /**
     * Create manager status change notification
     */
    private function createManagerStatusNotification($project, $newStatus, $notes, $updatedBy, $filePath = null)
    {
        $statusText = ucwords(str_replace('_', ' ', $newStatus));
        $message = "Project '{$project['project_name']}' status changed to {$statusText} by manager.";

        if ($notes) {
            $message .= " Notes: {$notes}";
        }

        if ($filePath) {
            $message .= " (File attachment included)";
        }

        // Notify project assignee
        if ($project['assigned_to'] && $project['assigned_to'] !== $updatedBy['id']) {
            $this->notificationModel->createNotification(
                $project['assigned_to'],
                'Project Review Update',
                $message,
                $newStatus === 'revision_needed' ? 'warning' : 'info'
            );
        }
    }

    /**
     * Create status change notification
     */
    private function createStatusChangeNotification($project, $newStatus, $comment, $updatedBy)
    {
        $statusText = ucwords(str_replace('_', ' ', $newStatus));
        $message = "Project '{$project['project_name']}' status changed to {$statusText}. Comment: {$comment}";

        // Notify project creator
        if ($project['created_by'] !== $updatedBy['id']) {
            $this->notificationModel->createNotification(
                $project['created_by'],
                'Project Status Updated',
                $message,
                'info'
            );
        }

        // Notify all admins and managers
        $managers = $this->userModel->where('roles', 'admin')
                                  ->orWhere('roles', 'manager')
                                  ->where('is_active', 1)
                                  ->findAll();

        foreach ($managers as $manager) {
            if ($manager['id'] !== $updatedBy['id']) {
                $this->notificationModel->createNotification(
                    $manager['id'],
                    'Project Status Updated',
                    $message,
                    'info'
                );
            }
        }
    }

    /**
     * Add new task to project
     */
    public function addTask($projectId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        $project = $this->projectModel->find($projectId);

        if (!$project) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Project not found'
            ])->setStatusCode(404);
        }

        // Check if user has permission to edit this project
        $canEdit = $this->authLib->canManageProjects() || ($project['created_by'] == $user['id']);
        if (!$canEdit) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        // Validation rules
        $rules = [
            'task_type_id' => 'required|integer',
            'task_name' => 'required|min_length[3]|max_length[150]',
            'description' => 'permit_empty|max_length[500]',
            'assigned_to' => 'required|integer',
            'priority' => 'required|in_list[low,medium,high,urgent]',
            'target_days' => 'permit_empty|integer|greater_than[0]',
            'depends_on' => 'permit_empty|integer'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ])->setStatusCode(400);
        }

        try {
            // Get the highest task order for this project
            $maxOrder = $this->projectTaskModel->where('project_id', $projectId)
                                              ->selectMax('task_order')
                                              ->first();
            $nextOrder = ($maxOrder['task_order'] ?? 0) + 1;

            $taskData = [
                'project_id' => $projectId,
                'task_type_id' => $this->request->getPost('task_type_id'),
                'task_name' => $this->request->getPost('task_name'),
                'description' => $this->request->getPost('description'),
                'assigned_to' => $this->request->getPost('assigned_to'),
                'status' => 'not_started',
                'priority' => $this->request->getPost('priority'),
                'target_days' => $this->request->getPost('target_days'),
                'depends_on' => $this->request->getPost('depends_on'),
                'task_order' => $nextOrder,
                'created_by' => $user['id'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $taskId = $this->projectTaskModel->insert($taskData);

            if ($taskId) {
                // Get the created task with user details
                $task = $this->projectTaskModel->getTaskWithDetails($taskId);

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Task added successfully',
                    'task' => $task
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to add task',
                    'errors' => $this->projectTaskModel->errors()
                ])->setStatusCode(500);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error adding task: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error adding task: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Update existing task
     */
    public function updateTask($taskId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        $task = $this->projectTaskModel->find($taskId);

        if (!$task) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Task not found'
            ])->setStatusCode(404);
        }

        $project = $this->projectModel->find($task['project_id']);
        if (!$project) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Project not found'
            ])->setStatusCode(404);
        }

        // Check if user has permission to edit this project
        $canEdit = $this->authLib->canManageProjects() || ($project['created_by'] == $user['id']);
        if (!$canEdit) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        // Validation rules
        $rules = [
            'task_name' => 'required|min_length[3]|max_length[150]',
            'description' => 'permit_empty|max_length[500]',
            'assigned_to' => 'required|integer',
            'priority' => 'required|in_list[low,medium,high,urgent]',
            'target_days' => 'permit_empty|integer|greater_than[0]',
            'depends_on' => 'permit_empty|integer'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ])->setStatusCode(400);
        }

        try {
            $updateData = [
                'task_name' => $this->request->getPost('task_name'),
                'description' => $this->request->getPost('description'),
                'assigned_to' => $this->request->getPost('assigned_to'),
                'priority' => $this->request->getPost('priority'),
                'target_days' => $this->request->getPost('target_days'),
                'depends_on' => $this->request->getPost('depends_on'),
                'updated_by' => $user['id'],
                'updated_at' => date('Y-m-d H:i:s')
            ];

            if ($this->projectTaskModel->update($taskId, $updateData)) {
                // Get the updated task with user details
                $task = $this->projectTaskModel->getTaskWithDetails($taskId);

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Task updated successfully',
                    'task' => $task
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update task',
                    'errors' => $this->projectTaskModel->errors()
                ])->setStatusCode(500);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error updating task: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating task: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Delete task
     */
    public function deleteTask($taskId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        $task = $this->projectTaskModel->find($taskId);

        if (!$task) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Task not found'
            ])->setStatusCode(404);
        }

        $project = $this->projectModel->find($task['project_id']);
        if (!$project) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Project not found'
            ])->setStatusCode(404);
        }

        // Check if user has permission to edit this project
        $canEdit = $this->authLib->canManageProjects() || ($project['created_by'] == $user['id']);
        if (!$canEdit) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        // Check if task is in progress
        if ($task['status'] === 'in_progress') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Cannot delete task that is in progress'
            ])->setStatusCode(400);
        }

        try {
            if ($this->projectTaskModel->delete($taskId)) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Task deleted successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to delete task'
                ])->setStatusCode(500);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error deleting task: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error deleting task: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Get available task types
     */
    public function getTaskTypes()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        try {
            $taskTypes = $this->taskTypeModel->where('is_active', 1)
                                           ->orderBy('name', 'ASC')
                                           ->findAll();

            return $this->response->setJSON([
                'success' => true,
                'task_types' => $taskTypes
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error getting task types: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error getting task types: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Debug endpoint to check task data for a specific project
     */
    public function debugProjectTasks($projectId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        if ($user['roles'] !== 'admin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Admin access required'
            ])->setStatusCode(403);
        }

        try {
            // Get project info
            $project = $this->projectModel->find($projectId);

            // Get tasks from project_tasks table
            $projectTasks = $this->projectTaskModel->where('project_id', $projectId)->findAll();

            // Get tasks with joins
            $tasksWithJoins = $this->projectTaskModel->getProjectTasks($projectId);

            // Get projects data as used in main view
            $projectsData = $this->projectModel->getProjectsForUser($user['id'], $user['roles'], 'all');
            $projectData = array_filter($projectsData, function($p) use ($projectId) {
                return $p['id'] == $projectId;
            });

            return $this->response->setJSON([
                'success' => true,
                'debug_data' => [
                    'project_id' => $projectId,
                    'project_info' => $project,
                    'raw_project_tasks' => $projectTasks,
                    'tasks_with_joins' => $tasksWithJoins,
                    'projects_data_sample' => array_slice($projectData, 0, 5),
                    'total_projects_data_count' => count($projectData)
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Debug error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Debug error: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ])->setStatusCode(500);
        }
    }

    /**
     * Save uploaded file to task_files table
     */
    private function saveTaskFile($file, $taskId, $userId, $taskFileModel)
    {
        try {
            // Load upload library
            $uploadLib = new \App\Libraries\UploadLib();
            $uploadResult = $uploadLib->uploadFile($file, 'task_files');

            if ($uploadResult['success']) {
                // Save file metadata to task_files table
                $fileData = [
                    'task_id' => $taskId,
                    'filename' => $file->getClientName(),
                    'stored_filename' => $uploadResult['filename'],
                    'file_path' => $uploadResult['path'],
                    'file_size' => $uploadResult['size'],
                    'mime_type' => $uploadResult['type'],
                    'uploaded_by' => $userId,
                    'is_public' => 1, // Make files public for client access
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $fileId = $taskFileModel->insert($fileData);

                if ($fileId) {
                    log_message('info', "Task file saved successfully: {$file->getClientName()} for task {$taskId}");
                    return true;
                } else {
                    log_message('error', "Failed to save task file metadata for task {$taskId}");
                    // Clean up uploaded file if database insert fails
                    if (file_exists($uploadResult['path'])) {
                        unlink($uploadResult['path']);
                    }
                }
            } else {
                log_message('error', "File upload failed for task {$taskId}: " . json_encode($uploadResult['errors']));
            }
        } catch (\Exception $e) {
            log_message('error', "Error saving task file for task {$taskId}: " . $e->getMessage());
        }

        return false;
    }
}
