<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\AuthLibrary;
use App\Libraries\SecureUploadLibrary;
use App\Models\FileModel;
use CodeIgniter\HTTP\ResponseInterface;

class BlobStorage extends BaseController
{
    protected $authLib;
    protected $uploadLib;
    protected $fileModel;

    public function __construct()
    {
        $this->authLib = new AuthLibrary();
        $this->uploadLib = new SecureUploadLibrary();
        $this->fileModel = new FileModel();
    }

    /**
     * Upload image file
     */
    public function uploadImage()
    {
        // Check authentication
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        // Check CSRF token
        if (!$this->validate(['csrf_token' => 'required'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid CSRF token'
            ])->setStatusCode(403);
        }

        // Check rate limiting
        if (!$this->checkRateLimit()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Rate limit exceeded. Please try again later.'
            ])->setStatusCode(429);
        }

        $file = $this->request->getFile('image');
        
        if (!$file || !$file->isValid()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No valid file uploaded'
            ])->setStatusCode(400);
        }

        // Upload file securely
        $result = $this->uploadLib->uploadFile($file, 'images');

        if (!$result['success']) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Upload failed',
                'errors' => $result['errors']
            ])->setStatusCode(400);
        }

        // Save file metadata to database
        $fileData = [
            'filename' => $result['filename'],
            'original_name' => $file->getClientName(),
            'file_path' => $result['path'],
            'file_size' => $result['size'],
            'mime_type' => $result['type'],
            'uploaded_by' => $this->authLib->user()['id'],
            'file_type' => 'image',
            'is_public' => $this->request->getPost('is_public') ? 1 : 0,
            'metadata' => json_encode($result['processed_files'] ?? [])
        ];

        $fileId = $this->fileModel->insert($fileData);

        if (!$fileId) {
            // Clean up uploaded file if database insert fails
            unlink($result['path']);
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to save file metadata'
            ])->setStatusCode(500);
        }

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Image uploaded successfully',
            'data' => [
                'file_id' => $fileId,
                'filename' => $result['filename'],
                'url' => base_url('blob/image/' . $fileId),
                'thumbnails' => $this->generateThumbnailUrls($fileId, $result['processed_files'])
            ]
        ]);
    }

    /**
     * Serve image file securely
     */
    public function serveImage($fileId)
    {
        $file = $this->fileModel->find($fileId);

        if (!$file) {
            return $this->response->setStatusCode(404, 'File not found');
        }

        // Check access permissions
        if (!$this->checkFileAccess($file)) {
            return $this->response->setStatusCode(403, 'Access denied');
        }

        // Check if file exists on disk
        if (!file_exists($file['file_path'])) {
            return $this->response->setStatusCode(404, 'File not found on disk');
        }

        // Set security headers
        $this->setSecurityHeaders();

        // Serve file
        return $this->response->download($file['file_path'], null)
                             ->setContentType($file['mime_type']);
    }

    /**
     * Serve thumbnail image
     */
    public function serveThumbnail($fileId, $size = 'medium')
    {
        $file = $this->fileModel->find($fileId);

        if (!$file) {
            return $this->response->setStatusCode(404, 'File not found');
        }

        // Check access permissions
        if (!$this->checkFileAccess($file)) {
            return $this->response->setStatusCode(403, 'Access denied');
        }

        $metadata = json_decode($file['metadata'], true);
        $thumbnailPath = $metadata['thumbnails'][$size] ?? null;

        if (!$thumbnailPath || !file_exists($thumbnailPath)) {
            // Fallback to original image
            return $this->serveImage($fileId);
        }

        // Set security headers
        $this->setSecurityHeaders();

        // Serve thumbnail
        return $this->response->download($thumbnailPath, null)
                             ->setContentType($file['mime_type']);
    }

    /**
     * Delete image file
     */
    public function deleteImage($fileId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $file = $this->fileModel->find($fileId);

        if (!$file) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'File not found'
            ])->setStatusCode(404);
        }

        // Check if user owns the file or has admin permissions
        if ($file['uploaded_by'] !== $this->authLib->user()['id'] && 
            !$this->authLib->hasPermission('file.delete')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        // Delete file from disk
        if (file_exists($file['file_path'])) {
            unlink($file['file_path']);
        }

        // Delete thumbnails
        $metadata = json_decode($file['metadata'], true);
        if (isset($metadata['thumbnails'])) {
            foreach ($metadata['thumbnails'] as $thumbnailPath) {
                if (file_exists($thumbnailPath)) {
                    unlink($thumbnailPath);
                }
            }
        }

        // Delete from database
        $this->fileModel->delete($fileId);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'File deleted successfully'
        ]);
    }

    /**
     * List user's uploaded files
     */
    public function listFiles()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $userId = $this->authLib->user()['id'];
        $page = (int) $this->request->getGet('page') ?: 1;
        $limit = 20;

        $files = $this->fileModel->where('uploaded_by', $userId)
                                ->orderBy('created_at', 'DESC')
                                ->paginate($limit, 'default', $page);

        $formattedFiles = [];
        foreach ($files as $file) {
            $formattedFiles[] = [
                'id' => $file['id'],
                'filename' => $file['filename'],
                'original_name' => $file['original_name'],
                'size' => $file['file_size'],
                'type' => $file['mime_type'],
                'uploaded_at' => $file['created_at'],
                'url' => base_url('blob/image/' . $file['id']),
                'thumbnail_url' => base_url('blob/thumbnail/' . $file['id'] . '/small')
            ];
        }

        return $this->response->setJSON([
            'success' => true,
            'data' => $formattedFiles,
            'pagination' => $this->fileModel->pager->getDetails()
        ]);
    }

    /**
     * Check file access permissions
     */
    protected function checkFileAccess($file): bool
    {
        // Public files are accessible to everyone
        if ($file['is_public']) {
            return true;
        }

        // Check if user is logged in
        if (!$this->authLib->isLoggedIn()) {
            return false;
        }

        $user = $this->authLib->user();

        // File owner can access
        if ($file['uploaded_by'] === $user['id']) {
            return true;
        }

        // Admin can access all files
        if ($this->authLib->hasPermission('file.view_all')) {
            return true;
        }

        return false;
    }

    /**
     * Check rate limiting
     */
    protected function checkRateLimit(): bool
    {
        if (!$this->authLib->isLoggedIn()) {
            return false;
        }

        $userId = $this->authLib->user()['id'];
        $cacheKey = "upload_rate_limit_{$userId}";
        $cache = \Config\Services::cache();

        $attempts = $cache->get($cacheKey) ?: 0;
        $rateLimit = config('Upload')->rateLimit;

        if ($attempts >= $rateLimit) {
            return false;
        }

        $cache->save($cacheKey, $attempts + 1, 60); // 1 minute window
        return true;
    }

    /**
     * Set security headers for file serving
     */
    protected function setSecurityHeaders(): void
    {
        $headers = config('Upload')->cspHeaders;
        
        foreach ($headers as $header => $value) {
            $this->response->setHeader($header, $value);
        }
    }

    /**
     * Generate thumbnail URLs
     */
    protected function generateThumbnailUrls($fileId, $processedFiles): array
    {
        $urls = [];

        if (isset($processedFiles['thumbnails'])) {
            foreach (array_keys($processedFiles['thumbnails']) as $size) {
                $urls[$size] = base_url('blob/thumbnail/' . $fileId . '/' . $size);
            }
        }

        return $urls;
    }

    /**
     * File manager interface
     */
    public function fileManager()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        $data = [
            'title' => 'File Manager - SmartFlo',
            'user' => $this->authLib->user()
        ];

        return view('blob/file_manager', $data);
    }

    /**
     * Get file statistics
     */
    public function getStats()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $userId = $this->authLib->user()['id'];
        $isAdmin = $this->authLib->hasPermission('file.view_all');

        // Get user stats or global stats for admin
        $stats = $this->fileModel->getFileStats($isAdmin ? null : $userId);
        $typeStats = $this->fileModel->getFileTypeStats($isAdmin ? null : $userId);
        $recentFiles = $this->fileModel->getRecentFiles(5, $isAdmin ? null : $userId);

        return $this->response->setJSON([
            'success' => true,
            'data' => [
                'general' => $stats,
                'by_type' => $typeStats,
                'recent_files' => $recentFiles
            ]
        ]);
    }
}
