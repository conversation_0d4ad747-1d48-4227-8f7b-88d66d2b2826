<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\AuthLibrary;
use App\Models\DashboardControlModel;

class DashboardControls extends BaseController
{
    protected $authLib;
    protected $controlModel;

    public function __construct()
    {
        $this->authLib = new AuthLibrary();
        $this->controlModel = new DashboardControlModel();
    }

    /**
     * Dashboard controls management page
     */
    public function index()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        // Check if user has permission to manage dashboard controls
        if (!$this->authLib->hasPermission('dashboard.manage')) {
            return redirect()->to('/dashboard')->with('error', 'Insufficient permissions.');
        }

        $user = $this->authLib->user();

        $data = [
            'title' => 'Dashboard Controls',
            'user' => $user,
            'page' => 'dashboard-controls'
        ];

        return view('dashboard_controls/index', $data);
    }

    /**
     * Get dashboard controls
     */
    public function getControls()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        $controls = $this->controlModel->getControlsForUser($user['id']);

        return $this->response->setJSON([
            'success' => true,
            'controls' => $controls
        ]);
    }

    /**
     * Get all available controls (for admin)
     */
    public function getAllControls()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        // Check admin permission
        if (!$this->authLib->hasPermission('dashboard.manage')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Insufficient permissions'
            ])->setStatusCode(403);
        }

        $controls = $this->controlModel->getAllControls();

        return $this->response->setJSON([
            'success' => true,
            'controls' => $controls
        ]);
    }

    /**
     * Create new dashboard control
     */
    public function create()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        // Check permission
        if (!$this->authLib->hasPermission('dashboard.manage')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Insufficient permissions'
            ])->setStatusCode(403);
        }

        // Validate CSRF
        if (!$this->validate(['csrf_token' => 'required'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid security token'
            ])->setStatusCode(403);
        }

        $rules = [
            'title' => 'required|max_length[100]',
            'description' => 'max_length[255]',
            'icon' => 'required|max_length[50]',
            'action_type' => 'required|in_list[url,function,modal]',
            'action_value' => 'required|max_length[255]',
            'color' => 'required|max_length[20]',
            'roles' => 'required'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ])->setStatusCode(400);
        }

        $user = $this->authLib->user();
        $controlData = [
            'title' => $this->request->getPost('title'),
            'description' => $this->request->getPost('description'),
            'icon' => $this->request->getPost('icon'),
            'action_type' => $this->request->getPost('action_type'),
            'action_value' => $this->request->getPost('action_value'),
            'color' => $this->request->getPost('color'),
            'roles' => $this->request->getPost('roles'),
            'is_active' => $this->request->getPost('is_active') ? 1 : 0,
            'sort_order' => $this->request->getPost('sort_order') ?: 0,
            'created_by' => $user['id']
        ];

        $controlId = $this->controlModel->insert($controlData);

        if ($controlId) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Dashboard control created successfully',
                'control_id' => $controlId
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to create dashboard control'
            ])->setStatusCode(500);
        }
    }

    /**
     * Update dashboard control
     */
    public function update($controlId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        // Check permission
        if (!$this->authLib->hasPermission('dashboard.manage')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Insufficient permissions'
            ])->setStatusCode(403);
        }

        // Validate CSRF
        if (!$this->validate(['csrf_token' => 'required'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid security token'
            ])->setStatusCode(403);
        }

        $control = $this->controlModel->find($controlId);
        if (!$control) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Control not found'
            ])->setStatusCode(404);
        }

        $rules = [
            'title' => 'required|max_length[100]',
            'description' => 'max_length[255]',
            'icon' => 'required|max_length[50]',
            'action_type' => 'required|in_list[url,function,modal]',
            'action_value' => 'required|max_length[255]',
            'color' => 'required|max_length[20]',
            'roles' => 'required'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ])->setStatusCode(400);
        }

        $updateData = [
            'title' => $this->request->getPost('title'),
            'description' => $this->request->getPost('description'),
            'icon' => $this->request->getPost('icon'),
            'action_type' => $this->request->getPost('action_type'),
            'action_value' => $this->request->getPost('action_value'),
            'color' => $this->request->getPost('color'),
            'roles' => $this->request->getPost('roles'),
            'is_active' => $this->request->getPost('is_active') ? 1 : 0,
            'sort_order' => $this->request->getPost('sort_order') ?: 0
        ];

        if ($this->controlModel->update($controlId, $updateData)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Dashboard control updated successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update dashboard control'
            ])->setStatusCode(500);
        }
    }

    /**
     * Delete dashboard control
     */
    public function delete($controlId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        // Check permission
        if (!$this->authLib->hasPermission('dashboard.manage')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Insufficient permissions'
            ])->setStatusCode(403);
        }

        $control = $this->controlModel->find($controlId);
        if (!$control) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Control not found'
            ])->setStatusCode(404);
        }

        if ($this->controlModel->delete($controlId)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Dashboard control deleted successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to delete dashboard control'
            ])->setStatusCode(500);
        }
    }

    /**
     * Toggle control active status
     */
    public function toggleActive($controlId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        // Check permission
        if (!$this->authLib->hasPermission('dashboard.manage')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Insufficient permissions'
            ])->setStatusCode(403);
        }

        $control = $this->controlModel->find($controlId);
        if (!$control) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Control not found'
            ])->setStatusCode(404);
        }

        $newStatus = $control['is_active'] ? 0 : 1;
        
        if ($this->controlModel->update($controlId, ['is_active' => $newStatus])) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Control status updated successfully',
                'is_active' => $newStatus
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update control status'
            ])->setStatusCode(500);
        }
    }

    /**
     * Update control order
     */
    public function updateOrder()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        // Check permission
        if (!$this->authLib->hasPermission('dashboard.manage')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Insufficient permissions'
            ])->setStatusCode(403);
        }

        $controlIds = $this->request->getPost('control_ids');
        if (!is_array($controlIds)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid control IDs'
            ])->setStatusCode(400);
        }

        $success = true;
        foreach ($controlIds as $index => $controlId) {
            if (!$this->controlModel->update($controlId, ['sort_order' => $index + 1])) {
                $success = false;
            }
        }

        if ($success) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Control order updated successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update control order'
            ])->setStatusCode(500);
        }
    }
}
