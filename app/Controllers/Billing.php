<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Libraries\AuthLibrary;
use App\Models\ProjectTaskModel;
use App\Models\ProjectModel;

class Billing extends Controller
{
    protected $authLib;
    protected $projectTaskModel;
    protected $projectModel;
    protected $projectTimelineModel;

    public function __construct()
    {
        $this->authLib = new AuthLibrary();
        $this->projectTaskModel = new ProjectTaskModel();
        $this->projectModel = new ProjectModel();
        $this->projectTimelineModel = new \App\Models\ProjectTimelineModel();
    }

    /**
     * Billing dashboard
     */
    public function index()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        $user = $this->authLib->user();

        // Check if user has permission to access billing
        if (!in_array($user['roles'], ['admin', 'manager'])) {
            return redirect()->to('/projects')->with('error', 'Access denied');
        }

        $data = [
            'title' => 'Billing Management - SmartFlo',
            'user' => $user,
            'page' => 'billing'
        ];

        return view('billing/index', $data);
    }

    /**
     * Get billing data for dashboard
     */
    public function getBillingData()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        if (!in_array($user['roles'], ['admin', 'manager'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        try {
            // Get payment statistics
            $totalPaid = $this->projectTaskModel
                ->selectSum('payment_amount')
                ->where('payment_status', 'paid')
                ->first()['payment_amount'] ?? 0;

            $totalUnpaid = $this->projectTaskModel
                ->selectSum('payment_amount')
                ->where('payment_status', 'unpaid')
                ->where('payment_amount >', 0)
                ->first()['payment_amount'] ?? 0;

            $totalPartial = $this->projectTaskModel
                ->selectSum('payment_amount')
                ->where('payment_status', 'partial')
                ->first()['payment_amount'] ?? 0;

            // Get recent payments
            $recentPayments = $this->projectTaskModel
                ->select('
                    project_tasks.id,
                    project_tasks.task_name,
                    project_tasks.payment_amount,
                    project_tasks.payment_status,
                    project_tasks.payment_account,
                    project_tasks.updated_at,
                    projects.project_name,
                    projects.project_id as project_number
                ')
                ->join('projects', 'projects.id = project_tasks.project_id')
                ->where('project_tasks.payment_amount >', 0)
                ->orderBy('project_tasks.updated_at', 'DESC')
                ->limit(10)
                ->findAll();

            // Get pending payments
            $pendingPayments = $this->projectTaskModel
                ->select('
                    project_tasks.id,
                    project_tasks.task_name,
                    project_tasks.payment_amount,
                    project_tasks.payment_status,
                    project_tasks.task_manager_status,
                    projects.project_name,
                    projects.project_id as project_number,
                    users.username as assigned_username
                ')
                ->join('projects', 'projects.id = project_tasks.project_id')
                ->join('users', 'users.id = project_tasks.assigned_to', 'left')
                ->where('project_tasks.task_manager_status', 'sent_for_review')
                ->where('project_tasks.payment_status !=', 'paid')
                ->orderBy('project_tasks.updated_at', 'DESC')
                ->findAll();

            return $this->response->setJSON([
                'success' => true,
                'data' => [
                    'summary' => [
                        'total_paid' => $totalPaid,
                        'total_unpaid' => $totalUnpaid,
                        'total_partial' => $totalPartial,
                        'total_revenue' => $totalPaid + $totalPartial
                    ],
                    'recent_payments' => $recentPayments,
                    'pending_payments' => $pendingPayments
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error getting billing data: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error loading billing data'
            ])->setStatusCode(500);
        }
    }

    /**
     * Update payment status
     */
    public function updatePaymentStatus($taskId)
    {
        log_message('info', "Billing::updatePaymentStatus called for task ID: {$taskId}");

        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        if (!in_array($user['roles'], ['admin', 'manager'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        try {
            $paymentStatus = $this->request->getPost('payment_status');
            $paymentAccount = $this->request->getPost('payment_account');
            $notes = $this->request->getPost('notes');

            $validStatuses = ['unpaid', 'paid', 'partial'];
            if (!in_array($paymentStatus, $validStatuses)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid payment status'
                ]);
            }

            $updateData = [
                'payment_status' => $paymentStatus,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            if ($paymentAccount) {
                $updateData['payment_account'] = $paymentAccount;
            }

            $result = $this->projectTaskModel->update($taskId, $updateData);

            if ($result) {
                // Get task details for timeline
                $task = $this->projectTaskModel->find($taskId);

                if ($task) {
                    // Add timeline entry for payment status change
                    try {
                        $timelineNotes = "Payment status changed to: " . ucfirst($paymentStatus);
                        if ($paymentAccount) {
                            $timelineNotes .= " | Account: " . $paymentAccount;
                        }
                        if ($notes) {
                            $timelineNotes .= " | Notes: " . $notes;
                        }

                        $this->projectTimelineModel->insert([
                            'project_id' => $task['project_id'],
                            'user_id' => $user['id'],
                            'action_type' => 'payment_status_change',
                            'title' => 'Payment status updated',
                            'description' => $timelineNotes,
                            'notes' => $notes,
                            'metadata' => json_encode([
                                'task_id' => $taskId,
                                'task_name' => $task['task_name'],
                                'old_payment_status' => $task['payment_status'] ?? 'unpaid',
                                'new_payment_status' => $paymentStatus,
                                'payment_account' => $paymentAccount,
                                'timestamp' => date('Y-m-d H:i:s')
                            ])
                        ]);

                        // Also update existing "sent_for_review" timeline entries to reflect new payment status
                        $this->updateExistingTimelinePaymentStatus($task['project_id'], $taskId, $paymentStatus);

                    } catch (\Exception $e) {
                        log_message('error', 'Timeline update failed for payment status: ' . $e->getMessage());
                        // Continue execution - timeline is not critical
                    }
                }

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Payment status updated successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update payment status'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error updating payment status: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating payment status'
            ])->setStatusCode(500);
        }
    }

    /**
     * Get payment accounts (for settings)
     */
    public function getPaymentAccounts()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        // For now, return hardcoded accounts
        // In production, these would be stored in database
        $accounts = [
            ['id' => 'bank_account_1', 'name' => 'Main Bank Account', 'type' => 'bank'],
            ['id' => 'bank_account_2', 'name' => 'Secondary Account', 'type' => 'bank'],
            ['id' => 'cash', 'name' => 'Cash', 'type' => 'cash'],
            ['id' => 'online', 'name' => 'Online Payment', 'type' => 'online']
        ];

        return $this->response->setJSON([
            'success' => true,
            'accounts' => $accounts
        ]);
    }

    /**
     * Update existing timeline entries to reflect new payment status
     */
    private function updateExistingTimelinePaymentStatus($projectId, $taskId, $newPaymentStatus)
    {
        try {
            // Find timeline entries related to this task's "sent_for_review" status
            $timelineEntries = $this->projectTimelineModel
                ->where('project_id', $projectId)
                ->where('action_type', 'status_change')
                ->where('new_status', 'sent_for_review')
                ->like('description', 'Payment:', 'after')
                ->findAll();

            foreach ($timelineEntries as $entry) {
                $metadata = json_decode($entry['metadata'], true);

                // Check if this timeline entry is for the specific task
                if (isset($metadata['task_id']) && $metadata['task_id'] == $taskId) {
                    // Update the description to reflect new payment status
                    $description = $entry['description'];

                    // Replace old payment status with new one
                    $description = preg_replace('/\(Pending\)/', '(' . ucfirst($newPaymentStatus) . ')', $description);
                    $description = preg_replace('/\(Unpaid\)/', '(' . ucfirst($newPaymentStatus) . ')', $description);
                    $description = preg_replace('/\(Paid\)/', '(' . ucfirst($newPaymentStatus) . ')', $description);
                    $description = preg_replace('/\(Partial\)/', '(' . ucfirst($newPaymentStatus) . ')', $description);

                    // Update the timeline entry
                    $this->projectTimelineModel->update($entry['id'], [
                        'description' => $description,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                }
            }
        } catch (\Exception $e) {
            log_message('error', 'Failed to update existing timeline payment status: ' . $e->getMessage());
        }
    }
}
