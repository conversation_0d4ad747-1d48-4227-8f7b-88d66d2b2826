<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\AuthLibrary;
use App\Libraries\SecurityLibrary;
use App\Models\NotificationModel;

class Notifications extends BaseController
{
    protected $authLib;
    protected $securityLib;
    protected $notificationModel;

    public function __construct()
    {
        $this->authLib = new AuthLibrary();
        $this->securityLib = new SecurityLibrary();
        $this->notificationModel = new NotificationModel();
    }

    /**
     * Notifications page
     */
    public function index()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        $user = $this->authLib->user();

        $data = [
            'title' => 'Notifications - SmartFlo',
            'user' => $user,
            'page' => 'notifications'
        ];

        return view('notifications/index', $data);
    }

    /**
     * Get user notifications (API endpoint)
     */
    public function getNotifications()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'You must be logged in to view notifications.'
            ]);
        }

        $user = $this->authLib->user();
        $filter = $this->request->getGet('filter') ?: 'all';
        $page = (int) $this->request->getGet('page') ?: 1;
        $limit = 20;

        try {
            // Get notifications from database
            $notifications = $this->notificationModel->getNotificationsByUser($user['id'], $filter, $limit, ($page - 1) * $limit);

            // Add time_ago and icon to each notification
            foreach ($notifications as &$notification) {
                $notification['time_ago'] = $this->timeAgo($notification['created_at']);
                $notification['icon'] = $this->getNotificationIcon($notification['type']);
            }

            // Get counts for filters
            $counts = [
                'all' => $this->notificationModel->countNotificationsByUser($user['id'], 'all'),
                'unread' => $this->notificationModel->countNotificationsByUser($user['id'], 'unread'),
                'info' => $this->notificationModel->countNotificationsByUser($user['id'], 'info'),
                'success' => $this->notificationModel->countNotificationsByUser($user['id'], 'success'),
                'warning' => $this->notificationModel->countNotificationsByUser($user['id'], 'warning'),
                'error' => $this->notificationModel->countNotificationsByUser($user['id'], 'error')
            ];

            return $this->response->setJSON([
                'success' => true,
                'notifications' => $notifications,
                'counts' => $counts,
                'unread_count' => $counts['unread']
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error loading notifications: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * View specific notification
     */
    public function view($notificationId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        $user = $this->authLib->user();

        try {
            $notification = $this->notificationModel->find($notificationId);

            if (!$notification) {
                return redirect()->to('/notifications')->with('error', 'Notification not found.');
            }

            // Check if user has access to this notification
            if ($notification['user_id'] !== $user['id']) {
                return redirect()->to('/notifications')->with('error', 'Access denied.');
            }

            // Mark as read if not already read
            if (!$notification['is_read']) {
                $this->notificationModel->update($notificationId, ['is_read' => 1]);
                $notification['is_read'] = 1; // Update local copy
            }

            $data = [
                'title' => htmlspecialchars($notification['title']) . ' - SmartFlo',
                'user' => $user,
                'notification' => $notification,
                'page' => 'view-notification'
            ];

            return view('notifications/view', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error viewing notification: ' . $e->getMessage());
            return redirect()->to('/notifications')->with('error', 'Error loading notification.');
        }
    }

    /**
     * Mark notification as read
     */
    public function markAsRead($notificationId = null)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'You must be logged in.'
            ]);
        }

        $user = $this->authLib->user();
        $userId = $user['id'];

        if ($notificationId) {
            // Mark specific notification as read
            $notification = $this->notificationModel->find($notificationId);
            
            if (!$notification || $notification['user_id'] != $userId) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Notification not found.'
                ]);
            }

            $this->notificationModel->update($notificationId, ['is_read' => 1]);
        } else {
            // Mark all notifications as read
            $this->notificationModel->markAllAsRead($userId);
        }

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Notification(s) marked as read.',
            'unread_count' => $this->notificationModel->getUnreadCount($userId)
        ]);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'You must be logged in.'
            ])->setStatusCode(401);
        }

        $userId = $this->authLib->user()['id'];

        try {
            $this->notificationModel->markAllAsRead($userId);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'All notifications marked as read.',
                'unread_count' => 0
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error marking notifications as read: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Delete notification
     */
    public function delete($notificationId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'You must be logged in.'
            ]);
        }

        $user = $this->authLib->user();
        $userId = $user['id'];

        $notification = $this->notificationModel->find($notificationId);
        
        if (!$notification || $notification['user_id'] != $userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Notification not found.'
            ]);
        }

        if ($this->notificationModel->delete($notificationId)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Notification deleted.',
                'unread_count' => $this->notificationModel->getUnreadCount($userId)
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to delete notification.'
        ]);
    }

    /**
     * Create a new notification
     */
    public function create()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied.'
            ]);
        }

        $rules = [
            'recipient' => 'required',
            'title' => 'required|max_length[255]',
            'message' => 'required|max_length[1000]',
            'type' => 'required|in_list[info,success,warning,error]'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Please check your input.',
                'errors' => $this->validator->getErrors()
            ]);
        }

        $recipient = $this->request->getPost('recipient');
        $title = $this->securityLib->sanitizeInput($this->request->getPost('title'));
        $message = $this->securityLib->sanitizeInput($this->request->getPost('message'));
        $type = $this->request->getPost('type');

        try {
            $notificationsSent = 0;

            if ($recipient === 'all') {
                // Send to all users
                $userModel = new \App\Models\UserModel();
                $allUsers = $userModel->findAll();

                foreach ($allUsers as $user) {
                    $data = [
                        'user_id' => $user['id'],
                        'title' => $title,
                        'message' => $message,
                        'type' => $type,
                        'is_read' => 0
                    ];

                    if ($this->notificationModel->insert($data)) {
                        $notificationsSent++;
                    }
                }

            } elseif ($recipient === 'admins') {
                // Send to admin users only
                $db = \Config\Database::connect();
                $adminUsers = $db->table('user_roles_view')
                                ->where('roles', 'admin')
                                ->get()
                                ->getResultArray();

                foreach ($adminUsers as $user) {
                    $data = [
                        'user_id' => $user['id'],
                        'title' => $title,
                        'message' => $message,
                        'type' => $type,
                        'is_read' => 0
                    ];

                    if ($this->notificationModel->insert($data)) {
                        $notificationsSent++;
                    }
                }

            } else {
                // Send to specific user (if recipient is a user ID)
                $data = [
                    'user_id' => (int) $recipient,
                    'title' => $title,
                    'message' => $message,
                    'type' => $type,
                    'is_read' => 0
                ];

                if ($this->notificationModel->insert($data)) {
                    $notificationsSent = 1;
                }
            }

            if ($notificationsSent > 0) {
                $responseMessage = $notificationsSent === 1 ?
                    'Notification sent successfully.' :
                    "Notification sent to {$notificationsSent} users.";

                return $this->response->setJSON([
                    'success' => true,
                    'message' => $responseMessage
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to send notification.'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Notification sending error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error sending notification: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get notification icon based on type
     */
    private function getNotificationIcon($type)
    {
        $icons = [
            'info' => 'fas fa-info-circle',
            'success' => 'fas fa-check-circle',
            'warning' => 'fas fa-exclamation-triangle',
            'error' => 'fas fa-times-circle',
            'user' => 'fas fa-user',
            'system' => 'fas fa-cog',
            'security' => 'fas fa-shield-alt'
        ];

        return $icons[$type] ?? 'fas fa-bell';
    }

    /**
     * Calculate time ago
     */
    private function timeAgo($datetime)
    {
        $time = time() - strtotime($datetime);

        if ($time < 60) {
            return 'Just now';
        } elseif ($time < 3600) {
            $minutes = floor($time / 60);
            return $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
        } elseif ($time < 86400) {
            $hours = floor($time / 3600);
            return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
        } elseif ($time < 2592000) {
            $days = floor($time / 86400);
            return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
        } else {
            return date('M j, Y', strtotime($datetime));
        }
    }
}
