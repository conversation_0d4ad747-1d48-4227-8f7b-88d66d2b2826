<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\AuthLibrary;
use App\Models\MessageModel;

class Messages extends BaseController
{
    protected $authLib;
    protected $messageModel;
    protected $validation;

    public function __construct()
    {
        $this->authLib = new AuthLibrary();
        $this->messageModel = new MessageModel();
        $this->validation = \Config\Services::validation();
    }

    /**
     * Messages inbox
     */
    public function index()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        $user = $this->authLib->user();

        $data = [
            'title' => 'Messages - SmartFlo',
            'user' => $user,
            'page' => 'messages'
        ];

        return view('messages/index', $data);
    }

    /**
     * Compose new message
     */
    public function compose()
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        $user = $this->authLib->user();

        $data = [
            'title' => 'Compose Message - SmartFlo',
            'user' => $user,
            'page' => 'compose-message'
        ];

        return view('messages/compose', $data);
    }

    /**
     * View specific message
     */
    public function view($messageId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return redirect()->to('/auth/login')->with('error', 'Please login to continue.');
        }

        $user = $this->authLib->user();

        try {
            // Get message with sender information
            $message = $this->messageModel->select('messages.*, users.username as sender_username')
                                         ->join('users', 'users.id = messages.sender_id', 'left')
                                         ->where('messages.id', $messageId)
                                         ->first();

            if (!$message) {
                return redirect()->to('/messages')->with('error', 'Message not found.');
            }

            // Check if user has access to this message
            if ($message['recipient_id'] !== $user['id'] && $message['sender_id'] !== $user['id']) {
                return redirect()->to('/messages')->with('error', 'Access denied.');
            }

            // Mark as read if user is recipient and message is unread
            if ($message['recipient_id'] === $user['id'] && !$message['is_read']) {
                $this->messageModel->update($messageId, ['is_read' => 1, 'read_at' => date('Y-m-d H:i:s')]);
                $message['is_read'] = 1; // Update local copy
            }

            // Get attachments if any (placeholder for future implementation)
            $message['attachments'] = [];

            $data = [
                'title' => htmlspecialchars($message['subject']) . ' - SmartFlo',
                'user' => $user,
                'message' => $message,
                'page' => 'view-message'
            ];

            return view('messages/view', $data);

        } catch (\Exception $e) {
            log_message('error', 'Error viewing message: ' . $e->getMessage());
            return redirect()->to('/messages')->with('error', 'Error loading message.');
        }
    }

    /**
     * Get messages for inbox
     */
    public function getMessages()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        $type = $this->request->getGet('type') ?: 'inbox';
        $page = (int) $this->request->getGet('page') ?: 1;
        $limit = 20;

        $messages = $this->messageModel->getMessagesByUser($user['id'], $type, $limit, ($page - 1) * $limit);
        $totalMessages = $this->messageModel->countMessagesByUser($user['id'], $type);

        return $this->response->setJSON([
            'success' => true,
            'messages' => $messages,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($totalMessages / $limit),
                'total_messages' => $totalMessages
            ]
        ]);
    }

    /**
     * Send new message
     */
    public function send()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        // Get form data
        $messageType = $this->request->getPost('message_type') ?: 'individual';
        $recipientId = $this->request->getPost('recipient_id');
        $recipientRole = $this->request->getPost('recipient_role');
        $subject = $this->request->getPost('subject');
        $message = $this->request->getPost('message');
        $priority = $this->request->getPost('priority') ?: 'normal';

        // Validation rules
        $rules = [
            'subject' => 'required|min_length[3]|max_length[255]',
            'message' => 'required|min_length[5]'
        ];

        // Add recipient validation based on message type
        if ($messageType === 'individual') {
            $rules['recipient_id'] = 'required|integer';
        } elseif ($messageType === 'role') {
            $rules['recipient_role'] = 'required|in_list[admin,manager,supervisor,staff,user]';
        }

        // Skip CSRF validation for now to debug
        $this->validation->setRules($rules);

        if (!$this->validation->run($this->request->getPost())) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validation->getErrors()
            ])->setStatusCode(400);
        }

        try {
            $messagesSent = 0;
            $attachmentIds = [];

            // Handle file attachments
            $files = $this->request->getFiles();
            if (isset($files['attachments'])) {
                foreach ($files['attachments'] as $file) {
                    if ($file->isValid() && !$file->hasMoved()) {
                        $validation = $this->validateFile($file);
                        if (!$validation['valid']) {
                            return $this->response->setJSON([
                                'success' => false,
                                'message' => $validation['message']
                            ])->setStatusCode(400);
                        }

                        $attachmentId = $this->saveAttachment($file, $user['id']);
                        if ($attachmentId) {
                            $attachmentIds[] = $attachmentId;
                        }
                    }
                }
            }

            if ($messageType === 'broadcast') {
                // Send to all users
                $userModel = new \App\Models\UserModel();
                $allUsers = $userModel->findAll();

                foreach ($allUsers as $recipient) {
                    if ($recipient['id'] != $user['id']) { // Don't send to self
                        $messageData = [
                            'sender_id' => $user['id'],
                            'recipient_id' => $recipient['id'],
                            'subject' => $subject,
                            'message' => $message,
                            'priority' => $priority,
                            'is_broadcast' => 1
                        ];

                        $messageId = $this->messageModel->insert($messageData);
                        if ($messageId) {
                            $messagesSent++;
                            $this->linkAttachmentsToMessage($messageId, $attachmentIds);
                        }
                    }
                }

            } elseif ($messageType === 'role') {
                // Send to users with specific role
                $userModel = new \App\Models\UserModel();
                $roleUsers = $userModel->where('roles', $recipientRole)->findAll();

                foreach ($roleUsers as $recipient) {
                    if ($recipient['id'] != $user['id']) { // Don't send to self
                        $messageData = [
                            'sender_id' => $user['id'],
                            'recipient_id' => $recipient['id'],
                            'subject' => $subject,
                            'message' => $message,
                            'priority' => $priority,
                            'is_broadcast' => 0
                        ];

                        if ($this->messageModel->insert($messageData)) {
                            $messagesSent++;
                        }
                    }
                }

            } else {
                // Individual message
                $messageData = [
                    'sender_id' => $user['id'],
                    'recipient_id' => (int) $recipientId,
                    'subject' => $subject,
                    'message' => $message,
                    'priority' => $priority,
                    'is_broadcast' => 0
                ];

                $messageId = $this->messageModel->insert($messageData);
                if ($messageId) {
                    $messagesSent = 1;
                    $this->linkAttachmentsToMessage($messageId, $attachmentIds);
                }
            }

            if ($messagesSent > 0) {
                $responseMessage = $messagesSent === 1 ?
                    'Message sent successfully' :
                    "Message sent to {$messagesSent} recipients";

                return $this->response->setJSON([
                    'success' => true,
                    'message' => $responseMessage,
                    'messages_sent' => $messagesSent
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to send message'
                ])->setStatusCode(500);
            }

        } catch (\Exception $e) {
            log_message('error', 'Message sending error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error sending message: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Delete message
     */
    public function delete($messageId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        $message = $this->messageModel->find($messageId);

        if (!$message) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Message not found'
            ])->setStatusCode(404);
        }

        // Check if user has access to delete this message
        if ($message['recipient_id'] !== $user['id'] && $message['sender_id'] !== $user['id']) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ])->setStatusCode(403);
        }

        // Soft delete
        if ($this->messageModel->delete($messageId)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Message deleted successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to delete message'
            ])->setStatusCode(500);
        }
    }

    /**
     * Mark message as read/unread
     */
    public function markAsRead($messageId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        $message = $this->messageModel->find($messageId);

        if (!$message || $message['recipient_id'] !== $user['id']) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Message not found or access denied'
            ])->setStatusCode(404);
        }

        $isRead = $this->request->getPost('is_read') ? 1 : 0;
        $updateData = ['is_read' => $isRead];

        if ($isRead) {
            $updateData['read_at'] = date('Y-m-d H:i:s');
        }

        if ($this->messageModel->update($messageId, $updateData)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => $isRead ? 'Message marked as read' : 'Message marked as unread'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update message status'
            ])->setStatusCode(500);
        }
    }

    /**
     * Get message statistics
     */
    public function getStats()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        $stats = $this->messageModel->getMessageStats($user['id']);

        return $this->response->setJSON([
            'success' => true,
            'stats' => $stats
        ]);
    }

    /**
     * Get users for recipient dropdown
     */
    public function getUsers()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        try {
            $db = \Config\Database::connect();
            $users = $db->table('user_roles_view')
                       ->select('id, username, email, roles')
                       ->where('is_active', 1)
                       ->orderBy('username', 'ASC')
                       ->get()
                       ->getResultArray();

            return $this->response->setJSON([
                'success' => true,
                'users' => $users
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error loading users: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Get specific message for reply/view
     */
    public function getMessage($messageId)
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        try {
            $message = $this->messageModel->select('messages.*,
                                                   sender.username as sender_username,
                                                   sender.first_name as sender_first_name,
                                                   sender.last_name as sender_last_name')
                                         ->join('users as sender', 'sender.id = messages.sender_id', 'left')
                                         ->find($messageId);

            if (!$message) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Message not found'
                ])->setStatusCode(404);
            }

            // Check if user has access to this message
            if ($message['recipient_id'] !== $user['id'] && $message['sender_id'] !== $user['id']) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Access denied'
                ])->setStatusCode(403);
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => $message
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error loading message: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Validate uploaded file
     */
    private function validateFile($file)
    {
        $maxSize = 10 * 1024 * 1024; // 10MB
        $allowedTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png', 'txt'];

        // Check file size
        if ($file->getSize() > $maxSize) {
            return [
                'valid' => false,
                'message' => 'File size exceeds 10MB limit'
            ];
        }

        // Check file type
        $extension = strtolower($file->getClientExtension());
        if (!in_array($extension, $allowedTypes)) {
            return [
                'valid' => false,
                'message' => 'File type not allowed. Allowed types: ' . implode(', ', $allowedTypes)
            ];
        }

        // Basic virus scan (check for suspicious patterns)
        $filename = $file->getClientName();
        $suspiciousPatterns = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com'];

        foreach ($suspiciousPatterns as $pattern) {
            if (stripos($filename, $pattern) !== false) {
                return [
                    'valid' => false,
                    'message' => 'File type potentially dangerous and not allowed'
                ];
            }
        }

        return ['valid' => true];
    }

    /**
     * Save attachment file
     */
    private function saveAttachment($file, $userId)
    {
        try {
            $uploadPath = WRITEPATH . 'uploads/messages/';

            // Create directory if it doesn't exist
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Generate unique filename
            $filename = uniqid() . '_' . $file->getClientName();
            $file->move($uploadPath, $filename);

            // Save to database (you'll need to create an attachments table)
            $db = \Config\Database::connect();
            $attachmentData = [
                'filename' => $file->getClientName(),
                'stored_filename' => $filename,
                'file_path' => $uploadPath . $filename,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getClientMimeType(),
                'uploaded_by' => $userId,
                'created_at' => date('Y-m-d H:i:s')
            ];

            // For now, return a mock ID since we don't have attachments table yet
            return uniqid();

        } catch (\Exception $e) {
            log_message('error', 'Error saving attachment: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Link attachments to message
     */
    private function linkAttachmentsToMessage($messageId, $attachmentIds)
    {
        // This would link attachments to messages in a junction table
        // For now, just log the action
        if (!empty($attachmentIds)) {
            log_message('info', "Linked attachments " . implode(',', $attachmentIds) . " to message {$messageId}");
        }
    }

    /**
     * Mark all messages as read
     */
    public function markAllAsRead()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        try {
            $this->messageModel->where('recipient_id', $user['id'])
                              ->where('is_read', 0)
                              ->set(['is_read' => 1, 'read_at' => date('Y-m-d H:i:s')])
                              ->update();

            return $this->response->setJSON([
                'success' => true,
                'message' => 'All messages marked as read'
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error marking messages as read: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Delete multiple messages
     */
    public function deleteMultiple()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();
        $messageIds = $this->request->getJSON(true)['message_ids'] ?? [];

        if (empty($messageIds)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No messages selected'
            ])->setStatusCode(400);
        }

        try {
            $deleted = 0;
            foreach ($messageIds as $messageId) {
                $message = $this->messageModel->find($messageId);
                if ($message && ($message['recipient_id'] === $user['id'] || $message['sender_id'] === $user['id'])) {
                    if ($this->messageModel->delete($messageId)) {
                        $deleted++;
                    }
                }
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => "Deleted {$deleted} message(s)"
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error deleting messages: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }

    /**
     * Save draft message
     */
    public function saveDraft()
    {
        if (!$this->authLib->isLoggedIn()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ])->setStatusCode(401);
        }

        $user = $this->authLib->user();

        // Validation rules
        $rules = [
            'subject' => 'required|min_length[3]|max_length[255]',
            'message' => 'required|min_length[10]'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $this->validation->getErrors()
            ])->setStatusCode(400);
        }

        try {
            $draftData = [
                'sender_id' => $user['id'],
                'recipient_id' => $this->request->getPost('recipient_id') ?: null,
                'subject' => $this->request->getPost('subject'),
                'message' => $this->request->getPost('message'),
                'priority' => $this->request->getPost('priority') ?: 'normal',
                'is_draft' => 1,
                'is_broadcast' => 0
            ];

            if ($this->messageModel->insert($draftData)) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Draft saved successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to save draft'
                ])->setStatusCode(500);
            }

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error saving draft: ' . $e->getMessage()
            ])->setStatusCode(500);
        }
    }
}
