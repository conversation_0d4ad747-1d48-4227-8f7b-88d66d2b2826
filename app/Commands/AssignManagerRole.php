<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class AssignManagerRole extends BaseCommand
{
    protected $group       = 'SmartFlo';
    protected $name        = 'smartflo:assign-manager';
    protected $description = 'Assign manager role to a user';
    protected $usage       = 'smartflo:assign-manager [username]';
    protected $arguments   = [
        'username' => 'Username of the user to assign manager role'
    ];

    public function run(array $params)
    {
        $userModel = new \App\Models\UserModel();
        $roleModel = new \App\Models\RoleModel();
        $userRoleModel = new \App\Models\UserRoleModel();

        // Get username from params or ask for it
        $username = $params[0] ?? CLI::prompt('Enter username to assign manager role');

        if (empty($username)) {
            CLI::error('Username is required');
            return;
        }

        // Find user
        $user = $userModel->getUserByUsername($username);
        
        if (!$user) {
            CLI::error("User '{$username}' not found");
            return;
        }

        // Check if user is active
        if (!$user['is_active']) {
            CLI::error("User '{$username}' is not active");
            return;
        }

        // Get manager role
        $managerRole = $roleModel->where('role_name', 'manager')->first();
        
        if (!$managerRole) {
            CLI::error('Manager role not found. Please run the ManagerRoleSeeder first.');
            return;
        }

        // Check if user already has manager role
        $existingRoles = $userRoleModel->getUserRoles($user['id']);
        $hasManagerRole = false;
        
        foreach ($existingRoles as $role) {
            if ($role['role_name'] === 'manager') {
                $hasManagerRole = true;
                break;
            }
        }

        if ($hasManagerRole) {
            CLI::write("User '{$username}' already has manager role", 'yellow');
            return;
        }

        // Show current roles
        if (!empty($existingRoles)) {
            CLI::write("Current roles for '{$username}':");
            foreach ($existingRoles as $role) {
                CLI::write("  - {$role['role_name']}: {$role['description']}", 'yellow');
            }
        } else {
            CLI::write("User '{$username}' currently has no roles assigned", 'yellow');
        }

        // Confirm assignment
        $confirm = CLI::prompt("Assign manager role to '{$username}'? This will replace existing roles", ['y', 'n']);
        
        if (strtolower($confirm) !== 'y') {
            CLI::write('Operation cancelled', 'yellow');
            return;
        }

        // Assign manager role (this will replace existing roles)
        $success = $userRoleModel->syncUserRoles($user['id'], [$managerRole['id']]);

        if ($success) {
            CLI::write("✓ Manager role successfully assigned to '{$username}'", 'green');
            CLI::write("\nManager capabilities granted:");
            CLI::write("  ✓ Full project management (create, edit, delete, assign)", 'green');
            CLI::write("  ✓ Complete task management and assignment", 'green');
            CLI::write("  ✓ Team member management and assignment", 'green');
            CLI::write("  ✓ Project status updates and timeline access", 'green');
            CLI::write("  ✓ Reports and analytics viewing/export", 'green');
            CLI::write("  ✓ Dashboard controls management", 'green');
            CLI::write("  ✓ User profile editing (limited user management)", 'green');
            CLI::write("  ✗ Cannot create/delete users (admin only)", 'red');
            CLI::write("  ✗ Cannot manage roles (admin only)", 'red');
            CLI::write("  ✗ No admin panel access", 'red');
        } else {
            CLI::error("Failed to assign manager role to '{$username}'");
        }
    }
}
