<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class ListUsers extends BaseCommand
{
    protected $group       = 'SmartFlo';
    protected $name        = 'smartflo:list-users';
    protected $description = 'List all users with their roles';
    protected $usage       = 'smartflo:list-users [--active-only]';
    protected $options     = [
        '--active-only' => 'Show only active users'
    ];

    public function run(array $params)
    {
        $userModel = new \App\Models\UserModel();
        $userRoleModel = new \App\Models\UserRoleModel();

        // Check if we should show only active users
        $activeOnly = CLI::getOption('active-only');

        // Get users
        $builder = $userModel->select('users.*')
            ->orderBy('users.username', 'ASC');

        if ($activeOnly) {
            $builder->where('users.is_active', 1);
        }

        $users = $builder->findAll();

        if (empty($users)) {
            CLI::write('No users found', 'yellow');
            return;
        }

        CLI::write('SmartFlo Users:', 'green');
        CLI::write(str_repeat('=', 80), 'green');

        foreach ($users as $user) {
            // Get user roles
            $roles = $userRoleModel->getUserRoles($user['id']);
            $roleNames = array_column($roles, 'role_name');
            
            $status = $user['is_active'] ? 'Active' : 'Inactive';
            $statusColor = $user['is_active'] ? 'green' : 'red';
            
            CLI::write("Username: {$user['username']}", 'white');
            CLI::write("Email: {$user['email']}", 'light_gray');
            CLI::write("Status: {$status}", $statusColor);
            CLI::write("Name: " . trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '')) ?: 'Not set', 'light_gray');
            
            if (!empty($roleNames)) {
                CLI::write("Roles: " . implode(', ', $roleNames), 'yellow');
            } else {
                CLI::write("Roles: No roles assigned", 'red');
            }
            
            if ($user['last_login']) {
                CLI::write("Last Login: {$user['last_login']}", 'light_gray');
            } else {
                CLI::write("Last Login: Never", 'light_gray');
            }
            
            CLI::write(str_repeat('-', 40), 'dark_gray');
        }

        CLI::write("\nTotal users: " . count($users), 'green');
        
        // Show role statistics
        $roleStats = [];
        foreach ($users as $user) {
            $roles = $userRoleModel->getUserRoles($user['id']);
            foreach ($roles as $role) {
                $roleStats[$role['role_name']] = ($roleStats[$role['role_name']] ?? 0) + 1;
            }
        }

        if (!empty($roleStats)) {
            CLI::write("\nRole Distribution:", 'green');
            foreach ($roleStats as $roleName => $count) {
                CLI::write("  {$roleName}: {$count} user(s)", 'yellow');
            }
        }

        CLI::write("\nTo assign manager role to a user, run:", 'cyan');
        CLI::write("php spark smartflo:assign-manager [username]", 'cyan');
    }
}
