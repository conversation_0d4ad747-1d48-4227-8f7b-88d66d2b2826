<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

class SmartFloModules extends BaseConfig
{
    /**
     * Enable or disable module system
     */
    public bool $enabled = true;

    /**
     * Auto-discovery of modules
     */
    public bool $autoDiscover = true;

    /**
     * Module discovery paths
     */
    public array $discoverPaths = [
        APPPATH . 'Modules',
    ];

    /**
     * Core module configuration
     */
    public array $coreModule = [
        'name' => 'Authentication System',
        'version' => '2.0.0',
        'description' => 'Core authentication and user management system',
        'author' => 'SmartFlo Team',
        'namespace' => 'App',
        'status' => 'active',
        'routes' => [
            [
                'pattern' => 'auth',
                'controller' => 'App\\Controllers\\Auth'
            ],
            [
                'pattern' => 'admin',
                'controller' => 'App\\Controllers\\Admin'
            ]
        ],
        'navigation' => [
            [
                'title' => 'Dashboard',
                'url' => '/admin/dashboard',
                'icon' => 'fas fa-tachometer-alt',
                'permission' => 'admin.access',
                'order' => 1
            ],
            [
                'title' => 'User Management',
                'url' => '/admin/users',
                'icon' => 'fas fa-users',
                'permission' => 'user.read',
                'order' => 2
            ],
            [
                'title' => 'Role Management',
                'url' => '/admin/roles',
                'icon' => 'fas fa-user-tag',
                'permission' => 'role.read',
                'order' => 3
            ]
        ],
        'permissions' => [
            'admin.access' => 'Access Admin Panel',
            'user.create' => 'Create Users',
            'user.read' => 'View Users',
            'user.update' => 'Edit Users',
            'user.delete' => 'Delete Users',
            'role.create' => 'Create Roles',
            'role.read' => 'View Roles',
            'role.update' => 'Edit Roles',
            'role.delete' => 'Delete Roles'
        ]
    ];

    /**
     * Module loading order
     */
    public array $loadOrder = [
        'core' => 1,
        'authentication' => 1,
        'cms' => 2,
        'ecommerce' => 3,
        'crm' => 4,
        'inventory' => 5,
        'reporting' => 6,
        'api' => 7,
        'files' => 8,
        'communication' => 9
    ];

    /**
     * Module dependencies
     */
    public array $dependencies = [
        'core' => [],
        'authentication' => ['core'],
        'cms' => ['authentication'],
        'ecommerce' => ['authentication', 'inventory'],
        'crm' => ['authentication'],
        'inventory' => ['authentication'],
        'reporting' => ['authentication'],
        'api' => ['authentication'],
        'files' => ['authentication'],
        'communication' => ['authentication']
    ];

    /**
     * Default module settings
     */
    public array $defaultSettings = [
        'enabled' => true,
        'autoload' => true,
        'cache' => true,
        'debug' => false
    ];

    /**
     * Module API endpoints
     */
    public array $apiEndpoints = [
        'modules' => '/api/modules',
        'module_info' => '/api/modules/{module}',
        'module_status' => '/api/modules/{module}/status',
        'module_config' => '/api/modules/{module}/config'
    ];

    /**
     * Security settings for modules
     */
    public array $security = [
        'require_signature' => false,
        'allowed_sources' => ['local', 'official'],
        'sandbox_mode' => false,
        'permission_inheritance' => true
    ];
}
