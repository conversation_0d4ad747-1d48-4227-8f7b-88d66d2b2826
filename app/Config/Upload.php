<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

class Upload extends BaseConfig
{
    /**
     * Maximum file size in bytes (10MB default)
     */
    public int $maxSize = 10485760; // 10MB

    /**
     * Maximum image dimensions
     */
    public array $maxDimensions = [
        'width' => 4096,
        'height' => 4096
    ];

    /**
     * Allowed image file types
     */
    public array $allowedTypes = [
        'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'
    ];

    /**
     * Allowed MIME types for images
     */
    public array $allowedMimes = [
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/gif',
        'image/webp',
        'image/svg+xml'
    ];

    /**
     * Upload directory paths
     */
    public array $uploadPaths = [
        'images' => WRITEPATH . 'uploads/images/',
        'documents' => WRITEPATH . 'uploads/documents/',
        'temp' => WRITEPATH . 'uploads/temp/'
    ];

    /**
     * File naming strategy
     * Options: 'random', 'timestamp', 'original'
     */
    public string $namingStrategy = 'random';

    /**
     * Enable virus scanning (requires ClamAV)
     */
    public bool $enableVirusScanning = false;

    /**
     * Enable image processing and optimization
     */
    public bool $enableImageProcessing = true;

    /**
     * Image quality for JPEG compression (1-100)
     */
    public int $imageQuality = 85;

    /**
     * Generate thumbnails
     */
    public bool $generateThumbnails = true;

    /**
     * Thumbnail sizes
     */
    public array $thumbnailSizes = [
        'small' => ['width' => 150, 'height' => 150],
        'medium' => ['width' => 300, 'height' => 300],
        'large' => ['width' => 800, 'height' => 600]
    ];

    /**
     * Enable file encryption for sensitive files
     */
    public bool $enableEncryption = false;

    /**
     * Quarantine directory for suspicious files
     */
    public string $quarantinePath = WRITEPATH . 'uploads/quarantine/';

    /**
     * Enable audit logging
     */
    public bool $enableAuditLog = true;

    /**
     * Rate limiting for uploads (uploads per minute per user)
     */
    public int $rateLimit = 10;

    /**
     * Blacklisted file extensions (security)
     */
    public array $blacklistedExtensions = [
        'php', 'php3', 'php4', 'php5', 'phtml', 'exe', 'bat', 'cmd', 'com', 'scr',
        'vbs', 'js', 'jar', 'asp', 'aspx', 'jsp', 'pl', 'py', 'sh', 'cgi'
    ];

    /**
     * Content Security Policy for served files
     */
    public array $cspHeaders = [
        'Content-Security-Policy' => "default-src 'none'; img-src 'self'; style-src 'unsafe-inline'",
        'X-Content-Type-Options' => 'nosniff',
        'X-Frame-Options' => 'DENY'
    ];
}
