<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */

// Default route - redirect to login
$routes->get('/', 'Auth::index');

// Main dashboard (protected by auth filter)
$routes->get('dashboard', 'Dashboard::index', ['filter' => 'auth']);
$routes->get('dashboard/data', 'Dashboard::getData', ['filter' => 'auth']);
$routes->get('dashboard/project-updates', 'Dashboard::getProjectUpdatesAjax', ['filter' => 'auth']);

// Notification routes (protected by AuthFilter)
$routes->group('notifications', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Notifications::index');
    $routes->post('mark-read/(:num)', 'Notifications::markAsRead/$1');
    $routes->post('mark-all-read', 'Notifications::markAsRead');
    $routes->delete('(:num)', 'Notifications::delete/$1');
    $routes->post('create', 'Notifications::create');
});

// User routes (protected by auth filter)
$routes->group('', ['filter' => 'auth'], function($routes) {
    // Status page
    $routes->get('status', 'Status::index');

    // Messages page (handled by messages group below)

    // Task Manager page
    $routes->get('tasks', 'Tasks::index');

    // Add Material Expense
    $routes->get('add-material', 'Expenses::addMaterial');
    $routes->post('add-material', 'Expenses::storeMaterial');

    // Add Labour Expense
    $routes->get('add-labour', 'Expenses::addLabour');
    $routes->post('add-labour', 'Expenses::storeLabour');

    // Add Office Expense
    $routes->get('add-office', 'Expenses::addOffice');
    $routes->post('add-office', 'Expenses::storeOffice');

    // My Entry
    $routes->get('my-entry', 'Entry::index');

    // Credit Purchases
    $routes->get('credit-purchases', 'Purchases::credit');

    // Client Pay
    $routes->get('client-pay', 'Payments::client');

    // My Petty Cash
    $routes->get('petty-cash', 'PettyCash::index');

    // Purchases
    $routes->get('purchases', 'Purchases::index');

    // Tools and Assets
    $routes->get('tools-assets', 'ToolsAssets::index');
});

// Authentication routes
$routes->group('auth', function($routes) {
    $routes->get('login', 'Auth::login');
    $routes->post('login', 'Auth::processLogin');
    $routes->get('logout', 'Auth::logout');
    $routes->get('forgot-password', 'Auth::forgotPassword');
    $routes->post('forgot-password', 'Auth::processForgotPassword');
    $routes->get('reset-password/(:segment)', 'Auth::resetPassword/$1');
    $routes->post('reset-password', 'Auth::processResetPassword');
    $routes->get('change-password', 'Auth::changePassword');
    $routes->post('change-password', 'Auth::processChangePassword');
    $routes->get('profile', 'Auth::profile');
    $routes->post('profile', 'Auth::updateProfile');
    $routes->get('status', 'Auth::status');
    $routes->get('csrf-token', 'Auth::csrfToken');
    $routes->post('csrf-token', 'Auth::csrfToken');
});

// Admin routes (protected by AdminFilter)
$routes->group('admin', ['filter' => 'admin'], function($routes) {
    // Admin Panel
    $routes->get('panel', 'AdminPanel::index');

    // Admin dashboard removed - use main dashboard instead

    // User management
    $routes->group('users', function($routes) {
        $routes->get('/', 'Admin\Users::index');
        $routes->get('data', 'Admin\Users::getData');
        $routes->get('create', 'Admin\Users::create');
        $routes->post('store', 'Admin\Users::store');
        $routes->get('edit/(:num)', 'Admin\Users::edit/$1');
        $routes->post('update/(:num)', 'Admin\Users::update/$1');
        // Delete route removed - users can only be deactivated
        $routes->post('toggle-status/(:num)', 'Admin\Users::toggleStatus/$1');
    });

    // Role management
    $routes->group('roles', function($routes) {
        $routes->get('/', 'Admin\Roles::index');
        $routes->get('data', 'Admin\Roles::getData');
        $routes->get('permissions', 'Admin\Roles::getPermissions');
        $routes->get('create', 'Admin\Roles::create');
        $routes->post('store', 'Admin\Roles::store');
        $routes->get('edit/(:num)', 'Admin\Roles::edit/$1');
        $routes->post('update/(:num)', 'Admin\Roles::update/$1');
        $routes->delete('delete/(:num)', 'Admin\Roles::delete/$1');
        $routes->get('view/(:num)', 'Admin\Roles::view/$1');
    });

    // System management
    $routes->get('system', 'Admin\System::index');
    $routes->post('system/update-version', 'Admin\System::updateVersion');

    // Add new items
    $routes->get('add', 'Admin\Add::index');

    // Activity log
    $routes->get('activity', 'Admin\Activity::index');

    // Security management
    $routes->group('security', function($routes) {
        $routes->get('failed-logins', 'Admin\Security::failedLogins');
        $routes->get('failed-logins-data', 'Admin\Security::getFailedLoginsData');
        $routes->get('statistics', 'Admin\Security::getStatistics');
        $routes->post('block-ip', 'Admin\Security::blockIP');
        $routes->post('unblock-ip', 'Admin\Security::unblockIP');
        $routes->post('clean-old-attempts', 'Admin\Security::cleanOldAttempts');
    });
});

// Secure Blob Storage routes (protected by auth filter)
$routes->group('blob', function($routes) {
    // Image upload and management (auth required)
    $routes->post('upload/image', 'BlobStorage::uploadImage', ['filter' => 'auth']);
    $routes->get('image/(:num)', 'BlobStorage::serveImage/$1');
    $routes->get('thumbnail/(:num)/(:alpha)', 'BlobStorage::serveThumbnail/$1/$2');
    $routes->delete('image/(:num)', 'BlobStorage::deleteImage/$1', ['filter' => 'auth']);
    $routes->get('files', 'BlobStorage::listFiles', ['filter' => 'auth']);

    // File management interface
    $routes->get('manager', 'BlobStorage::fileManager');
    $routes->get('stats', 'BlobStorage::getStats', ['filter' => 'auth']);
});

// Settings routes
$routes->group('settings', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Settings::index');
    $routes->get('account', 'Settings::account');
    $routes->get('notifications', 'Settings::notifications');
    $routes->get('security', 'Settings::security');
    $routes->get('preferences', 'Settings::preferences');
    $routes->post('update-notifications', 'Settings::updateNotifications');
    $routes->get('get-notification-settings', 'Settings::getNotificationSettings');
    $routes->post('update-preferences', 'Settings::updatePreferences');
    $routes->get('get-preferences', 'Settings::getPreferences');
});

// Messages routes
$routes->group('messages', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Messages::index');
    $routes->get('compose', 'Messages::compose');
    $routes->get('view/(:num)', 'Messages::view/$1');
    $routes->get('get-messages', 'Messages::getMessages');
    $routes->get('get-users', 'Messages::getUsers');
    $routes->get('get/(:num)', 'Messages::getMessage/$1');
    $routes->post('send', 'Messages::send');
    $routes->delete('(:num)', 'Messages::delete/$1');
    $routes->post('mark-read/(:num)', 'Messages::markAsRead/$1');
    $routes->post('mark-all-read', 'Messages::markAllAsRead');
    $routes->post('delete-multiple', 'Messages::deleteMultiple');
    $routes->post('save-draft', 'Messages::saveDraft');
    $routes->get('stats', 'Messages::getStats');
});

// Site Status routes
$routes->group('site-status', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'SiteStatus::index');
    $routes->get('get-status', 'SiteStatus::getStatus');
    $routes->post('update-status', 'SiteStatus::updateStatus');
    $routes->get('history', 'SiteStatus::getHistory');
    $routes->get('stats', 'SiteStatus::getStats');
    $routes->post('schedule-maintenance', 'SiteStatus::scheduleMaintenance');
    $routes->get('scheduled-maintenance', 'SiteStatus::getScheduledMaintenance');
    $routes->post('cancel-maintenance/(:num)', 'SiteStatus::cancelMaintenance/$1');
});

// Public site status (no auth required)
$routes->get('status', 'SiteStatus::publicStatus');
$routes->get('api/status', 'SiteStatus::apiStatus');

// Notifications routes
$routes->group('notifications', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Notifications::index');
    $routes->get('view/(:num)', 'Notifications::view/$1');
    $routes->get('get-notifications', 'Notifications::getNotifications');
    $routes->post('create', 'Notifications::create');
    $routes->post('mark-read/(:num)', 'Notifications::markAsRead/$1');
    $routes->post('mark-all-read', 'Notifications::markAllAsRead');
    $routes->delete('(:num)', 'Notifications::delete/$1');
});

// Dashboard Controls routes
$routes->group('dashboard-controls', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'DashboardControls::index');
    $routes->get('get-controls', 'DashboardControls::getControls');
    $routes->get('get-all-controls', 'DashboardControls::getAllControls');
    $routes->post('create', 'DashboardControls::create');
    $routes->post('update/(:num)', 'DashboardControls::update/$1');
    $routes->delete('(:num)', 'DashboardControls::delete/$1');
    $routes->post('toggle-active/(:num)', 'DashboardControls::toggleActive/$1');
    $routes->post('update-order', 'DashboardControls::updateOrder');
});

// Reports routes
$routes->group('reports', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Reports::index');
    $routes->get('getDailyReport', 'Reports::getDailyReport');
    $routes->get('getStaffList', 'Reports::getStaffList');
});

// Test AJAX route
$routes->get('test-ajax', function() {
    return view('test_ajax');
});

// Test CSRF route
$routes->get('test-csrf', function() {
    return view('test_csrf');
});

// Test database connection
$routes->get('test-db', function() {
    $db = \Config\Database::connect();
    try {
        // Test basic connection
        $query = $db->query('SELECT 1 as test');
        $result = $query->getRow();

        // Check if required tables exist
        $tables = [];
        $requiredTables = ['projects', 'project_tasks', 'users'];
        foreach ($requiredTables as $table) {
            try {
                $tableQuery = $db->query("SHOW TABLES LIKE '$table'");
                $tables[$table] = $tableQuery->getNumRows() > 0;
            } catch (\Exception $e) {
                $tables[$table] = false;
            }
        }

        return json_encode([
            'success' => true,
            'message' => 'Database connection successful',
            'test_result' => $result,
            'tables' => $tables
        ]);
    } catch (\Exception $e) {
        return json_encode([
            'success' => false,
            'message' => 'Database connection failed: ' . $e->getMessage()
        ]);
    }
});

// API Routes
$routes->group('api', ['filter' => 'auth'], function($routes) {
    $routes->get('unread-counts', 'Api::unreadCounts');
    $routes->get('recent-messages', 'Api::recentMessages');
    $routes->get('recent-notifications', 'Api::recentNotifications');
    $routes->post('mark-notification-read/(:num)', 'Api::markNotificationRead/$1');
});

// Projects routes (enhanced project management)
$routes->group('projects', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Projects::index');
    $routes->get('reports', 'Projects::reports');
    $routes->get('create', 'Projects::createPage');
    $routes->get('view/(:num)', 'Projects::view/$1');
    $routes->get('edit/(:num)', 'Projects::edit/$1');
    $routes->post('update/(:num)', 'Projects::update/$1');
    $routes->get('getProjects', 'Projects::getProjects');
    $routes->get('getStats', 'Projects::getStats');
    $routes->get('getUsers', 'Projects::getUsers');
    $routes->get('getAssignees', 'Projects::getAssignees');
    $routes->get('checkProjectId', 'Projects::checkProjectId');
    $routes->get('getProject/(:num)', 'Projects::getProject/$1');
    $routes->get('getProjectTasks/(:num)', 'Projects::getProjectTasks/$1');
    $routes->get('getTasks/(:num)', 'Projects::getTasks/$1');
    $routes->get('getProjectTimeline/(:num)', 'Projects::getProjectTimeline/$1');
    $routes->post('create', 'Projects::create');
    $routes->get('debugCreate', 'Projects::debugCreate');
    $routes->post('debugCreate', 'Projects::debugCreate');
    $routes->get('testInsert', 'Projects::testInsert');
    $routes->get('testButtons', 'Projects::testButtons');
    $routes->get('testManagerPermissions', 'Projects::testManagerPermissions');
    $routes->get('testStatusValidation', 'Projects::testStatusValidation');
    $routes->post('updateStatus/(:num)', 'Projects::updateStatus/$1');
    $routes->get('getTimeline/(:num)', 'Projects::getTimeline/$1');
    $routes->post('updateTaskStatus/(:num)', 'Projects::updateTaskStatus/$1');
    $routes->post('requestRevision/(:num)', 'Projects::requestRevision/$1');
    $routes->post('updateTaskManagerStatus/(:num)', 'Projects::updateTaskManagerStatus/$1');
    $routes->post('updateManagerStatus/(:num)', 'Projects::updateManagerStatus/$1');
    $routes->post('generateQR/(:num)', 'Projects::generateQR/$1');
    $routes->post('bulkImport', 'Projects::bulkImport');
    $routes->delete('delete/(:num)', 'Projects::delete/$1');
    $routes->post('delete/(:num)', 'Projects::delete/$1');
    $routes->post('purgeDeleted', 'Projects::purgeDeletedProjects');
    // Task management routes for project edit page
    $routes->post('addTask/(:num)', 'Projects::addTask/$1');
    $routes->post('updateTask/(:num)', 'Projects::updateTask/$1');
    $routes->delete('deleteTask/(:num)', 'Projects::deleteTask/$1');
    $routes->post('deleteTask/(:num)', 'Projects::deleteTask/$1');
    $routes->get('getTaskTypes', 'Projects::getTaskTypes');
    $routes->post('generateQR/(:num)', 'ClientAccess::generateQR/$1');
    $routes->post('revokeAccess/(:num)', 'ClientAccess::revokeAccess/$1');
    $routes->get('downloadQR/(:num)', 'ClientAccess::downloadQR/$1');
});

// Client access routes (no authentication required)
$routes->group('client', function($routes) {
    $routes->get('project/(:segment)', 'ClientAccess::viewProject/$1');
    $routes->get('api/project/(:segment)', 'ClientAccess::getProjectStatus/$1');
    $routes->get('qr/(:segment)', 'ClientAccess::getQRImage/$1');
    $routes->get('downloadTaskFiles/(:num)', 'ClientAccess::downloadTaskFiles/$1');
});

// Legacy Project Status routes (for backward compatibility)
$routes->group('projects/status', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Projects::index'); // Redirect to new projects page
    $routes->get('get-projects', 'Projects::getProjects');
    $routes->get('get-stats', 'Projects::getStats');
    $routes->get('get/(:num)', 'Projects::getProject/$1');
    $routes->post('create', 'Projects::create');
    $routes->post('update/(:num)', 'Projects::updateStatus/$1');
    $routes->post('update-progress/(:num)', 'Projects::updateStatus/$1');
    $routes->delete('(:num)', 'Projects::delete/$1');
});

// Database initialization routes (for development/testing)
$routes->group('DatabaseInit', function($routes) {
    $routes->get('createProjectTimelineTable', 'DatabaseInit::createProjectTimelineTable');
    $routes->get('checkTables', 'DatabaseInit::checkTables');
    $routes->get('addSampleTimelineData', 'DatabaseInit::addSampleTimelineData');
});

// Task Management routes
$routes->group('task-manager', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'TaskManager::index');
    $routes->get('getTaskData', 'TaskManager::getTaskData');
    $routes->post('addCategory', 'TaskManager::addCategory');
    $routes->post('addTask', 'TaskManager::addTask');
    $routes->post('updateTask', 'TaskManager::updateTask');
    $routes->post('deleteTask', 'TaskManager::deleteTask');
});

// Billing routes
$routes->group('billing', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Billing::index');
    $routes->get('getBillingData', 'Billing::getBillingData');
    $routes->post('updatePaymentStatus/(:num)', 'Billing::updatePaymentStatus/$1');
    $routes->get('getPaymentAccounts', 'Billing::getPaymentAccounts');
});

// Test routes
$routes->get('test-timeline', 'TestTimeline::index');
