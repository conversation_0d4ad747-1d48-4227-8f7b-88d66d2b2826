<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

class Version extends BaseConfig
{
    /**
     * Current system version
     */
    public string $version = '2.0.0';

    /**
     * Version release date
     */
    public string $releaseDate = '2024-06-08';

    /**
     * Version codename
     */
    public string $codename = 'Modular Foundation';

    /**
     * Build number
     */
    public string $build = '20240608001';

    /**
     * Version history
     */
    public array $history = [
        '2.0.0' => [
            'date' => '2024-06-08',
            'codename' => 'Modular Foundation',
            'changes' => [
                'Implemented modular architecture',
                'Enhanced password policy with 90-day expiration',
                'Added forced password change on first login',
                'Updated Remember Me to 7 days',
                'Removed CodeIgniter branding',
                'Added version management system',
                'Improved security features',
                'Added configurable rate limiting'
            ]
        ],
        '1.0.0' => [
            'date' => '2024-06-07',
            'codename' => 'Initial Release',
            'changes' => [
                'Initial authentication system',
                'User and role management',
                'Admin panel',
                'Security features',
                'Rate limiting',
                'Password reset functionality'
            ]
        ]
    ];

    /**
     * Get current version info
     */
    public function getCurrentVersion(): array
    {
        return [
            'version' => $this->version,
            'release_date' => $this->releaseDate,
            'codename' => $this->codename,
            'build' => $this->build
        ];
    }

    /**
     * Get version history
     */
    public function getVersionHistory(): array
    {
        return $this->history;
    }

    /**
     * Get formatted version string
     */
    public function getVersionString(): string
    {
        return "SmartFlo v{$this->version} ({$this->codename})";
    }

    /**
     * Check if version is newer than given version
     */
    public function isNewerThan(string $version): bool
    {
        return version_compare($this->version, $version, '>');
    }

    /**
     * Get system information
     */
    public function getSystemInfo(): array
    {
        return [
            'version' => $this->version,
            'codename' => $this->codename,
            'build' => $this->build,
            'release_date' => $this->releaseDate,
            'php_version' => PHP_VERSION,
            'codeigniter_version' => \CodeIgniter\CodeIgniter::CI_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_version' => $this->getDatabaseVersion()
        ];
    }

    /**
     * Get database version
     */
    private function getDatabaseVersion(): string
    {
        try {
            $db = \Config\Database::connect();
            $query = $db->query('SELECT VERSION() as version');
            $result = $query->getRowArray();
            return $result['version'] ?? 'Unknown';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }
}
