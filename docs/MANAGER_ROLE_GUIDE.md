# SmartFlo Manager Role Guide

## Overview

The Manager role in SmartFlo provides comprehensive project and team management capabilities while maintaining appropriate security boundaries. Managers have extensive control over projects, tasks, and team assignments without full administrative privileges.

## Manager Role Capabilities

### ✅ **FULL ACCESS**

#### Project Management
- **Create Projects**: Can create new projects with full details
- **Edit All Projects**: Can modify any project regardless of creator
- **Delete Projects**: Can remove projects from the system
- **Assign Projects**: Can assign projects to team members
- **Update Project Status**: Can change project status and add comments
- **View All Projects**: Can see all projects in the system
- **Project Timeline Access**: Can view detailed project timelines and history

#### Task Management
- **Create Tasks**: Can create new tasks within projects
- **Edit All Tasks**: Can modify any task regardless of assignee
- **Delete Tasks**: Can remove tasks from projects
- **Assign Tasks**: Can assign tasks to team members
- **Update Task Status**: Can change task status for any task
- **View All Tasks**: Can see all tasks across all projects

#### Team Management
- **Manage Team Members**: Can oversee team member assignments
- **Assign Team Members**: Can assign users to projects and tasks
- **View User Profiles**: Can view and edit user profile information (limited)

#### Reports & Analytics
- **View Reports**: Can access all project and team reports
- **Export Reports**: Can export reports in various formats
- **Analytics Dashboard**: Can view analytics and performance metrics

#### Dashboard Controls
- **Manage Dashboard**: Can configure dashboard elements and controls
- **Quick Actions**: Can perform quick project and task actions

### ❌ **RESTRICTED ACCESS**

#### User Administration
- **Cannot Create Users**: Only admins can create new user accounts
- **Cannot Delete Users**: Only admins can remove user accounts
- **Cannot Change User Roles**: Only admins can assign/modify user roles

#### System Administration
- **No Admin Panel Access**: Cannot access system administration features
- **Cannot Manage Roles**: Cannot create, edit, or delete user roles
- **No System Settings**: Cannot modify system-wide configurations

## Technical Implementation

### Role Assignment

#### Using CLI Commands
```bash
# List all users and their current roles
php spark smartflo:list-users --active-only

# Assign manager role to a user
php spark smartflo:assign-manager [username]
```

#### Using Code
```php
// Assign manager role to user
$userModel = new \App\Models\UserModel();
$success = $userModel->assignManagerRole($userId);

// Check if user is manager
$isManager = $userModel->isManager($userId);

// Get all managers
$managers = $userModel->getManagers();
```

### Permission Checking

#### In Controllers
```php
// Check if user can manage projects
if (!$this->authLib->canManageProjects()) {
    // Access denied
}

// Check if user can manage tasks
if (!$this->authLib->canManageTasks()) {
    // Access denied
}

// Check if user can view all projects
if (!$this->authLib->canViewAllProjects()) {
    // Access denied
}

// Check if user can assign work
if (!$this->authLib->canAssignWork()) {
    // Access denied
}
```

#### Available Permission Methods
- `canManageProjects()` - Full project management access
- `canManageTasks()` - Full task management access
- `canViewAllProjects()` - View all projects regardless of assignment
- `canAssignWork()` - Assign projects and tasks to team members
- `hasRole('manager')` - Check for specific manager role
- `hasAnyRole(['admin', 'manager'])` - Check for multiple roles

### Database Structure

#### Manager Role Permissions
The manager role includes the following permissions:
- `user.read` - View user information
- `user.update` - Edit user profiles (limited)
- `project.create` - Create new projects
- `project.read` - View all projects
- `project.update` - Edit all projects
- `project.delete` - Delete projects
- `project.assign` - Assign projects to users
- `project.status` - Update project status
- `project.timeline` - View project timelines
- `task.create` - Create tasks
- `task.read` - View all tasks
- `task.update` - Edit all tasks
- `task.delete` - Delete tasks
- `task.assign` - Assign tasks to users
- `task.status` - Update task status
- `report.view` - View reports
- `report.export` - Export reports
- `analytics.view` - View analytics
- `team.manage` - Manage team members
- `team.assign` - Assign team members
- `dashboard.manage` - Manage dashboard controls

## Usage Examples

### Assigning Manager Role

1. **List Current Users**:
   ```bash
   php spark smartflo:list-users --active-only
   ```

2. **Assign Manager Role**:
   ```bash
   php spark smartflo:assign-manager manager_office
   ```

3. **Verify Assignment**:
   ```bash
   php spark smartflo:list-users --active-only
   ```

### Testing Manager Permissions

Access the test endpoint to verify permissions:
```
GET /projects/testManagerPermissions
```

This will return a JSON response showing all permission checks for the current user.

## Security Considerations

### Role Hierarchy
1. **Admin** - Full system access
2. **Manager** - Project and team management
3. **User** - Limited access to assigned work

### Permission Boundaries
- Managers cannot escalate their own privileges
- Managers cannot create or delete user accounts
- Managers cannot access system administration features
- Managers cannot modify role definitions or permissions

### Audit Trail
All manager actions are logged and can be tracked through:
- Project timeline entries
- Task status changes
- User assignment history
- Report access logs

## Best Practices

### Manager Assignment
- Assign manager role to trusted team leads
- Regularly review manager permissions and access
- Ensure managers understand their responsibilities
- Provide training on project management features

### Project Management
- Use project assignment features to delegate work
- Regularly update project status and timelines
- Utilize reporting features for team oversight
- Maintain clear communication through project comments

### Team Coordination
- Assign tasks based on team member skills
- Monitor project progress through dashboard
- Use timeline features to track project history
- Export reports for stakeholder communication

## Troubleshooting

### Common Issues

1. **Manager Cannot See All Projects**:
   - Verify manager role is properly assigned
   - Check user session and re-login if needed

2. **Permission Denied Errors**:
   - Confirm user has manager role
   - Check if specific permission is included in manager role

3. **Cannot Assign Tasks**:
   - Verify `canAssignWork()` permission
   - Check if target users are active

### Support Commands

```bash
# Check user roles
php spark smartflo:list-users

# Re-assign manager role
php spark smartflo:assign-manager [username]

# Verify role permissions
php spark db:table roles
```

## Conclusion

The Manager role provides a balanced approach to project management, giving managers the tools they need to effectively oversee projects and teams while maintaining system security and administrative boundaries.
