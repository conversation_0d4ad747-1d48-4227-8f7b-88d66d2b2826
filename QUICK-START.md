# SmartFlo 2.0 Quick Start Guide

## 🚀 Development Server

### Start the Server
```bash
# Option 1: Use the start script
./start-server.sh

# Option 2: Use CodeIgniter Spark
php spark serve --host *********** --port 8080

# Option 3: Custom IP/Port
./start-server.sh *********** 8080
```

### Access URLs
- **Main Application**: http://***********:8080
- **Dashboard**: http://***********:8080/dashboard
- **File Manager**: http://***********:8080/blob/manager
- **Admin Panel**: http://***********:8080/admin/panel

## 🔐 Default Credentials

**Admin User:**
- Username: `admin`
- Password: `Admin@123`
- Email: `<EMAIL>`

⚠️ **Important**: Change the default password after first login!

## 📁 File Upload & Blob Storage

### Features Available
- ✅ Secure image upload with validation
- ✅ Automatic thumbnail generation
- ✅ File access control (public/private)
- ✅ Image preview and management
- ✅ Storage statistics and analytics
- ✅ Rate limiting and security scanning

### File Manager Access
1. Login to the application
2. Navigate to **File Manager** in the sidebar
3. Or visit: http://***********:8080/blob/manager

### Upload Limits
- **Max file size**: 10MB
- **Allowed types**: JPG, JPEG, PNG, GIF, WebP, SVG
- **Rate limit**: 10 uploads per minute per user

## 🛠️ Development Setup

### Run Development Configuration
```bash
php development-setup.php
```

This will:
- Configure environment for development
- Enable debug mode
- Disable HTTPS enforcement
- Set up proper directories
- Configure local IP address

### Manual Configuration
Edit `.env` file:
```env
CI_ENVIRONMENT = development
app.baseURL = 'http://***********:8080/'
app.debug = true
app.forceGlobalSecureRequests = false
```

## 🔒 Security Features

### Upload Security
- File type validation (whitelist)
- MIME type verification
- Content scanning for malicious code
- Secure filename generation
- Rate limiting per user
- Quarantine for suspicious files

### Access Control
- User-based file ownership
- Public/private file settings
- Role-based permissions
- Session authentication
- CSRF protection

### Infrastructure Security
- Security headers (HSTS, CSP, XSS protection)
- Directory access protection
- Secure file permissions
- Audit logging

## 📊 File Management

### Upload Files
1. Go to File Manager
2. Click "Upload Image" button
3. Select file and set privacy
4. File is automatically processed and thumbnails generated

### View Files
- Browse uploaded files in the file list
- Click thumbnails for preview
- Download or delete files as needed
- View storage statistics

### API Endpoints
- `POST /blob/upload/image` - Upload image
- `GET /blob/image/{id}` - Serve image
- `GET /blob/thumbnail/{id}/{size}` - Serve thumbnail
- `DELETE /blob/image/{id}` - Delete image
- `GET /blob/files` - List files
- `GET /blob/stats` - Storage statistics

## 🏗️ Production Deployment

### Run Production Setup
```bash
php production-setup.php
```

This will:
- Generate secure keys
- Set production environment
- Configure HTTPS enforcement
- Set proper file permissions
- Run database migrations
- Validate security settings

### Manual Production Steps
1. Update `app.baseURL` in `.env` with your domain
2. Set `CI_ENVIRONMENT = production`
3. Enable `app.forceGlobalSecureRequests = true`
4. Configure SSL certificates
5. Set up proper web server configuration
6. Run security checklist in `SECURITY-CHECKLIST.md`

## 🗄️ Database

### Tables Created
- `files` - File metadata and access control
- `users` - User accounts (existing)
- `roles` - User roles (existing)
- Other authentication tables (existing)

### Migration
```bash
php spark migrate
```

## 🔧 Troubleshooting

### Common Issues

**HTTPS Redirect Loop**
- Set `app.forceGlobalSecureRequests = false` in `.env`
- Ensure `app.baseURL` uses `http://` for development

**File Upload Fails**
- Check `writable/uploads/` directory permissions
- Verify file size and type restrictions
- Check error logs in `writable/logs/`

**Database Connection Error**
- Verify database credentials in `.env`
- Ensure MySQL is running
- Check database exists: `smartflo_auth`

**Permission Denied**
- Run: `chmod -R 755 writable/`
- Check file ownership
- Ensure web server can write to `writable/` directory

### Log Files
- Application logs: `writable/logs/`
- Upload activity: Check application logs
- Error logs: `writable/logs/log-YYYY-MM-DD.log`

## 📚 Documentation

- **Security Checklist**: `SECURITY-CHECKLIST.md`
- **Production Setup**: `production-setup.php`
- **Development Setup**: `development-setup.php`
- **Main README**: `README.md`

## 🆘 Support

For issues and questions:
1. Check the logs in `writable/logs/`
2. Review the security checklist
3. Verify file permissions
4. Check database connectivity
5. Review configuration in `.env`

---

**🎉 Your SmartFlo 2.0 application with secure blob storage is ready!**

Access it at: **http://***********:8080**
