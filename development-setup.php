<?php

/**
 * SmartFlo 2.0 Development Setup Script
 * 
 * This script configures the application for local development
 */

class DevelopmentSetup
{
    private $localIP;

    public function __construct()
    {
        echo "SmartFlo 2.0 Development Setup\n";
        echo "==============================\n\n";
        
        // Auto-detect local IP
        $this->localIP = $this->getLocalIP();
    }

    /**
     * Run development setup
     */
    public function run()
    {
        $this->updateEnvironment();
        $this->createDirectories();
        $this->setDevelopmentPermissions();
        $this->displayInstructions();
    }

    /**
     * Update .env for development
     */
    private function updateEnvironment()
    {
        echo "Configuring environment for development...\n";

        $envFile = '.env';
        if (!file_exists($envFile)) {
            echo "❌ .env file not found\n";
            return;
        }

        $envContent = file_get_contents($envFile);

        // Development settings
        $replacements = [
            '/CI_ENVIRONMENT = production/' => 'CI_ENVIRONMENT = development',
            '/app\.debug = false/' => 'app.debug = true',
            '/app\.forceGlobalSecureRequests = true/' => 'app.forceGlobalSecureRequests = false',
            '/app\.baseURL = \'https:\/\/.*?\'/' => "app.baseURL = 'http://{$this->localIP}:8080/'",
            '/app\.baseURL = \'http:\/\/localhost:8080\/\'/' => "app.baseURL = 'http://{$this->localIP}:8080/'",
        ];

        foreach ($replacements as $pattern => $replacement) {
            $envContent = preg_replace($pattern, $replacement, $envContent);
        }

        if (file_put_contents($envFile, $envContent)) {
            echo "✅ Environment configured for development\n";
            echo "   - Environment: development\n";
            echo "   - Debug: enabled\n";
            echo "   - HTTPS: disabled\n";
            echo "   - Base URL: http://{$this->localIP}:8080/\n";
        } else {
            echo "❌ Failed to update .env file\n";
        }

        echo "\n";
    }

    /**
     * Create development directories
     */
    private function createDirectories()
    {
        echo "Setting up development directories...\n";

        $directories = [
            'writable/uploads/images',
            'writable/uploads/images/thumbs',
            'writable/uploads/documents',
            'writable/uploads/temp',
            'writable/cache',
            'writable/logs',
            'writable/session'
        ];

        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                if (mkdir($dir, 0755, true)) {
                    echo "✅ Created directory: {$dir}\n";
                } else {
                    echo "❌ Failed to create directory: {$dir}\n";
                }
            } else {
                echo "✅ Directory exists: {$dir}\n";
            }
        }

        echo "\n";
    }

    /**
     * Set development-friendly permissions
     */
    private function setDevelopmentPermissions()
    {
        echo "Setting development permissions...\n";

        $permissions = [
            'writable' => 0755,
            'writable/uploads' => 0755,
            'writable/cache' => 0755,
            'writable/logs' => 0755,
            'writable/session' => 0755,
            '.env' => 0644, // More permissive for development
        ];

        foreach ($permissions as $path => $perm) {
            if (file_exists($path)) {
                if (chmod($path, $perm)) {
                    echo "✅ Set permissions {$perm} for {$path}\n";
                } else {
                    echo "⚠️  Failed to set permissions for {$path}\n";
                }
            }
        }

        echo "\n";
    }

    /**
     * Get local IP address
     */
    private function getLocalIP()
    {
        // Try to get IP from command line
        $ip = trim(shell_exec("ifconfig | grep 'inet ' | grep -v '127.0.0.1' | head -1 | awk '{print $2}'"));
        
        if (empty($ip)) {
            // Fallback method
            $ip = trim(shell_exec("hostname -I | awk '{print $1}'"));
        }
        
        if (empty($ip)) {
            // Default fallback
            $ip = '************';
        }

        return $ip;
    }

    /**
     * Display setup instructions
     */
    private function displayInstructions()
    {
        echo "🎉 Development setup completed!\n\n";
        echo "Next steps:\n";
        echo "1. Start the development server:\n";
        echo "   php spark serve --host {$this->localIP} --port 8080\n\n";
        echo "2. Access the application:\n";
        echo "   http://{$this->localIP}:8080\n\n";
        echo "3. Default login credentials:\n";
        echo "   Username: admin\n";
        echo "   Password: Admin@123\n\n";
        echo "4. File Manager access:\n";
        echo "   http://{$this->localIP}:8080/blob/manager\n\n";
        echo "5. Admin Panel:\n";
        echo "   http://{$this->localIP}:8080/admin/panel\n\n";
        echo "📝 Note: Remember to run production-setup.php before deploying to production!\n";
    }
}

// Run the setup if called directly
if (php_sapi_name() === 'cli') {
    $setup = new DevelopmentSetup();
    $setup->run();
}
