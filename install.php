<?php
/**
 * SmartFlo 2.0 Authentication System Installation Script
 * 
 * This script helps set up the SmartFlo authentication system
 * by checking requirements, configuring the database, and
 * creating the initial admin user.
 */

// Prevent direct access in production
if (php_sapi_name() !== 'cli' && !defined('INSTALL_MODE')) {
    die('Installation script can only be run from command line or with INSTALL_MODE defined.');
}

class SmartFloInstaller
{
    private $errors = [];
    private $warnings = [];
    private $config = [];

    public function __construct()
    {
        echo "===========================================\n";
        echo "SmartFlo 2.0 Authentication System Setup\n";
        echo "===========================================\n\n";
    }

    /**
     * Run the complete installation process
     */
    public function install()
    {
        $this->checkRequirements();
        $this->checkEnvironment();
        $this->setupDatabase();
        $this->createAdminUser();
        $this->setPermissions();
        $this->displayResults();
    }

    /**
     * Check system requirements
     */
    private function checkRequirements()
    {
        echo "Checking system requirements...\n";

        // PHP Version
        if (version_compare(PHP_VERSION, '8.1.0', '<')) {
            $this->errors[] = "PHP 8.1.0 or higher is required. Current version: " . PHP_VERSION;
        } else {
            echo "✓ PHP version: " . PHP_VERSION . "\n";
        }

        // Required extensions
        $required_extensions = ['mysqli', 'json', 'mbstring', 'openssl', 'curl'];
        foreach ($required_extensions as $ext) {
            if (!extension_loaded($ext)) {
                $this->errors[] = "Required PHP extension missing: {$ext}";
            } else {
                echo "✓ PHP extension: {$ext}\n";
            }
        }

        // Composer
        if (!file_exists('vendor/autoload.php')) {
            $this->errors[] = "Composer dependencies not installed. Run 'composer install' first.";
        } else {
            echo "✓ Composer dependencies installed\n";
        }

        // Writable directories
        $writable_dirs = ['writable', 'writable/cache', 'writable/logs', 'writable/session'];
        foreach ($writable_dirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
            if (!is_writable($dir)) {
                $this->warnings[] = "Directory not writable: {$dir}";
            } else {
                echo "✓ Writable directory: {$dir}\n";
            }
        }

        echo "\n";
    }

    /**
     * Check environment configuration
     */
    private function checkEnvironment()
    {
        echo "Checking environment configuration...\n";

        if (!file_exists('.env')) {
            $this->warnings[] = ".env file not found. Using default configuration.";
            $this->createDefaultEnv();
        } else {
            echo "✓ .env file exists\n";
        }

        // Load environment variables
        if (file_exists('.env')) {
            $env_content = file_get_contents('.env');
            $lines = explode("\n", $env_content);
            foreach ($lines as $line) {
                if (strpos($line, '=') !== false && !str_starts_with(trim($line), '#')) {
                    list($key, $value) = explode('=', $line, 2);
                    $this->config[trim($key)] = trim($value);
                }
            }
        }

        echo "\n";
    }

    /**
     * Setup database
     */
    private function setupDatabase()
    {
        echo "Setting up database...\n";

        $host = $this->config['database.default.hostname'] ?? 'localhost';
        $username = $this->config['database.default.username'] ?? '';
        $password = $this->config['database.default.password'] ?? '';
        $database = $this->config['database.default.database'] ?? 'smartflo_auth';

        if (empty($username)) {
            echo "Database configuration incomplete. Please update .env file.\n";
            return;
        }

        try {
            // Connect to MySQL
            $pdo = new PDO("mysql:host={$host}", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Create database if it doesn't exist
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$database}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "✓ Database '{$database}' created/verified\n";

            // Check if tables exist
            $pdo->exec("USE `{$database}`");
            $stmt = $pdo->query("SHOW TABLES LIKE 'users'");

            if ($stmt->rowCount() > 0) {
                echo "✓ Database tables already exist\n";
            } else {
                // Run migrations
                if (file_exists('database/schema.sql')) {
                    $sql = file_get_contents('database/schema.sql');
                    $pdo->exec($sql);
                    echo "✓ Database schema installed\n";
                } else {
                    echo "Running CodeIgniter migrations...\n";
                    exec('php spark migrate', $output, $return_code);
                    if ($return_code === 0) {
                        echo "✓ Migrations completed\n";
                    } else {
                        $this->errors[] = "Migration failed. Please run 'php spark migrate' manually.";
                    }
                }
            }

        } catch (PDOException $e) {
            $this->errors[] = "Database connection failed: " . $e->getMessage();
        }

        echo "\n";
    }

    /**
     * Create admin user
     */
    private function createAdminUser()
    {
        echo "Creating admin user...\n";

        $host = $this->config['database.default.hostname'] ?? 'localhost';
        $username = $this->config['database.default.username'] ?? '';
        $password = $this->config['database.default.password'] ?? '';
        $database = $this->config['database.default.database'] ?? 'smartflo_auth';

        if (empty($username)) {
            echo "Skipping admin user creation due to database configuration.\n";
            return;
        }

        try {
            $pdo = new PDO("mysql:host={$host};dbname={$database}", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Check if admin user already exists
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = 'admin'");
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                echo "✓ Admin user already exists\n";
            } else {
                // Create admin user
                $admin_password = password_hash('Admin@123', PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, email, password_hash, is_active, created_at, updated_at) 
                    VALUES ('admin', '<EMAIL>', ?, 1, NOW(), NOW())
                ");
                $stmt->execute([$admin_password]);
                $admin_id = $pdo->lastInsertId();

                // Assign admin role
                $stmt = $pdo->prepare("SELECT id FROM roles WHERE role_name = 'admin'");
                $stmt->execute();
                $admin_role = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($admin_role) {
                    $stmt = $pdo->prepare("
                        INSERT INTO user_roles (user_id, role_id, created_at) 
                        VALUES (?, ?, NOW())
                    ");
                    $stmt->execute([$admin_id, $admin_role['id']]);
                }

                echo "✓ Admin user created successfully\n";
                echo "  Username: admin\n";
                echo "  Password: Admin@123\n";
                echo "  Email: <EMAIL>\n";
                echo "  ⚠️  Please change the default password after first login!\n";
            }

        } catch (PDOException $e) {
            $this->errors[] = "Failed to create admin user: " . $e->getMessage();
        }

        echo "\n";
    }

    /**
     * Set proper file permissions
     */
    private function setPermissions()
    {
        echo "Setting file permissions...\n";

        $directories = [
            'writable' => 0755,
            'writable/cache' => 0755,
            'writable/logs' => 0755,
            'writable/session' => 0755,
            'writable/uploads' => 0755
        ];

        foreach ($directories as $dir => $perm) {
            if (!is_dir($dir)) {
                mkdir($dir, $perm, true);
            }
            chmod($dir, $perm);
            echo "✓ Set permissions for {$dir}\n";
        }

        echo "\n";
    }

    /**
     * Create default .env file
     */
    private function createDefaultEnv()
    {
        $env_content = file_get_contents('env');
        
        // Update with SmartFlo specific settings
        $env_content = str_replace(
            'app.baseURL = \'\'',
            'app.baseURL = \'http://localhost:8080/\'',
            $env_content
        );
        
        $env_content .= "\n\n# SmartFlo Authentication Settings\n";
        $env_content .= "database.default.database = smartflo_auth\n";
        $env_content .= "jwt.secret = " . bin2hex(random_bytes(32)) . "\n";
        $env_content .= "jwt.algorithm = HS256\n";
        $env_content .= "jwt.expiration = 3600\n";

        file_put_contents('.env', $env_content);
        echo "✓ Created .env file with default settings\n";
    }

    /**
     * Display installation results
     */
    private function displayResults()
    {
        echo "===========================================\n";
        echo "Installation Results\n";
        echo "===========================================\n\n";

        if (empty($this->errors)) {
            echo "🎉 Installation completed successfully!\n\n";
            
            echo "Next steps:\n";
            echo "1. Start the development server: php spark serve\n";
            echo "2. Visit http://localhost:8080 in your browser\n";
            echo "3. Login with admin/Admin@123\n";
            echo "4. Change the default admin password\n";
            echo "5. Configure email settings in .env for password reset\n\n";
            
            if (!empty($this->warnings)) {
                echo "⚠️  Warnings:\n";
                foreach ($this->warnings as $warning) {
                    echo "   - {$warning}\n";
                }
                echo "\n";
            }
            
        } else {
            echo "❌ Installation failed with errors:\n";
            foreach ($this->errors as $error) {
                echo "   - {$error}\n";
            }
            echo "\nPlease fix these errors and run the installer again.\n\n";
        }

        echo "For more information, see README.md and SECURITY.md\n";
        echo "===========================================\n";
    }
}

// Run installer if called directly
if (php_sapi_name() === 'cli' || defined('INSTALL_MODE')) {
    $installer = new SmartFloInstaller();
    $installer->install();
}
