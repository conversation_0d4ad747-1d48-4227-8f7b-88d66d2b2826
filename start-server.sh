#!/bin/bash

# SmartFlo 2.0 Development Server Starter
# Usage: ./start-server.sh [IP] [PORT]

# Default values
DEFAULT_IP="localhost"
DEFAULT_PORT="8080"

# Get IP and port from arguments or use defaults
IP=${1:-$DEFAULT_IP}
PORT=${2:-$DEFAULT_PORT}

echo "🚀 Starting SmartFlo 2.0 Development Server"
echo "==========================================="
echo ""
echo "Server will be available at: http://$IP:$PORT"
echo ""
echo "📝 Default login credentials:"
echo "   Username: admin"
echo "   Password: Admin@123"
echo ""
echo "🔗 Quick links:"
echo "   Dashboard:    http://$IP:$PORT/dashboard"
echo "   File Manager: http://$IP:$PORT/blob/manager"
echo "   Admin Panel:  http://$IP:$PORT/admin/panel"
echo ""
echo "Press Ctrl+C to stop the server"
echo "=================================="
echo ""

# Navigate to project directory (current directory)
# cd "/Users/<USER>/Desktop/aug/SmartFlo 2.0"

# Start the server
php spark serve --host $IP --port $PORT
