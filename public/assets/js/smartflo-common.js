/**
 * SmartFlo Common JavaScript
 * Handles navigation, user interactions, and common functionality
 */

// Global state
let userDropdownOpen = false;
let notificationTrayOpen = false;
let pullToRefreshEnabled = true;
let isRefreshing = false;
let startY = 0;
let currentY = 0;
let pullDistance = 0;
let refreshThreshold = 80;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * Initialize the application
 */
function initializeApp() {
    // Auto-hide alerts after 5 seconds
    autoHideAlerts();

    // Initialize click outside handlers
    initializeClickOutsideHandlers();

    // Initialize PWA features
    initializePWA();

    // Initialize touch interactions
    initializeTouchInteractions();

    // Initialize pull-to-refresh
    initializePullToRefresh();

    // Initialize real-time updates
    initializeRealTimeUpdates();

    // Initialize sidebar state
    initializeSidebarState();
}

/**
 * Toggle sidebar on mobile
 */
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
        sidebar.classList.toggle('show');
    }
}

/**
 * Toggle user menu dropdown
 */
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    if (dropdown) {
        userDropdownOpen = !userDropdownOpen;
        
        if (userDropdownOpen) {
            dropdown.classList.add('show');
            // Close notifications if open
            if (notificationTrayOpen) {
                toggleNotifications();
            }
        } else {
            dropdown.classList.remove('show');
        }
    }
}

/**
 * Toggle notifications (Simplified)
 */
function toggleNotifications() {
    const tray = document.getElementById('notificationTray');
    if (!tray) return;

    notificationTrayOpen = !notificationTrayOpen;

    if (notificationTrayOpen) {
        tray.classList.add('show');
        loadSimpleNotifications();
        // Close user menu if open
        if (userDropdownOpen) {
            toggleUserMenu();
        }
        // Auto-close after 5 seconds
        setTimeout(() => {
            if (notificationTrayOpen) {
                toggleNotifications();
            }
        }, 5000);
        // Add click outside listener
        setTimeout(() => {
            document.addEventListener('click', closeNotificationTray);
        }, 100);
    } else {
        tray.classList.remove('show');
        document.removeEventListener('click', closeNotificationTray);
    }
}

/**
 * Close notification tray when clicking outside
 */
function closeNotificationTray(e) {
    const tray = document.getElementById('notificationTray');
    const button = document.querySelector('[onclick="toggleNotifications()"]');

    if (tray && !tray.contains(e.target) && !button?.contains(e.target)) {
        tray.classList.remove('show');
        notificationTrayOpen = false;
        document.removeEventListener('click', closeNotificationTray);
    }
}

/**
 * Load simple notifications
 */
function loadSimpleNotifications() {
    const listEl = document.getElementById('notificationList');
    if (!listEl) return;

    // Load real notifications from API
    fetch('/api/recent-notifications')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data.length > 0) {
                renderSimpleNotifications(data.data);
            } else {
                showNotificationMessage('No new notifications');
            }
        })
        .catch(error => {
            console.error('Error loading notifications:', error);
            showNotificationMessage('Unable to load notifications');
        });
}

/**
 * Render simple notifications
 */
function renderSimpleNotifications(notifications) {
    const listEl = document.getElementById('notificationList');
    if (!listEl) return;

    if (notifications.length === 0) {
        listEl.innerHTML = `
            <div class="notification-empty">
                <i class="fas fa-bell-slash"></i>
                <p>No notifications</p>
            </div>
        `;
        return;
    }

    const notificationHTML = notifications.map(notification => `
        <div class="notification-item ${!notification.is_read ? 'unread' : ''}" onclick="markSimpleAsRead(${notification.id})">
            <div class="notification-title">${notification.title}</div>
            <div class="notification-message">${notification.message}</div>
            <div class="notification-time">
                <i class="fas fa-clock"></i>
                ${notification.time_ago}
            </div>
        </div>
    `).join('');

    listEl.innerHTML = notificationHTML;
}

/**
 * Get notification icon based on type
 */
function getNotificationIcon(type) {
    const icons = {
        'info': 'info-circle',
        'success': 'check-circle',
        'warning': 'exclamation-triangle',
        'error': 'times-circle',
        'system': 'cog'
    };
    return icons[type] || 'bell';
}

/**
 * Get notification color based on type
 */
function getNotificationColor(type) {
    const colors = {
        'info': '#3b82f6',
        'success': '#10b981',
        'warning': '#f59e0b',
        'error': '#ef4444',
        'system': '#6b7280'
    };
    return colors[type] || '#6b7280';
}

/**
 * Mark simple notification as read
 */
function markSimpleAsRead(id) {
    // Simple client-side marking as read
    const item = document.querySelector(`[onclick="markSimpleAsRead(${id})"]`);
    if (item) {
        item.classList.remove('unread');
    }
}

/**
 * Update notification badge
 */
function updateNotificationBadge(count) {
    const badges = document.querySelectorAll('.notification-badge');
    badges.forEach(badge => {
        if (count > 0) {
            badge.textContent = count > 99 ? '99+' : count;
            badge.style.display = 'inline-block';
        } else {
            badge.style.display = 'none';
        }
    });
}

/**
 * Show simple notification message
 */
function showNotificationMessage(message) {
    const listEl = document.getElementById('notificationList');
    if (!listEl) return;

    listEl.innerHTML = `
        <div class="notification-empty">
            <i class="fas fa-info-circle"></i>
            <p>${message}</p>
        </div>
    `;
}

/**
 * Mobile functions
 */
function showNotifications() {
    toggleNotifications();
}

function showUserMenu() {
    toggleUserMenu();
}

/**
 * Toggle sidebar (mobile only)
 */
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');

    if (sidebar && window.innerWidth <= 991) {
        // Mobile: Toggle show/hide
        sidebar.classList.toggle('show');
    }
}

/**
 * Initialize sidebar state - always start in mini mode on desktop
 */
function initializeSidebarState() {
    if (window.innerWidth > 991) {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.querySelector('.main-content');

        if (sidebar) {
            // Always start in mini mode on desktop
            sidebar.classList.add('mini');
            if (mainContent) {
                mainContent.classList.add('sidebar-mini');
            }

            // Add hover listeners for auto-expand
            setupSidebarHoverBehavior(sidebar, mainContent);
        }
    }
}

/**
 * Setup hover behavior for sidebar auto-expand
 */
function setupSidebarHoverBehavior(sidebar, mainContent) {
    let hoverTimeout;

    // Expand on hover
    sidebar.addEventListener('mouseenter', function() {
        clearTimeout(hoverTimeout);
        if (sidebar.classList.contains('mini')) {
            sidebar.classList.add('hover-expanded');
        }
    });

    // Collapse on mouse leave with delay
    sidebar.addEventListener('mouseleave', function() {
        hoverTimeout = setTimeout(() => {
            sidebar.classList.remove('hover-expanded');
        }, 300); // Small delay to prevent flickering
    });
}

/**
 * Auto-hide alerts after 5 seconds
 */
function autoHideAlerts() {
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            if (typeof bootstrap !== 'undefined' && bootstrap.Alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        });
    }, 5000);
}

/**
 * Initialize click outside handlers
 */
function initializeClickOutsideHandlers() {
    document.addEventListener('click', function(e) {
        const sidebar = document.getElementById('sidebar');
        const mobileHeader = document.querySelector('.mobile-header');
        const userDropdown = document.getElementById('userDropdown');
        const userMenu = document.querySelector('.user-menu');

        // Close sidebar when clicking outside on mobile
        if (window.innerWidth <= 991 &&
            sidebar &&
            !sidebar.contains(e.target) &&
            !mobileHeader?.contains(e.target) &&
            sidebar.classList.contains('show')) {
            sidebar.classList.remove('show');
        }

        // Close user dropdown when clicking outside
        if (userDropdownOpen &&
            userDropdown &&
            !userDropdown.contains(e.target) &&
            !userMenu?.contains(e.target)) {
            toggleUserMenu();
        }
    });
}

/**
 * Initialize PWA features
 */
function initializePWA() {
    // Register service worker if available
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('ServiceWorker registration successful');
            })
            .catch(function(err) {
                console.log('ServiceWorker registration failed');
            });
    }

    // Handle install prompt
    let deferredPrompt;
    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;
        showInstallPrompt();
    });

    function showInstallPrompt() {
        // Show install button or prompt
        const installBtn = document.getElementById('installBtn');
        if (installBtn) {
            installBtn.style.display = 'block';
            installBtn.addEventListener('click', () => {
                if (deferredPrompt) {
                    deferredPrompt.prompt();
                    deferredPrompt.userChoice.then((choiceResult) => {
                        deferredPrompt = null;
                        installBtn.style.display = 'none';
                    });
                }
            });
        }
    }
}

/**
 * Initialize touch interactions
 */
function initializeTouchInteractions() {
    // Add touch feedback to buttons
    const buttons = document.querySelectorAll('.btn, .nav-link, .bottom-nav-item, .header-btn');

    buttons.forEach(button => {
        button.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.95)';
        });

        button.addEventListener('touchend', function() {
            this.style.transform = '';
        });

        button.addEventListener('touchcancel', function() {
            this.style.transform = '';
        });
    });
}

/**
 * Initialize pull-to-refresh functionality
 */
function initializePullToRefresh() {
    if (!pullToRefreshEnabled || !('ontouchstart' in window)) {
        return;
    }

    const content = document.querySelector('.content') || document.body;
    let refreshIndicator = null;

    // Create refresh indicator
    function createRefreshIndicator() {
        if (refreshIndicator) return;

        refreshIndicator = document.createElement('div');
        refreshIndicator.className = 'pull-refresh-indicator';
        refreshIndicator.innerHTML = `
            <div class="refresh-spinner">
                <i class="fas fa-arrow-down refresh-arrow"></i>
                <i class="fas fa-spinner fa-spin refresh-loading" style="display: none;"></i>
            </div>
            <span class="refresh-text">Pull to refresh</span>
        `;

        // Add styles
        refreshIndicator.style.cssText = `
            position: fixed;
            top: -80px;
            left: 0;
            right: 0;
            height: 80px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: transform 0.3s ease;
            border-bottom: 1px solid #e2e8f0;
            color: #6366f1;
            font-weight: 500;
            font-size: 14px;
        `;

        document.body.appendChild(refreshIndicator);
    }

    // Touch event handlers
    function handleTouchStart(e) {
        if (isRefreshing || window.scrollY > 0) return;

        startY = e.touches[0].clientY;
        createRefreshIndicator();
    }

    function handleTouchMove(e) {
        if (isRefreshing || window.scrollY > 0 || !refreshIndicator) return;

        currentY = e.touches[0].clientY;
        pullDistance = Math.max(0, (currentY - startY) * 0.5);

        if (pullDistance > 0) {
            e.preventDefault();

            const progress = Math.min(pullDistance / refreshThreshold, 1);
            const translateY = Math.min(pullDistance - 80, 0);

            refreshIndicator.style.transform = `translateY(${translateY + 80}px)`;

            const arrow = refreshIndicator.querySelector('.refresh-arrow');
            const text = refreshIndicator.querySelector('.refresh-text');

            if (pullDistance >= refreshThreshold) {
                arrow.style.transform = 'rotate(180deg)';
                text.textContent = 'Release to refresh';
                refreshIndicator.style.color = '#10b981';
            } else {
                arrow.style.transform = 'rotate(0deg)';
                text.textContent = 'Pull to refresh';
                refreshIndicator.style.color = '#6366f1';
            }
        }
    }

    function handleTouchEnd(e) {
        if (isRefreshing || !refreshIndicator) return;

        if (pullDistance >= refreshThreshold) {
            triggerRefresh();
        } else {
            resetRefreshIndicator();
        }

        pullDistance = 0;
    }

    function triggerRefresh() {
        if (isRefreshing) return;

        isRefreshing = true;

        const arrow = refreshIndicator.querySelector('.refresh-arrow');
        const loading = refreshIndicator.querySelector('.refresh-loading');
        const text = refreshIndicator.querySelector('.refresh-text');

        arrow.style.display = 'none';
        loading.style.display = 'block';
        text.textContent = 'Refreshing...';

        refreshIndicator.style.transform = 'translateY(0px)';
        refreshIndicator.style.color = '#6366f1';

        // Perform refresh
        setTimeout(() => {
            window.location.reload();
        }, 500);
    }

    function resetRefreshIndicator() {
        if (!refreshIndicator) return;

        refreshIndicator.style.transform = 'translateY(-80px)';

        setTimeout(() => {
            if (refreshIndicator && refreshIndicator.parentNode) {
                refreshIndicator.parentNode.removeChild(refreshIndicator);
                refreshIndicator = null;
            }
        }, 300);
    }

    // Add event listeners
    content.addEventListener('touchstart', handleTouchStart, { passive: false });
    content.addEventListener('touchmove', handleTouchMove, { passive: false });
    content.addEventListener('touchend', handleTouchEnd, { passive: false });
}

/**
 * Initialize real-time updates
 */
function initializeRealTimeUpdates() {
    // Check for user status changes every 30 seconds
    setInterval(checkUserStatus, 30000);

    // Listen for storage events (for multi-tab sync)
    window.addEventListener('storage', handleStorageChange);

    // Listen for visibility change to refresh when app becomes visible
    document.addEventListener('visibilitychange', handleVisibilityChange);
}

/**
 * Check user status and handle deactivation
 */
function checkUserStatus() {
    makeRequest('/auth/status')
        .then(response => {
            if (!response.success || response.data.status === 'inactive') {
                handleUserDeactivation();
            }
        })
        .catch(error => {
            console.log('Status check failed:', error);
        });
}

/**
 * Handle user deactivation
 */
function handleUserDeactivation() {
    showToast('Your account has been deactivated. You will be logged out.', 'warning');

    setTimeout(() => {
        window.location.href = '/auth/logout';
    }, 2000);
}

/**
 * Handle storage changes for multi-tab sync
 */
function handleStorageChange(e) {
    if (e.key === 'user_deactivated') {
        handleUserDeactivation();
    }

    if (e.key === 'force_logout') {
        window.location.href = '/auth/logout';
    }
}

/**
 * Handle visibility change
 */
function handleVisibilityChange() {
    if (!document.hidden) {
        // App became visible, check status
        checkUserStatus();
    }
}

/**
 * Utility function to show loading state
 */
function showLoading(element) {
    if (element) {
        element.disabled = true;
        const originalText = element.innerHTML;
        element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
        element.dataset.originalText = originalText;
    }
}

/**
 * Utility function to hide loading state
 */
function hideLoading(element) {
    if (element && element.dataset.originalText) {
        element.disabled = false;
        element.innerHTML = element.dataset.originalText;
        delete element.dataset.originalText;
    }
}

/**
 * Utility function to show toast notifications
 */
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}

/**
 * Utility function for AJAX requests with proper error handling
 */
function makeRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    };

    const finalOptions = { ...defaultOptions, ...options };

    return fetch(url, finalOptions)
        .then(response => {
            if (!response.ok) {
                if (response.status === 403) {
                    // CSRF token expired, try to refresh
                    refreshCSRFToken();
                    throw new Error('Security token expired. Please try again.');
                }
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .catch(error => {
            console.error('Request failed:', error);
            if (error.message.includes('Security token expired')) {
                showToast('Security token expired. Please try again.', 'warning');
            } else {
                showToast('An error occurred. Please try again.', 'danger');
            }
            throw error;
        });
}

/**
 * Global CSRF token refresh function
 */
function refreshCSRFToken() {
    return fetch('/auth/csrf-token', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.csrf_token) {
            // Update meta tag
            const metaTag = document.querySelector('meta[name="csrf-token"]');
            if (metaTag) {
                metaTag.setAttribute('content', data.csrf_token);
            }
            // Update any hidden inputs
            const hiddenInputs = document.querySelectorAll('input[name="csrf_token"]');
            hiddenInputs.forEach(input => {
                input.value = data.csrf_token;
            });
            return data.csrf_token;
        }
        throw new Error('Failed to get new token');
    })
    .catch(error => {
        console.error('Failed to refresh CSRF token:', error);
        throw error;
    });
}

// Global AJAX interceptor for CSRF token handling
(function() {
    let originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
        // Add CSRF token to headers if not present
        if (!options.headers) {
            options.headers = {};
        }
        
        // Get CSRF token from meta tag or hidden input
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                         document.querySelector('input[name="csrf_token"]')?.value;
        
        if (csrfToken) {
            options.headers['X-CSRF-TOKEN'] = csrfToken;
        }
        
        // Add X-Requested-With header for AJAX detection
        options.headers['X-Requested-With'] = 'XMLHttpRequest';
        
        return originalFetch(url, options)
            .then(response => {
                if (response.status === 403) {
                    // CSRF token expired, try to refresh
                    return fetch('/auth/csrf-token', {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(res => res.json())
                    .then(data => {
                        if (data.success && data.csrf_token) {
                            // Update CSRF token in meta tag and hidden inputs
                            const metaTag = document.querySelector('meta[name="csrf-token"]');
                            if (metaTag) {
                                metaTag.setAttribute('content', data.csrf_token);
                            }
                            document.querySelectorAll('input[name="csrf_token"]').forEach(input => {
                                input.value = data.csrf_token;
                            });

                            // Retry the original request with new token
                            options.headers['X-CSRF-TOKEN'] = data.csrf_token;
                            return originalFetch(url, options);
                        }
                        throw new Error('Failed to refresh CSRF token');
                    });
                }
                return response;
            });
    };
})();

// Export functions for global use
window.SmartFlo = {
    toggleSidebar,
    toggleUserMenu,
    toggleNotifications,
    showNotifications,
    showUserMenu,
    showLoading,
    hideLoading,
    showToast,
    makeRequest
};
