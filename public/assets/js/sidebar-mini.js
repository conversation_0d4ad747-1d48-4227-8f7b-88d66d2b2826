/**
 * SmartFlo Sidebar Mini - Universal Implementation
 * Works consistently across all pages
 */

class SidebarMini {
    constructor() {
        this.sidebar = null;
        this.mainContent = null;
        this.isInitialized = false;
        this.hoverTimeout = null;
        this.expandDelay = 200;
        this.collapseDelay = 300;
        
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        this.sidebar = document.getElementById('sidebar');
        this.mainContent = document.querySelector('.main-content');

        if (!this.sidebar) {
            console.warn('Sidebar element not found');
            return;
        }

        // Only initialize on desktop
        if (window.innerWidth > 991) {
            this.initializeMiniMode();
            this.setupEventListeners();
            this.isInitialized = true;
        }

        // Handle window resize
        window.addEventListener('resize', () => this.handleResize());
    }

    initializeMiniMode() {
        // Add mini class to sidebar
        this.sidebar.classList.add('mini');
        
        // Adjust main content margin
        if (this.mainContent) {
            this.mainContent.classList.add('sidebar-mini');
        }

        console.log('✅ Sidebar mini mode initialized');
    }

    setupEventListeners() {
        if (!this.sidebar) return;

        // Mouse enter - expand with delay
        this.sidebar.addEventListener('mouseenter', () => {
            clearTimeout(this.hoverTimeout);
            this.hoverTimeout = setTimeout(() => {
                this.expand();
            }, this.expandDelay);
        });

        // Mouse leave - collapse with delay
        this.sidebar.addEventListener('mouseleave', () => {
            clearTimeout(this.hoverTimeout);
            this.hoverTimeout = setTimeout(() => {
                this.collapse();
            }, this.collapseDelay);
        });

        // Prevent collapse when hovering over expanded content
        this.sidebar.addEventListener('mousemove', (e) => {
            if (this.sidebar.classList.contains('hover-expanded')) {
                clearTimeout(this.hoverTimeout);
            }
        });
    }

    expand() {
        if (!this.sidebar || !this.sidebar.classList.contains('mini')) return;
        
        this.sidebar.classList.add('hover-expanded');
        console.log('🔄 Sidebar expanded');
    }

    collapse() {
        if (!this.sidebar) return;
        
        this.sidebar.classList.remove('hover-expanded');
        console.log('🔄 Sidebar collapsed');
    }

    handleResize() {
        const isDesktop = window.innerWidth > 991;
        
        if (isDesktop && !this.isInitialized) {
            // Initialize mini mode on desktop
            this.initializeMiniMode();
            this.setupEventListeners();
            this.isInitialized = true;
        } else if (!isDesktop && this.isInitialized) {
            // Disable mini mode on mobile
            this.disableMiniMode();
            this.isInitialized = false;
        }
    }

    disableMiniMode() {
        if (!this.sidebar) return;

        this.sidebar.classList.remove('mini', 'hover-expanded');
        
        if (this.mainContent) {
            this.mainContent.classList.remove('sidebar-mini');
        }

        clearTimeout(this.hoverTimeout);
        console.log('❌ Sidebar mini mode disabled');
    }

    // Public methods for manual control
    toggle() {
        if (this.sidebar.classList.contains('mini')) {
            this.disableMiniMode();
        } else {
            this.initializeMiniMode();
        }
    }

    forceExpand() {
        clearTimeout(this.hoverTimeout);
        this.expand();
    }

    forceCollapse() {
        clearTimeout(this.hoverTimeout);
        this.collapse();
    }
}

// Auto-initialize when script loads
const sidebarMini = new SidebarMini();

// Export for global access
window.SidebarMini = SidebarMini;
window.sidebarMini = sidebarMini;

// Legacy compatibility functions
window.initializeSidebarState = () => {
    console.log('Legacy initializeSidebarState called - using new SidebarMini');
    if (!sidebarMini.isInitialized && window.innerWidth > 991) {
        sidebarMini.setup();
    }
};

window.toggleSidebar = () => {
    if (window.innerWidth <= 991) {
        // Mobile toggle
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.toggle('show');
        }
    } else {
        // Desktop toggle mini mode
        sidebarMini.toggle();
    }
};

console.log('🚀 SidebarMini script loaded');
