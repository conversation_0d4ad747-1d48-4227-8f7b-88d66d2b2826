<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Project Creation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 8px; margin-bottom: 10px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        .result { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
    </style>
</head>
<body>
    <h1>Simple Project Creation Test</h1>
    
    <form id="testForm">
        <div class="form-group">
            <label>Project ID:</label>
            <input type="text" name="project_id" value="TEST-001" required>
        </div>
        
        <div class="form-group">
            <label>Project Name:</label>
            <input type="text" name="project_name" value="Test Project" required>
        </div>
        
        <div class="form-group">
            <label>Client Name:</label>
            <input type="text" name="client_name" value="Test Client" required>
        </div>
        
        <div class="form-group">
            <label>Client Mobile:</label>
            <input type="text" name="client_mobile" value="+1234567890">
        </div>
        
        <div class="form-group">
            <label>Location:</label>
            <input type="text" name="location" value="Test Location" required>
        </div>
        
        <div class="form-group">
            <label>Description:</label>
            <textarea name="description">Test project description</textarea>
        </div>
        
        <div class="form-group">
            <label>Start Date:</label>
            <input type="date" name="start_date" required>
        </div>
        
        <div class="form-group">
            <label>Target Completion:</label>
            <input type="date" name="target_completion" required>
        </div>
        
        <div class="form-group">
            <label>Task Category:</label>
            <select name="task_category[]">
                <option value="office">Office</option>
                <option value="site">Site</option>
                <option value="design">Design</option>
                <option value="management">Management</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Task Type:</label>
            <select name="task_type[]">
                <option value="planning">Planning</option>
                <option value="supervision">Supervision</option>
                <option value="design">Design</option>
                <option value="management">Management</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Assigned To (User ID):</label>
            <select name="assigned_to[]">
                <option value="1">Admin (ID: 1)</option>
            </select>
        </div>
        
        <button type="submit">Create Project</button>
    </form>
    
    <div id="result"></div>

    <script>
        // Set default dates
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            const futureDate = new Date();
            futureDate.setDate(futureDate.getDate() + 30);
            const future = futureDate.toISOString().split('T')[0];
            
            document.querySelector('input[name="start_date"]').value = today;
            document.querySelector('input[name="target_completion"]').value = future;
        });

        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="result">Submitting...</div>';
            
            const formData = new FormData(this);
            
            // Log form data
            console.log('Form data:');
            for (let [key, value] of formData.entries()) {
                console.log(key, value);
            }
            
            try {
                const response = await fetch('http://192.168.1.83:8080/projects/create', {
                    method: 'POST',
                    body: formData,
                    credentials: 'include',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);
                
                const text = await response.text();
                console.log('Response text:', text);
                
                if (response.ok) {
                    try {
                        const data = JSON.parse(text);
                        if (data.success) {
                            resultDiv.innerHTML = `
                                <div class="result success">
                                    <h3>✅ Success!</h3>
                                    <p>${data.message}</p>
                                    <p>Project ID: ${data.project_id}</p>
                                </div>
                            `;
                        } else {
                            resultDiv.innerHTML = `
                                <div class="result error">
                                    <h3>❌ Failed</h3>
                                    <p>${data.message}</p>
                                    <pre>${JSON.stringify(data, null, 2)}</pre>
                                </div>
                            `;
                        }
                    } catch (e) {
                        resultDiv.innerHTML = `
                            <div class="result error">
                                <h3>❌ Invalid JSON Response</h3>
                                <p>Response was not valid JSON</p>
                                <pre>${text}</pre>
                            </div>
                        `;
                    }
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ HTTP Error ${response.status}</h3>
                            <pre>${text}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Network Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
