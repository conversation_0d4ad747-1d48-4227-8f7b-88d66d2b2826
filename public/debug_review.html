<!DOCTYPE html>
<html>
<head>
    <title>Send for Client Review - Debug Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        pre { 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 4px;
            overflow-x: auto; 
            font-size: 12px;
        }
        button { 
            padding: 10px 20px; 
            margin: 5px; 
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            vertical-align: middle;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Send for Client Review - Debug Test</h1>
        <p>This page will test the "Send for Client Review" functionality and show you exactly what's happening.</p>
        
        <div class="test-section">
            <h2>📋 Test Results</h2>
            <div id="test-results">
                <div class="loading"></div> Running tests...
            </div>
        </div>
        
        <div class="test-section">
            <h2>🧪 Manual Test</h2>
            <p>Click this button to test the updateTaskManagerStatus endpoint directly:</p>
            <button class="btn-primary" onclick="testEndpointManually()">Test Task 110 Update</button>
            <div id="manual-test-results"></div>
        </div>
        
        <div class="test-section">
            <h2>📊 Database Check</h2>
            <p>Check the current status of Task 110 in the database:</p>
            <button class="btn-success" onclick="checkDatabaseStatus()">Check Database</button>
            <div id="database-results"></div>
        </div>
        
        <div class="test-section">
            <h2>🔧 Instructions</h2>
            <div id="instructions">
                <p>Based on the test results above, follow these steps:</p>
                <ol id="instruction-list">
                    <li>Loading instructions...</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // Auto-run tests when page loads
        window.onload = function() {
            runAllTests();
        };
        
        async function runAllTests() {
            const resultsDiv = document.getElementById('test-results');
            let results = [];
            
            // Test 1: Check if we can access the projects page
            results.push('<h3>Test 1: Authentication Check</h3>');
            try {
                const response = await fetch('/projects', {
                    method: 'GET',
                    headers: { 'X-Requested-With': 'XMLHttpRequest' }
                });
                
                if (response.ok) {
                    results.push('<div><span class="status-indicator status-success"></span><span class="success">✅ Can access projects page (authenticated)</span></div>');
                    
                    // Try to extract user info from the response
                    const html = await response.text();
                    const userMatch = html.match(/user_id["\']:\s*["\']?(\d+)["\']?/);
                    const roleMatch = html.match(/roles["\']:\s*["\']([^"\']+)["\']?/);
                    
                    if (userMatch) {
                        results.push(`<div>   - User ID: ${userMatch[1]}</div>`);
                    }
                    if (roleMatch) {
                        results.push(`<div>   - User Role: <strong>${roleMatch[1]}</strong></div>`);
                        
                        if (roleMatch[1] === 'admin' || roleMatch[1] === 'manager') {
                            results.push('<div><span class="status-indicator status-success"></span><span class="success">✅ User has manager privileges</span></div>');
                        } else {
                            results.push('<div><span class="status-indicator status-error"></span><span class="error">❌ User does NOT have manager privileges</span></div>');
                        }
                    }
                } else {
                    results.push('<div><span class="status-indicator status-error"></span><span class="error">❌ Cannot access projects page (not authenticated)</span></div>');
                }
            } catch (error) {
                results.push(`<div><span class="status-indicator status-error"></span><span class="error">❌ Error checking authentication: ${error.message}</span></div>`);
            }
            
            // Test 2: Check CSRF token
            results.push('<h3>Test 2: CSRF Token Check</h3>');
            try {
                const response = await fetch('/projects', {
                    method: 'GET',
                    headers: { 'X-Requested-With': 'XMLHttpRequest' }
                });
                
                if (response.ok) {
                    const html = await response.text();
                    const metaMatch = html.match(/<meta name="csrf-token" content="([^"]+)"/);
                    const inputMatch = html.match(/name="csrf_token" value="([^"]+)"/);
                    
                    if (metaMatch || inputMatch) {
                        const token = (metaMatch && metaMatch[1]) || (inputMatch && inputMatch[1]);
                        results.push('<div><span class="status-indicator status-success"></span><span class="success">✅ CSRF token found</span></div>');
                        results.push(`<div>   - Token: ${token.substring(0, 20)}...</div>`);
                        window.testCsrfToken = token; // Store for later use
                    } else {
                        results.push('<div><span class="status-indicator status-error"></span><span class="error">❌ No CSRF token found</span></div>');
                    }
                } else {
                    results.push('<div><span class="status-indicator status-error"></span><span class="error">❌ Cannot get CSRF token</span></div>');
                }
            } catch (error) {
                results.push(`<div><span class="status-indicator status-error"></span><span class="error">❌ Error getting CSRF token: ${error.message}</span></div>`);
            }
            
            // Test 3: Check if endpoint exists
            results.push('<h3>Test 3: Endpoint Accessibility</h3>');
            try {
                const response = await fetch('/projects/updateTaskManagerStatus/110', {
                    method: 'HEAD',
                    headers: { 'X-Requested-With': 'XMLHttpRequest' }
                });
                
                if (response.status === 200 || response.status === 405) {
                    results.push('<div><span class="status-indicator status-success"></span><span class="success">✅ Endpoint exists and is accessible</span></div>');
                } else if (response.status === 404) {
                    results.push('<div><span class="status-indicator status-error"></span><span class="error">❌ Endpoint not found (404)</span></div>');
                } else if (response.status === 401 || response.status === 403) {
                    results.push('<div><span class="status-indicator status-warning"></span><span class="warning">⚠️ Endpoint exists but access denied</span></div>');
                } else {
                    results.push(`<div><span class="status-indicator status-warning"></span><span class="warning">⚠️ Unexpected response: ${response.status}</span></div>`);
                }
            } catch (error) {
                results.push(`<div><span class="status-indicator status-error"></span><span class="error">❌ Error checking endpoint: ${error.message}</span></div>`);
            }
            
            resultsDiv.innerHTML = results.join('');
            generateInstructions();
        }
        
        async function testEndpointManually() {
            const resultsDiv = document.getElementById('manual-test-results');
            resultsDiv.innerHTML = '<div class="loading"></div> Testing endpoint...';
            
            if (!window.testCsrfToken) {
                resultsDiv.innerHTML = '<div><span class="status-indicator status-error"></span><span class="error">❌ No CSRF token available</span></div>';
                return;
            }
            
            try {
                const formData = new FormData();
                formData.append('status', 'sent_for_review');
                formData.append('payment_amount', '7500');
                formData.append('payment_status', 'paid');
                formData.append('payment_account', 'debug_test_account');
                formData.append('notes', 'Debug test from visual page - ' + new Date().toISOString());
                formData.append('csrf_token', window.testCsrfToken);
                
                const response = await fetch('/projects/updateTaskManagerStatus/110', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': window.testCsrfToken
                    }
                });
                
                const responseText = await response.text();
                
                let result = `<h4>Response Details:</h4>`;
                result += `<div><strong>Status:</strong> ${response.status} ${response.statusText}</div>`;
                result += `<div><strong>Response:</strong></div>`;
                result += `<pre>${responseText}</pre>`;
                
                try {
                    const jsonData = JSON.parse(responseText);
                    if (jsonData.success) {
                        result += '<div><span class="status-indicator status-success"></span><span class="success">🎉 SUCCESS: Request worked!</span></div>';
                        result += `<div><strong>Message:</strong> ${jsonData.message}</div>`;
                    } else {
                        result += '<div><span class="status-indicator status-error"></span><span class="error">❌ FAILED</span></div>';
                        result += `<div><strong>Error:</strong> ${jsonData.message}</div>`;
                    }
                } catch (e) {
                    if (response.ok) {
                        result += '<div><span class="status-indicator status-warning"></span><span class="warning">⚠️ Response is not JSON but request succeeded</span></div>';
                    } else {
                        result += '<div><span class="status-indicator status-error"></span><span class="error">❌ Request failed and response is not JSON</span></div>';
                    }
                }
                
                resultsDiv.innerHTML = result;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div><span class="status-indicator status-error"></span><span class="error">❌ Network error: ${error.message}</span></div>`;
            }
        }
        
        async function checkDatabaseStatus() {
            const resultsDiv = document.getElementById('database-results');
            resultsDiv.innerHTML = '<div class="loading"></div> Checking database...';
            
            try {
                // This would need a backend endpoint to check database status
                // For now, we'll just show a message
                resultsDiv.innerHTML = `
                    <div><span class="status-indicator status-warning"></span><span class="warning">⚠️ Database check requires backend endpoint</span></div>
                    <div>To check database manually:</div>
                    <div>1. Go to phpMyAdmin or database tool</div>
                    <div>2. Check table: project_tasks</div>
                    <div>3. Look for task ID 110</div>
                    <div>4. Check fields: task_manager_status, payment_amount, payment_status</div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `<div><span class="status-indicator status-error"></span><span class="error">❌ Error: ${error.message}</span></div>`;
            }
        }
        
        function generateInstructions() {
            const instructionsList = document.getElementById('instruction-list');
            
            const instructions = [
                'Run the "Test Task 110 Update" button above',
                'Check if the test succeeds or fails',
                'If it fails, check the error message',
                'Go to the actual projects page and try the "Send for Review" button',
                'Compare the results between this test and the actual page'
            ];
            
            instructionsList.innerHTML = instructions.map(instruction => `<li>${instruction}</li>`).join('');
        }
    </script>
</body>
</html>
