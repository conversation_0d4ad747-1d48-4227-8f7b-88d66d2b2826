<!DOCTYPE html>
<html>
<head>
    <title>Test Send for Review - Shuhaib Project</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        input, select, textarea { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Send for Review - Shuhaib Project Task 111</h1>
        <p>This page will test the "Send for Review" functionality for the Shuhaib project task.</p>
        
        <div class="test-section">
            <h2>📋 Authentication & CSRF Check</h2>
            <div id="auth-results">
                <div class="info">🔄 Checking authentication and CSRF token...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📝 Payment Details Form</h2>
            <form id="paymentForm">
                <div class="form-group">
                    <label>Payment Amount (₹)</label>
                    <input type="number" id="paymentAmount" value="4500" min="0" step="0.01" required>
                </div>
                <div class="form-group">
                    <label>Payment Status</label>
                    <select id="paymentStatus">
                        <option value="unpaid">Unpaid</option>
                        <option value="paid" selected>Paid</option>
                        <option value="partial">Partial</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Payment Account</label>
                    <select id="paymentAccount">
                        <option value="">Select Account</option>
                        <option value="bank_account_1" selected>Main Bank Account</option>
                        <option value="bank_account_2">Secondary Account</option>
                        <option value="cash">Cash</option>
                        <option value="online">Online Payment</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Notes</label>
                    <textarea id="paymentNotes" rows="3">Test payment for Shuhaib project - Task 111</textarea>
                </div>
                <div class="form-group">
                    <label>Google Drive Link (Optional)</label>
                    <input type="url" id="googleDriveLink" placeholder="https://drive.google.com/...">
                </div>
            </form>
        </div>
        
        <div class="test-section">
            <h2>🚀 Test Actions</h2>
            <button class="btn-primary" onclick="testSendForReview()">Test Send for Review (Task 111)</button>
            <button class="btn-success" onclick="checkDatabaseStatus()">Check Database Status</button>
            <div id="test-results"></div>
        </div>
        
        <div class="test-section">
            <h2>📊 Database Status</h2>
            <div id="database-results">
                <div class="info">Click "Check Database Status" to see current task status</div>
            </div>
        </div>
    </div>

    <script>
        let csrfToken = null;
        
        // Auto-run authentication check when page loads
        window.onload = function() {
            checkAuthAndCSRF();
        };
        
        async function checkAuthAndCSRF() {
            const resultsDiv = document.getElementById('auth-results');
            resultsDiv.innerHTML = '<div class="info">🔄 Checking authentication and CSRF token...</div>';
            
            try {
                // Check if we can access the projects page
                const response = await fetch('/projects', {
                    method: 'GET',
                    headers: { 'X-Requested-With': 'XMLHttpRequest' }
                });
                
                if (response.ok) {
                    const html = await response.text();
                    
                    // Extract CSRF token
                    const metaMatch = html.match(/<meta name="csrf-token" content="([^"]+)"/);
                    const inputMatch = html.match(/name="csrf_token" value="([^"]+)"/);
                    
                    csrfToken = (metaMatch && metaMatch[1]) || (inputMatch && inputMatch[1]);
                    
                    if (csrfToken) {
                        resultsDiv.innerHTML = `
                            <div class="success">✅ Authentication successful</div>
                            <div class="success">✅ CSRF token found: ${csrfToken.substring(0, 20)}...</div>
                        `;
                    } else {
                        resultsDiv.innerHTML = `
                            <div class="success">✅ Authentication successful</div>
                            <div class="error">❌ No CSRF token found</div>
                        `;
                    }
                } else {
                    resultsDiv.innerHTML = '<div class="error">❌ Authentication failed - please login first</div>';
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        async function testSendForReview() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="info">🔄 Testing Send for Review...</div>';
            
            if (!csrfToken) {
                resultsDiv.innerHTML = '<div class="error">❌ No CSRF token available. Please refresh the page.</div>';
                return;
            }
            
            // Get form data
            const paymentAmount = document.getElementById('paymentAmount').value;
            const paymentStatus = document.getElementById('paymentStatus').value;
            const paymentAccount = document.getElementById('paymentAccount').value;
            const paymentNotes = document.getElementById('paymentNotes').value;
            const googleDriveLink = document.getElementById('googleDriveLink').value;
            
            // Validate
            if (!paymentAmount || paymentAmount <= 0) {
                resultsDiv.innerHTML = '<div class="error">❌ Please enter a valid payment amount</div>';
                return;
            }
            
            try {
                const formData = new FormData();
                formData.append('status', 'sent_for_review');
                formData.append('payment_amount', paymentAmount);
                formData.append('payment_status', paymentStatus);
                formData.append('payment_account', paymentAccount);
                formData.append('notes', paymentNotes);
                formData.append('csrf_token', csrfToken);
                
                if (googleDriveLink) {
                    formData.append('google_drive_link', googleDriveLink);
                }
                
                console.log('🔄 Sending request to task 111:', {
                    url: '/projects/updateTaskManagerStatus/111',
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    formData: Object.fromEntries(formData.entries())
                });
                
                const response = await fetch('/projects/updateTaskManagerStatus/111', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': csrfToken
                    }
                });
                
                const responseText = await response.text();
                
                let result = `<h4>Response Details:</h4>`;
                result += `<div><strong>Status:</strong> ${response.status} ${response.statusText}</div>`;
                result += `<div><strong>Response:</strong></div>`;
                result += `<pre>${responseText}</pre>`;
                
                try {
                    const jsonData = JSON.parse(responseText);
                    if (jsonData.success) {
                        result += '<div class="success">🎉 SUCCESS: Send for Review worked!</div>';
                        result += `<div><strong>Message:</strong> ${jsonData.message}</div>`;
                        
                        // Auto-check database status
                        setTimeout(checkDatabaseStatus, 1000);
                    } else {
                        result += '<div class="error">❌ FAILED</div>';
                        result += `<div><strong>Error:</strong> ${jsonData.message}</div>`;
                    }
                } catch (e) {
                    if (response.ok) {
                        result += '<div class="warning">⚠️ Response is not JSON but request succeeded</div>';
                    } else {
                        result += '<div class="error">❌ Request failed and response is not JSON</div>';
                    }
                }
                
                resultsDiv.innerHTML = result;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
                console.error('❌ Error:', error);
            }
        }
        
        async function checkDatabaseStatus() {
            const resultsDiv = document.getElementById('database-results');
            resultsDiv.innerHTML = '<div class="info">🔄 Checking database status...</div>';
            
            try {
                // This is a simple way to check - we'll make a request to get project data
                const response = await fetch('/projects/view/104', {
                    method: 'GET',
                    headers: { 'X-Requested-With': 'XMLHttpRequest' }
                });
                
                if (response.ok) {
                    const html = await response.text();
                    
                    // Look for task status indicators in the HTML
                    const hasReviewStatus = html.includes('sent_for_review') || html.includes('Send for Review');
                    const hasPaymentAmount = html.includes('₹4500') || html.includes('4500');
                    
                    let result = '<h4>Database Status Check:</h4>';
                    result += `<div><strong>Response Status:</strong> ${response.status}</div>`;
                    
                    if (hasReviewStatus) {
                        result += '<div class="success">✅ Task appears to be sent for review</div>';
                    } else {
                        result += '<div class="warning">⚠️ Task review status unclear</div>';
                    }
                    
                    if (hasPaymentAmount) {
                        result += '<div class="success">✅ Payment amount appears to be saved</div>';
                    } else {
                        result += '<div class="warning">⚠️ Payment amount not visible</div>';
                    }
                    
                    result += '<div class="info">💡 Check the actual project page: <a href="/projects/view/104" target="_blank">View Shuhaib Project</a></div>';
                    
                    resultsDiv.innerHTML = result;
                } else {
                    resultsDiv.innerHTML = `<div class="error">❌ Failed to check database status: ${response.status}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error checking database: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
