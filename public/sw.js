const CACHE_NAME = 'smartflo-v2.0.3';
const STATIC_CACHE = 'smartflo-static-v2.0.3';
const DYNAMIC_CACHE = 'smartflo-dynamic-v2.0.3';

// Cache invalidation timestamp
let lastCacheInvalidation = Date.now();

// Files to cache immediately for optimal mobile performance
const STATIC_FILES = [
  '/',
  '/auth/login',
  '/dashboard',
  '/manifest.json',
  'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
  'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
  'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
  // Cache critical dashboard assets for offline functionality
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
];

// Files to cache on demand
const DYNAMIC_FILES = [
  '/admin/users',
  '/admin/roles',
  '/admin/system',
  '/auth/change-password'
];

// Network-first routes (always try network first)
const NETWORK_FIRST_ROUTES = [
  '/auth/login',
  '/auth/logout',
  '/auth/change-password',
  '/dashboard/data',
  '/admin/users/data',
  '/admin/roles/data',
  '/projects',
  '/projects/data',
  '/projects/view',
  '/projects/update',
  '/projects/status'
];

// Routes that should never be cached (always fresh)
const NO_CACHE_ROUTES = [
  '/projects/data',
  '/projects/checkUpdates',
  '/dashboard/getData',
  '/auth/status',
  '/api/'
];

// Install event - cache static files
self.addEventListener('install', event => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => {
        console.log('Service Worker: Caching static files');
        return cache.addAll(STATIC_FILES);
      })
      .then(() => {
        console.log('Service Worker: Static files cached');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('Service Worker: Error caching static files', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - handle requests
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip external requests
  if (url.origin !== location.origin) {
    // Cache external resources (CDN files)
    if (url.hostname.includes('cdn.jsdelivr.net') || url.hostname.includes('cdnjs.cloudflare.com')) {
      event.respondWith(cacheFirst(request));
    }
    return;
  }
  
  // Handle different types of requests
  if (isNoCacheRoute(url.pathname)) {
    event.respondWith(networkOnly(request));
  } else if (isNetworkFirstRoute(url.pathname)) {
    event.respondWith(networkFirst(request));
  } else if (isStaticFile(url.pathname)) {
    event.respondWith(cacheFirst(request));
  } else {
    event.respondWith(staleWhileRevalidate(request));
  }
});

// Cache strategies
async function cacheFirst(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.error('Cache first strategy failed:', error);
    return new Response('Offline', { status: 503 });
  }
}

async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.log('Network failed, trying cache:', request.url);
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // Return offline page for navigation requests
    if (request.mode === 'navigate') {
      return createOfflinePage();
    }

    // Return offline JSON response for API requests
    if (request.url.includes('/api/') || request.url.includes('/admin/') || request.url.includes('/auth/')) {
      return new Response(JSON.stringify({
        success: false,
        message: 'No internet connection. Please check your network and try again.',
        offline: true
      }), {
        status: 503,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    return new Response('Offline', { status: 503 });
  }
}

async function staleWhileRevalidate(request) {
  const cache = await caches.open(DYNAMIC_CACHE);
  const cachedResponse = await cache.match(request);
  
  const fetchPromise = fetch(request).then(networkResponse => {
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  }).catch(() => cachedResponse);
  
  return cachedResponse || fetchPromise;
}

// Network only strategy (no caching)
async function networkOnly(request) {
  try {
    return await fetch(request);
  } catch (error) {
    console.error('Network only request failed:', error);
    throw error;
  }
}

// Helper functions
function isNoCacheRoute(pathname) {
  return NO_CACHE_ROUTES.some(route => pathname.startsWith(route));
}

function isNetworkFirstRoute(pathname) {
  return NETWORK_FIRST_ROUTES.some(route => pathname.startsWith(route));
}

function isStaticFile(pathname) {
  return pathname.match(/\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/);
}

function createOfflinePage() {
  const offlineHTML = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
        <title>Offline - SmartFlo</title>
        <meta name="theme-color" content="#6366f1">
        <style>
            :root {
                --primary: #6366f1;
                --error: #ef4444;
                --gray-50: #f8fafc;
                --gray-100: #f1f5f9;
                --gray-600: #475569;
                --gray-800: #1e293b;
                --gray-900: #0f172a;
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
                color: var(--gray-800);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 1rem;
            }

            .offline-container {
                text-align: center;
                max-width: 400px;
                background: white;
                padding: 2rem;
                border-radius: 16px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            }

            .offline-icon {
                width: 80px;
                height: 80px;
                background: var(--error);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 1.5rem;
                color: white;
                font-size: 2rem;
            }

            .offline-title {
                font-size: 1.5rem;
                font-weight: 700;
                color: var(--gray-900);
                margin-bottom: 0.5rem;
            }

            .offline-message {
                color: var(--gray-600);
                margin-bottom: 2rem;
                line-height: 1.6;
            }

            .retry-btn {
                background: var(--primary);
                color: white;
                border: none;
                padding: 0.75rem 1.5rem;
                border-radius: 8px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s;
                margin-right: 0.5rem;
            }

            .retry-btn:hover {
                background: #4f46e5;
                transform: translateY(-1px);
            }

            .home-btn {
                background: var(--gray-100);
                color: var(--gray-700);
                border: none;
                padding: 0.75rem 1.5rem;
                border-radius: 8px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s;
                text-decoration: none;
                display: inline-block;
            }

            .home-btn:hover {
                background: var(--gray-200);
                transform: translateY(-1px);
            }

            .status-indicator {
                margin-top: 1.5rem;
                padding: 0.75rem;
                border-radius: 8px;
                font-size: 0.875rem;
                font-weight: 500;
            }

            .status-offline {
                background: rgba(239, 68, 68, 0.1);
                color: var(--error);
            }

            .status-online {
                background: rgba(16, 185, 129, 0.1);
                color: #10b981;
            }
        </style>
    </head>
    <body>
        <div class="offline-container">
            <div class="offline-icon">
                <i class="fas fa-wifi-slash">📡</i>
            </div>
            <h1 class="offline-title">No Internet Connection</h1>
            <p class="offline-message">
                You're currently offline. Please check your internet connection and try again.
            </p>
            <button class="retry-btn" onclick="window.location.reload()">
                Try Again
            </button>
            <a href="/dashboard" class="home-btn">
                Go to Dashboard
            </a>
            <div class="status-indicator status-offline" id="connectionStatus">
                <span id="statusText">Offline</span>
            </div>
        </div>

        <script>
            // Monitor connection status
            function updateConnectionStatus() {
                const statusEl = document.getElementById('connectionStatus');
                const statusText = document.getElementById('statusText');

                if (navigator.onLine) {
                    statusEl.className = 'status-indicator status-online';
                    statusText.textContent = 'Back Online - Click Try Again';
                } else {
                    statusEl.className = 'status-indicator status-offline';
                    statusText.textContent = 'Offline';
                }
            }

            window.addEventListener('online', updateConnectionStatus);
            window.addEventListener('offline', updateConnectionStatus);

            // Auto-retry when back online
            window.addEventListener('online', () => {
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            });

            updateConnectionStatus();
        </script>
    </body>
    </html>
  `;

  return new Response(offlineHTML, {
    status: 503,
    headers: {
      'Content-Type': 'text/html'
    }
  });
}

// Background sync for form submissions (foundation for future use)
self.addEventListener('sync', event => {
  if (event.tag === 'background-sync') {
    console.log('Service Worker: Background sync triggered');
    event.waitUntil(doBackgroundSync());
  }
});

async function doBackgroundSync() {
  // Placeholder for background sync functionality
  // This could be used for offline form submissions in the future
  console.log('Service Worker: Performing background sync');
}

// Handle cache invalidation messages
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'INVALIDATE_CACHE') {
    console.log('Service Worker: Cache invalidation requested');
    invalidateCache(event.data.pattern);
  } else if (event.data && event.data.type === 'CLEAR_ALL_CACHE') {
    console.log('Service Worker: Clear all cache requested');
    clearAllCache();
  }
});

// Invalidate specific cache patterns
async function invalidateCache(pattern) {
  try {
    const cacheNames = await caches.keys();

    for (const cacheName of cacheNames) {
      const cache = await caches.open(cacheName);
      const requests = await cache.keys();

      for (const request of requests) {
        if (pattern && request.url.includes(pattern)) {
          await cache.delete(request);
          console.log('Service Worker: Invalidated cache for', request.url);
        }
      }
    }

    lastCacheInvalidation = Date.now();
  } catch (error) {
    console.error('Service Worker: Error invalidating cache', error);
  }
}

// Clear all dynamic cache
async function clearAllCache() {
  try {
    await caches.delete(DYNAMIC_CACHE);
    console.log('Service Worker: All dynamic cache cleared');
    lastCacheInvalidation = Date.now();
  } catch (error) {
    console.error('Service Worker: Error clearing cache', error);
  }
}

// Push notifications (foundation for future use)
self.addEventListener('push', event => {
  if (event.data) {
    const data = event.data.json();
    const options = {
      body: data.body,
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      vibrate: [100, 50, 100],
      data: {
        dateOfArrival: Date.now(),
        primaryKey: data.primaryKey
      },
      actions: [
        {
          action: 'explore',
          title: 'View Details',
          icon: '/icons/checkmark.png'
        },
        {
          action: 'close',
          title: 'Close',
          icon: '/icons/xmark.png'
        }
      ]
    };
    
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// Notification click handling
self.addEventListener('notificationclick', event => {
  event.notification.close();
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/dashboard')
    );
  }
});

// Error handling
self.addEventListener('error', event => {
  console.error('Service Worker error:', event.error);
});

self.addEventListener('unhandledrejection', event => {
  console.error('Service Worker unhandled rejection:', event.reason);
});

console.log('Service Worker: Script loaded');
