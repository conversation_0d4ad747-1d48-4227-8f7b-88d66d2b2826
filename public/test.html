<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Creation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; }
        textarea { width: 100%; height: 200px; }
    </style>
</head>
<body>
    <h1>SmartFlo Project Creation Test</h1>
    
    <div class="test-section">
        <h2>1. Authentication Test</h2>
        <button onclick="testAuth()">Test Authentication</button>
        <div id="authResult"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Debug Endpoint Test</h2>
        <button onclick="testDebug()">Test Debug Endpoint</button>
        <div id="debugResult"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Project Creation Test</h2>
        <button onclick="testProjectCreation()">Test Project Creation</button>
        <div id="projectResult"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Full Form Test</h2>
        <button onclick="testFullForm()">Test Full Form Submission</button>
        <div id="formResult"></div>
    </div>
    
    <div class="test-section">
        <h2>Response Log</h2>
        <textarea id="responseLog" readonly></textarea>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        function log(message) {
            const logArea = document.getElementById('responseLog');
            logArea.value += new Date().toISOString() + ': ' + message + '\n';
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('responseLog').value = '';
        }
        
        async function testAuth() {
            const resultDiv = document.getElementById('authResult');
            resultDiv.innerHTML = '<div class="info">Testing authentication...</div>';
            
            try {
                const response = await fetch('http://************:8080/dashboard', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                log(`Auth test response: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    resultDiv.innerHTML = '<div class="success">✅ Authentication working</div>';
                } else if (response.status === 302 || response.status === 401) {
                    resultDiv.innerHTML = '<div class="error">❌ Not authenticated - redirected to login</div>';
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Unexpected response: ${response.status}</div>`;
                }
            } catch (error) {
                log(`Auth test error: ${error.message}`);
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        async function testDebug() {
            const resultDiv = document.getElementById('debugResult');
            resultDiv.innerHTML = '<div class="info">Testing debug endpoint...</div>';
            
            try {
                const response = await fetch('http://************:8080/projects/debugCreate', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                const text = await response.text();
                log(`Debug test response: ${response.status} ${response.statusText}`);
                log(`Debug response body: ${text.substring(0, 500)}...`);
                
                if (response.ok) {
                    try {
                        const data = JSON.parse(text);
                        resultDiv.innerHTML = `<div class="success">✅ Debug endpoint working</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
                    } catch (e) {
                        resultDiv.innerHTML = `<div class="error">❌ Response not JSON: ${text.substring(0, 200)}...</div>`;
                    }
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Debug endpoint failed: ${response.status}</div>`;
                }
            } catch (error) {
                log(`Debug test error: ${error.message}`);
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        async function testProjectCreation() {
            const resultDiv = document.getElementById('projectResult');
            resultDiv.innerHTML = '<div class="info">Testing project creation...</div>';
            
            const testData = {
                project_id: 'TEST-' + Date.now(),
                project_name: 'Test Project ' + new Date().toISOString(),
                client_name: 'Test Client',
                client_mobile: '+1234567890',
                location: 'Test Location',
                description: 'This is a test project',
                start_date: new Date().toISOString().split('T')[0],
                target_completion: new Date(Date.now() + 30*24*60*60*1000).toISOString().split('T')[0],
                'task_category[]': 'office',
                'task_type[]': 'planning',
                'assigned_to[]': '1'
            };
            
            const formData = new FormData();
            Object.keys(testData).forEach(key => {
                formData.append(key, testData[key]);
            });
            
            log(`Project creation data: ${JSON.stringify(testData, null, 2)}`);
            
            try {
                const response = await fetch('http://************:8080/projects/create', {
                    method: 'POST',
                    body: formData,
                    credentials: 'include',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                const text = await response.text();
                log(`Project creation response: ${response.status} ${response.statusText}`);
                log(`Project creation body: ${text}`);
                
                if (response.ok) {
                    try {
                        const data = JSON.parse(text);
                        if (data.success) {
                            resultDiv.innerHTML = `<div class="success">✅ Project created successfully!</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
                        } else {
                            resultDiv.innerHTML = `<div class="error">❌ Project creation failed: ${data.message}</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
                        }
                    } catch (e) {
                        resultDiv.innerHTML = `<div class="error">❌ Response not JSON: ${text.substring(0, 500)}...</div>`;
                    }
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ HTTP Error: ${response.status}</div><div>${text.substring(0, 500)}...</div>`;
                }
            } catch (error) {
                log(`Project creation error: ${error.message}`);
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        async function testFullForm() {
            const resultDiv = document.getElementById('formResult');
            resultDiv.innerHTML = '<div class="info">Testing full form workflow...</div>';
            
            // First test if we can access the create page
            try {
                const pageResponse = await fetch('http://************:8080/projects/create', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                log(`Create page response: ${pageResponse.status} ${pageResponse.statusText}`);
                
                if (pageResponse.ok) {
                    resultDiv.innerHTML += '<div class="success">✅ Create page accessible</div>';
                    
                    // Now test the form submission
                    await testProjectCreation();
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Cannot access create page: ${pageResponse.status}</div>`;
                }
            } catch (error) {
                log(`Full form test error: ${error.message}`);
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        // Auto-run tests on page load
        window.onload = function() {
            log('Test page loaded');
        };
    </script>
</body>
</html>
