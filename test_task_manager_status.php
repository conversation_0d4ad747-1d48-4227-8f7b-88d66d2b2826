<?php
// Test script for updateTaskManagerStatus
echo "<h2>Testing updateTaskManagerStatus endpoint</h2>";

// Test task ID
$taskId = 13;

echo "<h3>1. Testing Task Lookup</h3>";

// Database connection
$host = 'localhost';
$dbname = 'smartflo_auth';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if task exists
    $stmt = $pdo->prepare("SELECT id, task_name, status, task_manager_status FROM project_tasks WHERE id = ?");
    $stmt->execute([$taskId]);
    $task = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($task) {
        echo "✅ Task found: " . json_encode($task) . "<br>";
    } else {
        echo "❌ Task not found with ID: $taskId<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

echo "<h3>2. Testing Endpoint URL</h3>";
$url = "http://localhost:8080/projects/updateTaskManagerStatus/$taskId";
echo "URL: $url<br>";

echo "<h3>3. Testing with cURL (without CSRF)</h3>";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    'status' => 'sent_for_review',
    'payment_amount' => 1000,
    'payment_status' => 'unpaid',
    'notes' => 'Test submission'
]));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'X-Requested-With: XMLHttpRequest'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode<br>";
echo "Response: " . htmlspecialchars($response) . "<br>";

echo "<h3>4. Route Check</h3>";
echo "Expected route: POST /projects/updateTaskManagerStatus/(:num)<br>";
echo "Actual URL: $url<br>";

echo "<h3>5. Recommendations</h3>";
if ($httpCode == 403) {
    echo "❌ CSRF protection is blocking the request<br>";
    echo "✅ This is expected - the endpoint exists and is working<br>";
    echo "✅ The issue is likely in the frontend CSRF token handling<br>";
} elseif ($httpCode == 404) {
    echo "❌ Route not found - check Routes.php<br>";
} elseif ($httpCode == 500) {
    echo "❌ Server error - check logs<br>";
}

echo "<p><a href='/projects' target='_blank'>Test Projects Page</a></p>";
?>
