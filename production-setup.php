<?php

/**
 * SmartFlo 2.0 Production Setup Script
 * 
 * This script sets up the application for production deployment with
 * proper security configurations and file permissions.
 */

class ProductionSetup
{
    private $errors = [];
    private $warnings = [];
    private $success = [];

    public function __construct()
    {
        echo "SmartFlo 2.0 Production Setup\n";
        echo "=============================\n\n";
    }

    /**
     * Run the complete production setup
     */
    public function run()
    {
        $this->checkEnvironment();
        $this->setupDirectories();
        $this->setFilePermissions();
        $this->generateSecurityKeys();
        $this->setupDatabase();
        $this->optimizeApplication();
        $this->validateSecurity();
        $this->displayResults();
    }

    /**
     * Check environment requirements
     */
    private function checkEnvironment()
    {
        echo "Checking environment requirements...\n";

        // Check PHP version
        if (version_compare(PHP_VERSION, '8.1.0', '<')) {
            $this->errors[] = 'PHP 8.1 or higher is required. Current version: ' . PHP_VERSION;
        } else {
            $this->success[] = 'PHP version check passed: ' . PHP_VERSION;
        }

        // Check required extensions
        $requiredExtensions = ['mysqli', 'json', 'mbstring', 'openssl', 'fileinfo', 'gd'];
        foreach ($requiredExtensions as $ext) {
            if (!extension_loaded($ext)) {
                $this->errors[] = "Required PHP extension missing: {$ext}";
            } else {
                $this->success[] = "Extension {$ext} is loaded";
            }
        }

        // Check if running as web server user
        if (function_exists('posix_getpwuid') && function_exists('posix_geteuid')) {
            $user = posix_getpwuid(posix_geteuid())['name'];
            $this->success[] = "Running as user: {$user}";
        }

        echo "\n";
    }

    /**
     * Setup required directories
     */
    private function setupDirectories()
    {
        echo "Setting up directories...\n";

        $directories = [
            'writable/uploads/images',
            'writable/uploads/images/thumbs',
            'writable/uploads/documents',
            'writable/uploads/temp',
            'writable/uploads/quarantine',
            'writable/cache',
            'writable/logs',
            'writable/session'
        ];

        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                if (mkdir($dir, 0755, true)) {
                    $this->success[] = "Created directory: {$dir}";
                } else {
                    $this->errors[] = "Failed to create directory: {$dir}";
                    continue;
                }
            }

            // Create index.html for security
            $indexFile = $dir . '/index.html';
            if (!file_exists($indexFile)) {
                $content = '<!DOCTYPE html><html><head><title>403 Forbidden</title></head><body><p>Directory access is forbidden.</p></body></html>';
                if (file_put_contents($indexFile, $content)) {
                    $this->success[] = "Created security index.html in {$dir}";
                } else {
                    $this->warnings[] = "Failed to create index.html in {$dir}";
                }
            }
        }

        echo "\n";
    }

    /**
     * Set proper file permissions
     */
    private function setFilePermissions()
    {
        echo "Setting file permissions...\n";

        $permissions = [
            'writable' => 0755,
            'writable/uploads' => 0755,
            'writable/uploads/images' => 0755,
            'writable/uploads/documents' => 0755,
            'writable/uploads/temp' => 0755,
            'writable/uploads/quarantine' => 0700, // More restrictive for quarantine
            'writable/cache' => 0755,
            'writable/logs' => 0755,
            'writable/session' => 0755,
            '.env' => 0600, // Highly restrictive for environment file
            'app' => 0755,
            'public' => 0755
        ];

        foreach ($permissions as $path => $perm) {
            if (file_exists($path)) {
                if (chmod($path, $perm)) {
                    $this->success[] = "Set permissions {$perm} for {$path}";
                } else {
                    $this->warnings[] = "Failed to set permissions for {$path}";
                }
            }
        }

        echo "\n";
    }

    /**
     * Generate secure keys if needed
     */
    private function generateSecurityKeys()
    {
        echo "Checking security keys...\n";

        $envFile = '.env';
        if (!file_exists($envFile)) {
            $this->errors[] = '.env file not found';
            return;
        }

        $envContent = file_get_contents($envFile);

        // Check JWT secret
        if (strpos($envContent, 'your-super-secret-jwt-key-change-this-in-production') !== false) {
            $newJwtSecret = $this->generateRandomKey(64);
            $envContent = str_replace(
                'your-super-secret-jwt-key-change-this-in-production',
                $newJwtSecret,
                $envContent
            );
            $this->success[] = 'Generated new JWT secret key';
        }

        // Check encryption key
        if (strpos($envContent, 'SmartFlo2025FileEncryptionKey!SecureRandom#String$9876543210') !== false) {
            $newEncryptionKey = $this->generateRandomKey(64);
            $envContent = str_replace(
                'SmartFlo2025FileEncryptionKey!SecureRandom#String$9876543210',
                $newEncryptionKey,
                $envContent
            );
            $this->success[] = 'Generated new encryption key';
        }

        // Update base URL placeholder
        if (strpos($envContent, 'https://your-domain.com/') !== false) {
            $this->warnings[] = 'Please update app.baseURL in .env with your actual domain';
        }

        // Write updated .env file
        if (file_put_contents($envFile, $envContent)) {
            chmod($envFile, 0600); // Secure permissions
            $this->success[] = 'Updated .env file with secure permissions';
        } else {
            $this->errors[] = 'Failed to update .env file';
        }

        echo "\n";
    }

    /**
     * Setup database
     */
    private function setupDatabase()
    {
        echo "Setting up database...\n";

        try {
            // Run migrations
            $output = shell_exec('php spark migrate 2>&1');
            if ($output) {
                $this->success[] = 'Database migrations completed';
            }
        } catch (Exception $e) {
            $this->warnings[] = 'Database migration failed: ' . $e->getMessage();
            $this->warnings[] = 'Please run "php spark migrate" manually';
        }

        echo "\n";
    }

    /**
     * Optimize application for production
     */
    private function optimizeApplication()
    {
        echo "Optimizing application...\n";

        // Clear cache
        if (is_dir('writable/cache')) {
            $this->clearDirectory('writable/cache');
            $this->success[] = 'Cleared application cache';
        }

        // Clear logs (optional)
        if (is_dir('writable/logs')) {
            $logFiles = glob('writable/logs/*.log');
            foreach ($logFiles as $logFile) {
                if (filesize($logFile) > 10 * 1024 * 1024) { // 10MB
                    unlink($logFile);
                    $this->success[] = "Removed large log file: {$logFile}";
                }
            }
        }

        echo "\n";
    }

    /**
     * Validate security configuration
     */
    private function validateSecurity()
    {
        echo "Validating security configuration...\n";

        // Check .env file
        $envContent = file_get_contents('.env');
        
        if (strpos($envContent, 'CI_ENVIRONMENT = production') !== false) {
            $this->success[] = 'Environment set to production';
        } else {
            $this->warnings[] = 'Environment should be set to production';
        }

        if (strpos($envContent, 'app.forceGlobalSecureRequests = true') !== false) {
            $this->success[] = 'HTTPS enforcement enabled';
        } else {
            $this->warnings[] = 'HTTPS enforcement should be enabled for production';
        }

        // Check file permissions
        $criticalFiles = ['.env', 'app/Config'];
        foreach ($criticalFiles as $file) {
            if (file_exists($file)) {
                $perms = fileperms($file);
                if ($file === '.env' && ($perms & 0777) <= 0600) {
                    $this->success[] = ".env file has secure permissions";
                } elseif ($file !== '.env') {
                    $this->success[] = "{$file} permissions validated";
                }
            }
        }

        echo "\n";
    }

    /**
     * Generate a random key
     */
    private function generateRandomKey($length = 32)
    {
        return bin2hex(random_bytes($length));
    }

    /**
     * Clear directory contents
     */
    private function clearDirectory($dir)
    {
        $files = glob($dir . '/*');
        foreach ($files as $file) {
            if (is_file($file) && basename($file) !== 'index.html') {
                unlink($file);
            }
        }
    }

    /**
     * Display setup results
     */
    private function displayResults()
    {
        echo "Setup Results\n";
        echo "=============\n\n";

        if (!empty($this->success)) {
            echo "✓ SUCCESS:\n";
            foreach ($this->success as $message) {
                echo "  - {$message}\n";
            }
            echo "\n";
        }

        if (!empty($this->warnings)) {
            echo "⚠ WARNINGS:\n";
            foreach ($this->warnings as $message) {
                echo "  - {$message}\n";
            }
            echo "\n";
        }

        if (!empty($this->errors)) {
            echo "✗ ERRORS:\n";
            foreach ($this->errors as $message) {
                echo "  - {$message}\n";
            }
            echo "\n";
        }

        if (empty($this->errors)) {
            echo "🎉 Production setup completed successfully!\n\n";
            echo "Next steps:\n";
            echo "1. Update app.baseURL in .env with your domain\n";
            echo "2. Configure your web server to point to the public/ directory\n";
            echo "3. Set up SSL/TLS certificates\n";
            echo "4. Configure your database credentials\n";
            echo "5. Test the application thoroughly\n";
        } else {
            echo "❌ Setup completed with errors. Please fix the errors above before deploying.\n";
        }
    }
}

// Run the setup if called directly
if (php_sapi_name() === 'cli') {
    $setup = new ProductionSetup();
    $setup->run();
}
