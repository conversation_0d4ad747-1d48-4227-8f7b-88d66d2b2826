// Browser Console Test for Send for Client Review
// Copy and paste this into the browser console while on the projects page

console.log('🔍 Starting Send for Client Review Debug Test');

// 1. Check if user is authenticated and has the right data
console.log('1. Checking authentication and user data...');
if (typeof user !== 'undefined') {
    console.log('✅ User object found:', user);
    console.log('   - User ID:', user.id);
    console.log('   - Username:', user.username);
    console.log('   - Roles:', user.roles);
    
    if (user.roles === 'admin' || user.roles === 'manager') {
        console.log('✅ User has manager privileges');
    } else {
        console.log('❌ User does NOT have manager privileges');
    }
} else {
    console.log('❌ User object not found - authentication issue');
}

// 2. Check CSRF token availability
console.log('2. Checking CSRF token...');
const metaToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
const hiddenToken = document.querySelector('input[name="csrf_token"]')?.value;

console.log('   - Meta tag token:', metaToken ? metaToken.substring(0, 20) + '...' : 'Not found');
console.log('   - Hidden input token:', hiddenToken ? hiddenToken.substring(0, 20) + '...' : 'Not found');

const csrfToken = metaToken || hiddenToken;
if (csrfToken) {
    console.log('✅ CSRF token available');
} else {
    console.log('❌ No CSRF token found');
}

// 3. Check if the updateTaskManagerStatus function exists
console.log('3. Checking if functions exist...');
if (typeof updateTaskManagerStatus === 'function') {
    console.log('✅ updateTaskManagerStatus function exists');
} else {
    console.log('❌ updateTaskManagerStatus function not found');
}

if (typeof submitPaymentDetails === 'function') {
    console.log('✅ submitPaymentDetails function exists');
} else {
    console.log('❌ submitPaymentDetails function not found');
}

// 4. Test the actual endpoint with a real request
console.log('4. Testing updateTaskManagerStatus endpoint...');

async function testEndpoint() {
    if (!csrfToken) {
        console.log('❌ Cannot test without CSRF token');
        return;
    }
    
    const formData = new FormData();
    formData.append('status', 'sent_for_review');
    formData.append('payment_amount', '5000');
    formData.append('payment_status', 'paid');
    formData.append('payment_account', 'browser_console_test');
    formData.append('notes', 'Browser console test - ' + new Date().toISOString());
    formData.append('csrf_token', csrfToken);
    
    const headers = {
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-TOKEN': csrfToken
    };
    
    console.log('🔄 Sending test request to task 110...');
    console.log('   - URL: /projects/updateTaskManagerStatus/110');
    console.log('   - Method: POST');
    console.log('   - Headers:', headers);
    console.log('   - Form Data:', Object.fromEntries(formData.entries()));
    
    try {
        const response = await fetch('/projects/updateTaskManagerStatus/110', {
            method: 'POST',
            body: formData,
            headers: headers
        });
        
        console.log('📥 Response received:');
        console.log('   - Status:', response.status, response.statusText);
        console.log('   - Headers:', Object.fromEntries(response.headers.entries()));
        
        const responseText = await response.text();
        console.log('   - Raw response:', responseText);
        
        try {
            const jsonData = JSON.parse(responseText);
            console.log('   - Parsed JSON:', jsonData);
            
            if (jsonData.success) {
                console.log('🎉 SUCCESS: Request worked!');
                console.log('   - Message:', jsonData.message);
            } else {
                console.log('❌ FAILED: Request failed');
                console.log('   - Error:', jsonData.message);
            }
        } catch (e) {
            console.log('⚠️ Response is not JSON:', responseText.substring(0, 200));
        }
        
    } catch (error) {
        console.log('❌ Network error:', error);
    }
}

// 5. Check if there are any "Send for Review" buttons visible
console.log('5. Checking for Send for Review buttons...');
const sendReviewButtons = document.querySelectorAll('[onclick*="updateTaskManagerStatus"]');
console.log('   - Found', sendReviewButtons.length, 'Send for Review buttons');

sendReviewButtons.forEach((btn, index) => {
    console.log(`   - Button ${index + 1}:`, btn.outerHTML.substring(0, 100) + '...');
});

// 6. Check for completed tasks that should show the button
console.log('6. Checking for completed tasks...');
const taskCards = document.querySelectorAll('[data-task-id]');
console.log('   - Found', taskCards.length, 'task cards');

// Run the endpoint test
console.log('7. Running endpoint test...');
testEndpoint();

console.log('🔍 Debug test complete. Check the results above.');
console.log('📋 To manually test:');
console.log('   1. Look for a "Send for Review" button on a completed task');
console.log('   2. Click it and fill in the payment details');
console.log('   3. Check the Network tab in DevTools for the actual request');
console.log('   4. Check the Console for any JavaScript errors');
