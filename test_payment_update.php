<?php
// Test payment update functionality
require_once 'app/Config/Database.php';

$db = \Config\Database::connect();

echo "<h2>Testing Payment Update Functionality</h2>";

try {
    // 1. Check if required columns exist
    echo "<h3>1. Database Columns Check</h3>";
    
    $columns = $db->query("SHOW COLUMNS FROM project_tasks")->getResultArray();
    $columnNames = array_column($columns, 'Field');
    
    $requiredColumns = ['task_manager_status', 'payment_amount', 'payment_status', 'payment_account', 'google_drive_link'];
    
    foreach ($requiredColumns as $column) {
        if (in_array($column, $columnNames)) {
            echo "<p>✅ {$column} column exists</p>";
        } else {
            echo "<p>❌ {$column} column missing</p>";
        }
    }
    
    // 2. Find a completed task to test with
    echo "<h3>2. Finding Test Task</h3>";
    
    $task = $db->query("SELECT id, task_name, status, task_manager_status, payment_amount, payment_status FROM project_tasks WHERE status = 'completed' LIMIT 1")->getRowArray();
    
    if (!$task) {
        echo "<p>❌ No completed tasks found. Creating test task...</p>";
        
        // Create a test task
        $projectId = $db->query("SELECT id FROM projects LIMIT 1")->getRowArray()['id'];
        $userId = $db->query("SELECT id FROM users LIMIT 1")->getRowArray()['id'];
        
        $db->query("INSERT INTO project_tasks (project_id, task_type_id, task_name, assigned_to, status, task_order) VALUES (?, 1, 'Test Payment Task', ?, 'completed', 1)", [$projectId, $userId]);
        
        $taskId = $db->insertID();
        $task = $db->query("SELECT id, task_name, status, task_manager_status, payment_amount, payment_status FROM project_tasks WHERE id = ?", [$taskId])->getRowArray();
        
        echo "<p>✅ Created test task: {$task['id']}</p>";
    }
    
    echo "<p><strong>Test Task:</strong> ID {$task['id']} - {$task['task_name']}</p>";
    echo "<p>Current Status: {$task['status']} | Manager Status: " . ($task['task_manager_status'] ?: 'NULL') . " | Payment: ₹" . ($task['payment_amount'] ?: '0') . " ({$task['payment_status']})</p>";
    
    // 3. Test the update
    echo "<h3>3. Testing Payment Update</h3>";
    
    $testData = [
        'task_manager_status' => 'sent_for_review',
        'payment_amount' => 7500.00,
        'payment_status' => 'paid',
        'payment_account' => 'bank_account_1',
        'google_drive_link' => 'https://drive.google.com/drive/folders/test123'
    ];
    
    $sql = "UPDATE project_tasks SET 
            task_manager_status = ?, 
            payment_amount = ?, 
            payment_status = ?, 
            payment_account = ?, 
            google_drive_link = ?,
            updated_at = NOW()
            WHERE id = ?";
    
    $result = $db->query($sql, [
        $testData['task_manager_status'],
        $testData['payment_amount'],
        $testData['payment_status'],
        $testData['payment_account'],
        $testData['google_drive_link'],
        $task['id']
    ]);
    
    if ($result) {
        echo "<p>✅ Update query executed successfully</p>";
        
        // Verify the update
        $updatedTask = $db->query("SELECT id, task_name, status, task_manager_status, payment_amount, payment_status, payment_account, google_drive_link FROM project_tasks WHERE id = ?", [$task['id']])->getRowArray();
        
        echo "<p><strong>After Update:</strong></p>";
        echo "<ul>";
        echo "<li>Manager Status: {$updatedTask['task_manager_status']}</li>";
        echo "<li>Payment Amount: ₹{$updatedTask['payment_amount']}</li>";
        echo "<li>Payment Status: {$updatedTask['payment_status']}</li>";
        echo "<li>Payment Account: {$updatedTask['payment_account']}</li>";
        echo "<li>Google Drive: " . ($updatedTask['google_drive_link'] ? 'Yes' : 'No') . "</li>";
        echo "</ul>";
        
        // Check if all fields were updated correctly
        $allCorrect = true;
        foreach ($testData as $field => $expectedValue) {
            if ($updatedTask[$field] != $expectedValue) {
                echo "<p>❌ {$field}: Expected '{$expectedValue}', got '{$updatedTask[$field]}'</p>";
                $allCorrect = false;
            }
        }
        
        if ($allCorrect) {
            echo "<p>✅ All fields updated correctly!</p>";
        }
        
    } else {
        echo "<p>❌ Update query failed</p>";
    }
    
    // 4. Test the API endpoint
    echo "<h3>4. API Endpoint Test</h3>";
    echo "<p>To test the API endpoint, use this curl command:</p>";
    echo "<pre style='background: #f0f0f0; padding: 10px; border-radius: 5px;'>";
    echo "curl -X POST 'http://localhost:8080/projects/updateTaskManagerStatus/{$task['id']}' \\\n";
    echo "  -H 'Content-Type: application/x-www-form-urlencoded' \\\n";
    echo "  -H 'X-Requested-With: XMLHttpRequest' \\\n";
    echo "  -d 'status=sent_for_review&payment_amount=5000&payment_status=paid&payment_account=bank_account_1&google_drive_link=https://drive.google.com/test'";
    echo "</pre>";
    
    // 5. Show current billing data
    echo "<h3>5. Current Billing Data</h3>";
    
    $billingData = $db->query("
        SELECT pt.id, pt.task_name, pt.payment_amount, pt.payment_status, pt.task_manager_status, p.project_name
        FROM project_tasks pt 
        JOIN projects p ON p.id = pt.project_id 
        WHERE pt.payment_amount > 0 
        ORDER BY pt.updated_at DESC 
        LIMIT 5
    ")->getResultArray();
    
    if (empty($billingData)) {
        echo "<p>❌ No payment data found</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Task ID</th><th>Project</th><th>Task</th><th>Amount</th><th>Status</th><th>Manager Status</th></tr>";
        foreach ($billingData as $row) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['project_name']}</td>";
            echo "<td>{$row['task_name']}</td>";
            echo "<td>₹{$row['payment_amount']}</td>";
            echo "<td>{$row['payment_status']}</td>";
            echo "<td>{$row['task_manager_status']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>✅ Test Complete!</h3>";
    echo "<p><a href='/projects' target='_blank'>Test Projects Page</a> | <a href='/billing' target='_blank'>Test Billing Page</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>
