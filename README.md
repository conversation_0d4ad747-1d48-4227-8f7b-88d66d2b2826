# SmartFlo 2.0 - Modular Platform with Secure Authentication

A comprehensive, secure PHP-based modular platform with authentication system and admin panel built using CodeIgniter 4. This system provides enterprise-grade security features including CSRF protection, rate limiting, secure session management, and comprehensive user/role management. The modular architecture allows for easy expansion with additional modules.

## 🚀 Features

### Core Authentication System
- **Username-only login** (no email required for login)
- **Secure password hashing** using P<PERSON>'s `password_hash()` with `PASSWORD_DEFAULT`
- **Remember Me functionality** with secure persistent tokens (30-day expiration)
- **Email-based password reset** with cryptographically secure tokens (1-hour expiration)
- **Rate limiting** for login attempts (5 attempts per 15 minutes per IP/username)
- **CSRF protection** on all forms
- **SQL injection prevention** using PDO prepared statements
- **Session security** with httpOnly, secure flags, and session regeneration
- **Input validation and sanitization**

### Admin Panel Features
- **Dashboard** with system statistics and charts
- **User Management**: Create, edit, delete, activate/deactivate users
- **Role Management**: Create, edit, delete roles with granular permissions
- **Security Monitoring**: Failed login attempts, IP tracking
- **Responsive Design**: Mobile-first approach with Bootstrap 5
- **Real-time Updates**: AJAX-powered interface

### Modular Architecture
- **Module System**: Support for independent, self-contained modules
- **Dynamic Navigation**: Automatic integration of module navigation
- **Permission Integration**: Modules inherit the core permission system
- **Easy Expansion**: Simple module development and integration
- **Future-Ready**: Prepared for CMS, E-commerce, CRM, and other modules

### Technical Features
- **PHP 8.1+** compatibility
- **MySQL 8.0+** database with optimized schema
- **CodeIgniter 4** framework
- **JWT token support** for API endpoints
- **Comprehensive logging** of security events
- **Database migrations** and seeders
- **Unit tests** for critical functions

## 📋 Requirements

- PHP 8.1 or higher
- MySQL 8.0 or higher
- Composer
- Web server (Apache/Nginx)
- PHP extensions: mysqli, json, mbstring, openssl

## 🛠️ Installation

### 1. Install Dependencies
```bash
composer install
```

### 2. Environment Configuration
Configure your settings in `.env`:
```env
# Database Configuration
database.default.hostname = localhost
database.default.database = smartflo_auth
database.default.username = your_db_user
database.default.password = your_db_password

# Server Configuration
app.baseURL = 'http://************:8080/'

# Email Configuration (for password reset)
email.SMTPHost = smtp.gmail.com
email.SMTPUser = <EMAIL>
email.SMTPPass = your-app-password
email.SMTPPort = 587

# Security Keys (Generate strong random keys for production)
jwt.secret = your-super-secret-jwt-key-change-this-in-production
```

### 3. Database Setup

#### Option A: Using the Installation Script
```bash
php install.php
```

#### Option B: Manual Setup
```bash
mysql -u your_username -p < database/schema.sql
php spark migrate
php spark db:seed AdminSeeder
```

### 4. Set Permissions
```bash
chmod -R 755 writable/
```

### 5. Start the Development Server
```bash
php spark serve --host ************ --port 8080
```

Visit `http://************:8080` in your browser.

## 🔐 Default Credentials

**Admin User:**
- Username: `admin`
- Password: `Admin@123`
- Email: `<EMAIL>`

**⚠️ Important:** Change the default admin password immediately after first login!

## 📁 Project Structure

```
SmartFlo-2.0/
├── app/
│   ├── Controllers/
│   │   ├── Auth.php                 # Authentication controller
│   │   └── Admin/
│   │       ├── Dashboard.php        # Admin dashboard
│   │       ├── Users.php           # User management
│   │       └── Roles.php           # Role management
│   ├── Models/
│   │   ├── UserModel.php           # User data model
│   │   ├── RoleModel.php           # Role data model
│   │   ├── UserRoleModel.php       # User-role relationships
│   │   ├── RememberTokenModel.php  # Remember me tokens
│   │   ├── PasswordResetModel.php  # Password reset tokens
│   │   └── LoginAttemptModel.php   # Rate limiting
│   ├── Libraries/
│   │   ├── AuthLibrary.php         # Authentication service
│   │   ├── SecurityLibrary.php     # Security utilities
│   │   └── ModuleManager.php       # Module management system
│   ├── Modules/                    # Future modules directory
│   │   └── README.md              # Module development guide
│   ├── Filters/
│   │   ├── AuthFilter.php          # Authentication middleware
│   │   └── AdminFilter.php         # Admin access middleware
│   ├── Views/
│   │   ├── auth/                   # Authentication views
│   │   └── admin/                  # Admin panel views
│   ├── Config/
│   │   └── SmartFloModules.php     # Module configuration
│   └── Database/
│       ├── Migrations/             # Database migrations
│       └── Seeds/                  # Database seeders
├── database/
│   └── schema.sql                  # Complete database schema
├── .env                           # Environment configuration
├── composer.json                  # PHP dependencies
├── install.php                    # Installation script
├── SECURITY.md                    # Security documentation
└── README.md                      # This file
```

## 🔧 Module Development

### Creating a New Module

1. Create module directory: `app/Modules/YourModule/`
2. Follow the standard structure:
```
app/Modules/YourModule/
├── Controllers/
├── Models/
├── Views/
├── Config/
└── module.json
```

3. Create `module.json`:
```json
{
    "name": "Your Module",
    "version": "1.0.0",
    "description": "Description of your module",
    "author": "Your Name",
    "navigation": [
        {
            "title": "Your Module",
            "url": "/your-module",
            "icon": "fas fa-module",
            "permission": "module.access"
        }
    ]
}
```

4. The module will automatically appear in the admin navigation

## 🔒 Security Features

### Password Requirements
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character

### Rate Limiting
- **Login attempts**: 5 attempts per 15 minutes per IP/username
- **Password reset**: 3 requests per hour per email
- Automatic IP blocking for excessive attempts

### Session Security
- Secure session configuration
- Session regeneration on login
- HttpOnly and Secure flags
- Automatic session timeout

### CSRF Protection
- CSRF tokens on all forms
- Token validation on all POST requests
- Automatic token regeneration

## 🎯 Usage

### Accessing the System
1. Open your browser and go to: http://************:8080
2. Login with the admin credentials
3. Explore the admin panel features
4. Change the default password immediately

### User Management
1. Navigate to **Admin Panel** → **User Management**
2. **Create User**: Add new users with roles
3. **Edit User**: Modify user details and permissions
4. **Activate/Deactivate**: Control user access
5. **Delete User**: Remove users (with confirmation)

### Role Management
1. Navigate to **Admin Panel** → **Role Management**
2. **Create Role**: Define new roles with specific permissions
3. **Edit Role**: Modify role permissions
4. **View Role**: See role details and assigned users
5. **Delete Role**: Remove unused roles

## 🚀 Production Deployment

### Automated Production Setup
Run the production setup script to automatically configure security settings:
```bash
php production-setup.php
```

### Security Features Implemented
- **Secure File Upload System**: Complete blob storage with image processing
- **Advanced Security Headers**: HSTS, CSP, XSS protection, and more
- **File Access Control**: User-based permissions and public/private files
- **Upload Security**: File validation, virus scanning, rate limiting
- **Encryption Support**: Optional file encryption for sensitive data
- **Audit Logging**: Complete activity tracking and monitoring

### Security Checklist
- [ ] Run production setup script (`php production-setup.php`)
- [ ] Change default admin password
- [ ] Update app.baseURL in .env with your domain
- [ ] Configure proper SMTP settings
- [ ] Set up SSL/TLS certificates
- [ ] Configure firewall rules
- [ ] Verify file permissions (script handles this)
- [ ] Enable error logging
- [ ] Review security checklist in `SECURITY-CHECKLIST.md`

### File Upload & Blob Storage
The system now includes a complete secure file upload and blob storage system:
- **File Manager**: `/blob/manager` - Web interface for file management
- **Upload API**: `/blob/upload/image` - Secure image upload endpoint
- **File Serving**: `/blob/image/{id}` - Secure file serving with access control
- **Thumbnails**: `/blob/thumbnail/{id}/{size}` - Automatic thumbnail generation

### Server Configuration
- Configure web server to point to `public/` directory
- Set up proper virtual host configuration
- Enable HTTPS with valid SSL certificates
- Configure proper PHP settings for production
- Ensure required PHP extensions are installed (GD, fileinfo, etc.)

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the documentation in `SECURITY.md`
- Review the module development guide in `app/Modules/README.md`
- Create an issue for bug reports or feature requests

---

**⚠️ Security Notice:** This system handles sensitive user data. Always follow security best practices, keep dependencies updated, and conduct regular security audits.
